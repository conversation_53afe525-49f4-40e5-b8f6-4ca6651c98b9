(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,28157,91457,e=>{"use strict";e.s(["Button",()=>l,"buttonVariants",()=>a],28157);var t=e.i(67857),n=e.i(24302),r=e.i(81692),o=e.i(68316),i=e.i(92072);let a=(0,o.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-primary",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-destructive",outline:"border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-secondary",ghost:"hover:bg-accent hover:text-accent-foreground border-transparent",link:"text-primary underline-offset-4 hover:underline border-transparent"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=n.forwardRef((e,n)=>{let{className:o,variant:l,size:u,asChild:c=!1,...s}=e,f=c?r.Slot:"button";return(0,t.jsx)(f,{className:(0,i.cn)(a({variant:l,size:u,className:o})),ref:n,...s})});l.displayName="Button",e.s(["Input",()=>u],91457);let u=n.forwardRef((e,n)=>{let{className:r,type:o,...a}=e;return(0,t.jsx)("input",{type:o,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:n,...a})});u.displayName="Input"},40971,e=>{"use strict";e.s(["useFocusGuards",()=>r]);var t=e.i(24302),n=0;function r(){t.useEffect(()=>{var e,t;let r=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=r[0])?e:o()),document.body.insertAdjacentElement("beforeend",null!=(t=r[1])?t:o()),n++,()=>{1===n&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),n--}},[])}function o(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},89920,e=>{"use strict";e.s(["FocusScope",()=>c]);var t=e.i(24302),n=e.i(51906),r=e.i(32054),o=e.i(83875),i=e.i(67857),a="focusScope.autoFocusOnMount",l="focusScope.autoFocusOnUnmount",u={bubbles:!1,cancelable:!0},c=t.forwardRef((e,c)=>{let{loop:h=!1,trapped:m=!1,onMountAutoFocus:g,onUnmountAutoFocus:v,...y}=e,[b,w]=t.useState(null),x=(0,o.useCallbackRef)(g),E=(0,o.useCallbackRef)(v),R=t.useRef(null),S=(0,n.useComposedRefs)(c,e=>w(e)),C=t.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;t.useEffect(()=>{if(m){let e=function(e){if(C.paused||!b)return;let t=e.target;b.contains(t)?R.current=t:d(R.current,{select:!0})},t=function(e){if(C.paused||!b)return;let t=e.relatedTarget;null!==t&&(b.contains(t)||d(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&d(b)});return b&&n.observe(b,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[m,b,C.paused]),t.useEffect(()=>{if(b){p.add(C);let e=document.activeElement;if(!b.contains(e)){let t=new CustomEvent(a,u);b.addEventListener(a,x),b.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(d(r,{select:t}),document.activeElement!==n)return}(s(b).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&d(b))}return()=>{b.removeEventListener(a,x),setTimeout(()=>{let t=new CustomEvent(l,u);b.addEventListener(l,E),b.dispatchEvent(t),t.defaultPrevented||d(null!=e?e:document.body,{select:!0}),b.removeEventListener(l,E),p.remove(C)},0)}}},[b,x,E,C]);let P=t.useCallback(e=>{if(!h&&!m||C.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,n=document.activeElement;if(t&&n){let t=e.currentTarget,[r,o]=function(e){let t=s(e);return[f(t,e),f(t.reverse(),e)]}(t);r&&o?e.shiftKey||n!==o?e.shiftKey&&n===r&&(e.preventDefault(),h&&d(o,{select:!0})):(e.preventDefault(),h&&d(r,{select:!0})):n===t&&e.preventDefault()}},[h,m,C.paused]);return(0,i.jsx)(r.Primitive.div,{tabIndex:-1,...y,ref:S,onKeyDown:P})});function s(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function f(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function d(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}c.displayName="FocusScope";var p=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=h(e,t)).unshift(t)},remove(t){var n;null==(n=(e=h(e,t))[0])||n.resume()}}}();function h(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},36641,e=>{"use strict";e.s(["useId",()=>i]);var t=e.i(24302),n=e.i(3961),r=t[" useId ".trim().toString()]||(()=>void 0),o=0;function i(e){let[i,a]=t.useState(r());return(0,n.useLayoutEffect)(()=>{e||a(e=>null!=e?e:String(o++))},[e]),e||(i?"radix-".concat(i):"")}},36838,e=>{"use strict";e.s(["Anchor",()=>e$,"Arrow",()=>e0,"Content",()=>eJ,"Root",()=>eQ,"createPopperScope",()=>eD],36838);var t=e.i(24302);let n=["top","right","bottom","left"],r=Math.min,o=Math.max,i=Math.round,a=Math.floor,l=e=>({x:e,y:e}),u={left:"right",right:"left",bottom:"top",top:"bottom"},c={start:"end",end:"start"};function s(e,t){return"function"==typeof e?e(t):e}function f(e){return e.split("-")[0]}function d(e){return e.split("-")[1]}function p(e){return"x"===e?"y":"x"}function h(e){return"y"===e?"height":"width"}let m=new Set(["top","bottom"]);function g(e){return m.has(f(e))?"y":"x"}function v(e){return e.replace(/start|end/g,e=>c[e])}let y=["left","right"],b=["right","left"],w=["top","bottom"],x=["bottom","top"];function E(e){return e.replace(/left|right|bottom|top/g,e=>u[e])}function R(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function S(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function C(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),l=p(g(t)),u=h(l),c=f(t),s="y"===a,m=o.x+o.width/2-i.width/2,v=o.y+o.height/2-i.height/2,y=o[u]/2-i[u]/2;switch(c){case"top":r={x:m,y:o.y-i.height};break;case"bottom":r={x:m,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:v};break;case"left":r={x:o.x-i.width,y:v};break;default:r={x:o.x,y:o.y}}switch(d(t)){case"start":r[l]-=y*(n&&s?-1:1);break;case"end":r[l]+=y*(n&&s?-1:1)}return r}let P=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=C(c,r,u),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:g,y:v,data:y,reset:b}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=g?g:s,f=null!=v?v:f,p={...p,[i]:{...p[i],...y}},b&&h<=50&&(h++,"object"==typeof b&&(b.placement&&(d=b.placement),b.rects&&(c=!0===b.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):b.rects),{x:s,y:f}=C(c,d,u)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function A(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:c="clippingAncestors",rootBoundary:f="viewport",elementContext:d="floating",altBoundary:p=!1,padding:h=0}=s(t,e),m=R(h),g=l[p?"floating"===d?"reference":"floating":d],v=S(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(g)))||n?g:g.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:c,rootBoundary:f,strategy:u})),y="floating"===d?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,b=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(b))&&await (null==i.getScale?void 0:i.getScale(b))||{x:1,y:1},x=S(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:y,offsetParent:b,strategy:u}):y);return{top:(v.top-x.top+m.top)/w.y,bottom:(x.bottom-v.bottom+m.bottom)/w.y,left:(v.left-x.left+m.left)/w.x,right:(x.right-v.right+m.right)/w.x}}function O(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function T(e){return n.some(t=>e[t]>=0)}let L=new Set(["left","top"]);async function j(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=f(n),l=d(n),u="y"===g(n),c=L.has(a)?-1:1,p=i&&u?-1:1,h=s(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof h?{mainAxis:h,crossAxis:0,alignmentAxis:null}:{mainAxis:h.mainAxis||0,crossAxis:h.crossAxis||0,alignmentAxis:h.alignmentAxis};return l&&"number"==typeof y&&(v="end"===l?-1*y:y),u?{x:v*p,y:m*c}:{x:m*c,y:v*p}}function N(){return"undefined"!=typeof window}function k(e){return _(e)?(e.nodeName||"").toLowerCase():"#document"}function M(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function D(e){var t;return null==(t=(_(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function _(e){return!!N()&&(e instanceof Node||e instanceof M(e).Node)}function I(e){return!!N()&&(e instanceof Element||e instanceof M(e).Element)}function F(e){return!!N()&&(e instanceof HTMLElement||e instanceof M(e).HTMLElement)}function W(e){return!!N()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof M(e).ShadowRoot)}let B=new Set(["inline","contents"]);function H(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=$(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!B.has(o)}let U=new Set(["table","td","th"]),z=[":popover-open",":modal"];function K(e){return z.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let V=["transform","translate","scale","rotate","perspective"],X=["transform","translate","scale","rotate","perspective","filter"],Y=["paint","layout","strict","content"];function q(e){let t=Z(),n=I(e)?$(e):e;return V.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||X.some(e=>(n.willChange||"").includes(e))||Y.some(e=>(n.contain||"").includes(e))}function Z(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let G=new Set(["html","body","#document"]);function Q(e){return G.has(k(e))}function $(e){return M(e).getComputedStyle(e)}function J(e){return I(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function ee(e){if("html"===k(e))return e;let t=e.assignedSlot||e.parentNode||W(e)&&e.host||D(e);return W(t)?t.host:t}function et(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=ee(t);return Q(n)?t.ownerDocument?t.ownerDocument.body:t.body:F(n)&&H(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=M(o);if(i){let e=en(a);return t.concat(a,a.visualViewport||[],H(o)?o:[],e&&n?et(e):[])}return t.concat(o,et(o,[],n))}function en(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function er(e){let t=$(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=F(e),a=o?e.offsetWidth:n,l=o?e.offsetHeight:r,u=i(n)!==a||i(r)!==l;return u&&(n=a,r=l),{width:n,height:r,$:u}}function eo(e){return I(e)?e:e.contextElement}function ei(e){let t=eo(e);if(!F(t))return l(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:a}=er(t),u=(a?i(n.width):n.width)/r,c=(a?i(n.height):n.height)/o;return u&&Number.isFinite(u)||(u=1),c&&Number.isFinite(c)||(c=1),{x:u,y:c}}let ea=l(0);function el(e){let t=M(e);return Z()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:ea}function eu(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eo(e),u=l(1);t&&(r?I(r)&&(u=ei(r)):u=ei(e));let c=(void 0===(o=n)&&(o=!1),r&&(!o||r===M(a))&&o)?el(a):l(0),s=(i.left+c.x)/u.x,f=(i.top+c.y)/u.y,d=i.width/u.x,p=i.height/u.y;if(a){let e=M(a),t=r&&I(r)?M(r):r,n=e,o=en(n);for(;o&&r&&t!==n;){let e=ei(o),t=o.getBoundingClientRect(),r=$(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=en(n=M(o))}}return S({width:d,height:p,x:s,y:f})}function ec(e,t){let n=J(e).scrollLeft;return t?t.left+n:eu(D(e)).left+n}function es(e,t){let n=e.getBoundingClientRect();return{x:n.left+t.scrollLeft-ec(e,n),y:n.top+t.scrollTop}}let ef=new Set(["absolute","fixed"]);function ed(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=M(e),r=D(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=Z();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}let c=ec(r);if(c<=0){let e=r.ownerDocument,t=e.body,n=getComputedStyle(t),o="CSS1Compat"===e.compatMode&&parseFloat(n.marginLeft)+parseFloat(n.marginRight)||0,a=Math.abs(r.clientWidth-t.clientWidth-o);a<=25&&(i-=a)}else c<=25&&(i+=c);return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=D(e),n=J(e),r=e.ownerDocument.body,i=o(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),a=o(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),l=-n.scrollLeft+ec(e),u=-n.scrollTop;return"rtl"===$(r).direction&&(l+=o(t.clientWidth,r.clientWidth)-i),{width:i,height:a,x:l,y:u}}(D(e));else if(I(t))r=function(e,t){let n=eu(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=F(e)?ei(e):l(1),a=e.clientWidth*i.x,u=e.clientHeight*i.y;return{width:a,height:u,x:o*i.x,y:r*i.y}}(t,n);else{let n=el(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return S(r)}function ep(e){return"static"===$(e).position}function eh(e,t){if(!F(e)||"fixed"===$(e).position)return null;if(t)return t(e);let n=e.offsetParent;return D(e)===n&&(n=n.ownerDocument.body),n}function em(e,t){var n;let r=M(e);if(K(e))return r;if(!F(e)){let t=ee(e);for(;t&&!Q(t);){if(I(t)&&!ep(t))return t;t=ee(t)}return r}let o=eh(e,t);for(;o&&(n=o,U.has(k(n)))&&ep(o);)o=eh(o,t);return o&&Q(o)&&ep(o)&&!q(o)?r:o||function(e){let t=ee(e);for(;F(t)&&!Q(t);){if(q(t))return t;if(K(t))break;t=ee(t)}return null}(e)||r}let eg=async function(e){let t=this.getOffsetParent||em,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=F(t),o=D(t),i="fixed"===n,a=eu(e,!0,i,t),u={scrollLeft:0,scrollTop:0},c=l(0);if(r||!r&&!i)if(("body"!==k(t)||H(o))&&(u=J(t)),r){let e=eu(t,!0,i,t);c.x=e.x+t.clientLeft,c.y=e.y+t.clientTop}else o&&(c.x=ec(o));i&&!r&&o&&(c.x=ec(o));let s=!o||r||i?l(0):es(o,u);return{x:a.left+u.scrollLeft-c.x-s.x,y:a.top+u.scrollTop-c.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ev={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=D(r),u=!!t&&K(t.floating);if(r===a||u&&i)return n;let c={scrollLeft:0,scrollTop:0},s=l(1),f=l(0),d=F(r);if((d||!d&&!i)&&(("body"!==k(r)||H(a))&&(c=J(r)),F(r))){let e=eu(r);s=ei(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?l(0):es(a,c);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-c.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-c.scrollTop*s.y+f.y+p.y}},getDocumentElement:D,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:i,strategy:a}=e,l=[..."clippingAncestors"===n?K(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=et(e,[],!1).filter(e=>I(e)&&"body"!==k(e)),o=null,i="fixed"===$(e).position,a=i?ee(e):e;for(;I(a)&&!Q(a);){let t=$(a),n=q(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&ef.has(o.position)||H(a)&&!n&&function e(t,n){let r=ee(t);return!(r===n||!I(r)||Q(r))&&("fixed"===$(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=ee(a)}return t.set(e,r),r}(t,this._c):[].concat(n),i],u=l[0],c=l.reduce((e,n)=>{let i=ed(t,n,a);return e.top=o(i.top,e.top),e.right=r(i.right,e.right),e.bottom=r(i.bottom,e.bottom),e.left=o(i.left,e.left),e},ed(t,u,a));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:em,getElementRects:eg,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=er(e);return{width:t,height:n}},getScale:ei,isElement:I,isRTL:function(e){return"rtl"===$(e).direction}};function ey(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let eb=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:i,placement:a,rects:l,platform:u,elements:c,middlewareData:f}=t,{element:m,padding:v=0}=s(e,t)||{};if(null==m)return{};let y=R(v),b={x:n,y:i},w=p(g(a)),x=h(w),E=await u.getDimensions(m),S="y"===w,C=S?"clientHeight":"clientWidth",P=l.reference[x]+l.reference[w]-b[w]-l.floating[x],A=b[w]-l.reference[w],O=await (null==u.getOffsetParent?void 0:u.getOffsetParent(m)),T=O?O[C]:0;T&&await (null==u.isElement?void 0:u.isElement(O))||(T=c.floating[C]||l.floating[x]);let L=T/2-E[x]/2-1,j=r(y[S?"top":"left"],L),N=r(y[S?"bottom":"right"],L),k=T-E[x]-N,M=T/2-E[x]/2+(P/2-A/2),D=o(j,r(M,k)),_=!f.arrow&&null!=d(a)&&M!==D&&l.reference[x]/2-(M<j?j:N)-E[x]/2<0,I=_?M<j?M-j:M-k:0;return{[w]:b[w]+I,data:{[w]:D,centerOffset:M-D-I,..._&&{alignmentOffset:I}},reset:_}}});var ew=e.i(79238),ex="undefined"!=typeof document?t.useLayoutEffect:function(){};function eE(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eE(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eE(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eR(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eS(e,t){let n=eR(e);return Math.round(t*n)/n}function eC(e){let n=t.useRef(e);return ex(()=>{n.current=e}),n}var eP=e.i(32054),eA=e.i(67857),eO=t.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eA.jsx)(eP.Primitive.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eA.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eO.displayName="Arrow";var eT=e.i(51906),eL=e.i(76825),ej=e.i(83875),eN=e.i(3961),ek="Popper",[eM,eD]=(0,eL.createContextScope)(ek),[e_,eI]=eM(ek),eF=e=>{let{__scopePopper:n,children:r}=e,[o,i]=t.useState(null);return(0,eA.jsx)(e_,{scope:n,anchor:o,onAnchorChange:i,children:r})};eF.displayName=ek;var eW="PopperAnchor",eB=t.forwardRef((e,n)=>{let{__scopePopper:r,virtualRef:o,...i}=e,a=eI(eW,r),l=t.useRef(null),u=(0,eT.useComposedRefs)(n,l),c=t.useRef(null);return t.useEffect(()=>{let e=c.current;c.current=(null==o?void 0:o.current)||l.current,e!==c.current&&a.onAnchorChange(c.current)}),o?null:(0,eA.jsx)(eP.Primitive.div,{...i,ref:u})});eB.displayName=eW;var eH="PopperContent",[eU,ez]=eM(eH),eK=t.forwardRef((e,n)=>{var i,l,u,c,m,R,S,C;let{__scopePopper:N,side:k="bottom",sideOffset:M=0,align:_="center",alignOffset:I=0,arrowPadding:F=0,avoidCollisions:W=!0,collisionBoundary:B=[],collisionPadding:H=0,sticky:U="partial",hideWhenDetached:z=!1,updatePositionStrategy:K="optimized",onPlaced:V,...X}=e,Y=eI(eH,N),[q,Z]=t.useState(null),G=(0,eT.useComposedRefs)(n,e=>Z(e)),[Q,$]=t.useState(null),J=function(e){let[n,r]=t.useState(void 0);return(0,eN.useLayoutEffect)(()=>{if(e){r({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let n,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;n=t.inlineSize,o=t.blockSize}else n=e.offsetWidth,o=e.offsetHeight;r({width:n,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}r(void 0)},[e]),n}(Q),ee=null!=(S=null==J?void 0:J.width)?S:0,en=null!=(C=null==J?void 0:J.height)?C:0,er="number"==typeof H?H:{top:0,right:0,bottom:0,left:0,...H},ei=Array.isArray(B)?B:[B],ea=ei.length>0,el={padding:er,boundary:ei.filter(eq),altBoundary:ea},{refs:ec,floatingStyles:es,placement:ef,isPositioned:ed,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:n="bottom",strategy:r="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:c,open:s}=e,[f,d]=t.useState({x:0,y:0,strategy:r,placement:n,middlewareData:{},isPositioned:!1}),[p,h]=t.useState(o);eE(p,o)||h(o);let[m,g]=t.useState(null),[v,y]=t.useState(null),b=t.useCallback(e=>{e!==R.current&&(R.current=e,g(e))},[]),w=t.useCallback(e=>{e!==S.current&&(S.current=e,y(e))},[]),x=a||m,E=l||v,R=t.useRef(null),S=t.useRef(null),C=t.useRef(f),A=null!=c,O=eC(c),T=eC(i),L=eC(s),j=t.useCallback(()=>{if(!R.current||!S.current)return;let e={placement:n,strategy:r,middleware:p};T.current&&(e.platform=T.current),((e,t,n)=>{let r=new Map,o={platform:ev,...n},i={...o.platform,_c:r};return P(e,t,{...o,platform:i})})(R.current,S.current,e).then(e=>{let t={...e,isPositioned:!1!==L.current};N.current&&!eE(C.current,t)&&(C.current=t,ew.flushSync(()=>{d(t)}))})},[p,n,r,T,L]);ex(()=>{!1===s&&C.current.isPositioned&&(C.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let N=t.useRef(!1);ex(()=>(N.current=!0,()=>{N.current=!1}),[]),ex(()=>{if(x&&(R.current=x),E&&(S.current=E),x&&E){if(O.current)return O.current(x,E,j);j()}},[x,E,j,O,A]);let k=t.useMemo(()=>({reference:R,floating:S,setReference:b,setFloating:w}),[b,w]),M=t.useMemo(()=>({reference:x,floating:E}),[x,E]),D=t.useMemo(()=>{let e={position:r,left:0,top:0};if(!M.floating)return e;let t=eS(M.floating,f.x),n=eS(M.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+n+"px)",...eR(M.floating)>=1.5&&{willChange:"transform"}}:{position:r,left:t,top:n}},[r,u,M.floating,f.x,f.y]);return t.useMemo(()=>({...f,update:j,refs:k,elements:M,floatingStyles:D}),[f,j,k,M,D])}({strategy:"fixed",placement:k+("center"!==_?"-"+_:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,i){let l;void 0===i&&(i={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=i,p=eo(e),h=u||c?[...p?et(p):[],...et(t)]:[];h.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,i=null,l=D(e);function u(){var e;clearTimeout(n),null==(e=i)||e.disconnect(),i=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),u();let d=e.getBoundingClientRect(),{left:p,top:h,width:m,height:g}=d;if(s||t(),!m||!g)return;let v=a(h),y=a(l.clientWidth-(p+m)),b={rootMargin:-v+"px "+-y+"px "+-a(l.clientHeight-(h+g))+"px "+-a(p)+"px",threshold:o(0,r(1,f))||1},w=!0;function x(t){let r=t[0].intersectionRatio;if(r!==f){if(!w)return c();r?c(!1,r):n=setTimeout(()=>{c(!1,1e-7)},1e3)}1!==r||ey(d,e.getBoundingClientRect())||c(),w=!1}try{i=new IntersectionObserver(x,{...b,root:l.ownerDocument})}catch(e){i=new IntersectionObserver(x,b)}i.observe(e)}(!0),u}(p,n):null,g=-1,v=null;s&&(v=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&v&&(v.unobserve(t),cancelAnimationFrame(g),g=requestAnimationFrame(()=>{var e;null==(e=v)||e.observe(t)})),n()}),p&&!d&&v.observe(p),v.observe(t));let y=d?eu(e):null;return d&&function t(){let r=eu(e);y&&!ey(y,r)&&n(),y=r,l=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=v)||e.disconnect(),v=null,d&&cancelAnimationFrame(l)}}(...t,{animationFrame:"always"===K})},elements:{reference:Y.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await j(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}))({mainAxis:M+en,alignmentAxis:I}),W&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:i,placement:a}=t,{mainAxis:l=!0,crossAxis:u=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...d}=s(e,t),h={x:n,y:i},m=await A(t,d),v=g(f(a)),y=p(v),b=h[y],w=h[v];if(l){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=b+m[e],i=b-m[t];b=o(n,r(b,i))}if(u){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=w+m[e],i=w-m[t];w=o(n,r(w,i))}let x=c.fn({...t,[y]:b,[v]:w});return{...x,data:{x:x.x-n,y:x.y-i,enabled:{[y]:l,[v]:u}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===U?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:c=!0}=s(e,t),d={x:n,y:r},h=g(o),m=p(h),v=d[m],y=d[h],b=s(l,t),w="number"==typeof b?{mainAxis:b,crossAxis:0}:{mainAxis:0,crossAxis:0,...b};if(u){let e="y"===m?"height":"width",t=i.reference[m]-i.floating[e]+w.mainAxis,n=i.reference[m]+i.reference[e]-w.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===m?"width":"height",t=L.has(f(o)),n=i.reference[h]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[h])||0)+(t?0:w.crossAxis),r=i.reference[h]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[h])||0)-(t?w.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[m]:v,[h]:y}}}}(e),options:[e,t]}))():void 0,...el}),W&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:c,initialPlacement:m,platform:R,elements:S}=t,{mainAxis:C=!0,crossAxis:P=!0,fallbackPlacements:O,fallbackStrategy:T="bestFit",fallbackAxisSideDirection:L="none",flipAlignment:j=!0,...N}=s(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let k=f(l),M=g(m),D=f(m)===m,_=await (null==R.isRTL?void 0:R.isRTL(S.floating)),I=O||(D||!j?[E(m)]:function(e){let t=E(e);return[v(e),t,v(t)]}(m)),F="none"!==L;!O&&F&&I.push(...function(e,t,n,r){let o=d(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?b:y;return t?y:b;case"left":case"right":return t?w:x;default:return[]}}(f(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(v)))),i}(m,j,L,_));let W=[m,...I],B=await A(t,N),H=[],U=(null==(r=u.flip)?void 0:r.overflows)||[];if(C&&H.push(B[k]),P){let e=function(e,t,n){void 0===n&&(n=!1);let r=d(e),o=p(g(e)),i=h(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=E(a)),[a,E(a)]}(l,c,_);H.push(B[e[0]],B[e[1]])}if(U=[...U,{placement:l,overflows:H}],!H.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=W[e];if(t&&("alignment"!==P||M===g(t)||U.every(e=>g(e.placement)!==M||e.overflows[0]>0)))return{data:{index:e,overflows:U},reset:{placement:t}};let n=null==(i=U.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(T){case"bestFit":{let e=null==(a=U.filter(e=>{if(F){let t=g(e.placement);return t===M||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=m}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...el}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,i;let a,l,{placement:u,rects:c,platform:p,elements:h}=t,{apply:m=()=>{},...v}=s(e,t),y=await A(t,v),b=f(u),w=d(u),x="y"===g(u),{width:E,height:R}=c.floating;"top"===b||"bottom"===b?(a=b,l=w===(await (null==p.isRTL?void 0:p.isRTL(h.floating))?"start":"end")?"left":"right"):(l=b,a="end"===w?"top":"bottom");let S=R-y.top-y.bottom,C=E-y.left-y.right,P=r(R-y[a],S),O=r(E-y[l],C),T=!t.middlewareData.shift,L=P,j=O;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(j=C),null!=(i=t.middlewareData.shift)&&i.enabled.y&&(L=S),T&&!w){let e=o(y.left,0),t=o(y.right,0),n=o(y.top,0),r=o(y.bottom,0);x?j=E-2*(0!==e||0!==t?e+t:o(y.left,y.right)):L=R-2*(0!==n||0!==r?n+r:o(y.top,y.bottom))}await m({...t,availableWidth:j,availableHeight:L});let N=await p.getDimensions(h.floating);return E!==N.width||R!==N.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...el,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),Q&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?eb({element:n.current,padding:r}).fn(t):{}:n?eb({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:Q,padding:F}),eZ({arrowWidth:ee,arrowHeight:en}),z&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=s(e,t);switch(r){case"referenceHidden":{let e=O(await A(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:T(e)}}}case"escaped":{let e=O(await A(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:T(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...el})]}),[eh,em]=eG(ef),eg=(0,ej.useCallbackRef)(V);(0,eN.useLayoutEffect)(()=>{ed&&(null==eg||eg())},[ed,eg]);let eO=null==(i=ep.arrow)?void 0:i.x,eL=null==(l=ep.arrow)?void 0:l.y,ek=(null==(u=ep.arrow)?void 0:u.centerOffset)!==0,[eM,eD]=t.useState();return(0,eN.useLayoutEffect)(()=>{q&&eD(window.getComputedStyle(q).zIndex)},[q]),(0,eA.jsx)("div",{ref:ec.setFloating,"data-radix-popper-content-wrapper":"",style:{...es,transform:ed?es.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eM,"--radix-popper-transform-origin":[null==(c=ep.transformOrigin)?void 0:c.x,null==(m=ep.transformOrigin)?void 0:m.y].join(" "),...(null==(R=ep.hide)?void 0:R.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eA.jsx)(eU,{scope:N,placedSide:eh,onArrowChange:$,arrowX:eO,arrowY:eL,shouldHideArrow:ek,children:(0,eA.jsx)(eP.Primitive.div,{"data-side":eh,"data-align":em,...X,ref:G,style:{...X.style,animation:ed?void 0:"none"}})})})});eK.displayName=eH;var eV="PopperArrow",eX={top:"bottom",right:"left",bottom:"top",left:"right"},eY=t.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=ez(eV,n),i=eX[o.placedSide];return(0,eA.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eA.jsx)(eO,{...r,ref:t,style:{...r.style,display:"block"}})})});function eq(e){return null!==e}eY.displayName=eV;var eZ=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:c}=t,s=(null==(n=c.arrow)?void 0:n.centerOffset)!==0,f=s?0:e.arrowWidth,d=s?0:e.arrowHeight,[p,h]=eG(l),m={start:"0%",center:"50%",end:"100%"}[h],g=(null!=(i=null==(r=c.arrow)?void 0:r.x)?i:0)+f/2,v=(null!=(a=null==(o=c.arrow)?void 0:o.y)?a:0)+d/2,y="",b="";return"bottom"===p?(y=s?m:"".concat(g,"px"),b="".concat(-d,"px")):"top"===p?(y=s?m:"".concat(g,"px"),b="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),b=s?m:"".concat(v,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),b=s?m:"".concat(v,"px")),{data:{x:y,y:b}}}});function eG(e){let[t,n="center"]=e.split("-");return[t,n]}var eQ=eF,e$=eB,eJ=eK,e0=eY},15119,e=>{"use strict";e.s(["hideOthers",()=>l]);var t=new WeakMap,n=new WeakMap,r={},o=0,i=function(e){return e&&(e.host||i(e.parentNode))},a=function(e,a,l,u){var c=(Array.isArray(e)?e:[e]).map(function(e){if(a.contains(e))return e;var t=i(e);return t&&a.contains(t)?t:(console.error("aria-hidden",e,"in not contained inside",a,". Doing nothing"),null)}).filter(function(e){return!!e});r[l]||(r[l]=new WeakMap);var s=r[l],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var r=e.getAttribute(u),o=null!==r&&"false"!==r,i=(t.get(e)||0)+1,a=(s.get(e)||0)+1;t.set(e,i),s.set(e,a),f.push(e),1===i&&o&&n.set(e,!0),1===a&&e.setAttribute(l,"true"),o||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(a),d.clear(),o++,function(){f.forEach(function(e){var r=t.get(e)-1,o=s.get(e)-1;t.set(e,r),s.set(e,o),r||(n.has(e)||e.removeAttribute(u),n.delete(e)),o||e.removeAttribute(l)}),--o||(t=new WeakMap,t=new WeakMap,n=new WeakMap,r={})}},l=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),a(r,o,n,"aria-hidden")):function(){return null}}},67084,e=>{"use strict";e.s(["RemoveScroll",()=>K],67084);var t,n,r=function(){return(r=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function o(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var i=("function"==typeof SuppressedError&&SuppressedError,e.i(24302)),a="right-scroll-bar-position",l="width-before-scroll-bar";function u(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var c="undefined"!=typeof window?i.useLayoutEffect:i.useEffect,s=new WeakMap;function f(e){return e}var d=function(e){void 0===e&&(e={});var t,n,o,i=(void 0===t&&(t=f),n=[],o=!1,{read:function(){if(o)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var r=t(e,o);return n.push(r),function(){n=n.filter(function(e){return e!==r})}},assignSyncMedium:function(e){for(o=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){o=!0;var t=[];if(n.length){var r=n;n=[],r.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return i.options=r({async:!0,ssr:!1},e),i}(),p=function(){},h=i.forwardRef(function(e,t){var n,a,l,f,h=i.useRef(null),m=i.useState({onScrollCapture:p,onWheelCapture:p,onTouchMoveCapture:p}),g=m[0],v=m[1],y=e.forwardProps,b=e.children,w=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,S=e.sideCar,C=e.noRelative,P=e.noIsolation,A=e.inert,O=e.allowPinchZoom,T=e.as,L=e.gapMode,j=o(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),N=(n=[h,t],a=function(e){return n.forEach(function(t){return u(t,e)})},(l=(0,i.useState)(function(){return{value:null,callback:a,facade:{get current(){return l.value},set current(value){var e=l.value;e!==value&&(l.value=value,l.callback(value,e))}}}})[0]).callback=a,f=l.facade,c(function(){var e=s.get(f);if(e){var t=new Set(e),r=new Set(n),o=f.current;t.forEach(function(e){r.has(e)||u(e,null)}),r.forEach(function(e){t.has(e)||u(e,o)})}s.set(f,n)},[n]),f),k=r(r({},j),g);return i.createElement(i.Fragment,null,E&&i.createElement(S,{sideCar:d,removeScrollBar:x,shards:R,noRelative:C,noIsolation:P,inert:A,setCallbacks:v,allowPinchZoom:!!O,lockRef:h,gapMode:L}),y?i.cloneElement(i.Children.only(b),r(r({},k),{ref:N})):i.createElement(void 0===T?"div":T,r({},k,{className:w,ref:N}),b))});h.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},h.classNames={fullWidth:l,zeroRight:a};var m=function(e){var t=e.sideCar,n=o(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var a=t.read();if(!a)throw Error("Sidecar medium not found");return i.createElement(a,r({},n))};m.isSideCarExport=!0;var g=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=n||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return t&&e.setAttribute("nonce",t),e}())){var o,i;(o=t).styleSheet?o.styleSheet.cssText=r:o.appendChild(document.createTextNode(r)),i=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(i)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},v=function(){var e=g();return function(t,n){i.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},y=function(){var e=v();return function(t){return e(t.styles,t.dynamic),null}},b={left:0,top:0,right:0,gap:0},w=function(e){return parseInt(e||"",10)||0},x=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[w(n),w(r),w(o)]},E=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return b;var t=x(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},R=y(),S="data-scroll-locked",C=function(e,t,n,r){var o=e.left,i=e.top,u=e.right,c=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(c,"px ").concat(r,";\n  }\n  body[").concat(S,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(u,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(c,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(c,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(a," {\n    right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(l," {\n    margin-right: ").concat(c,"px ").concat(r,";\n  }\n  \n  .").concat(a," .").concat(a," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(l," .").concat(l," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(S,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(c,"px;\n  }\n")},P=function(){var e=parseInt(document.body.getAttribute(S)||"0",10);return isFinite(e)?e:0},A=function(){i.useEffect(function(){return document.body.setAttribute(S,(P()+1).toString()),function(){var e=P()-1;e<=0?document.body.removeAttribute(S):document.body.setAttribute(S,e.toString())}},[])},O=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;A();var a=i.useMemo(function(){return E(o)},[o]);return i.createElement(R,{styles:C(a,!t,o,n?"":"!important")})},T=!1;if("undefined"!=typeof window)try{var L=Object.defineProperty({},"passive",{get:function(){return T=!0,!0}});window.addEventListener("test",L,L),window.removeEventListener("test",L,L)}catch(e){T=!1}var j=!!T&&{passive:!1},N=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},k=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),M(e,r)){var o=D(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body)return!1},M=function(e,t){return"v"===e?N(t,"overflowY"):N(t,"overflowX")},D=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},_=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,c=t.contains(u),s=!1,f=l>0,d=0,p=0;do{if(!u)break;var h=D(e,u),m=h[0],g=h[1]-h[2]-a*m;(m||g)&&M(e,u)&&(d+=g,p+=m);var v=u.parentNode;u=v&&v.nodeType===Node.DOCUMENT_FRAGMENT_NODE?v.host:v}while(!c&&u!==document.body||c&&(t.contains(u)||t===u))return f&&(o&&1>Math.abs(d)||!o&&l>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(s=!0),s},I=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},F=function(e){return[e.deltaX,e.deltaY]},W=function(e){return e&&"current"in e?e.current:e},B=0,H=[];let U=(t=function(e){var t=i.useRef([]),n=i.useRef([0,0]),r=i.useRef(),o=i.useState(B++)[0],a=i.useState(y)[0],l=i.useRef(e);i.useEffect(function(){l.current=e},[e]),i.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(W),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=i.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!l.current.allowPinchZoom;var o,i=I(e),a=n.current,u="deltaX"in e?e.deltaX:a[0]-i[0],c="deltaY"in e?e.deltaY:a[1]-i[1],s=e.target,f=Math.abs(u)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=k(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=k(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||c)&&(r.current=o),!o)return!0;var p=r.current||o;return _(p,t,e,"h"===p?u:c,!0)},[]),c=i.useCallback(function(e){if(H.length&&H[H.length-1]===a){var n="deltaY"in e?F(e):I(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(l.current.shards||[]).map(W).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!l.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),s=i.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=i.useCallback(function(e){n.current=I(e),r.current=void 0},[]),d=i.useCallback(function(t){s(t.type,F(t),t.target,u(t,e.lockRef.current))},[]),p=i.useCallback(function(t){s(t.type,I(t),t.target,u(t,e.lockRef.current))},[]);i.useEffect(function(){return H.push(a),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",c,j),document.addEventListener("touchmove",c,j),document.addEventListener("touchstart",f,j),function(){H=H.filter(function(e){return e!==a}),document.removeEventListener("wheel",c,j),document.removeEventListener("touchmove",c,j),document.removeEventListener("touchstart",f,j)}},[]);var h=e.removeScrollBar,m=e.inert;return i.createElement(i.Fragment,null,m?i.createElement(a,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?i.createElement(O,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},d.useMedium(t),m);var z=i.forwardRef(function(e,t){return i.createElement(h,r({},e,{ref:t,sideCar:U}))});z.classNames=h.classNames;let K=z},48424,e=>{"use strict";e.s(["Close",()=>er,"Content",()=>ee,"Description",()=>en,"Overlay",()=>J,"Portal",()=>$,"Root",()=>G,"Title",()=>et,"Trigger",()=>Q,"WarningProvider",()=>X,"createDialogScope",()=>b]);var t=e.i(24302),n=e.i(61540),r=e.i(51906),o=e.i(76825),i=e.i(36641),a=e.i(89199),l=e.i(80435),u=e.i(89920),c=e.i(87804),s=e.i(22859),f=e.i(32054),d=e.i(40971),p=e.i(67084),h=e.i(15119),m=e.i(81692),g=e.i(67857),v="Dialog",[y,b]=(0,o.createContextScope)(v),[w,x]=y(v),E=e=>{let{__scopeDialog:n,children:r,open:o,defaultOpen:l,onOpenChange:u,modal:c=!0}=e,s=t.useRef(null),f=t.useRef(null),[d,p]=(0,a.useControllableState)({prop:o,defaultProp:null!=l&&l,onChange:u,caller:v});return(0,g.jsx)(w,{scope:n,triggerRef:s,contentRef:f,contentId:(0,i.useId)(),titleId:(0,i.useId)(),descriptionId:(0,i.useId)(),open:d,onOpenChange:p,onOpenToggle:t.useCallback(()=>p(e=>!e),[p]),modal:c,children:r})};E.displayName=v;var R="DialogTrigger",S=t.forwardRef((e,t)=>{let{__scopeDialog:o,...i}=e,a=x(R,o),l=(0,r.useComposedRefs)(t,a.triggerRef);return(0,g.jsx)(f.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":K(a.open),...i,ref:l,onClick:(0,n.composeEventHandlers)(e.onClick,a.onOpenToggle)})});S.displayName=R;var C="DialogPortal",[P,A]=y(C,{forceMount:void 0}),O=e=>{let{__scopeDialog:n,forceMount:r,children:o,container:i}=e,a=x(C,n);return(0,g.jsx)(P,{scope:n,forceMount:r,children:t.Children.map(o,e=>(0,g.jsx)(s.Presence,{present:r||a.open,children:(0,g.jsx)(c.Portal,{asChild:!0,container:i,children:e})}))})};O.displayName=C;var T="DialogOverlay",L=t.forwardRef((e,t)=>{let n=A(T,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=x(T,e.__scopeDialog);return i.modal?(0,g.jsx)(s.Presence,{present:r||i.open,children:(0,g.jsx)(N,{...o,ref:t})}):null});L.displayName=T;var j=(0,m.createSlot)("DialogOverlay.RemoveScroll"),N=t.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(T,n);return(0,g.jsx)(p.RemoveScroll,{as:j,allowPinchZoom:!0,shards:[o.contentRef],children:(0,g.jsx)(f.Primitive.div,{"data-state":K(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),k="DialogContent",M=t.forwardRef((e,t)=>{let n=A(k,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=x(k,e.__scopeDialog);return(0,g.jsx)(s.Presence,{present:r||i.open,children:i.modal?(0,g.jsx)(D,{...o,ref:t}):(0,g.jsx)(_,{...o,ref:t})})});M.displayName=k;var D=t.forwardRef((e,o)=>{let i=x(k,e.__scopeDialog),a=t.useRef(null),l=(0,r.useComposedRefs)(o,i.contentRef,a);return t.useEffect(()=>{let e=a.current;if(e)return(0,h.hideOthers)(e)},[]),(0,g.jsx)(I,{...e,ref:l,trapFocus:i.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,n.composeEventHandlers)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=i.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,n.composeEventHandlers)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,n.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault())})}),_=t.forwardRef((e,n)=>{let r=x(k,e.__scopeDialog),o=t.useRef(!1),i=t.useRef(!1);return(0,g.jsx)(I,{...e,ref:n,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var n,a;null==(n=e.onCloseAutoFocus)||n.call(e,t),t.defaultPrevented||(o.current||null==(a=r.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var n,a;null==(n=e.onInteractOutside)||n.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let l=t.target;(null==(a=r.triggerRef.current)?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),I=t.forwardRef((e,n)=>{let{__scopeDialog:o,trapFocus:i,onOpenAutoFocus:a,onCloseAutoFocus:c,...s}=e,f=x(k,o),p=t.useRef(null),h=(0,r.useComposedRefs)(n,p);return(0,d.useFocusGuards)(),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(u.FocusScope,{asChild:!0,loop:!0,trapped:i,onMountAutoFocus:a,onUnmountAutoFocus:c,children:(0,g.jsx)(l.DismissableLayer,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":K(f.open),...s,ref:h,onDismiss:()=>f.onOpenChange(!1)})}),(0,g.jsxs)(g.Fragment,{children:[(0,g.jsx)(q,{titleId:f.titleId}),(0,g.jsx)(Z,{contentRef:p,descriptionId:f.descriptionId})]})]})}),F="DialogTitle",W=t.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(F,n);return(0,g.jsx)(f.Primitive.h2,{id:o.titleId,...r,ref:t})});W.displayName=F;var B="DialogDescription",H=t.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=x(B,n);return(0,g.jsx)(f.Primitive.p,{id:o.descriptionId,...r,ref:t})});H.displayName=B;var U="DialogClose",z=t.forwardRef((e,t)=>{let{__scopeDialog:r,...o}=e,i=x(U,r);return(0,g.jsx)(f.Primitive.button,{type:"button",...o,ref:t,onClick:(0,n.composeEventHandlers)(e.onClick,()=>i.onOpenChange(!1))})});function K(e){return e?"open":"closed"}z.displayName=U;var V="DialogTitleWarning",[X,Y]=(0,o.createContext)(V,{contentName:k,titleName:F,docsSlug:"dialog"}),q=e=>{let{titleId:n}=e,r=Y(V),o="`".concat(r.contentName,"` requires a `").concat(r.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(r.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(r.docsSlug);return t.useEffect(()=>{n&&(document.getElementById(n)||console.error(o))},[o,n]),null},Z=e=>{let{contentRef:n,descriptionId:r}=e,o=Y("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return t.useEffect(()=>{var e;let t=null==(e=n.current)?void 0:e.getAttribute("aria-describedby");r&&t&&(document.getElementById(r)||console.warn(i))},[i,n,r]),null},G=E,Q=S,$=O,J=L,ee=M,et=W,en=H,er=z},83477,(e,t,n)=>{"use strict";function r(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function o(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function i(e){let t=new URLSearchParams;for(let[n,r]of Object.entries(e))if(Array.isArray(r))for(let e of r)t.append(n,o(e));else t.set(n,o(r));return t}function a(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(n,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(n,{assign:function(){return a},searchParamsToUrlQuery:function(){return r},urlQueryToSearchParams:function(){return i}})},42466,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(n,{formatUrl:function(){return i},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let r=e.r(88870)._(e.r(83477)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:n}=e,i=e.protocol||"",a=e.pathname||"",l=e.hash||"",u=e.query||"",c=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?c=t+e.host:n&&(c=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(c+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let s=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==c?(c="//"+(c||""),a&&"/"!==a[0]&&(a="/"+a)):c||(c=""),l&&"#"!==l[0]&&(l="#"+l),s&&"?"!==s[0]&&(s="?"+s),""+i+c+(a=a.replace(/[?#]/g,encodeURIComponent))+(s=s.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return i(e)}},56804,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=e.r(24302);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=i(e,r)),t&&(o.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),t.exports=n.default)},66936,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(n,{DecodeError:function(){return m},MiddlewareNotFoundError:function(){return b},MissingStaticPage:function(){return y},NormalizeError:function(){return g},PageNotFoundError:function(){return v},SP:function(){return p},ST:function(){return h},WEB_VITALS:function(){return r},execOnce:function(){return o},getDisplayName:function(){return c},getLocationOrigin:function(){return l},getURL:function(){return u},isAbsoluteUrl:function(){return a},isResSent:function(){return s},loadGetInitialProps:function(){return d},normalizeRepeatedSlashes:function(){return f},stringifyError:function(){return w}});let r=["CLS","FCP","FID","INP","LCP","TTFB"];function o(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let i=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,a=e=>i.test(e);function l(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function u(){let{href:e}=window.location,t=l();return e.substring(t.length)}function c(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function f(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function d(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await d(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Object.defineProperty(Error('"'+c(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let p="undefined"!=typeof performance,h=p&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class m extends Error{}class g extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class y extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class b extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}},19015,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=e.r(66936),o=e.r(38517);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},99535,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),Object.defineProperty(n,"errorOnce",{enumerable:!0,get:function(){return r}});let r=e=>{}},79431,(e,t,n)=>{"use strict";Object.defineProperty(n,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(n,{default:function(){return g},useLinkStatus:function(){return y}});let r=e.r(88870),o=e.r(67857),i=r._(e.r(24302)),a=e.r(42466),l=e.r(27462),u=e.r(56804),c=e.r(66936),s=e.r(25360);e.r(3083);let f=e.r(54372),d=e.r(19015),p=e.r(87004);e.r(99535);let h=e.r(68296);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function g(e){var t;let n,r,a,[g,y]=(0,i.useOptimistic)(f.IDLE_LINK_STATUS),b=(0,i.useRef)(null),{href:w,as:x,children:E,prefetch:R=null,passHref:S,replace:C,shallow:P,scroll:A,onClick:O,onMouseEnter:T,onTouchStart:L,legacyBehavior:j=!1,onNavigate:N,ref:k,unstable_dynamicOnHover:M,...D}=e;n=E,j&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let _=i.default.useContext(l.AppRouterContext),I=!1!==R,F=!1!==R?null===(t=R)||"auto"===t?h.FetchStrategy.PPR:h.FetchStrategy.Full:h.FetchStrategy.PPR,{href:W,as:B}=i.default.useMemo(()=>{let e=m(w);return{href:e,as:x?m(x):e}},[w,x]);j&&(r=i.default.Children.only(n));let H=j?r&&"object"==typeof r&&r.ref:k,U=i.default.useCallback(e=>(null!==_&&(b.current=(0,f.mountLinkInstance)(e,W,_,F,I,y)),()=>{b.current&&((0,f.unmountLinkForCurrentNavigation)(b.current),b.current=null),(0,f.unmountPrefetchableInstance)(e)}),[I,W,_,F,y]),z={ref:(0,u.useMergedRef)(U,H),onClick(e){j||"function"!=typeof O||O(e),j&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),_&&(e.defaultPrevented||function(e,t,n,r,o,a,l){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,d.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}i.default.startTransition(()=>{(0,p.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,W,B,b,C,A,N))},onMouseEnter(e){j||"function"!=typeof T||T(e),j&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),_&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)},onTouchStart:function(e){j||"function"!=typeof L||L(e),j&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),_&&I&&(0,f.onNavigationIntent)(e.currentTarget,!0===M)}};return(0,c.isAbsoluteUrl)(B)?z.href=B:j&&!S&&("a"!==r.type||"href"in r.props)||(z.href=(0,s.addBasePath)(B)),a=j?i.default.cloneElement(r,z):(0,o.jsx)("a",{...D,...z,children:n}),(0,o.jsx)(v.Provider,{value:g,children:a})}let v=(0,i.createContext)(f.IDLE_LINK_STATUS),y=()=>(0,i.useContext)(v);("function"==typeof n.default||"object"==typeof n.default&&null!==n.default)&&void 0===n.default.__esModule&&(Object.defineProperty(n.default,"__esModule",{value:!0}),Object.assign(n.default,n),t.exports=n.default)}]);