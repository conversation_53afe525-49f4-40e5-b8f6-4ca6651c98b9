"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-rewrite";
exports.ids = ["vendor-chunks/rehype-rewrite"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-rewrite/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/rehype-rewrite/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getCodeString: () => (/* binding */ getCodeString)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var hast_util_select__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-select */ \"(ssr)/./node_modules/hast-util-select/lib/index.js\");\n\n\n/** Get the node tree source code string */\nconst getCodeString = (data = [], code = '') => {\n    data.forEach((node) => {\n        if (node.type === 'text') {\n            code += node.value;\n        }\n        else if (node.type === 'element' && node.children && Array.isArray(node.children)) {\n            code += getCodeString(node.children);\n        }\n    });\n    return code;\n};\nconst remarkRewrite = (options) => {\n    const { selector, rewrite } = options || {};\n    return (tree) => {\n        if (!rewrite || typeof rewrite !== 'function')\n            return;\n        if (selector && typeof selector === 'string') {\n            const selected = (0,hast_util_select__WEBPACK_IMPORTED_MODULE_0__.selectAll)(selector, tree);\n            if (selected && selected.length > 0) {\n                (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, selected, (node, index, parent) => {\n                    rewrite(node, index, parent);\n                });\n            }\n            return;\n        }\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, (node, index, parent) => {\n            rewrite(node, index, parent);\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (remarkRewrite);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-rewrite/lib/index.js\n");

/***/ })

};
;