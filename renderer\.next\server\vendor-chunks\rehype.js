"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype";
exports.ids = ["vendor-chunks/rehype"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype/index.js":
/*!**************************************!*\
  !*** ./node_modules/rehype/index.js ***!
  \**************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   rehype: () => (/* binding */ rehype)\n/* harmony export */ });\n/* harmony import */ var rehype_parse__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! rehype-parse */ \"(ssr)/./node_modules/rehype-parse/lib/index.js\");\n/* harmony import */ var rehype_stringify__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! rehype-stringify */ \"(ssr)/./node_modules/rehype-stringify/lib/index.js\");\n/* harmony import */ var unified__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unified */ \"(ssr)/./node_modules/unified/lib/index.js\");\n// Note: types exposed from `index.d.ts`\n\n\n\n\n/**\n * Create a new unified processor that already uses `rehype-parse` and\n * `rehype-stringify`.\n */\nconst rehype = (0,unified__WEBPACK_IMPORTED_MODULE_0__.unified)().use(rehype_parse__WEBPACK_IMPORTED_MODULE_1__[\"default\"]).use(rehype_stringify__WEBPACK_IMPORTED_MODULE_2__[\"default\"]).freeze()\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQTtBQUNzQztBQUNRO0FBQ2Y7O0FBRS9CO0FBQ0E7QUFDQTtBQUNBO0FBQ08sZUFBZSxnREFBTyxPQUFPLG9EQUFXLE1BQU0sd0RBQWUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW50aG9cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcbXlub3RlXFxyZW5kZXJlclxcbm9kZV9tb2R1bGVzXFxyZWh5cGVcXGluZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIE5vdGU6IHR5cGVzIGV4cG9zZWQgZnJvbSBgaW5kZXguZC50c2BcbmltcG9ydCByZWh5cGVQYXJzZSBmcm9tICdyZWh5cGUtcGFyc2UnXG5pbXBvcnQgcmVoeXBlU3RyaW5naWZ5IGZyb20gJ3JlaHlwZS1zdHJpbmdpZnknXG5pbXBvcnQge3VuaWZpZWR9IGZyb20gJ3VuaWZpZWQnXG5cbi8qKlxuICogQ3JlYXRlIGEgbmV3IHVuaWZpZWQgcHJvY2Vzc29yIHRoYXQgYWxyZWFkeSB1c2VzIGByZWh5cGUtcGFyc2VgIGFuZFxuICogYHJlaHlwZS1zdHJpbmdpZnlgLlxuICovXG5leHBvcnQgY29uc3QgcmVoeXBlID0gdW5pZmllZCgpLnVzZShyZWh5cGVQYXJzZSkudXNlKHJlaHlwZVN0cmluZ2lmeSkuZnJlZXplKClcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype/index.js\n");

/***/ })

};
;