(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,57073,(e,t,r)=>{t.exports=e.r(67359)},95371,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"BailoutToCSR",{enumerable:!0,get:function(){return l}});let n=e.r(60370);function l(e){let{reason:t,children:r}=e;if("undefined"==typeof window)throw Object.defineProperty(new n.Bai<PERSON>tToCSRError(t),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}},44648,(e,t,r)=>{"use strict";function n(e){return e.split("/").map(e=>encodeURIComponent(e)).join("/")}Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"encodeURIPath",{enumerable:!0,get:function(){return n}})},3059,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"PreloadChunks",{enumerable:!0,get:function(){return i}});let n=e.r(67857),l=e.r(79238),o=e.r(39014),a=e.r(44648);function i(e){let{moduleIds:t}=e;if("undefined"!=typeof window)return null;let r=o.workAsyncStorage.getStore();if(void 0===r)return null;let i=[];if(r.reactLoadableManifest&&t){let e=r.reactLoadableManifest;for(let r of t){if(!e[r])continue;let t=e[r].files;i.push(...t)}}return 0===i.length?null:(0,n.jsx)(n.Fragment,{children:i.map(e=>{let t=r.assetPrefix+"/_next/"+(0,a.encodeURIPath)(e);return e.endsWith(".css")?(0,n.jsx)("link",{precedence:"dynamic",href:t,rel:"stylesheet",as:"style"},e):((0,l.preload)(t,{as:"script",fetchPriority:"low"}),null)})})}},11639,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return d}});let n=e.r(67857),l=e.r(24302),o=e.r(95371),a=e.r(3059);function i(e){return{default:e&&"default"in e?e.default:e}}let s={loader:()=>Promise.resolve(i(()=>null)),loading:null,ssr:!0},d=function(e){let t={...s,...e},r=(0,l.lazy)(()=>t.loader().then(i)),d=t.loading;function c(e){let i=d?(0,n.jsx)(d,{isLoading:!0,pastDelay:!0,error:null}):null,s=!t.ssr||!!t.loading,c=s?l.Suspense:l.Fragment,u=t.ssr?(0,n.jsxs)(n.Fragment,{children:["undefined"==typeof window?(0,n.jsx)(a.PreloadChunks,{moduleIds:t.modules}):null,(0,n.jsx)(r,{...e})]}):(0,n.jsx)(o.BailoutToCSR,{reason:"next/dynamic",children:(0,n.jsx)(r,{...e})});return(0,n.jsx)(c,{...s?{fallback:i}:{},children:u})}return c.displayName="LoadableComponent",c}},3873,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"default",{enumerable:!0,get:function(){return l}});let n=e.r(63593)._(e.r(11639));function l(e,t){var r;let l={};"function"==typeof e&&(l.loader=e);let o={...l,...t};return(0,n.default)({...o,modules:null==(r=o.loadableGenerated)?void 0:r.modules})}("function"==typeof r.default||"object"==typeof r.default&&null!==r.default)&&void 0===r.default.__esModule&&(Object.defineProperty(r.default,"__esModule",{value:!0}),Object.assign(r.default,r),t.exports=r.default)},80470,e=>{"use strict";let t,r;e.s(["default",()=>no],80470);var n=e.i(67857),l=e.i(24302),o=e.i(57073),a=e.i(28157),i=e.i(32054),s=e.i(22859),d=e.i(76825),c=e.i(51906),u=e.i(83875),f=e.i(53456),p=e.i(3961),h=e.i(56611),m=e.i(61540),v="ScrollArea",[g,x]=(0,d.createContextScope)(v),[w,y]=g(v),b=l.forwardRef((e,t)=>{let{__scopeScrollArea:r,type:o="hover",dir:a,scrollHideDelay:s=600,...d}=e,[u,p]=l.useState(null),[h,m]=l.useState(null),[v,g]=l.useState(null),[x,y]=l.useState(null),[b,j]=l.useState(null),[C,k]=l.useState(0),[N,S]=l.useState(0),[E,_]=l.useState(!1),[R,P]=l.useState(!1),M=(0,c.useComposedRefs)(t,e=>p(e)),z=(0,f.useDirection)(a);return(0,n.jsx)(w,{scope:r,type:o,dir:z,scrollHideDelay:s,scrollArea:u,viewport:h,onViewportChange:m,content:v,onContentChange:g,scrollbarX:x,onScrollbarXChange:y,scrollbarXEnabled:E,onScrollbarXEnabledChange:_,scrollbarY:b,onScrollbarYChange:j,scrollbarYEnabled:R,onScrollbarYEnabledChange:P,onCornerWidthChange:k,onCornerHeightChange:S,children:(0,n.jsx)(i.Primitive.div,{dir:z,...d,ref:M,style:{position:"relative","--radix-scroll-area-corner-width":C+"px","--radix-scroll-area-corner-height":N+"px",...e.style}})})});b.displayName=v;var j="ScrollAreaViewport",C=l.forwardRef((e,t)=>{let{__scopeScrollArea:r,children:o,nonce:a,...s}=e,d=y(j,r),u=l.useRef(null),f=(0,c.useComposedRefs)(t,u,d.onViewportChange);return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-scroll-area-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-scroll-area-viewport]::-webkit-scrollbar{display:none}"},nonce:a}),(0,n.jsx)(i.Primitive.div,{"data-radix-scroll-area-viewport":"",...s,ref:f,style:{overflowX:d.scrollbarXEnabled?"scroll":"hidden",overflowY:d.scrollbarYEnabled?"scroll":"hidden",...e.style},children:(0,n.jsx)("div",{ref:d.onContentChange,style:{minWidth:"100%",display:"table"},children:o})})]})});C.displayName=j;var k="ScrollAreaScrollbar",N=l.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=y(k,e.__scopeScrollArea),{onScrollbarXEnabledChange:i,onScrollbarYEnabledChange:s}=a,d="horizontal"===e.orientation;return l.useEffect(()=>(d?i(!0):s(!0),()=>{d?i(!1):s(!1)}),[d,i,s]),"hover"===a.type?(0,n.jsx)(S,{...o,ref:t,forceMount:r}):"scroll"===a.type?(0,n.jsx)(E,{...o,ref:t,forceMount:r}):"auto"===a.type?(0,n.jsx)(_,{...o,ref:t,forceMount:r}):"always"===a.type?(0,n.jsx)(R,{...o,ref:t}):null});N.displayName=k;var S=l.forwardRef((e,t)=>{let{forceMount:r,...o}=e,a=y(k,e.__scopeScrollArea),[i,d]=l.useState(!1);return l.useEffect(()=>{let e=a.scrollArea,t=0;if(e){let r=()=>{window.clearTimeout(t),d(!0)},n=()=>{t=window.setTimeout(()=>d(!1),a.scrollHideDelay)};return e.addEventListener("pointerenter",r),e.addEventListener("pointerleave",n),()=>{window.clearTimeout(t),e.removeEventListener("pointerenter",r),e.removeEventListener("pointerleave",n)}}},[a.scrollArea,a.scrollHideDelay]),(0,n.jsx)(s.Presence,{present:r||i,children:(0,n.jsx)(_,{"data-state":i?"visible":"hidden",...o,ref:t})})}),E=l.forwardRef((e,t)=>{var r;let{forceMount:o,...a}=e,i=y(k,e.__scopeScrollArea),d="horizontal"===e.orientation,c=X(()=>f("SCROLL_END"),100),[u,f]=(r={hidden:{SCROLL:"scrolling"},scrolling:{SCROLL_END:"idle",POINTER_ENTER:"interacting"},interacting:{SCROLL:"interacting",POINTER_LEAVE:"idle"},idle:{HIDE:"hidden",SCROLL:"scrolling",POINTER_ENTER:"interacting"}},l.useReducer((e,t)=>{let n=r[e][t];return null!=n?n:e},"hidden"));return l.useEffect(()=>{if("idle"===u){let e=window.setTimeout(()=>f("HIDE"),i.scrollHideDelay);return()=>window.clearTimeout(e)}},[u,i.scrollHideDelay,f]),l.useEffect(()=>{let e=i.viewport,t=d?"scrollLeft":"scrollTop";if(e){let r=e[t],n=()=>{let n=e[t];r!==n&&(f("SCROLL"),c()),r=n};return e.addEventListener("scroll",n),()=>e.removeEventListener("scroll",n)}},[i.viewport,d,f,c]),(0,n.jsx)(s.Presence,{present:o||"hidden"!==u,children:(0,n.jsx)(R,{"data-state":"hidden"===u?"hidden":"visible",...a,ref:t,onPointerEnter:(0,m.composeEventHandlers)(e.onPointerEnter,()=>f("POINTER_ENTER")),onPointerLeave:(0,m.composeEventHandlers)(e.onPointerLeave,()=>f("POINTER_LEAVE"))})})}),_=l.forwardRef((e,t)=>{let r=y(k,e.__scopeScrollArea),{forceMount:o,...a}=e,[i,d]=l.useState(!1),c="horizontal"===e.orientation,u=X(()=>{if(r.viewport){let e=r.viewport.offsetWidth<r.viewport.scrollWidth,t=r.viewport.offsetHeight<r.viewport.scrollHeight;d(c?e:t)}},10);return Y(r.viewport,u),Y(r.content,u),(0,n.jsx)(s.Presence,{present:o||i,children:(0,n.jsx)(R,{"data-state":i?"visible":"hidden",...a,ref:t})})}),R=l.forwardRef((e,t)=>{let{orientation:r="vertical",...o}=e,a=y(k,e.__scopeScrollArea),i=l.useRef(null),s=l.useRef(0),[d,c]=l.useState({content:0,viewport:0,scrollbar:{size:0,paddingStart:0,paddingEnd:0}}),u=W(d.viewport,d.content),f={...o,sizes:d,onSizesChange:c,hasThumb:!!(u>0&&u<1),onThumbChange:e=>i.current=e,onThumbPointerUp:()=>s.current=0,onThumbPointerDown:e=>s.current=e};function p(e,t){return function(e,t,r){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:"ltr",l=G(r),o=t||l/2,a=r.scrollbar.paddingStart+o,i=r.scrollbar.size-r.scrollbar.paddingEnd-(l-o),s=r.content-r.viewport;return K([a,i],"ltr"===n?[0,s]:[-1*s,0])(e)}(e,s.current,d,t)}return"horizontal"===r?(0,n.jsx)(P,{...f,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=U(a.viewport.scrollLeft,d,a.dir);i.current.style.transform="translate3d(".concat(e,"px, 0, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollLeft=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollLeft=p(e,a.dir))}}):"vertical"===r?(0,n.jsx)(M,{...f,ref:t,onThumbPositionChange:()=>{if(a.viewport&&i.current){let e=U(a.viewport.scrollTop,d);i.current.style.transform="translate3d(0, ".concat(e,"px, 0)")}},onWheelScroll:e=>{a.viewport&&(a.viewport.scrollTop=e)},onDragScroll:e=>{a.viewport&&(a.viewport.scrollTop=p(e))}}):null}),P=l.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=y(k,e.__scopeScrollArea),[s,d]=l.useState(),u=l.useRef(null),f=(0,c.useComposedRefs)(t,u,i.onScrollbarXChange);return l.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,n.jsx)(T,{"data-orientation":"horizontal",...a,ref:f,sizes:r,style:{bottom:0,left:"rtl"===i.dir?"var(--radix-scroll-area-corner-width)":0,right:"ltr"===i.dir?"var(--radix-scroll-area-corner-width)":0,"--radix-scroll-area-thumb-width":G(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.x),onDragScroll:t=>e.onDragScroll(t.x),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollLeft+t.deltaX;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollWidth,viewport:i.viewport.offsetWidth,scrollbar:{size:u.current.clientWidth,paddingStart:B(s.paddingLeft),paddingEnd:B(s.paddingRight)}})}})}),M=l.forwardRef((e,t)=>{let{sizes:r,onSizesChange:o,...a}=e,i=y(k,e.__scopeScrollArea),[s,d]=l.useState(),u=l.useRef(null),f=(0,c.useComposedRefs)(t,u,i.onScrollbarYChange);return l.useEffect(()=>{u.current&&d(getComputedStyle(u.current))},[u]),(0,n.jsx)(T,{"data-orientation":"vertical",...a,ref:f,sizes:r,style:{top:0,right:"ltr"===i.dir?0:void 0,left:"rtl"===i.dir?0:void 0,bottom:"var(--radix-scroll-area-corner-height)","--radix-scroll-area-thumb-height":G(r)+"px",...e.style},onThumbPointerDown:t=>e.onThumbPointerDown(t.y),onDragScroll:t=>e.onDragScroll(t.y),onWheelScroll:(t,r)=>{if(i.viewport){let n=i.viewport.scrollTop+t.deltaY;e.onWheelScroll(n),function(e,t){return e>0&&e<t}(n,r)&&t.preventDefault()}},onResize:()=>{u.current&&i.viewport&&s&&o({content:i.viewport.scrollHeight,viewport:i.viewport.offsetHeight,scrollbar:{size:u.current.clientHeight,paddingStart:B(s.paddingTop),paddingEnd:B(s.paddingBottom)}})}})}),[z,D]=g(k),T=l.forwardRef((e,t)=>{let{__scopeScrollArea:r,sizes:o,hasThumb:a,onThumbChange:s,onThumbPointerUp:d,onThumbPointerDown:f,onThumbPositionChange:p,onDragScroll:h,onWheelScroll:v,onResize:g,...x}=e,w=y(k,r),[b,j]=l.useState(null),C=(0,c.useComposedRefs)(t,e=>j(e)),N=l.useRef(null),S=l.useRef(""),E=w.viewport,_=o.content-o.viewport,R=(0,u.useCallbackRef)(v),P=(0,u.useCallbackRef)(p),M=X(g,10);function D(e){N.current&&h({x:e.clientX-N.current.left,y:e.clientY-N.current.top})}return l.useEffect(()=>{let e=e=>{let t=e.target;(null==b?void 0:b.contains(t))&&R(e,_)};return document.addEventListener("wheel",e,{passive:!1}),()=>document.removeEventListener("wheel",e,{passive:!1})},[E,b,_,R]),l.useEffect(P,[o,P]),Y(b,M),Y(w.content,M),(0,n.jsx)(z,{scope:r,scrollbar:b,hasThumb:a,onThumbChange:(0,u.useCallbackRef)(s),onThumbPointerUp:(0,u.useCallbackRef)(d),onThumbPositionChange:P,onThumbPointerDown:(0,u.useCallbackRef)(f),children:(0,n.jsx)(i.Primitive.div,{...x,ref:C,style:{position:"absolute",...x.style},onPointerDown:(0,m.composeEventHandlers)(e.onPointerDown,e=>{0===e.button&&(e.target.setPointerCapture(e.pointerId),N.current=b.getBoundingClientRect(),S.current=document.body.style.webkitUserSelect,document.body.style.webkitUserSelect="none",w.viewport&&(w.viewport.style.scrollBehavior="auto"),D(e))}),onPointerMove:(0,m.composeEventHandlers)(e.onPointerMove,D),onPointerUp:(0,m.composeEventHandlers)(e.onPointerUp,e=>{let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),document.body.style.webkitUserSelect=S.current,w.viewport&&(w.viewport.style.scrollBehavior=""),N.current=null})})})}),I="ScrollAreaThumb",L=l.forwardRef((e,t)=>{let{forceMount:r,...l}=e,o=D(I,e.__scopeScrollArea);return(0,n.jsx)(s.Presence,{present:r||o.hasThumb,children:(0,n.jsx)(A,{ref:t,...l})})}),A=l.forwardRef((e,t)=>{let{__scopeScrollArea:r,style:o,...a}=e,s=y(I,r),d=D(I,r),{onThumbPositionChange:u}=d,f=(0,c.useComposedRefs)(t,e=>d.onThumbChange(e)),p=l.useRef(void 0),h=X(()=>{p.current&&(p.current(),p.current=void 0)},100);return l.useEffect(()=>{let e=s.viewport;if(e){let t=()=>{h(),p.current||(p.current=V(e,u),u())};return u(),e.addEventListener("scroll",t),()=>e.removeEventListener("scroll",t)}},[s.viewport,h,u]),(0,n.jsx)(i.Primitive.div,{"data-state":d.hasThumb?"visible":"hidden",...a,ref:f,style:{width:"var(--radix-scroll-area-thumb-width)",height:"var(--radix-scroll-area-thumb-height)",...o},onPointerDownCapture:(0,m.composeEventHandlers)(e.onPointerDownCapture,e=>{let t=e.target.getBoundingClientRect(),r=e.clientX-t.left,n=e.clientY-t.top;d.onThumbPointerDown({x:r,y:n})}),onPointerUp:(0,m.composeEventHandlers)(e.onPointerUp,d.onThumbPointerUp)})});L.displayName=I;var H="ScrollAreaCorner",O=l.forwardRef((e,t)=>{let r=y(H,e.__scopeScrollArea),l=!!(r.scrollbarX&&r.scrollbarY);return"scroll"!==r.type&&l?(0,n.jsx)(F,{...e,ref:t}):null});O.displayName=H;var F=l.forwardRef((e,t)=>{let{__scopeScrollArea:r,...o}=e,a=y(H,r),[s,d]=l.useState(0),[c,u]=l.useState(0),f=!!(s&&c);return Y(a.scrollbarX,()=>{var e;let t=(null==(e=a.scrollbarX)?void 0:e.offsetHeight)||0;a.onCornerHeightChange(t),u(t)}),Y(a.scrollbarY,()=>{var e;let t=(null==(e=a.scrollbarY)?void 0:e.offsetWidth)||0;a.onCornerWidthChange(t),d(t)}),f?(0,n.jsx)(i.Primitive.div,{...o,ref:t,style:{width:s,height:c,position:"absolute",right:"ltr"===a.dir?0:void 0,left:"rtl"===a.dir?0:void 0,bottom:0,...e.style}}):null});function B(e){return e?parseInt(e,10):0}function W(e,t){let r=e/t;return isNaN(r)?0:r}function G(e){let t=W(e.viewport,e.content),r=e.scrollbar.paddingStart+e.scrollbar.paddingEnd;return Math.max((e.scrollbar.size-r)*t,18)}function U(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"ltr",n=G(t),l=t.scrollbar.paddingStart+t.scrollbar.paddingEnd,o=t.scrollbar.size-l,a=t.content-t.viewport,i=(0,h.clamp)(e,"ltr"===r?[0,a]:[-1*a,0]);return K([0,a],[0,o-n])(i)}function K(e,t){return r=>{if(e[0]===e[1]||t[0]===t[1])return t[0];let n=(t[1]-t[0])/(e[1]-e[0]);return t[0]+n*(r-e[0])}}var V=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:()=>{},r={left:e.scrollLeft,top:e.scrollTop},n=0;return!function l(){let o={left:e.scrollLeft,top:e.scrollTop},a=r.left!==o.left,i=r.top!==o.top;(a||i)&&t(),r=o,n=window.requestAnimationFrame(l)}(),()=>window.cancelAnimationFrame(n)};function X(e,t){let r=(0,u.useCallbackRef)(e),n=l.useRef(0);return l.useEffect(()=>()=>window.clearTimeout(n.current),[]),l.useCallback(()=>{window.clearTimeout(n.current),n.current=window.setTimeout(r,t)},[r,t])}function Y(e,t){let r=(0,u.useCallbackRef)(t);(0,p.useLayoutEffect)(()=>{let t=0;if(e){let n=new ResizeObserver(()=>{cancelAnimationFrame(t),t=window.requestAnimationFrame(r)});return n.observe(e),()=>{window.cancelAnimationFrame(t),n.unobserve(e)}}},[e,r])}var q=e.i(92072);let J=l.forwardRef((e,t)=>{let{className:r,children:l,...o}=e;return(0,n.jsxs)(b,{ref:t,className:(0,q.cn)("relative overflow-hidden",r),...o,children:[(0,n.jsx)(C,{className:"h-full w-full rounded-[inherit]",children:l}),(0,n.jsx)(Z,{}),(0,n.jsx)(O,{})]})});J.displayName=b.displayName;let Z=l.forwardRef((e,t)=>{let{className:r,orientation:l="vertical",...o}=e;return(0,n.jsx)(N,{ref:t,orientation:l,className:(0,q.cn)("flex touch-none select-none transition-colors","vertical"===l&&"h-full w-2.5 border-l border-l-transparent p-[1px]","horizontal"===l&&"h-2.5 flex-col border-t border-t-transparent p-[1px]",r),...o,children:(0,n.jsx)(L,{className:"relative flex-1 rounded-full bg-border"})})});Z.displayName=N.displayName;var Q=e.i(47244),$=e.i(57925),ee=e.i(93137);let et=(0,ee.default)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]]);function er(e){let{notes:t,onSelectNote:r,selectedNoteId:o}=e,{t:a}=(0,Q.useI18n)(),[i,s]=(0,l.useState)(""),d=t.filter(e=>e.title.toLowerCase().includes(i.toLowerCase())||e.content.toLowerCase().includes(i.toLowerCase())||e.tags.some(e=>e.toLowerCase().includes(i.toLowerCase())));return(0,n.jsxs)("div",{className:"flex flex-col h-full space-y-3",children:[(0,n.jsxs)("div",{className:"relative",children:[(0,n.jsx)($.Search,{className:"absolute w-4 h-4 transform -translate-y-1/2 left-3 top-1/2 text-muted-foreground/60"}),(0,n.jsx)("input",{type:"text",placeholder:a("note_list.search_notes"),value:i,onChange:e=>s(e.target.value),className:"w-full py-2.5 pl-10 pr-4 text-sm transition-colors rounded-lg bg-muted/30 border-0 focus:outline-none focus:bg-muted/50 placeholder:text-muted-foreground/60"})]}),(0,n.jsx)(J,{className:"flex-1 sidebar-scrollbar",children:(0,n.jsx)("div",{className:"pr-2 space-y-2",children:0===d.length?(0,n.jsxs)("div",{className:"py-12 text-center text-muted-foreground",children:[(0,n.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50",children:(0,n.jsx)(et,{className:"w-8 h-8 opacity-50"})}),(0,n.jsx)("p",{className:"mb-1 text-sm font-medium",children:i?a("note_list.no_notes_found"):a("note_list.no_notes")}),(0,n.jsx)("p",{className:"text-xs opacity-70",children:i?a("note_list.try_different_search"):a("note_list.create_first_note")})]}):d.map(e=>{let t;return(0,n.jsx)("div",{className:"p-3 cursor-pointer rounded-lg transition-all duration-200 border-l-2 ".concat(o===e.id?"bg-primary/10 border-l-primary shadow-sm":"hover:bg-accent/40 border-l-transparent hover:border-l-accent/50"),onClick:()=>r(e.id),children:(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-start justify-between gap-2",children:[(0,n.jsx)("h3",{className:"flex-1 text-sm font-medium line-clamp-2 ".concat(o===e.id?"text-primary font-semibold":""),children:e.title||(0,n.jsx)("span",{className:"italic font-normal text-muted-foreground",children:a("note_list.no_title")})}),o===e.id&&(0,n.jsx)("div",{className:"flex-shrink-0 w-1.5 h-1.5 mt-2 rounded-full bg-primary"})]}),(0,n.jsx)("p",{className:"text-xs leading-relaxed text-muted-foreground/80 line-clamp-3",children:e.content?(t=e.content).replace(/[#*`\n]/g," ").substring(0,80)+(t.length>80?"...":""):(0,n.jsx)("span",{className:"italic",children:a("note_list.no_content")})}),Array.isArray(e.tags)&&e.tags.length>0&&(0,n.jsx)("div",{className:"flex flex-wrap items-center gap-1 pt-1",children:(0,n.jsxs)("div",{className:"flex flex-wrap gap-1",children:[e.tags.slice(0,2).map(e=>(0,n.jsx)("span",{className:"text-[10px] px-1.5 py-0.5 rounded-full bg-muted/60 text-muted-foreground",children:e},e)),e.tags.length>2&&(0,n.jsxs)("span",{className:"text-[10px] px-1.5 py-0.5 rounded-full bg-muted/40 text-muted-foreground",children:["+",e.tags.length-2]})]})})]})},e.id)})})})]})}var en=e.i(73222),el=e.i(91457),eo=e.i(17388);function ea(e){let{value:t,onChange:r,placeholder:o}=e,[a,i]=(0,l.useState)(""),s=(0,l.useRef)(null),d=()=>{let e=a.trim();e&&!t.includes(e)&&r([...t,e]),i("")};return(0,n.jsxs)("div",{className:(0,q.cn)("flex flex-wrap items-center gap-2 bg-transparent px-3 py-2 text-sm ring-offset-background cursor-text","focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2"),onClick:()=>{var e;null==(e=s.current)||e.focus()},children:[t.map(e=>(0,n.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 text-xs rounded-full bg-secondary text-secondary-foreground",children:[e,(0,n.jsx)("button",{onClick:n=>{n.stopPropagation(),r(t.filter(t=>t!==e))},className:"text-xs font-bold",children:(0,n.jsx)(eo.X,{size:12})})]},e)),(0,n.jsx)(el.Input,{ref:s,value:a,onChange:e=>{i(e.target.value)},onKeyDown:e=>{("Enter"===e.key||","===e.key)&&(e.preventDefault(),d())},onBlur:d,placeholder:o||"新增標籤...",className:"flex-1 h-auto p-0 bg-transparent border-0 shadow-none focus-visible:ring-0 focus-visible:outline-none"})]})}let ei=(0,e.i(3873).default)(()=>e.A(34594).then(e=>e.default),{loadableGenerated:{modules:[54787]},ssr:!1}),es=(0,l.forwardRef)((t,r)=>{let{value:o,onChange:a,preview:i="live",hideToolbar:s=!1,visibleDragbar:d=!1,className:c="","data-color-mode":u,previewOptions:f,onSelectionChange:p}=t,h=(0,l.useRef)(null),m=(0,l.useRef)(null),[v,g]=(0,l.useState)(!1),[x,w]=(0,l.useState)(null),[y,b]=(0,l.useState)(!1),{theme:j}=(0,en.useTheme)(),C=u||("dark"===j?"dark":"light");return((0,l.useEffect)(()=>{g(!0);let t=setTimeout(()=>{console.log("Cherry loading timeout, using fallback editor"),b(!0)},5e3);return e.A(86868).then(e=>{console.log("Cherry Markdown loaded successfully"),w(e.default||e),clearTimeout(t)}).catch(e=>{console.error("Failed to load Cherry Markdown:",e),b(!0),clearTimeout(t)}),()=>{clearTimeout(t)}},[]),(0,l.useImperativeHandle)(r,()=>({getMarkdown:()=>m.current?m.current.getMarkdown():o,setMarkdown:e=>{m.current&&m.current.setMarkdown(e)},getSelection:()=>{var e;return m.current?m.current.getSelection()||"":(null==(e=window.getSelection())?void 0:e.toString())||""},focus:()=>{m.current&&m.current.focus()}}),[o]),(0,l.useEffect)(()=>{let e=()=>{var e;let t=(null==(e=window.getSelection())?void 0:e.toString())||"";p&&p(t)};return document.addEventListener("mouseup",e),document.addEventListener("keyup",e),()=>{document.removeEventListener("mouseup",e),document.removeEventListener("keyup",e)}},[p]),(0,l.useEffect)(()=>{if(v&&x&&h.current){if(console.log("Initializing Cherry Markdown..."),m.current){try{var e,t;null==(e=(t=m.current).destroy)||e.call(t)}catch(e){console.warn("Error destroying Cherry instance:",e)}m.current=null}h.current.innerHTML="";try{let e={id:h.current,value:o||"",editor:{defaultModel:"preview"===i?"previewOnly":"edit"===i?"editOnly":"edit&preview",height:"100%",width:"100%",autoHeight:!1,autoWidth:!1,codemirror:{lineNumbers:!0,lineWrapping:!0,theme:"dark"===C?"material-darker":"default",viewportMargin:0,scrollbarStyle:"native"}},previewer:{dom:!1,className:"cherry-previewer",enablePreviewerBubble:!1,lazyLoadImg:{loadingImgPath:"",maxNumPerTime:2,noLoadImgNum:5,autoLoadImgNum:5,maxTryTimesPerSrc:2,loadingImgMaxWaitTime:5e3}},toolbars:s?{toolbar:!1,bubble:!1,float:!1,sidebar:!1}:{toolbar:["bold","italic","strikethrough","|","header","list","quote","hr","|","link","image","code","table","|","undo","redo"]},callback:{afterChange:e=>{a&&a(e)},afterInit:()=>{console.log("Cherry initialized successfully");let e=h.current;e&&(e.setAttribute("data-color-mode",C),e.classList.add("dark"===C?"theme__dark":"theme__default"))}}};m.current=new x(e),console.log("Cherry instance created successfully")}catch(e){console.error("Error initializing Cherry Markdown:",e),b(!0)}}},[v,x,i,s,C]),(0,l.useEffect)(()=>{m.current&&void 0!==o&&m.current.getMarkdown()!==o&&m.current.setMarkdown(o)},[o]),(0,l.useEffect)(()=>()=>{if(m.current)try{var e,t;null==(e=(t=m.current).destroy)||e.call(t)}catch(e){console.warn("Error destroying Cherry instance on unmount:",e)}},[]),!x||y)?(0,n.jsx)("div",{className:"cherry-markdown-editor-fallback ".concat(c),style:{height:"100%",width:"100%",maxHeight:"100%",display:"flex",flexDirection:"column",position:"relative",overflow:"hidden"},children:(0,n.jsx)(ei,{value:o,onChange:a,preview:"preview"===i?"preview":"edit"===i?"edit":"live",hideToolbar:s,visibleDragbar:d,"data-color-mode":C,previewOptions:f,className:c})}):v?(0,n.jsx)("div",{ref:h,className:"cherry-markdown-editor ".concat(c),style:{height:"100%",width:"100%",maxHeight:"100%",display:"flex",flexDirection:"column",position:"relative",overflow:"hidden"},"data-color-mode":C}):(0,n.jsx)("div",{className:"cherry-markdown-editor-loading ".concat(c),children:(0,n.jsx)("div",{children:"Loading editor..."})})});es.displayName="CherryMarkdownEditor";var ed=e.i(21104),ec=e.i(54562),eu=e.i(89199),ef=e.i(49566),ep=e.i(80435),eh=e.i(40971),em=e.i(89920),ev=e.i(36641),eg=e.i(36838),ex=e.i(87804),ew=e.i(63772),ey=e.i(81692),eb=e.i(15119),ej=e.i(67084),eC=["Enter"," "],ek=["ArrowUp","PageDown","End"],eN=["ArrowDown","PageUp","Home",...ek],eS={ltr:[...eC,"ArrowRight"],rtl:[...eC,"ArrowLeft"]},eE={ltr:["ArrowLeft"],rtl:["ArrowRight"]},e_="Menu",[eR,eP,eM]=(0,ef.createCollection)(e_),[ez,eD]=(0,d.createContextScope)(e_,[eM,eg.createPopperScope,ew.createRovingFocusGroupScope]),eT=(0,eg.createPopperScope)(),eI=(0,ew.createRovingFocusGroupScope)(),[eL,eA]=ez(e_),[eH,eO]=ez(e_),eF=e=>{let{__scopeMenu:t,open:r=!1,children:o,dir:a,onOpenChange:i,modal:s=!0}=e,d=eT(t),[c,p]=l.useState(null),h=l.useRef(!1),m=(0,u.useCallbackRef)(i),v=(0,f.useDirection)(a);return l.useEffect(()=>{let e=()=>{h.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>h.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,n.jsx)(eg.Root,{...d,children:(0,n.jsx)(eL,{scope:t,open:r,onOpenChange:m,content:c,onContentChange:p,children:(0,n.jsx)(eH,{scope:t,onClose:l.useCallback(()=>m(!1),[m]),isUsingKeyboardRef:h,dir:v,modal:s,children:o})})})};eF.displayName=e_;var eB=l.forwardRef((e,t)=>{let{__scopeMenu:r,...l}=e,o=eT(r);return(0,n.jsx)(eg.Anchor,{...o,...l,ref:t})});eB.displayName="MenuAnchor";var eW="MenuPortal",[eG,eU]=ez(eW,{forceMount:void 0}),eK=e=>{let{__scopeMenu:t,forceMount:r,children:l,container:o}=e,a=eA(eW,t);return(0,n.jsx)(eG,{scope:t,forceMount:r,children:(0,n.jsx)(s.Presence,{present:r||a.open,children:(0,n.jsx)(ex.Portal,{asChild:!0,container:o,children:l})})})};eK.displayName=eW;var eV="MenuContent",[eX,eY]=ez(eV),eq=l.forwardRef((e,t)=>{let r=eU(eV,e.__scopeMenu),{forceMount:l=r.forceMount,...o}=e,a=eA(eV,e.__scopeMenu),i=eO(eV,e.__scopeMenu);return(0,n.jsx)(eR.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(s.Presence,{present:l||a.open,children:(0,n.jsx)(eR.Slot,{scope:e.__scopeMenu,children:i.modal?(0,n.jsx)(eJ,{...o,ref:t}):(0,n.jsx)(eZ,{...o,ref:t})})})})}),eJ=l.forwardRef((e,t)=>{let r=eA(eV,e.__scopeMenu),o=l.useRef(null),a=(0,c.useComposedRefs)(t,o);return l.useEffect(()=>{let e=o.current;if(e)return(0,eb.hideOthers)(e)},[]),(0,n.jsx)(e$,{...e,ref:a,trapFocus:r.open,disableOutsidePointerEvents:r.open,disableOutsideScroll:!0,onFocusOutside:(0,m.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>r.onOpenChange(!1)})}),eZ=l.forwardRef((e,t)=>{let r=eA(eV,e.__scopeMenu);return(0,n.jsx)(e$,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>r.onOpenChange(!1)})}),eQ=(0,ey.createSlot)("MenuContent.ScrollLock"),e$=l.forwardRef((e,t)=>{let{__scopeMenu:r,loop:o=!1,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:s,disableOutsidePointerEvents:d,onEntryFocus:u,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:v,onDismiss:g,disableOutsideScroll:x,...w}=e,y=eA(eV,r),b=eO(eV,r),j=eT(r),C=eI(r),k=eP(r),[N,S]=l.useState(null),E=l.useRef(null),_=(0,c.useComposedRefs)(t,E,y.onContentChange),R=l.useRef(0),P=l.useRef(""),M=l.useRef(0),z=l.useRef(null),D=l.useRef("right"),T=l.useRef(0),I=x?ej.RemoveScroll:l.Fragment;l.useEffect(()=>()=>window.clearTimeout(R.current),[]),(0,eh.useFocusGuards)();let L=l.useCallback(e=>{var t,r;return D.current===(null==(t=z.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:r,y:n}=e,l=!1;for(let e=0,o=t.length-1;e<t.length;o=e++){let a=t[e],i=t[o],s=a.x,d=a.y,c=i.x,u=i.y;d>n!=u>n&&r<(c-s)*(n-d)/(u-d)+s&&(l=!l)}return l}({x:e.clientX,y:e.clientY},t)}(e,null==(r=z.current)?void 0:r.area)},[]);return(0,n.jsx)(eX,{scope:r,searchRef:P,onItemEnter:l.useCallback(e=>{L(e)&&e.preventDefault()},[L]),onItemLeave:l.useCallback(e=>{var t;L(e)||(null==(t=E.current)||t.focus(),S(null))},[L]),onTriggerLeave:l.useCallback(e=>{L(e)&&e.preventDefault()},[L]),pointerGraceTimerRef:M,onPointerGraceIntentChange:l.useCallback(e=>{z.current=e},[]),children:(0,n.jsx)(I,{...x?{as:eQ,allowPinchZoom:!0}:void 0,children:(0,n.jsx)(em.FocusScope,{asChild:!0,trapped:a,onMountAutoFocus:(0,m.composeEventHandlers)(i,e=>{var t;e.preventDefault(),null==(t=E.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,n.jsx)(ep.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:d,onEscapeKeyDown:f,onPointerDownOutside:p,onFocusOutside:h,onInteractOutside:v,onDismiss:g,children:(0,n.jsx)(ew.Root,{asChild:!0,...C,dir:b.dir,orientation:"vertical",loop:o,currentTabStopId:N,onCurrentTabStopIdChange:S,onEntryFocus:(0,m.composeEventHandlers)(u,e=>{b.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,n.jsx)(eg.Content,{role:"menu","aria-orientation":"vertical","data-state":tm(y.open),"data-radix-menu-content":"",dir:b.dir,...j,...w,ref:_,style:{outline:"none",...w.style},onKeyDown:(0,m.composeEventHandlers)(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,r=e.ctrlKey||e.altKey||e.metaKey,n=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!r&&n&&(e=>{var t,r;let n=P.current+e,l=k().filter(e=>!e.disabled),o=document.activeElement,a=null==(t=l.find(e=>e.ref.current===o))?void 0:t.textValue,i=function(e,t,r){var n;let l=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,o=r?e.indexOf(r):-1,a=(n=Math.max(o,0),e.map((t,r)=>e[(n+r)%e.length]));1===l.length&&(a=a.filter(e=>e!==r));let i=a.find(e=>e.toLowerCase().startsWith(l.toLowerCase()));return i!==r?i:void 0}(l.map(e=>e.textValue),n,a),s=null==(r=l.find(e=>e.textValue===i))?void 0:r.ref.current;!function e(t){P.current=t,window.clearTimeout(R.current),""!==t&&(R.current=window.setTimeout(()=>e(""),1e3))}(n),s&&setTimeout(()=>s.focus())})(e.key));let l=E.current;if(e.target!==l||!eN.includes(e.key))return;e.preventDefault();let o=k().filter(e=>!e.disabled).map(e=>e.ref.current);ek.includes(e.key)&&o.reverse(),function(e){let t=document.activeElement;for(let r of e)if(r===t||(r.focus(),document.activeElement!==t))return}(o)}),onBlur:(0,m.composeEventHandlers)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(R.current),P.current="")}),onPointerMove:(0,m.composeEventHandlers)(e.onPointerMove,tx(e=>{let t=e.target,r=T.current!==e.clientX;e.currentTarget.contains(t)&&r&&(D.current=e.clientX>T.current?"right":"left",T.current=e.clientX)}))})})})})})})});eq.displayName=eV;var e0=l.forwardRef((e,t)=>{let{__scopeMenu:r,...l}=e;return(0,n.jsx)(i.Primitive.div,{role:"group",...l,ref:t})});e0.displayName="MenuGroup";var e1=l.forwardRef((e,t)=>{let{__scopeMenu:r,...l}=e;return(0,n.jsx)(i.Primitive.div,{...l,ref:t})});e1.displayName="MenuLabel";var e2="MenuItem",e5="menu.itemSelect",e4=l.forwardRef((e,t)=>{let{disabled:r=!1,onSelect:o,...a}=e,s=l.useRef(null),d=eO(e2,e.__scopeMenu),u=eY(e2,e.__scopeMenu),f=(0,c.useComposedRefs)(t,s),p=l.useRef(!1);return(0,n.jsx)(e3,{...a,ref:f,disabled:r,onClick:(0,m.composeEventHandlers)(e.onClick,()=>{let e=s.current;if(!r&&e){let t=new CustomEvent(e5,{bubbles:!0,cancelable:!0});e.addEventListener(e5,e=>null==o?void 0:o(e),{once:!0}),(0,i.dispatchDiscreteCustomEvent)(e,t),t.defaultPrevented?p.current=!1:d.onClose()}}),onPointerDown:t=>{var r;null==(r=e.onPointerDown)||r.call(e,t),p.current=!0},onPointerUp:(0,m.composeEventHandlers)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,m.composeEventHandlers)(e.onKeyDown,e=>{let t=""!==u.searchRef.current;r||t&&" "===e.key||eC.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});e4.displayName=e2;var e3=l.forwardRef((e,t)=>{let{__scopeMenu:r,disabled:o=!1,textValue:a,...s}=e,d=eY(e2,r),u=eI(r),f=l.useRef(null),p=(0,c.useComposedRefs)(t,f),[h,v]=l.useState(!1),[g,x]=l.useState("");return l.useEffect(()=>{let e=f.current;if(e){var t;x((null!=(t=e.textContent)?t:"").trim())}},[s.children]),(0,n.jsx)(eR.ItemSlot,{scope:r,disabled:o,textValue:null!=a?a:g,children:(0,n.jsx)(ew.Item,{asChild:!0,...u,focusable:!o,children:(0,n.jsx)(i.Primitive.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":o||void 0,"data-disabled":o?"":void 0,...s,ref:p,onPointerMove:(0,m.composeEventHandlers)(e.onPointerMove,tx(e=>{o?d.onItemLeave(e):(d.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,m.composeEventHandlers)(e.onPointerLeave,tx(e=>d.onItemLeave(e))),onFocus:(0,m.composeEventHandlers)(e.onFocus,()=>v(!0)),onBlur:(0,m.composeEventHandlers)(e.onBlur,()=>v(!1))})})})}),e8=l.forwardRef((e,t)=>{let{checked:r=!1,onCheckedChange:l,...o}=e;return(0,n.jsx)(tl,{scope:e.__scopeMenu,checked:r,children:(0,n.jsx)(e4,{role:"menuitemcheckbox","aria-checked":tv(r)?"mixed":r,...o,ref:t,"data-state":tg(r),onSelect:(0,m.composeEventHandlers)(o.onSelect,()=>null==l?void 0:l(!!tv(r)||!r),{checkForDefaultPrevented:!1})})})});e8.displayName="MenuCheckboxItem";var e6="MenuRadioGroup",[e9,e7]=ez(e6,{value:void 0,onValueChange:()=>{}}),te=l.forwardRef((e,t)=>{let{value:r,onValueChange:l,...o}=e,a=(0,u.useCallbackRef)(l);return(0,n.jsx)(e9,{scope:e.__scopeMenu,value:r,onValueChange:a,children:(0,n.jsx)(e0,{...o,ref:t})})});te.displayName=e6;var tt="MenuRadioItem",tr=l.forwardRef((e,t)=>{let{value:r,...l}=e,o=e7(tt,e.__scopeMenu),a=r===o.value;return(0,n.jsx)(tl,{scope:e.__scopeMenu,checked:a,children:(0,n.jsx)(e4,{role:"menuitemradio","aria-checked":a,...l,ref:t,"data-state":tg(a),onSelect:(0,m.composeEventHandlers)(l.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,r)},{checkForDefaultPrevented:!1})})})});tr.displayName=tt;var tn="MenuItemIndicator",[tl,to]=ez(tn,{checked:!1}),ta=l.forwardRef((e,t)=>{let{__scopeMenu:r,forceMount:l,...o}=e,a=to(tn,r);return(0,n.jsx)(s.Presence,{present:l||tv(a.checked)||!0===a.checked,children:(0,n.jsx)(i.Primitive.span,{...o,ref:t,"data-state":tg(a.checked)})})});ta.displayName=tn;var ti=l.forwardRef((e,t)=>{let{__scopeMenu:r,...l}=e;return(0,n.jsx)(i.Primitive.div,{role:"separator","aria-orientation":"horizontal",...l,ref:t})});ti.displayName="MenuSeparator";var ts=l.forwardRef((e,t)=>{let{__scopeMenu:r,...l}=e,o=eT(r);return(0,n.jsx)(eg.Arrow,{...o,...l,ref:t})});ts.displayName="MenuArrow";var[td,tc]=ez("MenuSub"),tu="MenuSubTrigger",tf=l.forwardRef((e,t)=>{let r=eA(tu,e.__scopeMenu),o=eO(tu,e.__scopeMenu),a=tc(tu,e.__scopeMenu),i=eY(tu,e.__scopeMenu),s=l.useRef(null),{pointerGraceTimerRef:d,onPointerGraceIntentChange:u}=i,f={__scopeMenu:e.__scopeMenu},p=l.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return l.useEffect(()=>p,[p]),l.useEffect(()=>{let e=d.current;return()=>{window.clearTimeout(e),u(null)}},[d,u]),(0,n.jsx)(eB,{asChild:!0,...f,children:(0,n.jsx)(e3,{id:a.triggerId,"aria-haspopup":"menu","aria-expanded":r.open,"aria-controls":a.contentId,"data-state":tm(r.open),...e,ref:(0,c.composeRefs)(t,a.onTriggerChange),onClick:t=>{var n;null==(n=e.onClick)||n.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),r.open||r.onOpenChange(!0))},onPointerMove:(0,m.composeEventHandlers)(e.onPointerMove,tx(t=>{i.onItemEnter(t),!t.defaultPrevented&&(e.disabled||r.open||s.current||(i.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{r.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,m.composeEventHandlers)(e.onPointerLeave,tx(e=>{var t,n;p();let l=null==(t=r.content)?void 0:t.getBoundingClientRect();if(l){let t=null==(n=r.content)?void 0:n.dataset.side,o="right"===t,a=l[o?"left":"right"],s=l[o?"right":"left"];i.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:a,y:l.top},{x:s,y:l.top},{x:s,y:l.bottom},{x:a,y:l.bottom}],side:t}),window.clearTimeout(d.current),d.current=window.setTimeout(()=>i.onPointerGraceIntentChange(null),300)}else{if(i.onTriggerLeave(e),e.defaultPrevented)return;i.onPointerGraceIntentChange(null)}})),onKeyDown:(0,m.composeEventHandlers)(e.onKeyDown,t=>{let n=""!==i.searchRef.current;if(!e.disabled&&(!n||" "!==t.key)&&eS[o.dir].includes(t.key)){var l;r.onOpenChange(!0),null==(l=r.content)||l.focus(),t.preventDefault()}})})})});tf.displayName=tu;var tp="MenuSubContent",th=l.forwardRef((e,t)=>{let r=eU(eV,e.__scopeMenu),{forceMount:o=r.forceMount,...a}=e,i=eA(eV,e.__scopeMenu),d=eO(eV,e.__scopeMenu),u=tc(tp,e.__scopeMenu),f=l.useRef(null),p=(0,c.useComposedRefs)(t,f);return(0,n.jsx)(eR.Provider,{scope:e.__scopeMenu,children:(0,n.jsx)(s.Presence,{present:o||i.open,children:(0,n.jsx)(eR.Slot,{scope:e.__scopeMenu,children:(0,n.jsx)(e$,{id:u.contentId,"aria-labelledby":u.triggerId,...a,ref:p,align:"start",side:"rtl"===d.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;d.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,m.composeEventHandlers)(e.onFocusOutside,e=>{e.target!==u.trigger&&i.onOpenChange(!1)}),onEscapeKeyDown:(0,m.composeEventHandlers)(e.onEscapeKeyDown,e=>{d.onClose(),e.preventDefault()}),onKeyDown:(0,m.composeEventHandlers)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),r=eE[d.dir].includes(e.key);if(t&&r){var n;i.onOpenChange(!1),null==(n=u.trigger)||n.focus(),e.preventDefault()}})})})})})});function tm(e){return e?"open":"closed"}function tv(e){return"indeterminate"===e}function tg(e){return tv(e)?"indeterminate":e?"checked":"unchecked"}function tx(e){return t=>"mouse"===t.pointerType?e(t):void 0}th.displayName=tp;var tw="DropdownMenu",[ty,tb]=(0,d.createContextScope)(tw,[eD]),tj=eD(),[tC,tk]=ty(tw),tN=e=>{let{__scopeDropdownMenu:t,children:r,dir:o,open:a,defaultOpen:i,onOpenChange:s,modal:d=!0}=e,c=tj(t),u=l.useRef(null),[f,p]=(0,eu.useControllableState)({prop:a,defaultProp:null!=i&&i,onChange:s,caller:tw});return(0,n.jsx)(tC,{scope:t,triggerId:(0,ev.useId)(),triggerRef:u,contentId:(0,ev.useId)(),open:f,onOpenChange:p,onOpenToggle:l.useCallback(()=>p(e=>!e),[p]),modal:d,children:(0,n.jsx)(eF,{...c,open:f,onOpenChange:p,dir:o,modal:d,children:r})})};tN.displayName=tw;var tS="DropdownMenuTrigger",tE=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,disabled:l=!1,...o}=e,a=tk(tS,r),s=tj(r);return(0,n.jsx)(eB,{asChild:!0,...s,children:(0,n.jsx)(i.Primitive.button,{type:"button",id:a.triggerId,"aria-haspopup":"menu","aria-expanded":a.open,"aria-controls":a.open?a.contentId:void 0,"data-state":a.open?"open":"closed","data-disabled":l?"":void 0,disabled:l,...o,ref:(0,c.composeRefs)(t,a.triggerRef),onPointerDown:(0,m.composeEventHandlers)(e.onPointerDown,e=>{!l&&0===e.button&&!1===e.ctrlKey&&(a.onOpenToggle(),a.open||e.preventDefault())}),onKeyDown:(0,m.composeEventHandlers)(e.onKeyDown,e=>{!l&&(["Enter"," "].includes(e.key)&&a.onOpenToggle(),"ArrowDown"===e.key&&a.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});tE.displayName=tS;var t_=e=>{let{__scopeDropdownMenu:t,...r}=e,l=tj(t);return(0,n.jsx)(eK,{...l,...r})};t_.displayName="DropdownMenuPortal";var tR="DropdownMenuContent",tP=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...o}=e,a=tk(tR,r),i=tj(r),s=l.useRef(!1);return(0,n.jsx)(eq,{id:a.contentId,"aria-labelledby":a.triggerId,...i,...o,ref:t,onCloseAutoFocus:(0,m.composeEventHandlers)(e.onCloseAutoFocus,e=>{var t;s.current||null==(t=a.triggerRef.current)||t.focus(),s.current=!1,e.preventDefault()}),onInteractOutside:(0,m.composeEventHandlers)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey,n=2===t.button||r;(!a.modal||n)&&(s.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tP.displayName=tR,l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(e0,{...o,...l,ref:t})}).displayName="DropdownMenuGroup";var tM=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(e1,{...o,...l,ref:t})});tM.displayName="DropdownMenuLabel";var tz=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(e4,{...o,...l,ref:t})});tz.displayName="DropdownMenuItem";var tD=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(e8,{...o,...l,ref:t})});tD.displayName="DropdownMenuCheckboxItem",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(te,{...o,...l,ref:t})}).displayName="DropdownMenuRadioGroup";var tT=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(tr,{...o,...l,ref:t})});tT.displayName="DropdownMenuRadioItem";var tI=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(ta,{...o,...l,ref:t})});tI.displayName="DropdownMenuItemIndicator";var tL=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(ti,{...o,...l,ref:t})});tL.displayName="DropdownMenuSeparator",l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(ts,{...o,...l,ref:t})}).displayName="DropdownMenuArrow";var tA=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(tf,{...o,...l,ref:t})});tA.displayName="DropdownMenuSubTrigger";var tH=l.forwardRef((e,t)=>{let{__scopeDropdownMenu:r,...l}=e,o=tj(r);return(0,n.jsx)(th,{...o,...l,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});tH.displayName="DropdownMenuSubContent";var tO=e.i(17412);let tF=(0,ee.default)("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),tB=(0,ee.default)("circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]]);l.forwardRef((e,t)=>{let{className:r,inset:l,children:o,...a}=e;return(0,n.jsxs)(tA,{ref:t,className:(0,q.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",l&&"pl-8",r),...a,children:[o,(0,n.jsx)(tF,{className:"w-4 h-4 ml-auto"})]})}).displayName=tA.displayName,l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)(tH,{ref:t,className:(0,q.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border border-border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...l})}).displayName=tH.displayName;let tW=l.forwardRef((e,t)=>{let{className:r,sideOffset:l=4,...o}=e;return(0,n.jsx)(t_,{children:(0,n.jsx)(tP,{ref:t,sideOffset:l,className:(0,q.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border border-border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",r),...o})})});tW.displayName=tP.displayName;let tG=l.forwardRef((e,t)=>{let{className:r,inset:l,...o}=e;return(0,n.jsx)(tz,{ref:t,className:(0,q.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",l&&"pl-8",r),...o})});tG.displayName=tz.displayName,l.forwardRef((e,t)=>{let{className:r,children:l,checked:o,...a}=e;return(0,n.jsxs)(tD,{ref:t,className:(0,q.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),checked:o,...a,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(tI,{children:(0,n.jsx)(tO.Check,{className:"w-4 h-4"})})}),l]})}).displayName=tD.displayName,l.forwardRef((e,t)=>{let{className:r,children:l,...o}=e;return(0,n.jsxs)(tT,{ref:t,className:(0,q.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...o,children:[(0,n.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,n.jsx)(tI,{children:(0,n.jsx)(tB,{className:"w-2 h-2 fill-current"})})}),l]})}).displayName=tT.displayName,l.forwardRef((e,t)=>{let{className:r,inset:l,...o}=e;return(0,n.jsx)(tM,{ref:t,className:(0,q.cn)("px-2 py-1.5 text-sm font-semibold",l&&"pl-8",r),...o})}).displayName=tM.displayName;let tU=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)(tL,{ref:t,className:(0,q.cn)("-mx-1 my-1 h-px bg-muted",r),...l})});tU.displayName=tL.displayName;var tK=e.i(41764),tV=e.i(45372);let tX=l.forwardRef((e,t)=>{let{className:r,...l}=e;return(0,n.jsx)("textarea",{className:(0,q.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",r),ref:t,...l})});tX.displayName="Textarea";var tY=e.i(61620),tq=e.i(29841);let tJ=(0,ee.default)("save",[["path",{d:"M15.2 3a2 2 0 0 1 1.4.6l3.8 3.8a2 2 0 0 1 .6 1.4V19a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2z",key:"1c8476"}],["path",{d:"M17 21v-7a1 1 0 0 0-1-1H8a1 1 0 0 0-1 1v7",key:"1ydtos"}],["path",{d:"M7 3v4a1 1 0 0 0 1 1h7",key:"t51u73"}]]),tZ=(0,ee.default)("trash-2",[["path",{d:"M10 11v6",key:"nco0om"}],["path",{d:"M14 11v6",key:"outv1u"}],["path",{d:"M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6",key:"miytrc"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M8 6V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2",key:"e791ji"}]]),tQ=(0,ee.default)("sparkles",[["path",{d:"M11.017 2.814a1 1 0 0 1 1.966 0l1.051 5.558a2 2 0 0 0 1.594 1.594l5.558 1.051a1 1 0 0 1 0 1.966l-5.558 1.051a2 2 0 0 0-1.594 1.594l-1.051 5.558a1 1 0 0 1-1.966 0l-1.051-5.558a2 2 0 0 0-1.594-1.594l-5.558-1.051a1 1 0 0 1 0-1.966l5.558-1.051a2 2 0 0 0 1.594-1.594z",key:"1s2grr"}],["path",{d:"M20 2v4",key:"1rf3ol"}],["path",{d:"M22 4h-4",key:"gwowj6"}],["circle",{cx:"4",cy:"20",r:"2",key:"6kqj1y"}]]);var t$=e.i(6458);let t0=(0,ee.default)("square-pen",[["path",{d:"M12 3H5a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7",key:"1m0v6g"}],["path",{d:"M18.375 2.625a1 1 0 0 1 3 3l-9.013 9.014a2 2 0 0 1-.853.505l-2.873.84a.5.5 0 0 1-.62-.62l.84-2.873a2 2 0 0 1 .506-.852z",key:"ohrbg2"}]]);function t1(e){let{note:t,onSave:r,onDelete:o}=e,{t:i}=(0,Q.useI18n)(),[s,d]=(0,l.useState)(""),[c,u]=(0,l.useState)(""),[f,p]=(0,l.useState)([]),[h,m]=(0,l.useState)(!1),{theme:v}=(0,en.useTheme)(),{toast:g}=(0,tY.useToast)(),[x,w]=(0,l.useState)(!1),[y,b]=(0,l.useState)(""),[j,C]=(0,l.useState)(""),[k,N]=(0,l.useState)(!1),[S,E]=(0,l.useState)(!1),[_,R]=(0,l.useState)(""),[P,M]=(0,l.useState)(""),[z,D]=(0,l.useState)(!1),T=(0,l.useRef)(null);(0,l.useEffect)(()=>{t?(d(t.title),u(t.content),p(Array.isArray(t.tags)?t.tags:[]),m(!1)):(d(""),u(""),p([]),m(!0))},[t]);let I=async(e,t)=>{w(e);try{switch(e){case"summarize":C(""),N(!0),console.log("Starting summarize streaming"),(0,tq.summarizeStream)(c,e=>{console.log("Received summarize chunk:",e),C(t=>t+e)}).catch(e=>{console.error("Summarize streaming error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"})});break;case"generateTitle":let r="";console.log("Starting title generation streaming"),await (0,tq.generateTitleStream)(c,e=>{console.log("Received title chunk:",e),r+=e}).catch(e=>{throw console.error("Title streaming error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"}),e}),M(r.trim()),D(!0);break;case"polish":if(!y)return void g({title:i("editor.selection_error"),description:i("editor.select_text_first"),variant:"destructive"});C(""),N(!0),console.log("Starting polish streaming"),(0,tq.polishStream)(y,e=>{console.log("Received polish chunk:",e),C(t=>t+e)}).catch(e=>{console.error("Polish streaming error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"})});break;case"expand":if(!t||!t.trim())return void g({title:i("editor.input_error"),description:i("editor.enter_valid_prompt"),variant:"destructive"});C(""),E(!1),N(!0),console.log("Starting expand streaming with payload:",t),(0,tq.expandContentStream)(t,e=>{console.log("Received expand chunk:",e),C(t=>t+e)}).catch(e=>{console.error("Expand streaming error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"})});break;case"changeTone":if(!y)return void g({title:i("editor.selection_error"),description:i("editor.select_tone_text_first"),variant:"destructive"});C(""),N(!0),console.log("Starting changeTone streaming with tone:",t),(0,tq.changeToneStream)(y,t,e=>{console.log("Received changeTone chunk:",e),C(t=>t+e)}).catch(e=>{console.error("ChangeTone streaming error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"})});break;case"generateTags":if(!c.trim())return void g({title:i("editor.input_error"),description:i("editor.content_required_for_tags"),variant:"destructive"});try{console.log("Starting tag generation");let e=await (0,tq.generateTags)(c),t=[...new Set([...f,...e])];p(t),g({title:i("editor.tags_generated"),description:i("editor.tags_added_successfully"),variant:"default"})}catch(e){console.error("Tag generation error:",e),g({title:i("editor.ai_operation_failed"),description:e instanceof Error?e.message:i("editor.unknown_error"),variant:"destructive"})}}}catch(t){let e=t instanceof Error?t.message:i("editor.unknown_error");g({title:i("editor.ai_operation_failed"),description:e,variant:"destructive"})}finally{w(!1)}};return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)("div",{className:"flex flex-col h-full",children:[(0,n.jsxs)("div",{className:"pb-6",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between",children:[(0,n.jsx)("div",{className:"flex-1",children:h?(0,n.jsx)(el.Input,{value:s,onChange:e=>d(e.target.value),placeholder:i("editor.title_placeholder"),className:"h-auto max-w-2xl px-0 py-1 text-xl font-semibold bg-transparent border-none shadow-none focus-visible:ring-0"}):(0,n.jsx)("h2",{className:"text-xl font-semibold",children:s})}),(0,n.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,n.jsxs)(tN,{children:[(0,n.jsx)(tE,{asChild:!0,children:(0,n.jsxs)(a.Button,{variant:"outline",size:"sm",disabled:!!x,children:[x?(0,n.jsx)(t$.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}):(0,n.jsx)(tQ,{className:"w-4 h-4 mr-2"}),i("editor.ai_tools")]})}),(0,n.jsxs)(tW,{children:[(0,n.jsx)(tG,{onClick:()=>I("summarize"),children:i("editor.summarize")}),(0,n.jsx)(tG,{onClick:()=>I("generateTitle"),children:i("editor.generate_title")}),(0,n.jsx)(tU,{}),(0,n.jsx)(tG,{onClick:()=>I("polish"),disabled:!y,children:i("editor.polish_selected")}),(0,n.jsx)(tG,{onClick:()=>E(!0),children:i("editor.expand_content")}),(0,n.jsx)(tG,{onClick:()=>I("changeTone","專業"),disabled:!y,children:i("editor.change_tone_professional")}),(0,n.jsx)(tG,{onClick:()=>I("changeTone","休閒"),disabled:!y,children:i("editor.change_tone_casual")}),(0,n.jsx)(tU,{}),(0,n.jsx)(tG,{onClick:()=>I("generateTags"),disabled:!c.trim()||!!x,children:i("editor.generate_tags")})]})]}),h?(0,n.jsxs)(a.Button,{onClick:()=>{r((null==t?void 0:t.id)||null,s,c,f),m(!1)},size:"sm",variant:"secondary",children:[(0,n.jsx)(tJ,{className:"w-4 h-4 mr-2"}),i("editor.save")]}):(0,n.jsxs)(a.Button,{onClick:()=>m(!0),size:"sm",variant:"outline",children:[(0,n.jsx)(t0,{className:"w-4 h-4 mr-2"}),i("editor.edit")]}),t&&(0,n.jsxs)(a.Button,{variant:"destructive",onClick:()=>{t&&o(t.id)},size:"sm",children:[(0,n.jsx)(tZ,{className:"w-4 h-4 mr-2"}),i("editor.delete")]})]})]}),(0,n.jsx)("div",{className:"mt-2",children:h?(0,n.jsx)(ea,{value:f,onChange:p,placeholder:i("editor.tags_placeholder")}):(0,n.jsx)("div",{className:"flex flex-wrap gap-2",children:Array.isArray(f)&&f.map(e=>(0,n.jsx)("div",{className:"px-3 py-1 text-sm rounded-full bg-secondary text-secondary-foreground",children:e},e))})})]}),(0,n.jsx)("div",{className:"flex-1 pt-4",ref:T,children:(0,n.jsx)(es,{value:c,onChange:e=>u(e||""),preview:h?"edit":"preview",hideToolbar:!h,visibleDragbar:!1,className:"min-h-[590px] flex-1","data-color-mode":"dark"===v?"dark":"light",previewOptions:{rehypePlugins:[[ec.default,{selector:"a",rewrite:e=>{"element"===e.type&&"a"===e.tagName&&(e.properties={...e.properties,target:"_blank",rel:"noopener noreferrer"})}}]]},onSelectionChange:b})})]}),(0,n.jsx)(tK.Dialog,{open:k,onOpenChange:N,children:(0,n.jsxs)(tK.DialogContent,{children:[(0,n.jsx)(tK.DialogHeader,{children:(0,n.jsx)(tK.DialogTitle,{children:i("editor.ai_result_title")})}),(0,n.jsx)("div",{className:"p-4 my-4 border rounded-md bg-muted max-h-[70vh] overflow-auto",children:(0,n.jsx)(ed.default,{source:j})}),(0,n.jsxs)(tK.DialogFooter,{children:[(0,n.jsx)(a.Button,{variant:"outline",onClick:()=>N(!1),children:i("editor.cancel")}),(0,n.jsx)(a.Button,{onClick:()=>{u("".concat(c,"\n\n---\n\n").concat(j)),N(!1)},variant:"secondary",children:i("editor.insert_at_end")}),y&&(0,n.jsx)(a.Button,{onClick:()=>{u(c.replace(y,j)),N(!1)},variant:"secondary",children:i("editor.replace_selected")})]})]})}),(0,n.jsx)(tK.Dialog,{open:S,onOpenChange:E,children:(0,n.jsxs)(tK.DialogContent,{children:[(0,n.jsx)(tK.DialogHeader,{children:(0,n.jsx)(tK.DialogTitle,{children:i("editor.expand_content_title")})}),(0,n.jsx)(tX,{placeholder:i("editor.expand_placeholder"),value:_,onChange:e=>R(e.target.value),disabled:"expand"===x}),(0,n.jsxs)(tK.DialogFooter,{children:[(0,n.jsx)(a.Button,{variant:"outline",onClick:()=>E(!1),disabled:"expand"===x,children:i("editor.cancel")}),(0,n.jsx)(a.Button,{onClick:()=>I("expand",_),variant:"secondary",disabled:!_.trim()||"expand"===x,children:"expand"===x?(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(t$.Loader2,{className:"w-4 h-4 mr-2 animate-spin"}),i("editor.generating")]}):i("editor.generate")})]})]})}),(0,n.jsx)(tV.AlertDialog,{open:z,onOpenChange:D,children:(0,n.jsxs)(tV.AlertDialogContent,{children:[(0,n.jsxs)(tV.AlertDialogHeader,{children:[(0,n.jsx)(tV.AlertDialogTitle,{children:i("editor.suggested_title")}),(0,n.jsx)(tV.AlertDialogDescription,{children:i("editor.suggested_title_desc")})]}),(0,n.jsx)("div",{className:"p-4 font-semibold border rounded-md bg-muted",children:P}),(0,n.jsxs)(tV.AlertDialogFooter,{children:[(0,n.jsx)(tV.AlertDialogCancel,{children:i("editor.cancel")}),(0,n.jsx)(tV.AlertDialogAction,{onClick:()=>{d(P),D(!1)},className:"text-white bg-black hover:bg-gray-800",children:i("editor.replace")})]})]})})]})}var t2=e.i(30438),t5=e.i(99782);let t4=(0,ee.default)("panel-left-close",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m16 15-3-3 3-3",key:"14y99z"}]]),t3=(0,ee.default)("panel-left-open",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"m14 9 3 3-3 3",key:"8010ee"}]]);var t8=e.i(79431);let t6=(0,ee.default)("grip-vertical",[["circle",{cx:"9",cy:"12",r:"1",key:"1vctgf"}],["circle",{cx:"9",cy:"5",r:"1",key:"hp0tcf"}],["circle",{cx:"9",cy:"19",r:"1",key:"fkjjf6"}],["circle",{cx:"15",cy:"12",r:"1",key:"1tmaij"}],["circle",{cx:"15",cy:"5",r:"1",key:"19l28e"}],["circle",{cx:"15",cy:"19",r:"1",key:"f4zoj3"}]]),t9=(0,l.createContext)(null);t9.displayName="PanelGroupContext";let t7={group:"data-panel-group",groupDirection:"data-panel-group-direction",groupId:"data-panel-group-id",panel:"data-panel",panelCollapsible:"data-panel-collapsible",panelId:"data-panel-id",panelSize:"data-panel-size",resizeHandle:"data-resize-handle",resizeHandleActive:"data-resize-handle-active",resizeHandleEnabled:"data-panel-resize-handle-enabled",resizeHandleId:"data-panel-resize-handle-id",resizeHandleState:"data-resize-handle-state"},re=l.useLayoutEffect,rt=l["useId".toString()],rr="function"==typeof rt?rt:()=>null,rn=0;function rl(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=rr(),r=(0,l.useRef)(e||t||null);return null===r.current&&(r.current=""+rn++),null!=e?e:r.current}function ro(e){let{children:t,className:r="",collapsedSize:n,collapsible:o,defaultSize:a,forwardedRef:i,id:s,maxSize:d,minSize:c,onCollapse:u,onExpand:f,onResize:p,order:h,style:m,tagName:v="div",...g}=e,x=(0,l.useContext)(t9);if(null===x)throw Error("Panel components must be rendered within a PanelGroup container");let{collapsePanel:w,expandPanel:y,getPanelSize:b,getPanelStyle:j,groupId:C,isPanelCollapsed:k,reevaluatePanelConstraints:N,registerPanel:S,resizePanel:E,unregisterPanel:_}=x,R=rl(s),P=(0,l.useRef)({callbacks:{onCollapse:u,onExpand:f,onResize:p},constraints:{collapsedSize:n,collapsible:o,defaultSize:a,maxSize:d,minSize:c},id:R,idIsFromProps:void 0!==s,order:h});(0,l.useRef)({didLogMissingDefaultSizeWarning:!1}),re(()=>{let{callbacks:e,constraints:t}=P.current,r={...t};P.current.id=R,P.current.idIsFromProps=void 0!==s,P.current.order=h,e.onCollapse=u,e.onExpand=f,e.onResize=p,t.collapsedSize=n,t.collapsible=o,t.defaultSize=a,t.maxSize=d,t.minSize=c,(r.collapsedSize!==t.collapsedSize||r.collapsible!==t.collapsible||r.maxSize!==t.maxSize||r.minSize!==t.minSize)&&N(P.current,r)}),re(()=>{let e=P.current;return S(e),()=>{_(e)}},[h,R,S,_]),(0,l.useImperativeHandle)(i,()=>({collapse:()=>{w(P.current)},expand:e=>{y(P.current,e)},getId:()=>R,getSize:()=>b(P.current),isCollapsed:()=>k(P.current),isExpanded:()=>!k(P.current),resize:e=>{E(P.current,e)}}),[w,y,b,k,R,E]);let M=j(P.current,a);return(0,l.createElement)(v,{...g,children:t,className:r,id:R,style:{...M,...m},[t7.groupId]:C,[t7.panel]:"",[t7.panelCollapsible]:o||void 0,[t7.panelId]:R,[t7.panelSize]:parseFloat(""+M.flexGrow).toFixed(1)})}let ra=(0,l.forwardRef)((e,t)=>(0,l.createElement)(ro,{...e,forwardedRef:t}));ro.displayName="Panel",ra.displayName="forwardRef(Panel)";let ri=null,rs=-1,rd=null;function rc(e,r,n){var l,o,a;let i=function(e,t,r){let n=(t&ry)!=0,l=(t&rb)!=0,o=(t&rj)!=0,a=(t&rC)!=0;if(t){if(n)if(o)return"se-resize";else if(a)return"ne-resize";else return"e-resize";else if(l)if(o)return"sw-resize";else if(a)return"nw-resize";else return"w-resize";else if(o)return"s-resize";else if(a)return"n-resize"}switch(e){case"horizontal":return"ew-resize";case"intersection":return"move";case"vertical":return"ns-resize"}}(e,r,0);ri!==i&&(ri=i,null===rd&&(rd=document.createElement("style"),t&&rd.setAttribute("nonce",t),document.head.appendChild(rd)),rs>=0&&(null==(a=rd.sheet)||a.removeRule(rs)),rs=null!=(l=null==(o=rd.sheet)?void 0:o.insertRule("*{cursor: ".concat(i," !important;}")))?l:-1)}function ru(e){return"keydown"===e.type}function rf(e){return e.type.startsWith("pointer")}function rp(e){return e.type.startsWith("mouse")}function rh(e){if(rf(e)){if(e.isPrimary)return{x:e.clientX,y:e.clientY}}else if(rp(e))return{x:e.clientX,y:e.clientY};return{x:1/0,y:1/0}}let rm=/\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\b/;function rv(e){let t=e.length;for(;t--;){let r=e[t];if(rH(r,"Missing node"),function(e){let t=getComputedStyle(e);return!!("fixed"===t.position||"auto"!==t.zIndex&&("static"!==t.position||function(e){var t;let r=getComputedStyle(null!=(t=rw(e))?t:e).display;return"flex"===r||"inline-flex"===r}(e))||1>+t.opacity||"transform"in t&&"none"!==t.transform||"webkitTransform"in t&&"none"!==t.webkitTransform||"mixBlendMode"in t&&"normal"!==t.mixBlendMode||"filter"in t&&"none"!==t.filter||"webkitFilter"in t&&"none"!==t.webkitFilter||"isolation"in t&&"isolate"===t.isolation||rm.test(t.willChange))||"touch"===t.webkitOverflowScrolling}(r))return r}return null}function rg(e){return e&&Number(getComputedStyle(e).zIndex)||0}function rx(e){let t=[];for(;e;)t.push(e),e=rw(e);return t}function rw(e){let{parentNode:t}=e;return t&&t instanceof ShadowRoot?t.host:t}let ry=1,rb=2,rj=4,rC=8,rk="coarse"===function(){if("function"==typeof matchMedia)return matchMedia("(pointer:coarse)").matches?"coarse":"fine"}(),rN=[],rS=!1,rE=new Map,r_=new Map,rR=new Set;function rP(e){let{target:t}=e,{x:r,y:n}=rh(e);rS=!0,rT({target:t,x:r,y:n}),rL(),rN.length>0&&(rA("down",e),rI(),e.preventDefault(),rD(t)||e.stopImmediatePropagation())}function rM(e){let{x:t,y:r}=rh(e);if(rS&&0===e.buttons&&(rS=!1,rA("up",e)),!rS){let{target:n}=e;rT({target:n,x:t,y:r})}rA("move",e),rI(),rN.length>0&&e.preventDefault()}function rz(e){let{target:t}=e,{x:r,y:n}=rh(e);r_.clear(),rS=!1,rN.length>0&&(e.preventDefault(),rD(t)||e.stopImmediatePropagation()),rA("up",e),rT({target:t,x:r,y:n}),rI(),rL()}function rD(e){let t=e;for(;t;){if(t.hasAttribute(t7.resizeHandle))return!0;t=t.parentElement}return!1}function rT(e){let{target:t,x:r,y:n}=e;rN.splice(0);let l=null;(t instanceof HTMLElement||t instanceof SVGElement)&&(l=t),rR.forEach(e=>{let{element:t,hitAreaMargins:o}=e,a=t.getBoundingClientRect(),{bottom:i,left:s,right:d,top:c}=a,u=rk?o.coarse:o.fine;if(r>=s-u&&r<=d+u&&n>=c-u&&n<=i+u){if(null!==l&&document.contains(l)&&t!==l&&!t.contains(l)&&!l.contains(t)&&function(e,t){let r;if(e===t)throw Error("Cannot compare node with itself");let n={a:rx(e),b:rx(t)};for(;n.a.at(-1)===n.b.at(-1);)e=n.a.pop(),t=n.b.pop(),r=e;rH(r,"Stacking order can only be calculated for elements with a common ancestor");let l={a:rg(rv(n.a)),b:rg(rv(n.b))};if(l.a===l.b){let e=r.childNodes,t={a:n.a.at(-1),b:n.b.at(-1)},l=e.length;for(;l--;){let r=e[l];if(r===t.a)return 1;if(r===t.b)return -1}}return Math.sign(l.a-l.b)}(l,t)>0){let e=l,r=!1;for(;e;){var f;if(e.contains(t))break;if(f=e.getBoundingClientRect(),f.x<a.x+a.width&&f.x+f.width>a.x&&f.y<a.y+a.height&&f.y+f.height>a.y){r=!0;break}e=e.parentElement}if(r)return}rN.push(e)}})}function rI(){let e=!1,t=!1;rN.forEach(r=>{let{direction:n}=r;"horizontal"===n?e=!0:t=!0});let r=0;r_.forEach(e=>{r|=e}),e&&t?rc("intersection",r,rS):e?rc("horizontal",r,rS):t?rc("vertical",r,rS):null!==rd&&(document.head.removeChild(rd),ri=null,rd=null,rs=-1)}function rL(){var e;null==(e=r)||e.abort();let t={capture:!0,signal:(r=new AbortController).signal};rR.size&&(rS?(rN.length>0&&rE.forEach((e,r)=>{let{body:n}=r;e>0&&(n.addEventListener("contextmenu",rz,t),n.addEventListener("pointerleave",rM,t),n.addEventListener("pointermove",rM,t))}),rE.forEach((e,r)=>{let{body:n}=r;n.addEventListener("pointerup",rz,t),n.addEventListener("pointercancel",rz,t)})):rE.forEach((e,r)=>{let{body:n}=r;e>0&&(n.addEventListener("pointerdown",rP,t),n.addEventListener("pointermove",rM,t))}))}function rA(e,t){rR.forEach(r=>{let{setResizeHandlerState:n}=r;n(e,rN.includes(r),t)})}function rH(e,t){if(!e)throw console.error(t),Error(t)}function rO(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return e.toFixed(r)===t.toFixed(r)?0:e>t?1:-1}function rF(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:10;return 0===rO(e,t,r)}function rB(e,t,r){return 0===rO(e,t,r)}function rW(e){let{panelConstraints:t,panelIndex:r,size:n}=e,l=t[r];rH(null!=l,"Panel constraints not found for index ".concat(r));let{collapsedSize:o=0,collapsible:a,maxSize:i=100,minSize:s=0}=l;return 0>rO(n,s)&&(n=a&&0>rO(n,(o+s)/2)?o:s),n=parseFloat((n=Math.min(i,n)).toFixed(10))}function rG(e){let{delta:t,initialLayout:r,panelConstraints:n,pivotIndices:l,prevLayout:o,trigger:a}=e;if(rB(t,0))return r;let i=[...r],[s,d]=l;rH(null!=s,"Invalid first pivot index"),rH(null!=d,"Invalid second pivot index");let c=0;if("keyboard"===a){{let e=t<0?d:s,l=n[e];rH(l,"Panel constraints not found for index ".concat(e));let{collapsedSize:o=0,collapsible:a,minSize:i=0}=l;if(a){let n=r[e];if(rH(null!=n,"Previous layout not found for panel index ".concat(e)),rB(n,o)){let e=i-n;rO(e,Math.abs(t))>0&&(t=t<0?0-e:e)}}}{let e=t<0?s:d,l=n[e];rH(l,"No panel constraints found for index ".concat(e));let{collapsedSize:o=0,collapsible:a,minSize:i=0}=l;if(a){let n=r[e];if(rH(null!=n,"Previous layout not found for panel index ".concat(e)),rB(n,i)){let e=n-o;rO(e,Math.abs(t))>0&&(t=t<0?0-e:e)}}}}{let e=t<0?1:-1,l=t<0?d:s,o=0;for(;;){let t=r[l];if(rH(null!=t,"Previous layout not found for panel index ".concat(l)),o+=rW({panelConstraints:n,panelIndex:l,size:100})-t,(l+=e)<0||l>=n.length)break}let a=Math.min(Math.abs(t),Math.abs(o));t=t<0?0-a:a}{let e=t<0?s:d;for(;e>=0&&e<n.length;){let l=Math.abs(t)-Math.abs(c),o=r[e];rH(null!=o,"Previous layout not found for panel index ".concat(e));let a=rW({panelConstraints:n,panelIndex:e,size:o-l});if(!rB(o,a)&&(c+=o-a,i[e]=a,c.toFixed(3).localeCompare(Math.abs(t).toFixed(3),void 0,{numeric:!0})>=0))break;t<0?e--:e++}}if(function(e,t,r){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(!rB(e[r],t[r],void 0))return!1;return!0}(o,i))return o;{let e=t<0?d:s,l=r[e];rH(null!=l,"Previous layout not found for panel index ".concat(e));let o=l+c,a=rW({panelConstraints:n,panelIndex:e,size:o});if(i[e]=a,!rB(a,o)){let e=o-a,r=t<0?d:s;for(;r>=0&&r<n.length;){let l=i[r];rH(null!=l,"Previous layout not found for panel index ".concat(r));let o=rW({panelConstraints:n,panelIndex:r,size:l+e});if(rB(l,o)||(e-=o-l,i[r]=o),rB(e,0))break;t>0?r--:r++}}}return rB(i.reduce((e,t)=>t+e,0),100)?i:o}function rU(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document;return Array.from(t.querySelectorAll("[".concat(t7.resizeHandleId,'][data-panel-group-id="').concat(e,'"]')))}function rK(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:document,n=rU(e,r).findIndex(e=>e.getAttribute(t7.resizeHandleId)===t);return null!=n?n:null}function rV(e,t,r){let n=rK(e,t,r);return null!=n?[n,n+1]:[-1,-1]}function rX(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document;if((t instanceof HTMLElement||"object"==typeof t&&null!==t&&"tagName"in t&&"getAttribute"in t)&&t.dataset.panelGroupId==e)return t;let r=t.querySelector('[data-panel-group][data-panel-group-id="'.concat(e,'"]'));return r||null}function rY(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:document,r=t.querySelector("[".concat(t7.resizeHandleId,'="').concat(e,'"]'));return r||null}function rq(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!==t[r])return!1;return!0}function rJ(e,t){let{x:r,y:n}=rh(t);return"horizontal"===e?r:n}function rZ(e,t,r){t.forEach((t,n)=>{let l=e[n];rH(l,"Panel data not found for index ".concat(n));let{callbacks:o,constraints:a,id:i}=l,{collapsedSize:s=0,collapsible:d}=a,c=r[i];if(null==c||t!==c){r[i]=t;let{onCollapse:e,onExpand:n,onResize:l}=o;l&&l(t,c),d&&(e||n)&&(n&&(null==c||rF(c,s))&&!rF(t,s)&&n(),e&&(null==c||!rF(c,s))&&rF(t,s)&&e())}})}function rQ(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++)if(e[r]!=t[r])return!1;return!0}function r$(e){try{if("undefined"!=typeof localStorage)e.getItem=e=>localStorage.getItem(e),e.setItem=(e,t)=>{localStorage.setItem(e,t)};else throw Error("localStorage not supported in this environment")}catch(t){console.error(t),e.getItem=()=>null,e.setItem=()=>{}}}function r0(e){return"react-resizable-panels:".concat(e)}function r1(e){return e.map(e=>{let{constraints:t,id:r,idIsFromProps:n,order:l}=e;return n?r:l?"".concat(l,":").concat(JSON.stringify(t)):JSON.stringify(t)}).sort((e,t)=>e.localeCompare(t)).join(",")}function r2(e,t){try{let r=r0(e),n=t.getItem(r);if(n){let e=JSON.parse(n);if("object"==typeof e&&null!=e)return e}}catch(e){}return null}function r5(e,t,r,n,l){var o;let a=r0(e),i=r1(t),s=null!=(o=r2(e,l))?o:{};s[i]={expandToSizes:Object.fromEntries(r.entries()),layout:n};try{l.setItem(a,JSON.stringify(s))}catch(e){console.error(e)}}function r4(e){let{layout:t,panelConstraints:r}=e,n=[...t],l=n.reduce((e,t)=>e+t,0);if(n.length!==r.length)throw Error("Invalid ".concat(r.length," panel layout: ").concat(n.map(e=>"".concat(e,"%")).join(", ")));if(!rB(l,100)&&n.length>0)for(let e=0;e<r.length;e++){let t=n[e];rH(null!=t,"No layout data found for index ".concat(e));let r=100/l*t;n[e]=r}let o=0;for(let e=0;e<r.length;e++){let t=n[e];rH(null!=t,"No layout data found for index ".concat(e));let l=rW({panelConstraints:r,panelIndex:e,size:t});t!=l&&(o+=t-l,n[e]=l)}if(!rB(o,0))for(let e=0;e<r.length;e++){let t=n[e];rH(null!=t,"No layout data found for index ".concat(e));let l=rW({panelConstraints:r,panelIndex:e,size:t+o});if(t!==l&&(o-=l-t,n[e]=l,rB(o,0)))break}return n}let r3={getItem:e=>(r$(r3),r3.getItem(e)),setItem:(e,t)=>{r$(r3),r3.setItem(e,t)}},r8={};function r6(e){let{autoSaveId:t=null,children:r,className:n="",direction:o,forwardedRef:a,id:i=null,onLayout:s=null,keyboardResizeBy:d=null,storage:c=r3,style:u,tagName:f="div",...p}=e,h=rl(i),m=(0,l.useRef)(null),[v,g]=(0,l.useState)(null),[x,w]=(0,l.useState)([]),y=function(){let[e,t]=(0,l.useState)(0);return(0,l.useCallback)(()=>t(e=>e+1),[])}(),b=(0,l.useRef)({}),j=(0,l.useRef)(new Map),C=(0,l.useRef)(0),k=(0,l.useRef)({autoSaveId:t,direction:o,dragState:v,id:h,keyboardResizeBy:d,onLayout:s,storage:c}),N=(0,l.useRef)({layout:x,panelDataArray:[],panelDataArrayChanged:!1});(0,l.useRef)({didLogIdAndOrderWarning:!1,didLogPanelConstraintsWarning:!1,prevPanelIds:[]}),(0,l.useImperativeHandle)(a,()=>({getId:()=>k.current.id,getLayout:()=>{let{layout:e}=N.current;return e},setLayout:e=>{let{onLayout:t}=k.current,{layout:r,panelDataArray:n}=N.current,l=r4({layout:e,panelConstraints:n.map(e=>e.constraints)});rq(r,l)||(w(l),N.current.layout=l,t&&t(l),rZ(n,l,b.current))}}),[]),re(()=>{k.current.autoSaveId=t,k.current.direction=o,k.current.dragState=v,k.current.id=h,k.current.onLayout=s,k.current.storage=c}),function(e){let{committedValuesRef:t,eagerValuesRef:r,groupId:n,layout:o,panelDataArray:a,panelGroupElement:i,setLayout:s}=e;(0,l.useRef)({didWarnAboutMissingResizeHandle:!1}),re(()=>{if(!i)return;let e=rU(n,i);for(let t=0;t<a.length-1;t++){let{valueMax:r,valueMin:n,valueNow:l}=function(e){let{layout:t,panelsArray:r,pivotIndices:n}=e,l=0,o=100,a=0,i=0,s=n[0];return rH(null!=s,"No pivot index found"),r.forEach((e,t)=>{let{constraints:r}=e,{maxSize:n=100,minSize:d=0}=r;t===s?(l=d,o=n):(a+=d,i+=n)}),{valueMax:Math.min(o,100-a),valueMin:Math.max(l,100-i),valueNow:t[s]}}({layout:o,panelsArray:a,pivotIndices:[t,t+1]}),i=e[t];if(null==i);else{let e=a[t];rH(e,'No panel data found for index "'.concat(t,'"')),i.setAttribute("aria-controls",e.id),i.setAttribute("aria-valuemax",""+Math.round(r)),i.setAttribute("aria-valuemin",""+Math.round(n)),i.setAttribute("aria-valuenow",null!=l?""+Math.round(l):"")}}return()=>{e.forEach((e,t)=>{e.removeAttribute("aria-controls"),e.removeAttribute("aria-valuemax"),e.removeAttribute("aria-valuemin"),e.removeAttribute("aria-valuenow")})}},[n,o,a,i]),(0,l.useEffect)(()=>{if(!i)return;let e=r.current;rH(e,"Eager values not found");let{panelDataArray:t}=e;rH(null!=rX(n,i),'No group found for id "'.concat(n,'"'));let l=rU(n,i);rH(l,'No resize handles found for group id "'.concat(n,'"'));let a=l.map(e=>{let r=e.getAttribute(t7.resizeHandleId);rH(r,"Resize handle element has no handle id attribute");let[l,a]=function(e,t,r){var n,l,o,a;let i=arguments.length>3&&void 0!==arguments[3]?arguments[3]:document,s=rY(t,i),d=rU(e,i),c=s?d.indexOf(s):-1;return[null!=(n=null==(l=r[c])?void 0:l.id)?n:null,null!=(o=null==(a=r[c+1])?void 0:a.id)?o:null]}(n,r,t,i);if(null==l||null==a)return()=>{};let d=e=>{if(!e.defaultPrevented&&"Enter"===e.key){e.preventDefault();let a=t.findIndex(e=>e.id===l);if(a>=0){let e=t[a];rH(e,"No panel data found for index ".concat(a));let l=o[a],{collapsedSize:d=0,collapsible:c,minSize:u=0}=e.constraints;if(null!=l&&c){let e=rG({delta:rB(l,d)?u-d:d-l,initialLayout:o,panelConstraints:t.map(e=>e.constraints),pivotIndices:rV(n,r,i),prevLayout:o,trigger:"keyboard"});o!==e&&s(e)}}}};return e.addEventListener("keydown",d),()=>{e.removeEventListener("keydown",d)}});return()=>{a.forEach(e=>e())}},[i,t,r,n,o,a,s])}({committedValuesRef:k,eagerValuesRef:N,groupId:h,layout:x,panelDataArray:N.current.panelDataArray,setLayout:w,panelGroupElement:m.current}),(0,l.useEffect)(()=>{let{panelDataArray:e}=N.current;if(t){if(0===x.length||x.length!==e.length)return;let r=r8[t];null==r&&(r=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:10,r=null;return function(){for(var n=arguments.length,l=Array(n),o=0;o<n;o++)l[o]=arguments[o];null!==r&&clearTimeout(r),r=setTimeout(()=>{e(...l)},t)}}(r5,100),r8[t]=r),r(t,[...e],new Map(j.current),x,c)}},[t,x,c]),(0,l.useEffect)(()=>{});let S=(0,l.useCallback)(e=>{let{onLayout:t}=k.current,{layout:r,panelDataArray:n}=N.current;if(e.constraints.collapsible){let l=n.map(e=>e.constraints),{collapsedSize:o=0,panelSize:a,pivotIndices:i}=ne(n,e,r);if(rH(null!=a,'Panel size not found for panel "'.concat(e.id,'"')),!rF(a,o)){j.current.set(e.id,a);let s=rG({delta:r7(n,e)===n.length-1?a-o:o-a,initialLayout:r,panelConstraints:l,pivotIndices:i,prevLayout:r,trigger:"imperative-api"});rQ(r,s)||(w(s),N.current.layout=s,t&&t(s),rZ(n,s,b.current))}}},[]),E=(0,l.useCallback)((e,t)=>{let{onLayout:r}=k.current,{layout:n,panelDataArray:l}=N.current;if(e.constraints.collapsible){let o=l.map(e=>e.constraints),{collapsedSize:a=0,panelSize:i=0,minSize:s=0,pivotIndices:d}=ne(l,e,n),c=null!=t?t:s;if(rF(i,a)){let t=j.current.get(e.id),a=null!=t&&t>=c?t:c,s=rG({delta:r7(l,e)===l.length-1?i-a:a-i,initialLayout:n,panelConstraints:o,pivotIndices:d,prevLayout:n,trigger:"imperative-api"});rQ(n,s)||(w(s),N.current.layout=s,r&&r(s),rZ(l,s,b.current))}}},[]),_=(0,l.useCallback)(e=>{let{layout:t,panelDataArray:r}=N.current,{panelSize:n}=ne(r,e,t);return rH(null!=n,'Panel size not found for panel "'.concat(e.id,'"')),n},[]),R=(0,l.useCallback)((e,t)=>{let{panelDataArray:r}=N.current,n=r7(r,e);return function(e){let{defaultSize:t,dragState:r,layout:n,panelData:l,panelIndex:o,precision:a=3}=e,i=n[o];return{flexBasis:0,flexGrow:null==i?void 0!=t?t.toFixed(a):"1":1===l.length?"1":i.toFixed(a),flexShrink:1,overflow:"hidden",pointerEvents:null!==r?"none":void 0}}({defaultSize:t,dragState:v,layout:x,panelData:r,panelIndex:n})},[v,x]),P=(0,l.useCallback)(e=>{let{layout:t,panelDataArray:r}=N.current,{collapsedSize:n=0,collapsible:l,panelSize:o}=ne(r,e,t);return rH(null!=o,'Panel size not found for panel "'.concat(e.id,'"')),!0===l&&rF(o,n)},[]),M=(0,l.useCallback)(e=>{let{layout:t,panelDataArray:r}=N.current,{collapsedSize:n=0,collapsible:l,panelSize:o}=ne(r,e,t);return rH(null!=o,'Panel size not found for panel "'.concat(e.id,'"')),!l||rO(o,n)>0},[]),z=(0,l.useCallback)(e=>{let{panelDataArray:t}=N.current;t.push(e),t.sort((e,t)=>{let r=e.order,n=t.order;return null==r&&null==n?0:null==r?-1:null==n?1:r-n}),N.current.panelDataArrayChanged=!0,y()},[y]);re(()=>{if(N.current.panelDataArrayChanged){N.current.panelDataArrayChanged=!1;let{autoSaveId:l,onLayout:o,storage:a}=k.current,{layout:i,panelDataArray:s}=N.current,d=null;if(l){var e,t,r,n;let o=(e=l,t=s,null!=(n=(null!=(r=r2(e,a))?r:{})[r1(t)])?n:null);o&&(j.current=new Map(Object.entries(o.expandToSizes)),d=o.layout)}null==d&&(d=function(e){let{panelDataArray:t}=e,r=Array(t.length),n=t.map(e=>e.constraints),l=0,o=100;for(let e=0;e<t.length;e++){let t=n[e];rH(t,"Panel constraints not found for index ".concat(e));let{defaultSize:a}=t;null!=a&&(l++,r[e]=a,o-=a)}for(let e=0;e<t.length;e++){let a=n[e];rH(a,"Panel constraints not found for index ".concat(e));let{defaultSize:i}=a;if(null!=i)continue;let s=o/(t.length-l);l++,r[e]=s,o-=s}return r}({panelDataArray:s}));let c=r4({layout:d,panelConstraints:s.map(e=>e.constraints)});rq(i,c)||(w(c),N.current.layout=c,o&&o(c),rZ(s,c,b.current))}}),re(()=>{let e=N.current;return()=>{e.layout=[]}},[]);let D=(0,l.useCallback)(e=>{let t=!1,r=m.current;return r&&"rtl"===window.getComputedStyle(r,null).getPropertyValue("direction")&&(t=!0),function(r){var n,l;r.preventDefault();let o=m.current;if(!o)return()=>null;let{direction:a,dragState:i,id:s,keyboardResizeBy:d,onLayout:c}=k.current,{layout:u,panelDataArray:f}=N.current,{initialLayout:p}=null!=i?i:{},h=rV(s,e,o),v=function(e,t,r,n,l,o){if(ru(e)){let t="horizontal"===r,n=0;n=e.shiftKey?100:null!=l?l:10;let o=0;switch(e.key){case"ArrowDown":o=t?0:n;break;case"ArrowLeft":o=t?-n:0;break;case"ArrowRight":o=t?n:0;break;case"ArrowUp":o=t?0:-n;break;case"End":o=100;break;case"Home":o=-100}return o}return null==n?0:function(e,t,r,n,l){let o="horizontal"===r,a=rY(t,l);rH(a,'No resize handle element found for id "'.concat(t,'"'));let i=a.getAttribute(t7.groupId);rH(i,"Resize handle element has no group id attribute");let{initialCursorPosition:s}=n,d=rJ(r,e),c=rX(i,l);rH(c,'No group element found for id "'.concat(i,'"'));let u=c.getBoundingClientRect();return(d-s)/(o?u.width:u.height)*100}(e,t,r,n,o)}(r,e,a,i,d,o),g="horizontal"===a;g&&t&&(v=-v);let x=rG({delta:v,initialLayout:null!=p?p:u,panelConstraints:f.map(e=>e.constraints),pivotIndices:h,prevLayout:u,trigger:ru(r)?"keyboard":"mouse-or-touch"}),y=!rQ(u,x);(rf(r)||rp(r))&&C.current!=v&&((C.current=v,y||0===v)?r_.set(e,0):g?(n=v<0?ry:rb,r_.set(e,n)):(l=v<0?rj:rC,r_.set(e,l))),y&&(w(x),N.current.layout=x,c&&c(x),rZ(f,x,b.current))}},[]),T=(0,l.useCallback)((e,t)=>{let{onLayout:r}=k.current,{layout:n,panelDataArray:l}=N.current,o=l.map(e=>e.constraints),{panelSize:a,pivotIndices:i}=ne(l,e,n);rH(null!=a,'Panel size not found for panel "'.concat(e.id,'"'));let s=rG({delta:r7(l,e)===l.length-1?a-t:t-a,initialLayout:n,panelConstraints:o,pivotIndices:i,prevLayout:n,trigger:"imperative-api"});rQ(n,s)||(w(s),N.current.layout=s,r&&r(s),rZ(l,s,b.current))},[]),I=(0,l.useCallback)((e,t)=>{let{layout:r,panelDataArray:n}=N.current,{collapsedSize:l=0,collapsible:o}=t,{collapsedSize:a=0,collapsible:i,maxSize:s=100,minSize:d=0}=e.constraints,{panelSize:c}=ne(n,e,r);null!=c&&(o&&i&&rF(c,l)?rF(l,a)||T(e,a):c<d?T(e,d):c>s&&T(e,s))},[T]),L=(0,l.useCallback)((e,t)=>{let{direction:r}=k.current,{layout:n}=N.current;if(!m.current)return;let l=rY(e,m.current);rH(l,'Drag handle element not found for id "'.concat(e,'"'));let o=rJ(r,t);g({dragHandleId:e,dragHandleRect:l.getBoundingClientRect(),initialCursorPosition:o,initialLayout:n})},[]),A=(0,l.useCallback)(()=>{g(null)},[]),H=(0,l.useCallback)(e=>{let{panelDataArray:t}=N.current,r=r7(t,e);r>=0&&(t.splice(r,1),delete b.current[e.id],N.current.panelDataArrayChanged=!0,y())},[y]),O=(0,l.useMemo)(()=>({collapsePanel:S,direction:o,dragState:v,expandPanel:E,getPanelSize:_,getPanelStyle:R,groupId:h,isPanelCollapsed:P,isPanelExpanded:M,reevaluatePanelConstraints:I,registerPanel:z,registerResizeHandle:D,resizePanel:T,startDragging:L,stopDragging:A,unregisterPanel:H,panelGroupElement:m.current}),[S,v,o,E,_,R,h,P,M,I,z,D,T,L,A,H]);return(0,l.createElement)(t9.Provider,{value:O},(0,l.createElement)(f,{...p,children:r,className:n,id:i,ref:m,style:{display:"flex",flexDirection:"horizontal"===o?"row":"column",height:"100%",overflow:"hidden",width:"100%",...u},[t7.group]:"",[t7.groupDirection]:o,[t7.groupId]:h}))}let r9=(0,l.forwardRef)((e,t)=>(0,l.createElement)(r6,{...e,forwardedRef:t}));function r7(e,t){return e.findIndex(e=>e===t||e.id===t.id)}function ne(e,t,r){let n=r7(e,t),l=n===e.length-1,o=r[n];return{...t.constraints,panelSize:o,pivotIndices:l?[n-1,n]:[n,n+1]}}function nt(e){var t,r;let{children:n=null,className:o="",disabled:a=!1,hitAreaMargins:i,id:s,onBlur:d,onClick:c,onDragging:u,onFocus:f,onPointerDown:p,onPointerUp:h,style:m={},tabIndex:v=0,tagName:g="div",...x}=e,w=(0,l.useRef)(null),y=(0,l.useRef)({onClick:c,onDragging:u,onPointerDown:p,onPointerUp:h});(0,l.useEffect)(()=>{y.current.onClick=c,y.current.onDragging=u,y.current.onPointerDown=p,y.current.onPointerUp=h});let b=(0,l.useContext)(t9);if(null===b)throw Error("PanelResizeHandle components must be rendered within a PanelGroup container");let{direction:j,groupId:C,registerResizeHandle:k,startDragging:N,stopDragging:S,panelGroupElement:E}=b,_=rl(s),[R,P]=(0,l.useState)("inactive"),[M,z]=(0,l.useState)(!1),[D,T]=(0,l.useState)(null),I=(0,l.useRef)({state:R});re(()=>{I.current.state=R}),(0,l.useEffect)(()=>{if(a)T(null);else{let e=k(_);T(()=>e)}},[a,_,k]);let L=null!=(t=null==i?void 0:i.coarse)?t:15,A=null!=(r=null==i?void 0:i.fine)?r:5;return(0,l.useEffect)(()=>{if(a||null==D)return;let e=w.current;rH(e,"Element ref not attached");let t=!1;return function(e,t,r,n,l){var o;let{ownerDocument:a}=t,i={direction:r,element:t,hitAreaMargins:n,setResizeHandlerState:l},s=null!=(o=rE.get(a))?o:0;return rE.set(a,s+1),rR.add(i),rL(),function(){var t;r_.delete(e),rR.delete(i);let r=null!=(t=rE.get(a))?t:1;if(rE.set(a,r-1),rL(),1===r&&rE.delete(a),rN.includes(i)){let e=rN.indexOf(i);e>=0&&rN.splice(e,1),rI(),l("up",!0,null)}}}(_,e,j,{coarse:L,fine:A},(e,r,n)=>{if(!r)return void P("inactive");switch(e){case"down":{P("drag"),t=!1,rH(n,'Expected event to be defined for "down" action'),N(_,n);let{onDragging:e,onPointerDown:r}=y.current;null==e||e(!0),null==r||r();break}case"move":{let{state:e}=I.current;t=!0,"drag"!==e&&P("hover"),rH(n,'Expected event to be defined for "move" action'),D(n);break}case"up":{P("hover"),S();let{onClick:e,onDragging:r,onPointerUp:n}=y.current;null==r||r(!1),null==n||n(),t||null==e||e()}}})},[L,j,a,A,k,_,D,N,S]),!function(e){let{disabled:t,handleId:r,resizeHandler:n,panelGroupElement:o}=e;(0,l.useEffect)(()=>{if(t||null==n||null==o)return;let e=rY(r,o);if(null==e)return;let l=t=>{if(!t.defaultPrevented)switch(t.key){case"ArrowDown":case"ArrowLeft":case"ArrowRight":case"ArrowUp":case"End":case"Home":t.preventDefault(),n(t);break;case"F6":{t.preventDefault();let n=e.getAttribute(t7.groupId);rH(n,'No group element found for id "'.concat(n,'"'));let l=rU(n,o),a=rK(n,r,o);rH(null!==a,'No resize element found for id "'.concat(r,'"'));let i=t.shiftKey?a>0?a-1:l.length-1:a+1<l.length?a+1:0;l[i].focus()}}};return e.addEventListener("keydown",l),()=>{e.removeEventListener("keydown",l)}},[o,t,r,n])}({disabled:a,handleId:_,resizeHandler:D,panelGroupElement:E}),(0,l.createElement)(g,{...x,children:n,className:o,id:s,onBlur:()=>{z(!1),null==d||d()},onFocus:()=>{z(!0),null==f||f()},ref:w,role:"separator",style:{touchAction:"none",userSelect:"none",...m},tabIndex:v,[t7.groupDirection]:j,[t7.groupId]:C,[t7.resizeHandle]:"",[t7.resizeHandleActive]:"drag"===R?"pointer":M?"keyboard":void 0,[t7.resizeHandleEnabled]:!a,[t7.resizeHandleId]:_,[t7.resizeHandleState]:R})}r6.displayName="PanelGroup",r9.displayName="forwardRef(PanelGroup)",nt.displayName="PanelResizeHandle";let nr=e=>{let{className:t,...r}=e;return(0,n.jsx)(r9,{className:(0,q.cn)("flex h-full w-full data-[panel-group-direction=vertical]:flex-col",t),...r})},nn=e=>{let{withHandle:t,className:r,...l}=e;return(0,n.jsx)(nt,{className:(0,q.cn)("relative flex w-px items-center justify-center bg-border after:absolute after:inset-y-0 after:left-1/2 after:w-1 after:-translate-x-1/2 focus-visible:outline-none data-[panel-group-direction=vertical]:h-px data-[panel-group-direction=vertical]:w-full data-[panel-group-direction=vertical]:after:left-0 data-[panel-group-direction=vertical]:after:h-1 data-[panel-group-direction=vertical]:after:w-full data-[panel-group-direction=vertical]:after:-translate-y-1/2 data-[panel-group-direction=vertical]:after:translate-x-0 [&[data-panel-group-direction=vertical]>div]:rotate-90",r),...l,children:t&&(0,n.jsx)("div",{className:"z-10 flex items-center justify-center w-3 h-4 border rounded-sm bg-border",children:(0,n.jsx)(t6,{className:"h-2.5 w-2.5"})})})};function nl(){let{t:e}=(0,Q.useI18n)(),[t,r]=(0,l.useState)([]),[i,s]=(0,l.useState)(null),[d,c]=(0,l.useState)(!1),[u,f]=(0,l.useState)(!0),[p,h]=(0,l.useState)(!1),{toast:m}=(0,tY.useToast)(),v=(0,o.useSearchParams)(),g=(0,l.useCallback)(async()=>{var t;if("function"!=typeof(null==(t=window.electron)?void 0:t.getNotes)){f(!1),m({title:e("edit.error"),description:e("edit.electron_api_unavailable"),variant:"destructive"});return}try{let e=(await window.electron.getNotes()).map(e=>({id:e.id,title:e.title,content:e.content,tags:e.tags}));r(e);let t=v.get("id");t?x(t):c(!0)}catch(r){let t=r instanceof Error?r.message:e("edit.cannot_load_notes");m({title:e("edit.load_failed"),description:t,variant:"destructive"})}finally{f(!1)}},[m,v]);(0,l.useEffect)(()=>{g()},[g]);let x=async t=>{try{let e=await window.electron.getNote(t);s(e),c(!1)}catch(r){let t=r instanceof Error?r.message:e("edit.cannot_load_note_content");m({title:e("edit.load_failed"),description:t,variant:"destructive"})}},w=async(t,n,l,o)=>{try{let a;a=t?await window.electron.updateNote(t,n,l,o):await window.electron.createNote(n,l,o),s(a),c(!1),r(e=>{let t=e.findIndex(e=>e.id===a.id);if(!(t>-1))return[{id:a.id,title:a.title,content:a.content,tags:a.tags},...e];{let r=[...e];return r[t]={id:a.id,title:a.title,content:a.content,tags:a.tags},r}}),m({title:e("edit.save_success"),description:e("edit.note_saved")})}catch(r){let t=r instanceof Error?r.message:e("edit.cannot_save_note");m({title:e("edit.save_failed"),description:t,variant:"destructive"})}},y=async t=>{try{await window.electron.deleteNote(t),s(null),await g(),m({title:e("edit.delete_success"),description:e("edit.note_deleted")})}catch(r){let t=r instanceof Error?r.message:e("edit.cannot_delete_note");m({title:e("edit.delete_failed"),description:t,variant:"destructive"})}},b=()=>{s(null),c(!0)};return u?(0,n.jsx)("div",{className:"flex items-center justify-center h-screen",children:e("edit.loading")}):(0,n.jsxs)("div",{className:"flex h-screen bg-background text-foreground",children:[(0,n.jsxs)("div",{className:"md:hidden",children:[(0,n.jsx)("div",{className:"\n            ".concat(p?"translate-x-0":"-translate-x-full","\n            fixed inset-y-0 left-0 z-50 w-80 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 border-r shadow-lg transition-transform duration-300 ease-in-out\n          "),children:(0,n.jsxs)("div",{className:"flex flex-col h-full p-4",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,n.jsxs)(t8.default,{href:"/",className:"flex items-center gap-2",children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"w-6 h-6",children:[(0,n.jsx)("path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"}),(0,n.jsx)("polyline",{points:"9 22 9 12 15 12 15 22"})]}),(0,n.jsx)("h1",{className:"text-2xl font-bold",children:"MyNote"})]}),(0,n.jsxs)("div",{className:"flex items-center gap-2",children:[(0,n.jsx)(t5.ThemeToggle,{}),(0,n.jsx)(a.Button,{variant:"ghost",size:"icon",onClick:()=>h(!1),children:(0,n.jsx)(t4,{className:"w-5 h-5"})})]})]}),(0,n.jsx)(a.Button,{variant:"outline",onClick:b,className:"mb-4",children:e("edit.new_note")}),(0,n.jsx)("div",{className:"flex-1 overflow-hidden",children:(0,n.jsx)(er,{notes:t,onSelectNote:e=>{x(e),h(!1)},selectedNoteId:null==i?void 0:i.id})})]})}),p&&(0,n.jsx)("div",{className:"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm",onClick:()=>h(!1)})]}),(0,n.jsx)("div",{className:"hidden w-full h-full md:flex",children:(0,n.jsxs)(nr,{direction:"horizontal",className:"h-full",children:[(0,n.jsx)(ra,{defaultSize:25,minSize:20,maxSize:40,className:"min-w-[280px]",children:(0,n.jsxs)("div",{className:"flex flex-col h-full bg-card/30",children:[(0,n.jsxs)("div",{className:"flex items-center justify-between p-4 bg-card/50",children:[(0,n.jsxs)(t8.default,{href:"/",className:"flex items-center gap-2 transition-opacity hover:opacity-80",children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"24",height:"24",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"w-6 h-6 text-primary",children:[(0,n.jsx)("path",{d:"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"}),(0,n.jsx)("polyline",{points:"9 22 9 12 15 12 15 22"})]}),(0,n.jsx)("h1",{className:"text-xl font-bold text-transparent bg-gradient-to-r from-primary to-primary/70 bg-clip-text",children:"MyNote"})]}),(0,n.jsx)(t5.ThemeToggle,{})]}),(0,n.jsx)("div",{className:"p-4",children:(0,n.jsxs)(a.Button,{variant:"default",onClick:b,className:"w-full shadow-sm bg-primary hover:bg-primary/90 text-primary-foreground",size:"sm",children:[(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-2",children:[(0,n.jsx)("path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"}),(0,n.jsx)("polyline",{points:"14 2 14 8 20 8"}),(0,n.jsx)("path",{d:"M12 18v-6"}),(0,n.jsx)("path",{d:"M9 15h6"})]}),e("edit.new_note")]})}),(0,n.jsx)("div",{className:"flex-1 p-2 overflow-hidden",children:(0,n.jsx)(er,{notes:t,onSelectNote:x,selectedNoteId:null==i?void 0:i.id})})]})}),(0,n.jsx)(nn,{withHandle:!0,className:"resizable-handle-custom"}),(0,n.jsx)(ra,{defaultSize:75,minSize:50,children:(0,n.jsx)("main",{className:"flex-1 h-full overflow-hidden",children:i||d?(0,n.jsx)("div",{className:"h-full p-6",children:(0,n.jsx)(t1,{note:i,onSave:w,onDelete:y})}):(0,n.jsx)("div",{className:"flex items-center justify-center h-full",children:(0,n.jsxs)("div",{className:"space-y-4 text-center",children:[(0,n.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10",children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-primary",children:[(0,n.jsx)("path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"}),(0,n.jsx)("polyline",{points:"14 2 14 8 20 8"})]})}),(0,n.jsx)("h2",{className:"text-2xl font-semibold",children:e("edit.welcome_title")}),(0,n.jsx)("p",{className:"max-w-md text-muted-foreground",children:e("edit.welcome_description")})]})})})})]})}),(0,n.jsx)("div",{className:"md:hidden fixed top-0 left-0 right-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b",children:(0,n.jsxs)("div",{className:"flex items-center justify-between p-4",children:[(0,n.jsx)(a.Button,{variant:"ghost",size:"icon",onClick:()=>h(!p),children:(0,n.jsx)(t3,{className:"w-5 h-5"})}),(0,n.jsx)("h1",{className:"text-lg font-semibold",children:"MyNote"}),(0,n.jsx)(t5.ThemeToggle,{})]})}),(0,n.jsx)("div",{className:"w-full h-full pt-16 md:hidden",children:i||d?(0,n.jsx)("div",{className:"h-full p-4",children:(0,n.jsx)(t1,{note:i,onSave:w,onDelete:y})}):(0,n.jsx)("div",{className:"flex items-center justify-center h-full p-8",children:(0,n.jsxs)("div",{className:"space-y-4 text-center",children:[(0,n.jsx)("div",{className:"flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10",children:(0,n.jsxs)("svg",{xmlns:"http://www.w3.org/2000/svg",width:"32",height:"32",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"text-primary",children:[(0,n.jsx)("path",{d:"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"}),(0,n.jsx)("polyline",{points:"14 2 14 8 20 8"})]})}),(0,n.jsx)("h2",{className:"text-2xl font-semibold",children:e("edit.welcome_title")}),(0,n.jsx)("p",{className:"max-w-md text-muted-foreground",children:e("edit.welcome_description")})]})})}),(0,n.jsx)(t2.Toaster,{})]})}function no(){return(0,n.jsx)(l.Suspense,{fallback:(0,n.jsx)("div",{children:"載入中..."}),children:(0,n.jsx)(nl,{})})}}]);