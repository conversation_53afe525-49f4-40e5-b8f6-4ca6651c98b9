"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-from-parse5";
exports.ids = ["vendor-chunks/hast-util-from-parse5"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-from-parse5/lib/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-from-parse5/lib/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fromParse5: () => (/* binding */ fromParse5)\n/* harmony export */ });\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hastscript__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! hastscript */ \"(ssr)/./node_modules/hastscript/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var vfile_location__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! vfile-location */ \"(ssr)/./node_modules/vfile-location/lib/index.js\");\n/* harmony import */ var web_namespaces__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! web-namespaces */ \"(ssr)/./node_modules/web-namespaces/index.js\");\n/**\n * @import {ElementData, Element, Nodes, RootContent, Root} from 'hast'\n * @import {DefaultTreeAdapterMap, Token} from 'parse5'\n * @import {Schema} from 'property-information'\n * @import {Point, Position} from 'unist'\n * @import {VFile} from 'vfile'\n * @import {Options} from 'hast-util-from-parse5'\n */\n\n/**\n * @typedef State\n *   Info passed around about the current state.\n * @property {VFile | undefined} file\n *   Corresponding file.\n * @property {boolean} location\n *   Whether location info was found.\n * @property {Schema} schema\n *   Current schema.\n * @property {boolean | undefined} verbose\n *   Add extra positional info.\n */\n\n\n\n\n\n\n\nconst own = {}.hasOwnProperty\n/** @type {unknown} */\n// type-coverage:ignore-next-line\nconst proto = Object.prototype\n\n/**\n * Transform a `parse5` AST to hast.\n *\n * @param {DefaultTreeAdapterMap['node']} tree\n *   `parse5` tree to transform.\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {Nodes}\n *   hast tree.\n */\nfunction fromParse5(tree, options) {\n  const settings = options || {}\n\n  return one(\n    {\n      file: settings.file || undefined,\n      location: false,\n      schema: settings.space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html,\n      verbose: settings.verbose || false\n    },\n    tree\n  )\n}\n\n/**\n * Transform a node.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} node\n *   p5 node.\n * @returns {Nodes}\n *   hast node.\n */\nfunction one(state, node) {\n  /** @type {Nodes} */\n  let result\n\n  switch (node.nodeName) {\n    case '#comment': {\n      const reference = /** @type {DefaultTreeAdapterMap['commentNode']} */ (\n        node\n      )\n      result = {type: 'comment', value: reference.data}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#document':\n    case '#document-fragment': {\n      const reference =\n        /** @type {DefaultTreeAdapterMap['document'] | DefaultTreeAdapterMap['documentFragment']} */ (\n          node\n        )\n      const quirksMode =\n        'mode' in reference\n          ? reference.mode === 'quirks' || reference.mode === 'limited-quirks'\n          : false\n\n      result = {\n        type: 'root',\n        children: all(state, node.childNodes),\n        data: {quirksMode}\n      }\n\n      if (state.file && state.location) {\n        const document = String(state.file)\n        const loc = (0,vfile_location__WEBPACK_IMPORTED_MODULE_1__.location)(document)\n        const start = loc.toPoint(0)\n        const end = loc.toPoint(document.length)\n        // Always defined as we give valid input.\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(start, 'expected `start`')\n        ;(0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(end, 'expected `end`')\n        result.position = {start, end}\n      }\n\n      return result\n    }\n\n    case '#documentType': {\n      const reference = /** @type {DefaultTreeAdapterMap['documentType']} */ (\n        node\n      )\n      result = {type: 'doctype'}\n      patch(state, reference, result)\n      return result\n    }\n\n    case '#text': {\n      const reference = /** @type {DefaultTreeAdapterMap['textNode']} */ (node)\n      result = {type: 'text', value: reference.value}\n      patch(state, reference, result)\n      return result\n    }\n\n    // Element.\n    default: {\n      const reference = /** @type {DefaultTreeAdapterMap['element']} */ (node)\n      result = element(state, reference)\n      return result\n    }\n  }\n}\n\n/**\n * Transform children.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Array<DefaultTreeAdapterMap['node']>} nodes\n *   Nodes.\n * @returns {Array<RootContent>}\n *   hast nodes.\n */\nfunction all(state, nodes) {\n  let index = -1\n  /** @type {Array<RootContent>} */\n  const results = []\n\n  while (++index < nodes.length) {\n    // Assume no roots in `nodes`.\n    const result = /** @type {RootContent} */ (one(state, nodes[index]))\n    results.push(result)\n  }\n\n  return results\n}\n\n/**\n * Transform an element.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['element']} node\n *   `parse5` node to transform.\n * @returns {Element}\n *   hast node.\n */\nfunction element(state, node) {\n  const schema = state.schema\n\n  state.schema = node.namespaceURI === web_namespaces__WEBPACK_IMPORTED_MODULE_3__.webNamespaces.svg ? property_information__WEBPACK_IMPORTED_MODULE_0__.svg : property_information__WEBPACK_IMPORTED_MODULE_0__.html\n\n  // Props.\n  let index = -1\n  /** @type {Record<string, string>} */\n  const properties = {}\n\n  while (++index < node.attrs.length) {\n    const attribute = node.attrs[index]\n    const name =\n      (attribute.prefix ? attribute.prefix + ':' : '') + attribute.name\n    if (!own.call(proto, name)) {\n      properties[name] = attribute.value\n    }\n  }\n\n  // Build.\n  const x = state.schema.space === 'svg' ? hastscript__WEBPACK_IMPORTED_MODULE_4__.s : hastscript__WEBPACK_IMPORTED_MODULE_4__.h\n  const result = x(node.tagName, properties, all(state, node.childNodes))\n  patch(state, node, result)\n\n  // Switch content.\n  if (result.tagName === 'template') {\n    const reference = /** @type {DefaultTreeAdapterMap['template']} */ (node)\n    const pos = reference.sourceCodeLocation\n    const startTag = pos && pos.startTag && position(pos.startTag)\n    const endTag = pos && pos.endTag && position(pos.endTag)\n\n    // Root in, root out.\n    const content = /** @type {Root} */ (one(state, reference.content))\n\n    if (startTag && endTag && state.file) {\n      content.position = {start: startTag.end, end: endTag.start}\n    }\n\n    result.content = content\n  }\n\n  state.schema = schema\n\n  return result\n}\n\n/**\n * Patch positional info from `from` onto `to`.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {DefaultTreeAdapterMap['node']} from\n *   p5 node.\n * @param {Nodes} to\n *   hast node.\n * @returns {undefined}\n *   Nothing.\n */\nfunction patch(state, from, to) {\n  if ('sourceCodeLocation' in from && from.sourceCodeLocation && state.file) {\n    const position = createLocation(state, to, from.sourceCodeLocation)\n\n    if (position) {\n      state.location = true\n      to.position = position\n    }\n  }\n}\n\n/**\n * Create clean positional information.\n *\n * @param {State} state\n *   Info passed around about the current state.\n * @param {Nodes} node\n *   hast node.\n * @param {Token.ElementLocation} location\n *   p5 location info.\n * @returns {Position | undefined}\n *   Position, or nothing.\n */\nfunction createLocation(state, node, location) {\n  const result = position(location)\n\n  if (node.type === 'element') {\n    const tail = node.children[node.children.length - 1]\n\n    // Bug for unclosed with children.\n    // See: <https://github.com/inikulin/parse5/issues/109>.\n    if (\n      result &&\n      !location.endTag &&\n      tail &&\n      tail.position &&\n      tail.position.end\n    ) {\n      result.end = Object.assign({}, tail.position.end)\n    }\n\n    if (state.verbose) {\n      /** @type {Record<string, Position | undefined>} */\n      const properties = {}\n      /** @type {string} */\n      let key\n\n      if (location.attrs) {\n        for (key in location.attrs) {\n          if (own.call(location.attrs, key)) {\n            properties[(0,property_information__WEBPACK_IMPORTED_MODULE_5__.find)(state.schema, key).property] = position(\n              location.attrs[key]\n            )\n          }\n        }\n      }\n\n      (0,devlop__WEBPACK_IMPORTED_MODULE_2__.ok)(location.startTag, 'a start tag should exist')\n      const opening = position(location.startTag)\n      const closing = location.endTag ? position(location.endTag) : undefined\n      /** @type {ElementData['position']} */\n      const data = {opening}\n      if (closing) data.closing = closing\n      data.properties = properties\n\n      node.data = {position: data}\n    }\n  }\n\n  return result\n}\n\n/**\n * Turn a p5 location into a position.\n *\n * @param {Token.Location} loc\n *   Location.\n * @returns {Position | undefined}\n *   Position or nothing.\n */\nfunction position(loc) {\n  const start = point({\n    line: loc.startLine,\n    column: loc.startCol,\n    offset: loc.startOffset\n  })\n  const end = point({\n    line: loc.endLine,\n    column: loc.endCol,\n    offset: loc.endOffset\n  })\n\n  // @ts-expect-error: we do use `undefined` for points if one or the other\n  // exists.\n  return start || end ? {start, end} : undefined\n}\n\n/**\n * Filter out invalid points.\n *\n * @param {Point} point\n *   Point with potentially `undefined` values.\n * @returns {Point | undefined}\n *   Point or nothing.\n */\nfunction point(point) {\n  return point.line && point.column ? point : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-from-parse5/lib/index.js\n");

/***/ })

};
;