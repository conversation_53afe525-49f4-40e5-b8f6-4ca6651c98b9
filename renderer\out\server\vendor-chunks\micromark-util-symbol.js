"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/micromark-util-symbol";
exports.ids = ["vendor-chunks/micromark-util-symbol"];
exports.modules = {

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/codes.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/codes.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   codes: () => (/* binding */ codes)\n/* harmony export */ });\n/**\n * Character codes.\n *\n * This module is compiled away!\n *\n * micromark works based on character codes.\n * This module contains constants for the ASCII block and the replacement\n * character.\n * A couple of them are handled in a special way, such as the line endings\n * (CR, LF, and CR+LF, commonly known as end-of-line: EOLs), the tab (horizontal\n * tab) and its expansion based on what column it’s at (virtual space),\n * and the end-of-file (eof) character.\n * As values are preprocessed before handling them, the actual characters LF,\n * CR, HT, and NUL (which is present as the replacement character), are\n * guaranteed to not exist.\n *\n * Unicode basic latin block.\n */\nconst codes = /** @type {const} */ ({\n  carriageReturn: -5,\n  lineFeed: -4,\n  carriageReturnLineFeed: -3,\n  horizontalTab: -2,\n  virtualSpace: -1,\n  eof: null,\n  nul: 0,\n  soh: 1,\n  stx: 2,\n  etx: 3,\n  eot: 4,\n  enq: 5,\n  ack: 6,\n  bel: 7,\n  bs: 8,\n  ht: 9, // `\\t`\n  lf: 10, // `\\n`\n  vt: 11, // `\\v`\n  ff: 12, // `\\f`\n  cr: 13, // `\\r`\n  so: 14,\n  si: 15,\n  dle: 16,\n  dc1: 17,\n  dc2: 18,\n  dc3: 19,\n  dc4: 20,\n  nak: 21,\n  syn: 22,\n  etb: 23,\n  can: 24,\n  em: 25,\n  sub: 26,\n  esc: 27,\n  fs: 28,\n  gs: 29,\n  rs: 30,\n  us: 31,\n  space: 32,\n  exclamationMark: 33, // `!`\n  quotationMark: 34, // `\"`\n  numberSign: 35, // `#`\n  dollarSign: 36, // `$`\n  percentSign: 37, // `%`\n  ampersand: 38, // `&`\n  apostrophe: 39, // `'`\n  leftParenthesis: 40, // `(`\n  rightParenthesis: 41, // `)`\n  asterisk: 42, // `*`\n  plusSign: 43, // `+`\n  comma: 44, // `,`\n  dash: 45, // `-`\n  dot: 46, // `.`\n  slash: 47, // `/`\n  digit0: 48, // `0`\n  digit1: 49, // `1`\n  digit2: 50, // `2`\n  digit3: 51, // `3`\n  digit4: 52, // `4`\n  digit5: 53, // `5`\n  digit6: 54, // `6`\n  digit7: 55, // `7`\n  digit8: 56, // `8`\n  digit9: 57, // `9`\n  colon: 58, // `:`\n  semicolon: 59, // `;`\n  lessThan: 60, // `<`\n  equalsTo: 61, // `=`\n  greaterThan: 62, // `>`\n  questionMark: 63, // `?`\n  atSign: 64, // `@`\n  uppercaseA: 65, // `A`\n  uppercaseB: 66, // `B`\n  uppercaseC: 67, // `C`\n  uppercaseD: 68, // `D`\n  uppercaseE: 69, // `E`\n  uppercaseF: 70, // `F`\n  uppercaseG: 71, // `G`\n  uppercaseH: 72, // `H`\n  uppercaseI: 73, // `I`\n  uppercaseJ: 74, // `J`\n  uppercaseK: 75, // `K`\n  uppercaseL: 76, // `L`\n  uppercaseM: 77, // `M`\n  uppercaseN: 78, // `N`\n  uppercaseO: 79, // `O`\n  uppercaseP: 80, // `P`\n  uppercaseQ: 81, // `Q`\n  uppercaseR: 82, // `R`\n  uppercaseS: 83, // `S`\n  uppercaseT: 84, // `T`\n  uppercaseU: 85, // `U`\n  uppercaseV: 86, // `V`\n  uppercaseW: 87, // `W`\n  uppercaseX: 88, // `X`\n  uppercaseY: 89, // `Y`\n  uppercaseZ: 90, // `Z`\n  leftSquareBracket: 91, // `[`\n  backslash: 92, // `\\`\n  rightSquareBracket: 93, // `]`\n  caret: 94, // `^`\n  underscore: 95, // `_`\n  graveAccent: 96, // `` ` ``\n  lowercaseA: 97, // `a`\n  lowercaseB: 98, // `b`\n  lowercaseC: 99, // `c`\n  lowercaseD: 100, // `d`\n  lowercaseE: 101, // `e`\n  lowercaseF: 102, // `f`\n  lowercaseG: 103, // `g`\n  lowercaseH: 104, // `h`\n  lowercaseI: 105, // `i`\n  lowercaseJ: 106, // `j`\n  lowercaseK: 107, // `k`\n  lowercaseL: 108, // `l`\n  lowercaseM: 109, // `m`\n  lowercaseN: 110, // `n`\n  lowercaseO: 111, // `o`\n  lowercaseP: 112, // `p`\n  lowercaseQ: 113, // `q`\n  lowercaseR: 114, // `r`\n  lowercaseS: 115, // `s`\n  lowercaseT: 116, // `t`\n  lowercaseU: 117, // `u`\n  lowercaseV: 118, // `v`\n  lowercaseW: 119, // `w`\n  lowercaseX: 120, // `x`\n  lowercaseY: 121, // `y`\n  lowercaseZ: 122, // `z`\n  leftCurlyBrace: 123, // `{`\n  verticalBar: 124, // `|`\n  rightCurlyBrace: 125, // `}`\n  tilde: 126, // `~`\n  del: 127,\n  // Unicode Specials block.\n  byteOrderMarker: 65_279,\n  // Unicode Specials block.\n  replacementCharacter: 65_533 // `�`\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/codes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/constants.js":
/*!*************************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/constants.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   constants: () => (/* binding */ constants)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Parsing markdown comes with a couple of constants, such as minimum or maximum\n * sizes of certain sequences.\n * Additionally, there are a couple symbols used inside micromark.\n * These are all defined here, but compiled away by scripts.\n */\nconst constants = /** @type {const} */ ({\n  attentionSideAfter: 2, // Symbol to mark an attention sequence as after content: `a*`\n  attentionSideBefore: 1, // Symbol to mark an attention sequence as before content: `*a`\n  atxHeadingOpeningFenceSizeMax: 6, // 6 number signs is fine, 7 isn’t.\n  autolinkDomainSizeMax: 63, // 63 characters is fine, 64 is too many.\n  autolinkSchemeSizeMax: 32, // 32 characters is fine, 33 is too many.\n  cdataOpeningString: 'CDATA[', // And preceded by `<![`.\n  characterGroupPunctuation: 2, // Symbol used to indicate a character is punctuation\n  characterGroupWhitespace: 1, // Symbol used to indicate a character is whitespace\n  characterReferenceDecimalSizeMax: 7, // `&#9999999;`.\n  characterReferenceHexadecimalSizeMax: 6, // `&#xff9999;`.\n  characterReferenceNamedSizeMax: 31, // `&CounterClockwiseContourIntegral;`.\n  codeFencedSequenceSizeMin: 3, // At least 3 ticks or tildes are needed.\n  contentTypeContent: 'content',\n  contentTypeDocument: 'document',\n  contentTypeFlow: 'flow',\n  contentTypeString: 'string',\n  contentTypeText: 'text',\n  hardBreakPrefixSizeMin: 2, // At least 2 trailing spaces are needed.\n  htmlBasic: 6, // Symbol for `<div`\n  htmlCdata: 5, // Symbol for `<![CDATA[]]>`\n  htmlComment: 2, // Symbol for `<!---->`\n  htmlComplete: 7, // Symbol for `<x>`\n  htmlDeclaration: 4, // Symbol for `<!doctype>`\n  htmlInstruction: 3, // Symbol for `<?php?>`\n  htmlRawSizeMax: 8, // Length of `textarea`.\n  htmlRaw: 1, // Symbol for `<script>`\n  linkResourceDestinationBalanceMax: 32, // See: <https://spec.commonmark.org/0.30/#link-destination>, <https://github.com/remarkjs/react-markdown/issues/658#issuecomment-984345577>\n  linkReferenceSizeMax: 999, // See: <https://spec.commonmark.org/0.30/#link-label>\n  listItemValueSizeMax: 10, // See: <https://spec.commonmark.org/0.30/#ordered-list-marker>\n  numericBaseDecimal: 10,\n  numericBaseHexadecimal: 0x10,\n  tabSize: 4, // Tabs have a hard-coded size of 4, per CommonMark.\n  thematicBreakMarkerCountMin: 3, // At least 3 asterisks, dashes, or underscores are needed.\n  v8MaxSafeChunkSize: 10_000 // V8 (and potentially others) have problems injecting giant arrays into other arrays, hence we operate in chunks.\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/constants.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/types.js":
/*!*********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/types.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   types: () => (/* binding */ types)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * Here is the list of all types of tokens exposed by micromark, with a short\n * explanation of what they include and where they are found.\n * In picking names, generally, the rule is to be as explicit as possible\n * instead of reusing names.\n * For example, there is a `definitionDestination` and a `resourceDestination`,\n * instead of one shared name.\n */\n\n// Note: when changing the next record, you must also change `TokenTypeMap`\n// in `micromark-util-types/index.d.ts`.\nconst types = /** @type {const} */ ({\n  // Generic type for data, such as in a title, a destination, etc.\n  data: 'data',\n\n  // Generic type for syntactic whitespace (tabs, virtual spaces, spaces).\n  // Such as, between a fenced code fence and an info string.\n  whitespace: 'whitespace',\n\n  // Generic type for line endings (line feed, carriage return, carriage return +\n  // line feed).\n  lineEnding: 'lineEnding',\n\n  // A line ending, but ending a blank line.\n  lineEndingBlank: 'lineEndingBlank',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the start of a\n  // line.\n  linePrefix: 'linePrefix',\n\n  // Generic type for whitespace (tabs, virtual spaces, spaces) at the end of a\n  // line.\n  lineSuffix: 'lineSuffix',\n\n  // Whole ATX heading:\n  //\n  // ```markdown\n  // #\n  // ## Alpha\n  // ### Bravo ###\n  // ```\n  //\n  // Includes `atxHeadingSequence`, `whitespace`, `atxHeadingText`.\n  atxHeading: 'atxHeading',\n\n  // Sequence of number signs in an ATX heading (`###`).\n  atxHeadingSequence: 'atxHeadingSequence',\n\n  // Content in an ATX heading (`alpha`).\n  // Includes text.\n  atxHeadingText: 'atxHeadingText',\n\n  // Whole autolink (`<https://example.com>` or `<<EMAIL>>`)\n  // Includes `autolinkMarker` and `autolinkProtocol` or `autolinkEmail`.\n  autolink: 'autolink',\n\n  // Email autolink w/o markers (`<EMAIL>`)\n  autolinkEmail: 'autolinkEmail',\n\n  // Marker around an `autolinkProtocol` or `autolinkEmail` (`<` or `>`).\n  autolinkMarker: 'autolinkMarker',\n\n  // Protocol autolink w/o markers (`https://example.com`)\n  autolinkProtocol: 'autolinkProtocol',\n\n  // A whole character escape (`\\-`).\n  // Includes `escapeMarker` and `characterEscapeValue`.\n  characterEscape: 'characterEscape',\n\n  // The escaped character (`-`).\n  characterEscapeValue: 'characterEscapeValue',\n\n  // A whole character reference (`&amp;`, `&#8800;`, or `&#x1D306;`).\n  // Includes `characterReferenceMarker`, an optional\n  // `characterReferenceMarkerNumeric`, in which case an optional\n  // `characterReferenceMarkerHexadecimal`, and a `characterReferenceValue`.\n  characterReference: 'characterReference',\n\n  // The start or end marker (`&` or `;`).\n  characterReferenceMarker: 'characterReferenceMarker',\n\n  // Mark reference as numeric (`#`).\n  characterReferenceMarkerNumeric: 'characterReferenceMarkerNumeric',\n\n  // Mark reference as numeric (`x` or `X`).\n  characterReferenceMarkerHexadecimal: 'characterReferenceMarkerHexadecimal',\n\n  // Value of character reference w/o markers (`amp`, `8800`, or `1D306`).\n  characterReferenceValue: 'characterReferenceValue',\n\n  // Whole fenced code:\n  //\n  // ````markdown\n  // ```js\n  // alert(1)\n  // ```\n  // ````\n  codeFenced: 'codeFenced',\n\n  // A fenced code fence, including whitespace, sequence, info, and meta\n  // (` ```js `).\n  codeFencedFence: 'codeFencedFence',\n\n  // Sequence of grave accent or tilde characters (` ``` `) in a fence.\n  codeFencedFenceSequence: 'codeFencedFenceSequence',\n\n  // Info word (`js`) in a fence.\n  // Includes string.\n  codeFencedFenceInfo: 'codeFencedFenceInfo',\n\n  // Meta words (`highlight=\"1\"`) in a fence.\n  // Includes string.\n  codeFencedFenceMeta: 'codeFencedFenceMeta',\n\n  // A line of code.\n  codeFlowValue: 'codeFlowValue',\n\n  // Whole indented code:\n  //\n  // ```markdown\n  //     alert(1)\n  // ```\n  //\n  // Includes `lineEnding`, `linePrefix`, and `codeFlowValue`.\n  codeIndented: 'codeIndented',\n\n  // A text code (``` `alpha` ```).\n  // Includes `codeTextSequence`, `codeTextData`, `lineEnding`, and can include\n  // `codeTextPadding`.\n  codeText: 'codeText',\n\n  codeTextData: 'codeTextData',\n\n  // A space or line ending right after or before a tick.\n  codeTextPadding: 'codeTextPadding',\n\n  // A text code fence (` `` `).\n  codeTextSequence: 'codeTextSequence',\n\n  // Whole content:\n  //\n  // ```markdown\n  // [a]: b\n  // c\n  // =\n  // d\n  // ```\n  //\n  // Includes `paragraph` and `definition`.\n  content: 'content',\n  // Whole definition:\n  //\n  // ```markdown\n  // [micromark]: https://github.com/micromark/micromark\n  // ```\n  //\n  // Includes `definitionLabel`, `definitionMarker`, `whitespace`,\n  // `definitionDestination`, and optionally `lineEnding` and `definitionTitle`.\n  definition: 'definition',\n\n  // Destination of a definition (`https://github.com/micromark/micromark` or\n  // `<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteral` or `definitionDestinationRaw`.\n  definitionDestination: 'definitionDestination',\n\n  // Enclosed destination of a definition\n  // (`<https://github.com/micromark/micromark>`).\n  // Includes `definitionDestinationLiteralMarker` and optionally\n  // `definitionDestinationString`.\n  definitionDestinationLiteral: 'definitionDestinationLiteral',\n\n  // Markers of an enclosed definition destination (`<` or `>`).\n  definitionDestinationLiteralMarker: 'definitionDestinationLiteralMarker',\n\n  // Unenclosed destination of a definition\n  // (`https://github.com/micromark/micromark`).\n  // Includes `definitionDestinationString`.\n  definitionDestinationRaw: 'definitionDestinationRaw',\n\n  // Text in an destination (`https://github.com/micromark/micromark`).\n  // Includes string.\n  definitionDestinationString: 'definitionDestinationString',\n\n  // Label of a definition (`[micromark]`).\n  // Includes `definitionLabelMarker` and `definitionLabelString`.\n  definitionLabel: 'definitionLabel',\n\n  // Markers of a definition label (`[` or `]`).\n  definitionLabelMarker: 'definitionLabelMarker',\n\n  // Value of a definition label (`micromark`).\n  // Includes string.\n  definitionLabelString: 'definitionLabelString',\n\n  // Marker between a label and a destination (`:`).\n  definitionMarker: 'definitionMarker',\n\n  // Title of a definition (`\"x\"`, `'y'`, or `(z)`).\n  // Includes `definitionTitleMarker` and optionally `definitionTitleString`.\n  definitionTitle: 'definitionTitle',\n\n  // Marker around a title of a definition (`\"`, `'`, `(`, or `)`).\n  definitionTitleMarker: 'definitionTitleMarker',\n\n  // Data without markers in a title (`z`).\n  // Includes string.\n  definitionTitleString: 'definitionTitleString',\n\n  // Emphasis (`*alpha*`).\n  // Includes `emphasisSequence` and `emphasisText`.\n  emphasis: 'emphasis',\n\n  // Sequence of emphasis markers (`*` or `_`).\n  emphasisSequence: 'emphasisSequence',\n\n  // Emphasis text (`alpha`).\n  // Includes text.\n  emphasisText: 'emphasisText',\n\n  // The character escape marker (`\\`).\n  escapeMarker: 'escapeMarker',\n\n  // A hard break created with a backslash (`\\\\n`).\n  // Note: does not include the line ending.\n  hardBreakEscape: 'hardBreakEscape',\n\n  // A hard break created with trailing spaces (`  \\n`).\n  // Does not include the line ending.\n  hardBreakTrailing: 'hardBreakTrailing',\n\n  // Flow HTML:\n  //\n  // ```markdown\n  // <div\n  // ```\n  //\n  // Inlcudes `lineEnding`, `htmlFlowData`.\n  htmlFlow: 'htmlFlow',\n\n  htmlFlowData: 'htmlFlowData',\n\n  // HTML in text (the tag in `a <i> b`).\n  // Includes `lineEnding`, `htmlTextData`.\n  htmlText: 'htmlText',\n\n  htmlTextData: 'htmlTextData',\n\n  // Whole image (`![alpha](bravo)`, `![alpha][bravo]`, `![alpha][]`, or\n  // `![alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  image: 'image',\n\n  // Whole link label (`[*alpha*]`).\n  // Includes `labelLink` or `labelImage`, `labelText`, and `labelEnd`.\n  label: 'label',\n\n  // Text in an label (`*alpha*`).\n  // Includes text.\n  labelText: 'labelText',\n\n  // Start a link label (`[`).\n  // Includes a `labelMarker`.\n  labelLink: 'labelLink',\n\n  // Start an image label (`![`).\n  // Includes `labelImageMarker` and `labelMarker`.\n  labelImage: 'labelImage',\n\n  // Marker of a label (`[` or `]`).\n  labelMarker: 'labelMarker',\n\n  // Marker to start an image (`!`).\n  labelImageMarker: 'labelImageMarker',\n\n  // End a label (`]`).\n  // Includes `labelMarker`.\n  labelEnd: 'labelEnd',\n\n  // Whole link (`[alpha](bravo)`, `[alpha][bravo]`, `[alpha][]`, or `[alpha]`).\n  // Includes `label` and an optional `resource` or `reference`.\n  link: 'link',\n\n  // Whole paragraph:\n  //\n  // ```markdown\n  // alpha\n  // bravo.\n  // ```\n  //\n  // Includes text.\n  paragraph: 'paragraph',\n\n  // A reference (`[alpha]` or `[]`).\n  // Includes `referenceMarker` and an optional `referenceString`.\n  reference: 'reference',\n\n  // A reference marker (`[` or `]`).\n  referenceMarker: 'referenceMarker',\n\n  // Reference text (`alpha`).\n  // Includes string.\n  referenceString: 'referenceString',\n\n  // A resource (`(https://example.com \"alpha\")`).\n  // Includes `resourceMarker`, an optional `resourceDestination` with an optional\n  // `whitespace` and `resourceTitle`.\n  resource: 'resource',\n\n  // A resource destination (`https://example.com`).\n  // Includes `resourceDestinationLiteral` or `resourceDestinationRaw`.\n  resourceDestination: 'resourceDestination',\n\n  // A literal resource destination (`<https://example.com>`).\n  // Includes `resourceDestinationLiteralMarker` and optionally\n  // `resourceDestinationString`.\n  resourceDestinationLiteral: 'resourceDestinationLiteral',\n\n  // A resource destination marker (`<` or `>`).\n  resourceDestinationLiteralMarker: 'resourceDestinationLiteralMarker',\n\n  // A raw resource destination (`https://example.com`).\n  // Includes `resourceDestinationString`.\n  resourceDestinationRaw: 'resourceDestinationRaw',\n\n  // Resource destination text (`https://example.com`).\n  // Includes string.\n  resourceDestinationString: 'resourceDestinationString',\n\n  // A resource marker (`(` or `)`).\n  resourceMarker: 'resourceMarker',\n\n  // A resource title (`\"alpha\"`, `'alpha'`, or `(alpha)`).\n  // Includes `resourceTitleMarker` and optionally `resourceTitleString`.\n  resourceTitle: 'resourceTitle',\n\n  // A resource title marker (`\"`, `'`, `(`, or `)`).\n  resourceTitleMarker: 'resourceTitleMarker',\n\n  // Resource destination title (`alpha`).\n  // Includes string.\n  resourceTitleString: 'resourceTitleString',\n\n  // Whole setext heading:\n  //\n  // ```markdown\n  // alpha\n  // bravo\n  // =====\n  // ```\n  //\n  // Includes `setextHeadingText`, `lineEnding`, `linePrefix`, and\n  // `setextHeadingLine`.\n  setextHeading: 'setextHeading',\n\n  // Content in a setext heading (`alpha\\nbravo`).\n  // Includes text.\n  setextHeadingText: 'setextHeadingText',\n\n  // Underline in a setext heading, including whitespace suffix (`==`).\n  // Includes `setextHeadingLineSequence`.\n  setextHeadingLine: 'setextHeadingLine',\n\n  // Sequence of equals or dash characters in underline in a setext heading (`-`).\n  setextHeadingLineSequence: 'setextHeadingLineSequence',\n\n  // Strong (`**alpha**`).\n  // Includes `strongSequence` and `strongText`.\n  strong: 'strong',\n\n  // Sequence of strong markers (`**` or `__`).\n  strongSequence: 'strongSequence',\n\n  // Strong text (`alpha`).\n  // Includes text.\n  strongText: 'strongText',\n\n  // Whole thematic break:\n  //\n  // ```markdown\n  // * * *\n  // ```\n  //\n  // Includes `thematicBreakSequence` and `whitespace`.\n  thematicBreak: 'thematicBreak',\n\n  // A sequence of one or more thematic break markers (`***`).\n  thematicBreakSequence: 'thematicBreakSequence',\n\n  // Whole block quote:\n  //\n  // ```markdown\n  // > a\n  // >\n  // > b\n  // ```\n  //\n  // Includes `blockQuotePrefix` and flow.\n  blockQuote: 'blockQuote',\n  // The `>` or `> ` of a block quote.\n  blockQuotePrefix: 'blockQuotePrefix',\n  // The `>` of a block quote prefix.\n  blockQuoteMarker: 'blockQuoteMarker',\n  // The optional ` ` of a block quote prefix.\n  blockQuotePrefixWhitespace: 'blockQuotePrefixWhitespace',\n\n  // Whole ordered list:\n  //\n  // ```markdown\n  // 1. a\n  //    b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listOrdered: 'listOrdered',\n\n  // Whole unordered list:\n  //\n  // ```markdown\n  // - a\n  //   b\n  // ```\n  //\n  // Includes `listItemPrefix`, flow, and optionally  `listItemIndent` on further\n  // lines.\n  listUnordered: 'listUnordered',\n\n  // The indent of further list item lines.\n  listItemIndent: 'listItemIndent',\n\n  // A marker, as in, `*`, `+`, `-`, `.`, or `)`.\n  listItemMarker: 'listItemMarker',\n\n  // The thing that starts a list item, such as `1. `.\n  // Includes `listItemValue` if ordered, `listItemMarker`, and\n  // `listItemPrefixWhitespace` (unless followed by a line ending).\n  listItemPrefix: 'listItemPrefix',\n\n  // The whitespace after a marker.\n  listItemPrefixWhitespace: 'listItemPrefixWhitespace',\n\n  // The numerical value of an ordered item.\n  listItemValue: 'listItemValue',\n\n  // Internal types used for subtokenizers, compiled away\n  chunkDocument: 'chunkDocument',\n  chunkContent: 'chunkContent',\n  chunkFlow: 'chunkFlow',\n  chunkText: 'chunkText',\n  chunkString: 'chunkString'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/types.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/micromark-util-symbol/lib/values.js":
/*!**********************************************************!*\
  !*** ./node_modules/micromark-util-symbol/lib/values.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   values: () => (/* binding */ values)\n/* harmony export */ });\n/**\n * This module is compiled away!\n *\n * While micromark works based on character codes, this module includes the\n * string versions of ’em.\n * The C0 block, except for LF, CR, HT, and w/ the replacement character added,\n * are available here.\n */\nconst values = /** @type {const} */ ({\n  ht: '\\t',\n  lf: '\\n',\n  cr: '\\r',\n  space: ' ',\n  exclamationMark: '!',\n  quotationMark: '\"',\n  numberSign: '#',\n  dollarSign: '$',\n  percentSign: '%',\n  ampersand: '&',\n  apostrophe: \"'\",\n  leftParenthesis: '(',\n  rightParenthesis: ')',\n  asterisk: '*',\n  plusSign: '+',\n  comma: ',',\n  dash: '-',\n  dot: '.',\n  slash: '/',\n  digit0: '0',\n  digit1: '1',\n  digit2: '2',\n  digit3: '3',\n  digit4: '4',\n  digit5: '5',\n  digit6: '6',\n  digit7: '7',\n  digit8: '8',\n  digit9: '9',\n  colon: ':',\n  semicolon: ';',\n  lessThan: '<',\n  equalsTo: '=',\n  greaterThan: '>',\n  questionMark: '?',\n  atSign: '@',\n  uppercaseA: 'A',\n  uppercaseB: 'B',\n  uppercaseC: 'C',\n  uppercaseD: 'D',\n  uppercaseE: 'E',\n  uppercaseF: 'F',\n  uppercaseG: 'G',\n  uppercaseH: 'H',\n  uppercaseI: 'I',\n  uppercaseJ: 'J',\n  uppercaseK: 'K',\n  uppercaseL: 'L',\n  uppercaseM: 'M',\n  uppercaseN: 'N',\n  uppercaseO: 'O',\n  uppercaseP: 'P',\n  uppercaseQ: 'Q',\n  uppercaseR: 'R',\n  uppercaseS: 'S',\n  uppercaseT: 'T',\n  uppercaseU: 'U',\n  uppercaseV: 'V',\n  uppercaseW: 'W',\n  uppercaseX: 'X',\n  uppercaseY: 'Y',\n  uppercaseZ: 'Z',\n  leftSquareBracket: '[',\n  backslash: '\\\\',\n  rightSquareBracket: ']',\n  caret: '^',\n  underscore: '_',\n  graveAccent: '`',\n  lowercaseA: 'a',\n  lowercaseB: 'b',\n  lowercaseC: 'c',\n  lowercaseD: 'd',\n  lowercaseE: 'e',\n  lowercaseF: 'f',\n  lowercaseG: 'g',\n  lowercaseH: 'h',\n  lowercaseI: 'i',\n  lowercaseJ: 'j',\n  lowercaseK: 'k',\n  lowercaseL: 'l',\n  lowercaseM: 'm',\n  lowercaseN: 'n',\n  lowercaseO: 'o',\n  lowercaseP: 'p',\n  lowercaseQ: 'q',\n  lowercaseR: 'r',\n  lowercaseS: 's',\n  lowercaseT: 't',\n  lowercaseU: 'u',\n  lowercaseV: 'v',\n  lowercaseW: 'w',\n  lowercaseX: 'x',\n  lowercaseY: 'y',\n  lowercaseZ: 'z',\n  leftCurlyBrace: '{',\n  verticalBar: '|',\n  rightCurlyBrace: '}',\n  tilde: '~',\n  replacementCharacter: '�'\n})\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/micromark-util-symbol/lib/values.js\n");

/***/ })

};
;