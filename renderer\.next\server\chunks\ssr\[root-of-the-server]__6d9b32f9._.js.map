{"version": 3, "sources": ["turbopack:///[next]/internal/font/google/geist_a71539c9.module.css [app-rsc] (css module)", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.module.css [app-rsc] (css module)", "turbopack:///[project]/renderer/src/components/theme-provider.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/renderer/src/contexts/i18n.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[project]/renderer/src/components/ui/toaster.tsx/__nextjs-internal-proxy.mjs", "turbopack:///[next]/internal/font/google/geist_a71539c9.js", "turbopack:///[next]/internal/font/google/geist_mono_8d43a2aa.js", "turbopack:///[project]/renderer/src/app/layout.tsx"], "sourcesContent": ["__turbopack_context__.v({\n  \"className\": \"geist_a71539c9-module__T19VSG__className\",\n  \"variable\": \"geist_a71539c9-module__T19VSG__variable\",\n});\n", "__turbopack_context__.v({\n  \"className\": \"geist_mono_8d43a2aa-module__8Li5zG__className\",\n  \"variable\": \"geist_mono_8d43a2aa-module__8Li5zG__variable\",\n});\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const ThemeProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/renderer/src/components/theme-provider.tsx\",\n    \"ThemeProvider\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const I18nProvider = registerClientReference(\n    function() { throw new Error(\"Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/renderer/src/contexts/i18n.tsx\",\n    \"I18nProvider\",\n);\nexport const useI18n = registerClientReference(\n    function() { throw new Error(\"Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/renderer/src/contexts/i18n.tsx\",\n    \"useI18n\",\n);\n", "// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Toaster = registerClientReference(\n    function() { throw new Error(\"Attempted to call Toaster() from the server but To<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/renderer/src/components/ui/toaster.tsx\",\n    \"Toaster\",\n);\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist%22,%22arguments%22:[{%22variable%22:%22--font-geist-sans%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistSans%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist', 'Geist Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import cssModule from \"@vercel/turbopack-next/internal/font/google/cssmodule.module.css?{%22path%22:%22layout.tsx%22,%22import%22:%22Geist_Mono%22,%22arguments%22:[{%22variable%22:%22--font-geist-mono%22,%22subsets%22:[%22latin%22]}],%22variableName%22:%22geistMono%22}\";\nconst fontData = {\n    className: cssModule.className,\n    style: {\n        fontFamily: \"'Geist Mono', 'Geist Mono Fallback'\",\n        fontStyle: \"normal\",\n\n    },\n};\n\nif (cssModule.variable != null) {\n    fontData.variable = cssModule.variable;\n}\n\nexport default fontData;\n", "import type { <PERSON>ada<PERSON> } from \"next\";\nimport { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from \"next/font/google\";\nimport \"./globals.css\";\nimport { ThemeProvider } from \"@/components/theme-provider\";\nimport { I18nProvider } from \"@/contexts/i18n\";\nimport { Toaster } from \"@/components/ui/toaster\";\n\nconst geistSans = Geist({\n  variable: \"--font-geist-sans\",\n  subsets: [\"latin\"],\n});\n\nconst geistMono = Geist_Mono({\n  variable: \"--font-geist-mono\",\n  subsets: [\"latin\"],\n});\n\nexport const metadata: Metadata = {\n  title: \"MyNote\",\n  description: \"A simple note-taking app\",\n};\n\nexport default function RootLayout({\n  children,\n}: Readonly<{\n  children: React.ReactNode;\n}>) {\n  return (\n    <html lang=\"en\" suppressHydrationWarning>\n      <body\n        className={`${geistSans.variable} ${geistMono.variable} min-h-screen bg-background font-sans antialiased`}\n      >\n        <ThemeProvider\n          attribute=\"class\"\n          defaultTheme=\"system\"\n          enableSystem\n          disableTransitionOnChange\n        >\n          <I18nProvider>{children}</I18nProvider>\n          <Toaster />\n        </ThemeProvider>\n      </body>\n    </html>\n  );\n}\n"], "names": [], "mappings": "0BAAA,EAAA,CAAA,CAAA,CACA,UAAA,2CACA,SAAA,yCACA,cCHA,EAAA,CAAA,CAAA,CACA,UAAA,gDACA,SAAA,8CACA,uDCDO,IAAM,EAAgB,CAAA,EAD7B,AAC6B,EAD7B,CAAA,CAAA,OAC6B,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,2EACA,qEAHG,IAAM,EAAgB,CAAA,EAD7B,AAC6B,EAD7B,CAAA,CAAA,OAC6B,uBAAA,AAAuB,EAChD,WAAa,MAAM,AAAI,MAAM,wOAA0O,EACvQ,uDACA,8ICJJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAe,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC/C,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,+DACA,gBAES,EAAU,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,+DACA,+EATJ,IAAA,EAAA,EAAA,CAAA,CAAA,OACO,IAAM,EAAe,CAAA,EAAA,EAAA,uBAAuB,AAAvB,EACxB,WAAa,MAAM,AAAI,MAAM,sOAAwO,EACrQ,2CACA,gBAES,EAAU,CAAA,EAAA,EAAA,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,2CACA,qHCRG,IAAM,EAAU,CAAA,EADvB,AACuB,EADvB,CAAA,CAAA,OACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,uEACA,0DAHG,IAAM,EAAU,CAAA,EAAA,AADvB,EAAA,CAAA,CAAA,OACuB,uBAAA,AAAuB,EAC1C,WAAa,MAAM,AAAI,MAAM,4NAA8N,EAC3P,mDACA,6JCLJ,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,4BACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAA,AAAQ,ECX1C,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,IAAM,EAAW,CACb,UAAW,EAAA,OAAS,CAAC,SAAS,CAC9B,MAAO,CACH,WAAY,sCACZ,UAAW,QAEf,CACJ,CAE0B,MAAM,CAA5B,EAAA,OAAS,CAAC,QAAQ,GAClB,EAAS,QAAQ,CAAG,EAAA,OAAS,CAAC,QAAQ,AAAR,ECRlC,IAAA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAYO,IAAM,EAAqB,CAChC,MAAO,SACP,YAAa,0BACf,EAEe,SAAS,EAAW,UACjC,CAAQ,CAGR,EACA,MACE,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CAAK,KAAK,KAAK,wBAAwB,CAAA,CAAA,WACtC,CAAA,EAAA,EAAA,GAAA,EAAC,OAAA,CACC,UAAW,CAAA,EFhBJ,AEgBO,EAAU,QAAQ,CAAC,CAAC,EAAE,ADhB7B,ECgBuC,QAAQ,CAAC,iDAAiD,CAAC,UAEzG,CAAA,EAAA,EAAA,IAAA,EAAC,EAAA,aAAa,CAAA,CACZ,UAAU,QACV,aAAa,SACb,YAAY,CAAA,CAAA,EACZ,yBAAyB,CAAA,CAAA,YAEzB,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,YAAY,CAAA,UAAE,IACf,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,OAAO,CAAA,CAAA,SAKlB", "ignoreList": [0, 1, 2, 3, 4, 5, 6]}