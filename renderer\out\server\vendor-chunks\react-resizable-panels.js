"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/react-resizable-panels";
exports.ids = ["vendor-chunks/react-resizable-panels"];
exports.modules = {

/***/ "(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.edge-light.js":
/*!***************************************************************************************************!*\
  !*** ./node_modules/react-resizable-panels/dist/react-resizable-panels.development.edge-light.js ***!
  \***************************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("var react__WEBPACK_IMPORTED_MODULE_0___namespace_cache;\n__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   DATA_ATTRIBUTES: () => (/* binding */ DATA_ATTRIBUTES),\n/* harmony export */   Panel: () => (/* binding */ Panel),\n/* harmony export */   PanelGroup: () => (/* binding */ PanelGroup),\n/* harmony export */   PanelResizeHandle: () => (/* binding */ PanelResizeHandle),\n/* harmony export */   assert: () => (/* binding */ assert),\n/* harmony export */   customizeGlobalCursorStyles: () => (/* binding */ customizeGlobalCursorStyles),\n/* harmony export */   disableGlobalCursorStyles: () => (/* binding */ disableGlobalCursorStyles),\n/* harmony export */   enableGlobalCursorStyles: () => (/* binding */ enableGlobalCursorStyles),\n/* harmony export */   getIntersectingRectangle: () => (/* binding */ getIntersectingRectangle),\n/* harmony export */   getPanelElement: () => (/* binding */ getPanelElement),\n/* harmony export */   getPanelElementsForGroup: () => (/* binding */ getPanelElementsForGroup),\n/* harmony export */   getPanelGroupElement: () => (/* binding */ getPanelGroupElement),\n/* harmony export */   getResizeHandleElement: () => (/* binding */ getResizeHandleElement),\n/* harmony export */   getResizeHandleElementIndex: () => (/* binding */ getResizeHandleElementIndex),\n/* harmony export */   getResizeHandleElementsForGroup: () => (/* binding */ getResizeHandleElementsForGroup),\n/* harmony export */   getResizeHandlePanelIds: () => (/* binding */ getResizeHandlePanelIds),\n/* harmony export */   intersects: () => (/* binding */ intersects),\n/* harmony export */   setNonce: () => (/* binding */ setNonce),\n/* harmony export */   usePanelGroupContext: () => (/* binding */ usePanelGroupContext)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\n\n\n// The \"contextmenu\" event is not supported as a PointerEvent in all browsers yet, so MouseEvent still need to be handled\n\nconst PanelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.createContext)(null);\nPanelGroupContext.displayName = \"PanelGroupContext\";\n\nconst DATA_ATTRIBUTES = {\n  group: \"data-panel-group\",\n  groupDirection: \"data-panel-group-direction\",\n  groupId: \"data-panel-group-id\",\n  panel: \"data-panel\",\n  panelCollapsible: \"data-panel-collapsible\",\n  panelId: \"data-panel-id\",\n  panelSize: \"data-panel-size\",\n  resizeHandle: \"data-resize-handle\",\n  resizeHandleActive: \"data-resize-handle-active\",\n  resizeHandleEnabled: \"data-panel-resize-handle-enabled\",\n  resizeHandleId: \"data-panel-resize-handle-id\",\n  resizeHandleState: \"data-resize-handle-state\"\n};\nconst PRECISION = 10;\n\nconst useId = /*#__PURE__*/ (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache || (react__WEBPACK_IMPORTED_MODULE_0___namespace_cache = __webpack_require__.t(react__WEBPACK_IMPORTED_MODULE_0__, 2)))[\"useId\".toString()];\nconst wrappedUseId = typeof useId === \"function\" ? useId : () => null;\nlet counter = 0;\nfunction useUniqueId(idFromParams = null) {\n  const idFromUseId = wrappedUseId();\n  const idRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(idFromParams || idFromUseId || null);\n  if (idRef.current === null) {\n    idRef.current = \"\" + counter++;\n  }\n  return idFromParams !== null && idFromParams !== void 0 ? idFromParams : idRef.current;\n}\n\nfunction PanelWithForwardedRef({\n  children,\n  className: classNameFromProps = \"\",\n  collapsedSize,\n  collapsible,\n  defaultSize,\n  forwardedRef,\n  id: idFromProps,\n  maxSize,\n  minSize,\n  onCollapse,\n  onExpand,\n  onResize,\n  order,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (context === null) {\n    throw Error(`Panel components must be rendered within a PanelGroup container`);\n  }\n  const {\n    collapsePanel,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    reevaluatePanelConstraints,\n    registerPanel,\n    resizePanel,\n    unregisterPanel\n  } = context;\n  const panelId = useUniqueId(idFromProps);\n  const panelDataRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    callbacks: {\n      onCollapse,\n      onExpand,\n      onResize\n    },\n    constraints: {\n      collapsedSize,\n      collapsible,\n      defaultSize,\n      maxSize,\n      minSize\n    },\n    id: panelId,\n    idIsFromProps: idFromProps !== undefined,\n    order\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogMissingDefaultSizeWarning: false\n  });\n\n  // Normally we wouldn't log a warning during render,\n  // but effects don't run on the server, so we can't do it there\n  {\n    if (!devWarningsRef.current.didLogMissingDefaultSizeWarning) {\n      if (defaultSize == null) {\n        devWarningsRef.current.didLogMissingDefaultSizeWarning = true;\n        console.warn(`WARNING: Panel defaultSize prop recommended to avoid layout shift after server rendering`);\n      }\n    }\n  }\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    collapse: () => {\n      collapsePanel(panelDataRef.current);\n    },\n    expand: minSize => {\n      expandPanel(panelDataRef.current, minSize);\n    },\n    getId() {\n      return panelId;\n    },\n    getSize() {\n      return getPanelSize(panelDataRef.current);\n    },\n    isCollapsed() {\n      return isPanelCollapsed(panelDataRef.current);\n    },\n    isExpanded() {\n      return !isPanelCollapsed(panelDataRef.current);\n    },\n    resize: size => {\n      resizePanel(panelDataRef.current, size);\n    }\n  }), [collapsePanel, expandPanel, getPanelSize, isPanelCollapsed, panelId, resizePanel]);\n  const style = getPanelStyle(panelDataRef.current, defaultSize);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: panelId,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.panel]: \"\",\n    [DATA_ATTRIBUTES.panelCollapsible]: collapsible || undefined,\n    [DATA_ATTRIBUTES.panelId]: panelId,\n    [DATA_ATTRIBUTES.panelSize]: parseFloat(\"\" + style.flexGrow).toFixed(1)\n  });\n}\nconst Panel = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelWithForwardedRef.displayName = \"Panel\";\nPanel.displayName = \"forwardRef(Panel)\";\n\nlet nonce;\nfunction getNonce() {\n  return nonce;\n}\nfunction setNonce(value) {\n  nonce = value;\n}\n\nlet currentCursorStyle = null;\nlet enabled = true;\nlet getCustomCursorStyleFunction = null;\nlet prevRuleIndex = -1;\nlet styleElement = null;\nfunction customizeGlobalCursorStyles(callback) {\n  getCustomCursorStyleFunction = callback;\n}\nfunction disableGlobalCursorStyles() {\n  enabled = false;\n}\nfunction enableGlobalCursorStyles() {\n  enabled = true;\n}\nfunction getCursorStyle(state, constraintFlags, isPointerDown) {\n  const horizontalMin = (constraintFlags & EXCEEDED_HORIZONTAL_MIN) !== 0;\n  const horizontalMax = (constraintFlags & EXCEEDED_HORIZONTAL_MAX) !== 0;\n  const verticalMin = (constraintFlags & EXCEEDED_VERTICAL_MIN) !== 0;\n  const verticalMax = (constraintFlags & EXCEEDED_VERTICAL_MAX) !== 0;\n  if (getCustomCursorStyleFunction) {\n    return getCustomCursorStyleFunction({\n      exceedsHorizontalMaximum: horizontalMax,\n      exceedsHorizontalMinimum: horizontalMin,\n      exceedsVerticalMaximum: verticalMax,\n      exceedsVerticalMinimum: verticalMin,\n      intersectsHorizontalDragHandle: state === \"horizontal\" || state === \"intersection\",\n      intersectsVerticalDragHandle: state === \"vertical\" || state === \"intersection\",\n      isPointerDown\n    });\n  }\n  if (constraintFlags) {\n    if (horizontalMin) {\n      if (verticalMin) {\n        return \"se-resize\";\n      } else if (verticalMax) {\n        return \"ne-resize\";\n      } else {\n        return \"e-resize\";\n      }\n    } else if (horizontalMax) {\n      if (verticalMin) {\n        return \"sw-resize\";\n      } else if (verticalMax) {\n        return \"nw-resize\";\n      } else {\n        return \"w-resize\";\n      }\n    } else if (verticalMin) {\n      return \"s-resize\";\n    } else if (verticalMax) {\n      return \"n-resize\";\n    }\n  }\n  switch (state) {\n    case \"horizontal\":\n      return \"ew-resize\";\n    case \"intersection\":\n      return \"move\";\n    case \"vertical\":\n      return \"ns-resize\";\n  }\n}\nfunction resetGlobalCursorStyle() {\n  if (styleElement !== null) {\n    document.head.removeChild(styleElement);\n    currentCursorStyle = null;\n    styleElement = null;\n    prevRuleIndex = -1;\n  }\n}\nfunction setGlobalCursorStyle(state, constraintFlags, isPointerDown) {\n  var _styleElement$sheet$i, _styleElement$sheet2;\n  if (!enabled) {\n    return;\n  }\n  const style = getCursorStyle(state, constraintFlags, isPointerDown);\n  if (currentCursorStyle === style) {\n    return;\n  }\n  currentCursorStyle = style;\n  if (styleElement === null) {\n    styleElement = document.createElement(\"style\");\n    const nonce = getNonce();\n    if (nonce) {\n      styleElement.setAttribute(\"nonce\", nonce);\n    }\n    document.head.appendChild(styleElement);\n  }\n  if (prevRuleIndex >= 0) {\n    var _styleElement$sheet;\n    (_styleElement$sheet = styleElement.sheet) === null || _styleElement$sheet === void 0 ? void 0 : _styleElement$sheet.removeRule(prevRuleIndex);\n  }\n  prevRuleIndex = (_styleElement$sheet$i = (_styleElement$sheet2 = styleElement.sheet) === null || _styleElement$sheet2 === void 0 ? void 0 : _styleElement$sheet2.insertRule(`*{cursor: ${style} !important;}`)) !== null && _styleElement$sheet$i !== void 0 ? _styleElement$sheet$i : -1;\n}\n\nfunction isKeyDown(event) {\n  return event.type === \"keydown\";\n}\nfunction isPointerEvent(event) {\n  return event.type.startsWith(\"pointer\");\n}\nfunction isMouseEvent(event) {\n  return event.type.startsWith(\"mouse\");\n}\n\nfunction getResizeEventCoordinates(event) {\n  if (isPointerEvent(event)) {\n    if (event.isPrimary) {\n      return {\n        x: event.clientX,\n        y: event.clientY\n      };\n    }\n  } else if (isMouseEvent(event)) {\n    return {\n      x: event.clientX,\n      y: event.clientY\n    };\n  }\n  return {\n    x: Infinity,\n    y: Infinity\n  };\n}\n\nfunction getInputType() {\n  if (typeof matchMedia === \"function\") {\n    return matchMedia(\"(pointer:coarse)\").matches ? \"coarse\" : \"fine\";\n  }\n}\n\nfunction intersects(rectOne, rectTwo, strict) {\n  if (strict) {\n    return rectOne.x < rectTwo.x + rectTwo.width && rectOne.x + rectOne.width > rectTwo.x && rectOne.y < rectTwo.y + rectTwo.height && rectOne.y + rectOne.height > rectTwo.y;\n  } else {\n    return rectOne.x <= rectTwo.x + rectTwo.width && rectOne.x + rectOne.width >= rectTwo.x && rectOne.y <= rectTwo.y + rectTwo.height && rectOne.y + rectOne.height >= rectTwo.y;\n  }\n}\n\n// Forked from NPM stacking-order@2.0.0\n\n/**\n * Determine which of two nodes appears in front of the other —\n * if `a` is in front, returns 1, otherwise returns -1\n * @param {HTMLElement | SVGElement} a\n * @param {HTMLElement | SVGElement} b\n */\nfunction compare(a, b) {\n  if (a === b) throw new Error(\"Cannot compare node with itself\");\n  const ancestors = {\n    a: get_ancestors(a),\n    b: get_ancestors(b)\n  };\n  let common_ancestor;\n\n  // remove shared ancestors\n  while (ancestors.a.at(-1) === ancestors.b.at(-1)) {\n    a = ancestors.a.pop();\n    b = ancestors.b.pop();\n    common_ancestor = a;\n  }\n  assert(common_ancestor, \"Stacking order can only be calculated for elements with a common ancestor\");\n  const z_indexes = {\n    a: get_z_index(find_stacking_context(ancestors.a)),\n    b: get_z_index(find_stacking_context(ancestors.b))\n  };\n  if (z_indexes.a === z_indexes.b) {\n    const children = common_ancestor.childNodes;\n    const furthest_ancestors = {\n      a: ancestors.a.at(-1),\n      b: ancestors.b.at(-1)\n    };\n    let i = children.length;\n    while (i--) {\n      const child = children[i];\n      if (child === furthest_ancestors.a) return 1;\n      if (child === furthest_ancestors.b) return -1;\n    }\n  }\n  return Math.sign(z_indexes.a - z_indexes.b);\n}\nconst props = /\\b(?:position|zIndex|opacity|transform|webkitTransform|mixBlendMode|filter|webkitFilter|isolation)\\b/;\n\n/** @param {HTMLElement | SVGElement} node */\nfunction is_flex_item(node) {\n  var _get_parent;\n  // @ts-ignore\n  const display = getComputedStyle((_get_parent = get_parent(node)) !== null && _get_parent !== void 0 ? _get_parent : node).display;\n  return display === \"flex\" || display === \"inline-flex\";\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction creates_stacking_context(node) {\n  const style = getComputedStyle(node);\n\n  // https://developer.mozilla.org/en-US/docs/Web/CSS/CSS_Positioning/Understanding_z_index/The_stacking_context\n  if (style.position === \"fixed\") return true;\n  // Forked to fix upstream bug https://github.com/Rich-Harris/stacking-order/issues/3\n  // if (\n  //   (style.zIndex !== \"auto\" && style.position !== \"static\") ||\n  //   is_flex_item(node)\n  // )\n  if (style.zIndex !== \"auto\" && (style.position !== \"static\" || is_flex_item(node))) return true;\n  if (+style.opacity < 1) return true;\n  if (\"transform\" in style && style.transform !== \"none\") return true;\n  if (\"webkitTransform\" in style && style.webkitTransform !== \"none\") return true;\n  if (\"mixBlendMode\" in style && style.mixBlendMode !== \"normal\") return true;\n  if (\"filter\" in style && style.filter !== \"none\") return true;\n  if (\"webkitFilter\" in style && style.webkitFilter !== \"none\") return true;\n  if (\"isolation\" in style && style.isolation === \"isolate\") return true;\n  if (props.test(style.willChange)) return true;\n  // @ts-expect-error\n  if (style.webkitOverflowScrolling === \"touch\") return true;\n  return false;\n}\n\n/** @param {(HTMLElement| SVGElement)[]} nodes */\nfunction find_stacking_context(nodes) {\n  let i = nodes.length;\n  while (i--) {\n    const node = nodes[i];\n    assert(node, \"Missing node\");\n    if (creates_stacking_context(node)) return node;\n  }\n  return null;\n}\n\n/** @param {HTMLElement | SVGElement} node */\nfunction get_z_index(node) {\n  return node && Number(getComputedStyle(node).zIndex) || 0;\n}\n\n/** @param {HTMLElement} node */\nfunction get_ancestors(node) {\n  const ancestors = [];\n  while (node) {\n    ancestors.push(node);\n    // @ts-ignore\n    node = get_parent(node);\n  }\n  return ancestors; // [ node, ... <body>, <html>, document ]\n}\n\n/** @param {HTMLElement} node */\nfunction get_parent(node) {\n  const {\n    parentNode\n  } = node;\n  if (parentNode && parentNode instanceof ShadowRoot) {\n    return parentNode.host;\n  }\n  return parentNode;\n}\n\nconst EXCEEDED_HORIZONTAL_MIN = 0b0001;\nconst EXCEEDED_HORIZONTAL_MAX = 0b0010;\nconst EXCEEDED_VERTICAL_MIN = 0b0100;\nconst EXCEEDED_VERTICAL_MAX = 0b1000;\nconst isCoarsePointer = getInputType() === \"coarse\";\nlet intersectingHandles = [];\nlet isPointerDown = false;\nlet ownerDocumentCounts = new Map();\nlet panelConstraintFlags = new Map();\nconst registeredResizeHandlers = new Set();\nfunction registerResizeHandle(resizeHandleId, element, direction, hitAreaMargins, setResizeHandlerState) {\n  var _ownerDocumentCounts$;\n  const {\n    ownerDocument\n  } = element;\n  const data = {\n    direction,\n    element,\n    hitAreaMargins,\n    setResizeHandlerState\n  };\n  const count = (_ownerDocumentCounts$ = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$ !== void 0 ? _ownerDocumentCounts$ : 0;\n  ownerDocumentCounts.set(ownerDocument, count + 1);\n  registeredResizeHandlers.add(data);\n  updateListeners();\n  return function unregisterResizeHandle() {\n    var _ownerDocumentCounts$2;\n    panelConstraintFlags.delete(resizeHandleId);\n    registeredResizeHandlers.delete(data);\n    const count = (_ownerDocumentCounts$2 = ownerDocumentCounts.get(ownerDocument)) !== null && _ownerDocumentCounts$2 !== void 0 ? _ownerDocumentCounts$2 : 1;\n    ownerDocumentCounts.set(ownerDocument, count - 1);\n    updateListeners();\n    if (count === 1) {\n      ownerDocumentCounts.delete(ownerDocument);\n    }\n\n    // If the resize handle that is currently unmounting is intersecting with the pointer,\n    // update the global pointer to account for the change\n    if (intersectingHandles.includes(data)) {\n      const index = intersectingHandles.indexOf(data);\n      if (index >= 0) {\n        intersectingHandles.splice(index, 1);\n      }\n      updateCursor();\n\n      // Also instruct the handle to stop dragging; this prevents the parent group from being left in an inconsistent state\n      // See github.com/bvaughn/react-resizable-panels/issues/402\n      setResizeHandlerState(\"up\", true, null);\n    }\n  };\n}\nfunction handlePointerDown(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  isPointerDown = true;\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateListeners();\n  if (intersectingHandles.length > 0) {\n    updateResizeHandlerStates(\"down\", event);\n\n    // Update cursor based on return value(s) from active handles\n    updateCursor();\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n}\nfunction handlePointerMove(event) {\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n\n  // Edge case (see #340)\n  // Detect when the pointer has been released outside an iframe on a different domain\n  if (isPointerDown && event.buttons === 0) {\n    isPointerDown = false;\n    updateResizeHandlerStates(\"up\", event);\n  }\n  if (!isPointerDown) {\n    const {\n      target\n    } = event;\n\n    // Recalculate intersecting handles whenever the pointer moves, except if it has already been pressed\n    // at that point, the handles may not move with the pointer (depending on constraints)\n    // but the same set of active handles should be locked until the pointer is released\n    recalculateIntersectingHandles({\n      target,\n      x,\n      y\n    });\n  }\n  updateResizeHandlerStates(\"move\", event);\n\n  // Update cursor based on return value(s) from active handles\n  updateCursor();\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n  }\n}\nfunction handlePointerUp(event) {\n  const {\n    target\n  } = event;\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  panelConstraintFlags.clear();\n  isPointerDown = false;\n  if (intersectingHandles.length > 0) {\n    event.preventDefault();\n    if (!isWithinResizeHandle(target)) {\n      event.stopImmediatePropagation();\n    }\n  }\n  updateResizeHandlerStates(\"up\", event);\n  recalculateIntersectingHandles({\n    target,\n    x,\n    y\n  });\n  updateCursor();\n  updateListeners();\n}\nfunction isWithinResizeHandle(element) {\n  let currentElement = element;\n  while (currentElement) {\n    if (currentElement.hasAttribute(DATA_ATTRIBUTES.resizeHandle)) {\n      return true;\n    }\n    currentElement = currentElement.parentElement;\n  }\n  return false;\n}\nfunction recalculateIntersectingHandles({\n  target,\n  x,\n  y\n}) {\n  intersectingHandles.splice(0);\n  let targetElement = null;\n  if (target instanceof HTMLElement || target instanceof SVGElement) {\n    targetElement = target;\n  }\n  registeredResizeHandlers.forEach(data => {\n    const {\n      element: dragHandleElement,\n      hitAreaMargins\n    } = data;\n    const dragHandleRect = dragHandleElement.getBoundingClientRect();\n    const {\n      bottom,\n      left,\n      right,\n      top\n    } = dragHandleRect;\n    const margin = isCoarsePointer ? hitAreaMargins.coarse : hitAreaMargins.fine;\n    const eventIntersects = x >= left - margin && x <= right + margin && y >= top - margin && y <= bottom + margin;\n    if (eventIntersects) {\n      // TRICKY\n      // We listen for pointers events at the root in order to support hit area margins\n      // (determining when the pointer is close enough to an element to be considered a \"hit\")\n      // Clicking on an element \"above\" a handle (e.g. a modal) should prevent a hit though\n      // so at this point we need to compare stacking order of a potentially intersecting drag handle,\n      // and the element that was actually clicked/touched\n      if (targetElement !== null && document.contains(targetElement) && dragHandleElement !== targetElement && !dragHandleElement.contains(targetElement) && !targetElement.contains(dragHandleElement) &&\n      // Calculating stacking order has a cost, so we should avoid it if possible\n      // That is why we only check potentially intersecting handles,\n      // and why we skip if the event target is within the handle's DOM\n      compare(targetElement, dragHandleElement) > 0) {\n        // If the target is above the drag handle, then we also need to confirm they overlap\n        // If they are beside each other (e.g. a panel and its drag handle) then the handle is still interactive\n        //\n        // It's not enough to compare only the target\n        // The target might be a small element inside of a larger container\n        // (For example, a SPAN or a DIV inside of a larger modal dialog)\n        let currentElement = targetElement;\n        let didIntersect = false;\n        while (currentElement) {\n          if (currentElement.contains(dragHandleElement)) {\n            break;\n          } else if (intersects(currentElement.getBoundingClientRect(), dragHandleRect, true)) {\n            didIntersect = true;\n            break;\n          }\n          currentElement = currentElement.parentElement;\n        }\n        if (didIntersect) {\n          return;\n        }\n      }\n      intersectingHandles.push(data);\n    }\n  });\n}\nfunction reportConstraintsViolation(resizeHandleId, flag) {\n  panelConstraintFlags.set(resizeHandleId, flag);\n}\nfunction updateCursor() {\n  let intersectsHorizontal = false;\n  let intersectsVertical = false;\n  intersectingHandles.forEach(data => {\n    const {\n      direction\n    } = data;\n    if (direction === \"horizontal\") {\n      intersectsHorizontal = true;\n    } else {\n      intersectsVertical = true;\n    }\n  });\n  let constraintFlags = 0;\n  panelConstraintFlags.forEach(flag => {\n    constraintFlags |= flag;\n  });\n  if (intersectsHorizontal && intersectsVertical) {\n    setGlobalCursorStyle(\"intersection\", constraintFlags, isPointerDown);\n  } else if (intersectsHorizontal) {\n    setGlobalCursorStyle(\"horizontal\", constraintFlags, isPointerDown);\n  } else if (intersectsVertical) {\n    setGlobalCursorStyle(\"vertical\", constraintFlags, isPointerDown);\n  } else {\n    resetGlobalCursorStyle();\n  }\n}\nlet listenersAbortController;\nfunction updateListeners() {\n  var _listenersAbortContro;\n  (_listenersAbortContro = listenersAbortController) === null || _listenersAbortContro === void 0 ? void 0 : _listenersAbortContro.abort();\n  listenersAbortController = new AbortController();\n  const options = {\n    capture: true,\n    signal: listenersAbortController.signal\n  };\n  if (!registeredResizeHandlers.size) {\n    return;\n  }\n  if (isPointerDown) {\n    if (intersectingHandles.length > 0) {\n      ownerDocumentCounts.forEach((count, ownerDocument) => {\n        const {\n          body\n        } = ownerDocument;\n        if (count > 0) {\n          body.addEventListener(\"contextmenu\", handlePointerUp, options);\n          body.addEventListener(\"pointerleave\", handlePointerMove, options);\n          body.addEventListener(\"pointermove\", handlePointerMove, options);\n        }\n      });\n    }\n    ownerDocumentCounts.forEach((_, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      body.addEventListener(\"pointerup\", handlePointerUp, options);\n      body.addEventListener(\"pointercancel\", handlePointerUp, options);\n    });\n  } else {\n    ownerDocumentCounts.forEach((count, ownerDocument) => {\n      const {\n        body\n      } = ownerDocument;\n      if (count > 0) {\n        body.addEventListener(\"pointerdown\", handlePointerDown, options);\n        body.addEventListener(\"pointermove\", handlePointerMove, options);\n      }\n    });\n  }\n}\nfunction updateResizeHandlerStates(action, event) {\n  registeredResizeHandlers.forEach(data => {\n    const {\n      setResizeHandlerState\n    } = data;\n    const isActive = intersectingHandles.includes(data);\n    setResizeHandlerState(action, isActive, event);\n  });\n}\n\nfunction useForceUpdate() {\n  const [_, setCount] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(0);\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => setCount(prevCount => prevCount + 1), []);\n}\n\nfunction assert(expectedCondition, message) {\n  if (!expectedCondition) {\n    console.error(message);\n    throw Error(message);\n  }\n}\n\nfunction fuzzyCompareNumbers(actual, expected, fractionDigits = PRECISION) {\n  if (actual.toFixed(fractionDigits) === expected.toFixed(fractionDigits)) {\n    return 0;\n  } else {\n    return actual > expected ? 1 : -1;\n  }\n}\nfunction fuzzyNumbersEqual$1(actual, expected, fractionDigits = PRECISION) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyNumbersEqual(actual, expected, fractionDigits) {\n  return fuzzyCompareNumbers(actual, expected, fractionDigits) === 0;\n}\n\nfunction fuzzyLayoutsEqual(actual, expected, fractionDigits) {\n  if (actual.length !== expected.length) {\n    return false;\n  }\n  for (let index = 0; index < actual.length; index++) {\n    const actualSize = actual[index];\n    const expectedSize = expected[index];\n    if (!fuzzyNumbersEqual(actualSize, expectedSize, fractionDigits)) {\n      return false;\n    }\n  }\n  return true;\n}\n\n// Panel size must be in percentages; pixel values should be pre-converted\nfunction resizePanel({\n  panelConstraints: panelConstraintsArray,\n  panelIndex,\n  size\n}) {\n  const panelConstraints = panelConstraintsArray[panelIndex];\n  assert(panelConstraints != null, `Panel constraints not found for index ${panelIndex}`);\n  let {\n    collapsedSize = 0,\n    collapsible,\n    maxSize = 100,\n    minSize = 0\n  } = panelConstraints;\n  if (fuzzyCompareNumbers(size, minSize) < 0) {\n    if (collapsible) {\n      // Collapsible panels should snap closed or open only once they cross the halfway point between collapsed and min size.\n      const halfwayPoint = (collapsedSize + minSize) / 2;\n      if (fuzzyCompareNumbers(size, halfwayPoint) < 0) {\n        size = collapsedSize;\n      } else {\n        size = minSize;\n      }\n    } else {\n      size = minSize;\n    }\n  }\n  size = Math.min(maxSize, size);\n  size = parseFloat(size.toFixed(PRECISION));\n  return size;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction adjustLayoutByDelta({\n  delta,\n  initialLayout,\n  panelConstraints: panelConstraintsArray,\n  pivotIndices,\n  prevLayout,\n  trigger\n}) {\n  if (fuzzyNumbersEqual(delta, 0)) {\n    return initialLayout;\n  }\n  const nextLayout = [...initialLayout];\n  const [firstPivotIndex, secondPivotIndex] = pivotIndices;\n  assert(firstPivotIndex != null, \"Invalid first pivot index\");\n  assert(secondPivotIndex != null, \"Invalid second pivot index\");\n  let deltaApplied = 0;\n\n  // const DEBUG = [];\n  // DEBUG.push(`adjustLayoutByDelta()`);\n  // DEBUG.push(`  initialLayout: ${initialLayout.join(\", \")}`);\n  // DEBUG.push(`  prevLayout: ${prevLayout.join(\", \")}`);\n  // DEBUG.push(`  delta: ${delta}`);\n  // DEBUG.push(`  pivotIndices: ${pivotIndices.join(\", \")}`);\n  // DEBUG.push(`  trigger: ${trigger}`);\n  // DEBUG.push(\"\");\n\n  // A resizing panel affects the panels before or after it.\n  //\n  // A negative delta means the panel(s) immediately after the resize handle should grow/expand by decreasing its offset.\n  // Other panels may also need to shrink/contract (and shift) to make room, depending on the min weights.\n  //\n  // A positive delta means the panel(s) immediately before the resize handle should \"expand\".\n  // This is accomplished by shrinking/contracting (and shifting) one or more of the panels after the resize handle.\n\n  {\n    // If this is a resize triggered by a keyboard event, our logic for expanding/collapsing is different.\n    // We no longer check the halfway threshold because this may prevent the panel from expanding at all.\n    if (trigger === \"keyboard\") {\n      {\n        // Check if we should expand a collapsed panel\n        const index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `Panel constraints not found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 1: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, collapsedSize)) {\n            const localDelta = minSize - prevSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n\n      {\n        // Check if we should collapse a panel at its minimum size\n        const index = delta < 0 ? firstPivotIndex : secondPivotIndex;\n        const panelConstraints = panelConstraintsArray[index];\n        assert(panelConstraints, `No panel constraints found for index ${index}`);\n        const {\n          collapsedSize = 0,\n          collapsible,\n          minSize = 0\n        } = panelConstraints;\n\n        // DEBUG.push(`edge case check 2: ${index}`);\n        // DEBUG.push(`  -> collapsible? ${collapsible}`);\n        if (collapsible) {\n          const prevSize = initialLayout[index];\n          assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n          if (fuzzyNumbersEqual(prevSize, minSize)) {\n            const localDelta = prevSize - collapsedSize;\n            // DEBUG.push(`  -> expand delta: ${localDelta}`);\n\n            if (fuzzyCompareNumbers(localDelta, Math.abs(delta)) > 0) {\n              delta = delta < 0 ? 0 - localDelta : localDelta;\n              // DEBUG.push(`  -> delta: ${delta}`);\n            }\n          }\n        }\n      }\n    }\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Pre-calculate max available delta in the opposite direction of our pivot.\n    // This will be the maximum amount we're allowed to expand/contract the panels in the primary direction.\n    // If this amount is less than the requested delta, adjust the requested delta.\n    // If this amount is greater than the requested delta, that's useful information too–\n    // as an expanding panel might change from collapsed to min size.\n\n    const increment = delta < 0 ? 1 : -1;\n    let index = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    let maxAvailableDelta = 0;\n\n    // DEBUG.push(\"pre calc...\");\n    while (true) {\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const maxSafeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: 100\n      });\n      const delta = maxSafeSize - prevSize;\n      // DEBUG.push(`  ${index}: ${prevSize} -> ${maxSafeSize}`);\n\n      maxAvailableDelta += delta;\n      index += increment;\n      if (index < 0 || index >= panelConstraintsArray.length) {\n        break;\n      }\n    }\n\n    // DEBUG.push(`  -> max available delta: ${maxAvailableDelta}`);\n    const minAbsDelta = Math.min(Math.abs(delta), Math.abs(maxAvailableDelta));\n    delta = delta < 0 ? 0 - minAbsDelta : minAbsDelta;\n    // DEBUG.push(`  -> adjusted delta: ${delta}`);\n    // DEBUG.push(\"\");\n  }\n\n  {\n    // Delta added to a panel needs to be subtracted from other panels (within the constraints that those panels allow).\n\n    const pivotIndex = delta < 0 ? firstPivotIndex : secondPivotIndex;\n    let index = pivotIndex;\n    while (index >= 0 && index < panelConstraintsArray.length) {\n      const deltaRemaining = Math.abs(delta) - Math.abs(deltaApplied);\n      const prevSize = initialLayout[index];\n      assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n      const unsafeSize = prevSize - deltaRemaining;\n      const safeSize = resizePanel({\n        panelConstraints: panelConstraintsArray,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n        deltaApplied += prevSize - safeSize;\n        nextLayout[index] = safeSize;\n        if (deltaApplied.toFixed(3).localeCompare(Math.abs(delta).toFixed(3), undefined, {\n          numeric: true\n        }) >= 0) {\n          break;\n        }\n      }\n      if (delta < 0) {\n        index--;\n      } else {\n        index++;\n      }\n    }\n  }\n  // DEBUG.push(`after 1: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  // If we were unable to resize any of the panels panels, return the previous state.\n  // This will essentially bailout and ignore e.g. drags past a panel's boundaries\n  if (fuzzyLayoutsEqual(prevLayout, nextLayout)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n  {\n    // Now distribute the applied delta to the panels in the other direction\n    const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n    const prevSize = initialLayout[pivotIndex];\n    assert(prevSize != null, `Previous layout not found for panel index ${pivotIndex}`);\n    const unsafeSize = prevSize + deltaApplied;\n    const safeSize = resizePanel({\n      panelConstraints: panelConstraintsArray,\n      panelIndex: pivotIndex,\n      size: unsafeSize\n    });\n\n    // Adjust the pivot panel before, but only by the amount that surrounding panels were able to shrink/contract.\n    nextLayout[pivotIndex] = safeSize;\n\n    // Edge case where expanding or contracting one panel caused another one to change collapsed state\n    if (!fuzzyNumbersEqual(safeSize, unsafeSize)) {\n      let deltaRemaining = unsafeSize - safeSize;\n      const pivotIndex = delta < 0 ? secondPivotIndex : firstPivotIndex;\n      let index = pivotIndex;\n      while (index >= 0 && index < panelConstraintsArray.length) {\n        const prevSize = nextLayout[index];\n        assert(prevSize != null, `Previous layout not found for panel index ${index}`);\n        const unsafeSize = prevSize + deltaRemaining;\n        const safeSize = resizePanel({\n          panelConstraints: panelConstraintsArray,\n          panelIndex: index,\n          size: unsafeSize\n        });\n        if (!fuzzyNumbersEqual(prevSize, safeSize)) {\n          deltaRemaining -= safeSize - prevSize;\n          nextLayout[index] = safeSize;\n        }\n        if (fuzzyNumbersEqual(deltaRemaining, 0)) {\n          break;\n        }\n        if (delta > 0) {\n          index--;\n        } else {\n          index++;\n        }\n      }\n    }\n  }\n  // DEBUG.push(`after 2: ${nextLayout.join(\", \")}`);\n  // DEBUG.push(`  deltaApplied: ${deltaApplied}`);\n  // DEBUG.push(\"\");\n\n  const totalSize = nextLayout.reduce((total, size) => size + total, 0);\n  // DEBUG.push(`total size: ${totalSize}`);\n\n  // If our new layout doesn't add up to 100%, that means the requested delta can't be applied\n  // In that case, fall back to our most recent valid layout\n  if (!fuzzyNumbersEqual(totalSize, 100)) {\n    // DEBUG.push(`bailout to previous layout: ${prevLayout.join(\", \")}`);\n    // console.log(DEBUG.join(\"\\n\"));\n\n    return prevLayout;\n  }\n\n  // console.log(DEBUG.join(\"\\n\"));\n  return nextLayout;\n}\n\nfunction getResizeHandleElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[${DATA_ATTRIBUTES.resizeHandleId}][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getResizeHandleElementIndex(groupId, id, scope = document) {\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handles.findIndex(handle => handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId) === id);\n  return index !== null && index !== void 0 ? index : null;\n}\n\nfunction determinePivotIndices(groupId, dragHandleId, panelGroupElement) {\n  const index = getResizeHandleElementIndex(groupId, dragHandleId, panelGroupElement);\n  return index != null ? [index, index + 1] : [-1, -1];\n}\n\nfunction isHTMLElement(target) {\n  if (target instanceof HTMLElement) {\n    return true;\n  }\n\n  // Fallback to duck typing to handle edge case of portals within a popup window\n  return typeof target === \"object\" && target !== null && \"tagName\" in target && \"getAttribute\" in target;\n}\n\nfunction getPanelGroupElement(id, rootElement = document) {\n  // If the root element is the PanelGroup\n  if (isHTMLElement(rootElement) && rootElement.dataset.panelGroupId == id) {\n    return rootElement;\n  }\n\n  // Else query children\n  const element = rootElement.querySelector(`[data-panel-group][data-panel-group-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandleElement(id, scope = document) {\n  const element = scope.querySelector(`[${DATA_ATTRIBUTES.resizeHandleId}=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getResizeHandlePanelIds(groupId, handleId, panelsArray, scope = document) {\n  var _panelsArray$index$id, _panelsArray$index, _panelsArray$id, _panelsArray;\n  const handle = getResizeHandleElement(handleId, scope);\n  const handles = getResizeHandleElementsForGroup(groupId, scope);\n  const index = handle ? handles.indexOf(handle) : -1;\n  const idBefore = (_panelsArray$index$id = (_panelsArray$index = panelsArray[index]) === null || _panelsArray$index === void 0 ? void 0 : _panelsArray$index.id) !== null && _panelsArray$index$id !== void 0 ? _panelsArray$index$id : null;\n  const idAfter = (_panelsArray$id = (_panelsArray = panelsArray[index + 1]) === null || _panelsArray === void 0 ? void 0 : _panelsArray.id) !== null && _panelsArray$id !== void 0 ? _panelsArray$id : null;\n  return [idBefore, idAfter];\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterPanelGroupBehavior({\n  committedValuesRef,\n  eagerValuesRef,\n  groupId,\n  layout,\n  panelDataArray,\n  panelGroupElement,\n  setLayout\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didWarnAboutMissingResizeHandle: false\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (!panelGroupElement) {\n      return;\n    }\n    const eagerValues = eagerValuesRef.current;\n    assert(eagerValues, `Eager values not found`);\n    const {\n      panelDataArray\n    } = eagerValues;\n    const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n    assert(groupElement != null, `No group found for id \"${groupId}\"`);\n    const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n    assert(handles, `No resize handles found for group id \"${groupId}\"`);\n    const cleanupFunctions = handles.map(handle => {\n      const handleId = handle.getAttribute(DATA_ATTRIBUTES.resizeHandleId);\n      assert(handleId, `Resize handle element has no handle id attribute`);\n      const [idBefore, idAfter] = getResizeHandlePanelIds(groupId, handleId, panelDataArray, panelGroupElement);\n      if (idBefore == null || idAfter == null) {\n        return () => {};\n      }\n      const onKeyDown = event => {\n        if (event.defaultPrevented) {\n          return;\n        }\n        switch (event.key) {\n          case \"Enter\":\n            {\n              event.preventDefault();\n              const index = panelDataArray.findIndex(panelData => panelData.id === idBefore);\n              if (index >= 0) {\n                const panelData = panelDataArray[index];\n                assert(panelData, `No panel data found for index ${index}`);\n                const size = layout[index];\n                const {\n                  collapsedSize = 0,\n                  collapsible,\n                  minSize = 0\n                } = panelData.constraints;\n                if (size != null && collapsible) {\n                  const nextLayout = adjustLayoutByDelta({\n                    delta: fuzzyNumbersEqual(size, collapsedSize) ? minSize - collapsedSize : collapsedSize - size,\n                    initialLayout: layout,\n                    panelConstraints: panelDataArray.map(panelData => panelData.constraints),\n                    pivotIndices: determinePivotIndices(groupId, handleId, panelGroupElement),\n                    prevLayout: layout,\n                    trigger: \"keyboard\"\n                  });\n                  if (layout !== nextLayout) {\n                    setLayout(nextLayout);\n                  }\n                }\n              }\n              break;\n            }\n        }\n      };\n      handle.addEventListener(\"keydown\", onKeyDown);\n      return () => {\n        handle.removeEventListener(\"keydown\", onKeyDown);\n      };\n    });\n    return () => {\n      cleanupFunctions.forEach(cleanupFunction => cleanupFunction());\n    };\n  }, [panelGroupElement, committedValuesRef, eagerValuesRef, groupId, layout, panelDataArray, setLayout]);\n}\n\nfunction areEqual(arrayA, arrayB) {\n  if (arrayA.length !== arrayB.length) {\n    return false;\n  }\n  for (let index = 0; index < arrayA.length; index++) {\n    if (arrayA[index] !== arrayB[index]) {\n      return false;\n    }\n  }\n  return true;\n}\n\nfunction getResizeEventCursorPosition(direction, event) {\n  const isHorizontal = direction === \"horizontal\";\n  const {\n    x,\n    y\n  } = getResizeEventCoordinates(event);\n  return isHorizontal ? x : y;\n}\n\nfunction calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement) {\n  const isHorizontal = direction === \"horizontal\";\n  const handleElement = getResizeHandleElement(dragHandleId, panelGroupElement);\n  assert(handleElement, `No resize handle element found for id \"${dragHandleId}\"`);\n  const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n  assert(groupId, `Resize handle element has no group id attribute`);\n  let {\n    initialCursorPosition\n  } = initialDragState;\n  const cursorPosition = getResizeEventCursorPosition(direction, event);\n  const groupElement = getPanelGroupElement(groupId, panelGroupElement);\n  assert(groupElement, `No group element found for id \"${groupId}\"`);\n  const groupRect = groupElement.getBoundingClientRect();\n  const groupSizeInPixels = isHorizontal ? groupRect.width : groupRect.height;\n  const offsetPixels = cursorPosition - initialCursorPosition;\n  const offsetPercentage = offsetPixels / groupSizeInPixels * 100;\n  return offsetPercentage;\n}\n\n// https://developer.mozilla.org/en-US/docs/Web/API/MouseEvent/movementX\nfunction calculateDeltaPercentage(event, dragHandleId, direction, initialDragState, keyboardResizeBy, panelGroupElement) {\n  if (isKeyDown(event)) {\n    const isHorizontal = direction === \"horizontal\";\n    let delta = 0;\n    if (event.shiftKey) {\n      delta = 100;\n    } else if (keyboardResizeBy != null) {\n      delta = keyboardResizeBy;\n    } else {\n      delta = 10;\n    }\n    let movement = 0;\n    switch (event.key) {\n      case \"ArrowDown\":\n        movement = isHorizontal ? 0 : delta;\n        break;\n      case \"ArrowLeft\":\n        movement = isHorizontal ? -delta : 0;\n        break;\n      case \"ArrowRight\":\n        movement = isHorizontal ? delta : 0;\n        break;\n      case \"ArrowUp\":\n        movement = isHorizontal ? 0 : -delta;\n        break;\n      case \"End\":\n        movement = 100;\n        break;\n      case \"Home\":\n        movement = -100;\n        break;\n    }\n    return movement;\n  } else {\n    if (initialDragState == null) {\n      return 0;\n    }\n    return calculateDragOffsetPercentage(event, dragHandleId, direction, initialDragState, panelGroupElement);\n  }\n}\n\n// Layout should be pre-converted into percentages\nfunction callPanelCallbacks(panelsArray, layout, panelIdToLastNotifiedSizeMap) {\n  layout.forEach((size, index) => {\n    const panelData = panelsArray[index];\n    assert(panelData, `Panel data not found for index ${index}`);\n    const {\n      callbacks,\n      constraints,\n      id: panelId\n    } = panelData;\n    const {\n      collapsedSize = 0,\n      collapsible\n    } = constraints;\n    const lastNotifiedSize = panelIdToLastNotifiedSizeMap[panelId];\n    if (lastNotifiedSize == null || size !== lastNotifiedSize) {\n      panelIdToLastNotifiedSizeMap[panelId] = size;\n      const {\n        onCollapse,\n        onExpand,\n        onResize\n      } = callbacks;\n      if (onResize) {\n        onResize(size, lastNotifiedSize);\n      }\n      if (collapsible && (onCollapse || onExpand)) {\n        if (onExpand && (lastNotifiedSize == null || fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && !fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onExpand();\n        }\n        if (onCollapse && (lastNotifiedSize == null || !fuzzyNumbersEqual$1(lastNotifiedSize, collapsedSize)) && fuzzyNumbersEqual$1(size, collapsedSize)) {\n          onCollapse();\n        }\n      }\n    }\n  });\n}\n\nfunction compareLayouts(a, b) {\n  if (a.length !== b.length) {\n    return false;\n  } else {\n    for (let index = 0; index < a.length; index++) {\n      if (a[index] != b[index]) {\n        return false;\n      }\n    }\n  }\n  return true;\n}\n\n// This method returns a number between 1 and 100 representing\n\n// the % of the group's overall space this panel should occupy.\nfunction computePanelFlexBoxStyle({\n  defaultSize,\n  dragState,\n  layout,\n  panelData,\n  panelIndex,\n  precision = 3\n}) {\n  const size = layout[panelIndex];\n  let flexGrow;\n  if (size == null) {\n    // Initial render (before panels have registered themselves)\n    // In order to support server rendering, fall back to default size if provided\n    flexGrow = defaultSize != undefined ? defaultSize.toFixed(precision) : \"1\";\n  } else if (panelData.length === 1) {\n    // Special case: Single panel group should always fill full width/height\n    flexGrow = \"1\";\n  } else {\n    flexGrow = size.toFixed(precision);\n  }\n  return {\n    flexBasis: 0,\n    flexGrow,\n    flexShrink: 1,\n    // Without this, Panel sizes may be unintentionally overridden by their content\n    overflow: \"hidden\",\n    // Disable pointer events inside of a panel during resize\n    // This avoid edge cases like nested iframes\n    pointerEvents: dragState !== null ? \"none\" : undefined\n  };\n}\n\nfunction debounce(callback, durationMs = 10) {\n  let timeoutId = null;\n  let callable = (...args) => {\n    if (timeoutId !== null) {\n      clearTimeout(timeoutId);\n    }\n    timeoutId = setTimeout(() => {\n      callback(...args);\n    }, durationMs);\n  };\n  return callable;\n}\n\n// PanelGroup might be rendering in a server-side environment where localStorage is not available\n// or on a browser with cookies/storage disabled.\n// In either case, this function avoids accessing localStorage until needed,\n// and avoids throwing user-visible errors.\nfunction initializeDefaultStorage(storageObject) {\n  try {\n    if (typeof localStorage !== \"undefined\") {\n      // Bypass this check for future calls\n      storageObject.getItem = name => {\n        return localStorage.getItem(name);\n      };\n      storageObject.setItem = (name, value) => {\n        localStorage.setItem(name, value);\n      };\n    } else {\n      throw new Error(\"localStorage not supported in this environment\");\n    }\n  } catch (error) {\n    console.error(error);\n    storageObject.getItem = () => null;\n    storageObject.setItem = () => {};\n  }\n}\n\nfunction getPanelGroupKey(autoSaveId) {\n  return `react-resizable-panels:${autoSaveId}`;\n}\n\n// Note that Panel ids might be user-provided (stable) or useId generated (non-deterministic)\n// so they should not be used as part of the serialization key.\n// Using the min/max size attributes should work well enough as a backup.\n// Pre-sorting by minSize allows remembering layouts even if panels are re-ordered/dragged.\nfunction getPanelKey(panels) {\n  return panels.map(panel => {\n    const {\n      constraints,\n      id,\n      idIsFromProps,\n      order\n    } = panel;\n    if (idIsFromProps) {\n      return id;\n    } else {\n      return order ? `${order}:${JSON.stringify(constraints)}` : JSON.stringify(constraints);\n    }\n  }).sort((a, b) => a.localeCompare(b)).join(\",\");\n}\nfunction loadSerializedPanelGroupState(autoSaveId, storage) {\n  try {\n    const panelGroupKey = getPanelGroupKey(autoSaveId);\n    const serialized = storage.getItem(panelGroupKey);\n    if (serialized) {\n      const parsed = JSON.parse(serialized);\n      if (typeof parsed === \"object\" && parsed != null) {\n        return parsed;\n      }\n    }\n  } catch (error) {}\n  return null;\n}\nfunction savePanelGroupState(autoSaveId, panels, panelSizesBeforeCollapse, sizes, storage) {\n  var _loadSerializedPanelG2;\n  const panelGroupKey = getPanelGroupKey(autoSaveId);\n  const panelKey = getPanelKey(panels);\n  const state = (_loadSerializedPanelG2 = loadSerializedPanelGroupState(autoSaveId, storage)) !== null && _loadSerializedPanelG2 !== void 0 ? _loadSerializedPanelG2 : {};\n  state[panelKey] = {\n    expandToSizes: Object.fromEntries(panelSizesBeforeCollapse.entries()),\n    layout: sizes\n  };\n  try {\n    storage.setItem(panelGroupKey, JSON.stringify(state));\n  } catch (error) {\n    console.error(error);\n  }\n}\n\nfunction validatePanelConstraints({\n  panelConstraints: panelConstraintsArray,\n  panelId,\n  panelIndex\n}) {\n  {\n    const warnings = [];\n    const panelConstraints = panelConstraintsArray[panelIndex];\n    assert(panelConstraints, `No panel constraints found for index ${panelIndex}`);\n    const {\n      collapsedSize = 0,\n      collapsible = false,\n      defaultSize,\n      maxSize = 100,\n      minSize = 0\n    } = panelConstraints;\n    if (minSize > maxSize) {\n      warnings.push(`min size (${minSize}%) should not be greater than max size (${maxSize}%)`);\n    }\n    if (defaultSize != null) {\n      if (defaultSize < 0) {\n        warnings.push(\"default size should not be less than 0\");\n      } else if (defaultSize < minSize && (!collapsible || defaultSize !== collapsedSize)) {\n        warnings.push(\"default size should not be less than min size\");\n      }\n      if (defaultSize > 100) {\n        warnings.push(\"default size should not be greater than 100\");\n      } else if (defaultSize > maxSize) {\n        warnings.push(\"default size should not be greater than max size\");\n      }\n    }\n    if (collapsedSize > minSize) {\n      warnings.push(\"collapsed size should not be greater than min size\");\n    }\n    if (warnings.length > 0) {\n      const name = panelId != null ? `Panel \"${panelId}\"` : \"Panel\";\n      console.warn(`${name} has an invalid configuration:\\n\\n${warnings.join(\"\\n\")}`);\n      return false;\n    }\n  }\n  return true;\n}\n\n// All units must be in percentages; pixel values should be pre-converted\nfunction validatePanelGroupLayout({\n  layout: prevLayout,\n  panelConstraints\n}) {\n  const nextLayout = [...prevLayout];\n  const nextLayoutTotalSize = nextLayout.reduce((accumulated, current) => accumulated + current, 0);\n\n  // Validate layout expectations\n  if (nextLayout.length !== panelConstraints.length) {\n    throw Error(`Invalid ${panelConstraints.length} panel layout: ${nextLayout.map(size => `${size}%`).join(\", \")}`);\n  } else if (!fuzzyNumbersEqual(nextLayoutTotalSize, 100) && nextLayout.length > 0) {\n    // This is not ideal so we should warn about it, but it may be recoverable in some cases\n    // (especially if the amount is small)\n    {\n      console.warn(`WARNING: Invalid layout total size: ${nextLayout.map(size => `${size}%`).join(\", \")}. Layout normalization will be applied.`);\n    }\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const unsafeSize = nextLayout[index];\n      assert(unsafeSize != null, `No layout data found for index ${index}`);\n      const safeSize = 100 / nextLayoutTotalSize * unsafeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n  let remainingSize = 0;\n\n  // First pass: Validate the proposed layout given each panel's constraints\n  for (let index = 0; index < panelConstraints.length; index++) {\n    const unsafeSize = nextLayout[index];\n    assert(unsafeSize != null, `No layout data found for index ${index}`);\n    const safeSize = resizePanel({\n      panelConstraints,\n      panelIndex: index,\n      size: unsafeSize\n    });\n    if (unsafeSize != safeSize) {\n      remainingSize += unsafeSize - safeSize;\n      nextLayout[index] = safeSize;\n    }\n  }\n\n  // If there is additional, left over space, assign it to any panel(s) that permits it\n  // (It's not worth taking multiple additional passes to evenly distribute)\n  if (!fuzzyNumbersEqual(remainingSize, 0)) {\n    for (let index = 0; index < panelConstraints.length; index++) {\n      const prevSize = nextLayout[index];\n      assert(prevSize != null, `No layout data found for index ${index}`);\n      const unsafeSize = prevSize + remainingSize;\n      const safeSize = resizePanel({\n        panelConstraints,\n        panelIndex: index,\n        size: unsafeSize\n      });\n      if (prevSize !== safeSize) {\n        remainingSize -= safeSize - prevSize;\n        nextLayout[index] = safeSize;\n\n        // Once we've used up the remainder, bail\n        if (fuzzyNumbersEqual(remainingSize, 0)) {\n          break;\n        }\n      }\n    }\n  }\n  return nextLayout;\n}\n\nconst LOCAL_STORAGE_DEBOUNCE_INTERVAL = 100;\nconst defaultStorage = {\n  getItem: name => {\n    initializeDefaultStorage(defaultStorage);\n    return defaultStorage.getItem(name);\n  },\n  setItem: (name, value) => {\n    initializeDefaultStorage(defaultStorage);\n    defaultStorage.setItem(name, value);\n  }\n};\nconst debounceMap = {};\nfunction PanelGroupWithForwardedRef({\n  autoSaveId = null,\n  children,\n  className: classNameFromProps = \"\",\n  direction,\n  forwardedRef,\n  id: idFromProps = null,\n  onLayout = null,\n  keyboardResizeBy = null,\n  storage = defaultStorage,\n  style: styleFromProps,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  const groupId = useUniqueId(idFromProps);\n  const panelGroupElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n  const [dragState, setDragState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const [layout, setLayout] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n  const forceUpdate = useForceUpdate();\n  const panelIdToLastNotifiedSizeMapRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({});\n  const panelSizeBeforeCollapseRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(new Map());\n  const prevDeltaRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    autoSaveId,\n    direction,\n    dragState,\n    id: groupId,\n    keyboardResizeBy,\n    onLayout,\n    storage\n  });\n  const eagerValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    layout,\n    panelDataArray: [],\n    panelDataArrayChanged: false\n  });\n  const devWarningsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    didLogIdAndOrderWarning: false,\n    didLogPanelConstraintsWarning: false,\n    prevPanelIds: []\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useImperativeHandle)(forwardedRef, () => ({\n    getId: () => committedValuesRef.current.id,\n    getLayout: () => {\n      const {\n        layout\n      } = eagerValuesRef.current;\n      return layout;\n    },\n    setLayout: unsafeLayout => {\n      const {\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const safeLayout = validatePanelGroupLayout({\n        layout: unsafeLayout,\n        panelConstraints: panelDataArray.map(panelData => panelData.constraints)\n      });\n      if (!areEqual(prevLayout, safeLayout)) {\n        setLayout(safeLayout);\n        eagerValuesRef.current.layout = safeLayout;\n        if (onLayout) {\n          onLayout(safeLayout);\n        }\n        callPanelCallbacks(panelDataArray, safeLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    }\n  }), []);\n  useWindowSplitterPanelGroupBehavior({\n    committedValuesRef,\n    eagerValuesRef,\n    groupId,\n    layout,\n    panelDataArray: eagerValuesRef.current.panelDataArray,\n    setLayout,\n    panelGroupElement: panelGroupElementRef.current\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n\n    // If this panel has been configured to persist sizing information, save sizes to local storage.\n    if (autoSaveId) {\n      if (layout.length === 0 || layout.length !== panelDataArray.length) {\n        return;\n      }\n      let debouncedSave = debounceMap[autoSaveId];\n\n      // Limit the frequency of localStorage updates.\n      if (debouncedSave == null) {\n        debouncedSave = debounce(savePanelGroupState, LOCAL_STORAGE_DEBOUNCE_INTERVAL);\n        debounceMap[autoSaveId] = debouncedSave;\n      }\n\n      // Clone mutable data before passing to the debounced function,\n      // else we run the risk of saving an incorrect combination of mutable and immutable values to state.\n      const clonedPanelDataArray = [...panelDataArray];\n      const clonedPanelSizesBeforeCollapse = new Map(panelSizeBeforeCollapseRef.current);\n      debouncedSave(autoSaveId, clonedPanelDataArray, clonedPanelSizesBeforeCollapse, layout, storage);\n    }\n  }, [autoSaveId, layout, storage]);\n\n  // DEV warnings\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    {\n      const {\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        didLogIdAndOrderWarning,\n        didLogPanelConstraintsWarning,\n        prevPanelIds\n      } = devWarningsRef.current;\n      if (!didLogIdAndOrderWarning) {\n        const panelIds = panelDataArray.map(({\n          id\n        }) => id);\n        devWarningsRef.current.prevPanelIds = panelIds;\n        const panelsHaveChanged = prevPanelIds.length > 0 && !areEqual(prevPanelIds, panelIds);\n        if (panelsHaveChanged) {\n          if (panelDataArray.find(({\n            idIsFromProps,\n            order\n          }) => !idIsFromProps || order == null)) {\n            devWarningsRef.current.didLogIdAndOrderWarning = true;\n            console.warn(`WARNING: Panel id and order props recommended when panels are dynamically rendered`);\n          }\n        }\n      }\n      if (!didLogPanelConstraintsWarning) {\n        const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n        for (let panelIndex = 0; panelIndex < panelConstraints.length; panelIndex++) {\n          const panelData = panelDataArray[panelIndex];\n          assert(panelData, `Panel data not found for index ${panelIndex}`);\n          const isValid = validatePanelConstraints({\n            panelConstraints,\n            panelId: panelData.id,\n            panelIndex\n          });\n          if (!isValid) {\n            devWarningsRef.current.didLogPanelConstraintsWarning = true;\n            break;\n          }\n        }\n      }\n    }\n  });\n\n  // External APIs are safe to memoize via committed values ref\n  const collapsePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n      if (!fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Store size before collapse;\n        // This is the size that gets restored if the expand() API is used.\n        panelSizeBeforeCollapseRef.current.set(panelData.id, panelSize);\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - collapsedSize : collapsedSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const expandPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, minSizeOverride) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    if (panelData.constraints.collapsible) {\n      const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n      const {\n        collapsedSize = 0,\n        panelSize = 0,\n        minSize: minSizeFromProps = 0,\n        pivotIndices\n      } = panelDataHelper(panelDataArray, panelData, prevLayout);\n      const minSize = minSizeOverride !== null && minSizeOverride !== void 0 ? minSizeOverride : minSizeFromProps;\n      if (fuzzyNumbersEqual$1(panelSize, collapsedSize)) {\n        // Restore this panel to the size it was before it was collapsed, if possible.\n        const prevPanelSize = panelSizeBeforeCollapseRef.current.get(panelData.id);\n        const baseSize = prevPanelSize != null && prevPanelSize >= minSize ? prevPanelSize : minSize;\n        const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n        const delta = isLastPanel ? panelSize - baseSize : baseSize - panelSize;\n        const nextLayout = adjustLayoutByDelta({\n          delta,\n          initialLayout: prevLayout,\n          panelConstraints: panelConstraintsArray,\n          pivotIndices,\n          prevLayout,\n          trigger: \"imperative-api\"\n        });\n        if (!compareLayouts(prevLayout, nextLayout)) {\n          setLayout(nextLayout);\n          eagerValuesRef.current.layout = nextLayout;\n          if (onLayout) {\n            onLayout(nextLayout);\n          }\n          callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n        }\n      }\n    }\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const getPanelSize = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return panelSize;\n  }, []);\n\n  // This API should never read from committedValuesRef\n  const getPanelStyle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, defaultSize) => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n    return computePanelFlexBoxStyle({\n      defaultSize,\n      dragState,\n      layout,\n      panelData: panelDataArray,\n      panelIndex\n    });\n  }, [dragState, layout]);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelCollapsed = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return collapsible === true && fuzzyNumbersEqual$1(panelSize, collapsedSize);\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const isPanelExpanded = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize = 0,\n      collapsible,\n      panelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    return !collapsible || fuzzyCompareNumbers(panelSize, collapsedSize) > 0;\n  }, []);\n  const registerPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    panelDataArray.push(panelData);\n    panelDataArray.sort((panelA, panelB) => {\n      const orderA = panelA.order;\n      const orderB = panelB.order;\n      if (orderA == null && orderB == null) {\n        return 0;\n      } else if (orderA == null) {\n        return -1;\n      } else if (orderB == null) {\n        return 1;\n      } else {\n        return orderA - orderB;\n      }\n    });\n    eagerValuesRef.current.panelDataArrayChanged = true;\n    forceUpdate();\n  }, [forceUpdate]);\n  const registerResizeHandle = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(dragHandleId => {\n    let isRTL = false;\n    const panelGroupElement = panelGroupElementRef.current;\n    if (panelGroupElement) {\n      const style = window.getComputedStyle(panelGroupElement, null);\n      if (style.getPropertyValue(\"direction\") === \"rtl\") {\n        isRTL = true;\n      }\n    }\n    return function resizeHandler(event) {\n      event.preventDefault();\n      const panelGroupElement = panelGroupElementRef.current;\n      if (!panelGroupElement) {\n        return () => null;\n      }\n      const {\n        direction,\n        dragState,\n        id: groupId,\n        keyboardResizeBy,\n        onLayout\n      } = committedValuesRef.current;\n      const {\n        layout: prevLayout,\n        panelDataArray\n      } = eagerValuesRef.current;\n      const {\n        initialLayout\n      } = dragState !== null && dragState !== void 0 ? dragState : {};\n      const pivotIndices = determinePivotIndices(groupId, dragHandleId, panelGroupElement);\n      let delta = calculateDeltaPercentage(event, dragHandleId, direction, dragState, keyboardResizeBy, panelGroupElement);\n      const isHorizontal = direction === \"horizontal\";\n      if (isHorizontal && isRTL) {\n        delta = -delta;\n      }\n      const panelConstraints = panelDataArray.map(panelData => panelData.constraints);\n      const nextLayout = adjustLayoutByDelta({\n        delta,\n        initialLayout: initialLayout !== null && initialLayout !== void 0 ? initialLayout : prevLayout,\n        panelConstraints,\n        pivotIndices,\n        prevLayout,\n        trigger: isKeyDown(event) ? \"keyboard\" : \"mouse-or-touch\"\n      });\n      const layoutChanged = !compareLayouts(prevLayout, nextLayout);\n\n      // Only update the cursor for layout changes triggered by touch/mouse events (not keyboard)\n      // Update the cursor even if the layout hasn't changed (we may need to show an invalid cursor state)\n      if (isPointerEvent(event) || isMouseEvent(event)) {\n        // Watch for multiple subsequent deltas; this might occur for tiny cursor movements.\n        // In this case, Panel sizes might not change–\n        // but updating cursor in this scenario would cause a flicker.\n        if (prevDeltaRef.current != delta) {\n          prevDeltaRef.current = delta;\n          if (!layoutChanged && delta !== 0) {\n            // If the pointer has moved too far to resize the panel any further, note this so we can update the cursor.\n            // This mimics VS Code behavior.\n            if (isHorizontal) {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_HORIZONTAL_MIN : EXCEEDED_HORIZONTAL_MAX);\n            } else {\n              reportConstraintsViolation(dragHandleId, delta < 0 ? EXCEEDED_VERTICAL_MIN : EXCEEDED_VERTICAL_MAX);\n            }\n          } else {\n            reportConstraintsViolation(dragHandleId, 0);\n          }\n        }\n      }\n      if (layoutChanged) {\n        setLayout(nextLayout);\n        eagerValuesRef.current.layout = nextLayout;\n        if (onLayout) {\n          onLayout(nextLayout);\n        }\n        callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n      }\n    };\n  }, []);\n\n  // External APIs are safe to memoize via committed values ref\n  const resizePanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, unsafePanelSize) => {\n    const {\n      onLayout\n    } = committedValuesRef.current;\n    const {\n      layout: prevLayout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const panelConstraintsArray = panelDataArray.map(panelData => panelData.constraints);\n    const {\n      panelSize,\n      pivotIndices\n    } = panelDataHelper(panelDataArray, panelData, prevLayout);\n    assert(panelSize != null, `Panel size not found for panel \"${panelData.id}\"`);\n    const isLastPanel = findPanelDataIndex(panelDataArray, panelData) === panelDataArray.length - 1;\n    const delta = isLastPanel ? panelSize - unsafePanelSize : unsafePanelSize - panelSize;\n    const nextLayout = adjustLayoutByDelta({\n      delta,\n      initialLayout: prevLayout,\n      panelConstraints: panelConstraintsArray,\n      pivotIndices,\n      prevLayout,\n      trigger: \"imperative-api\"\n    });\n    if (!compareLayouts(prevLayout, nextLayout)) {\n      setLayout(nextLayout);\n      eagerValuesRef.current.layout = nextLayout;\n      if (onLayout) {\n        onLayout(nextLayout);\n      }\n      callPanelCallbacks(panelDataArray, nextLayout, panelIdToLastNotifiedSizeMapRef.current);\n    }\n  }, []);\n  const reevaluatePanelConstraints = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((panelData, prevConstraints) => {\n    const {\n      layout,\n      panelDataArray\n    } = eagerValuesRef.current;\n    const {\n      collapsedSize: prevCollapsedSize = 0,\n      collapsible: prevCollapsible\n    } = prevConstraints;\n    const {\n      collapsedSize: nextCollapsedSize = 0,\n      collapsible: nextCollapsible,\n      maxSize: nextMaxSize = 100,\n      minSize: nextMinSize = 0\n    } = panelData.constraints;\n    const {\n      panelSize: prevPanelSize\n    } = panelDataHelper(panelDataArray, panelData, layout);\n    if (prevPanelSize == null) {\n      // It's possible that the panels in this group have changed since the last render\n      return;\n    }\n    if (prevCollapsible && nextCollapsible && fuzzyNumbersEqual$1(prevPanelSize, prevCollapsedSize)) {\n      if (!fuzzyNumbersEqual$1(prevCollapsedSize, nextCollapsedSize)) {\n        resizePanel(panelData, nextCollapsedSize);\n      }\n    } else if (prevPanelSize < nextMinSize) {\n      resizePanel(panelData, nextMinSize);\n    } else if (prevPanelSize > nextMaxSize) {\n      resizePanel(panelData, nextMaxSize);\n    }\n  }, [resizePanel]);\n\n  // TODO Multiple drag handles can be active at the same time so this API is a bit awkward now\n  const startDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)((dragHandleId, event) => {\n    const {\n      direction\n    } = committedValuesRef.current;\n    const {\n      layout\n    } = eagerValuesRef.current;\n    if (!panelGroupElementRef.current) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(dragHandleId, panelGroupElementRef.current);\n    assert(handleElement, `Drag handle element not found for id \"${dragHandleId}\"`);\n    const initialCursorPosition = getResizeEventCursorPosition(direction, event);\n    setDragState({\n      dragHandleId,\n      dragHandleRect: handleElement.getBoundingClientRect(),\n      initialCursorPosition,\n      initialLayout: layout\n    });\n  }, []);\n  const stopDragging = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(() => {\n    setDragState(null);\n  }, []);\n  const unregisterPanel = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)(panelData => {\n    const {\n      panelDataArray\n    } = eagerValuesRef.current;\n    const index = findPanelDataIndex(panelDataArray, panelData);\n    if (index >= 0) {\n      panelDataArray.splice(index, 1);\n\n      // TRICKY\n      // When a panel is removed from the group, we should delete the most recent prev-size entry for it.\n      // If we don't do this, then a conditionally rendered panel might not call onResize when it's re-mounted.\n      // Strict effects mode makes this tricky though because all panels will be registered, unregistered, then re-registered on mount.\n      delete panelIdToLastNotifiedSizeMapRef.current[panelData.id];\n      eagerValuesRef.current.panelDataArrayChanged = true;\n      forceUpdate();\n    }\n  }, [forceUpdate]);\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(() => ({\n    collapsePanel,\n    direction,\n    dragState,\n    expandPanel,\n    getPanelSize,\n    getPanelStyle,\n    groupId,\n    isPanelCollapsed,\n    isPanelExpanded,\n    reevaluatePanelConstraints,\n    registerPanel,\n    registerResizeHandle,\n    resizePanel,\n    startDragging,\n    stopDragging,\n    unregisterPanel,\n    panelGroupElement: panelGroupElementRef.current\n  }), [collapsePanel, dragState, direction, expandPanel, getPanelSize, getPanelStyle, groupId, isPanelCollapsed, isPanelExpanded, reevaluatePanelConstraints, registerPanel, registerResizeHandle, resizePanel, startDragging, stopDragging, unregisterPanel]);\n  const style = {\n    display: \"flex\",\n    flexDirection: direction === \"horizontal\" ? \"row\" : \"column\",\n    height: \"100%\",\n    overflow: \"hidden\",\n    width: \"100%\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupContext.Provider, {\n    value: context\n  }, (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    ref: panelGroupElementRef,\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    // CSS selectors\n    [DATA_ATTRIBUTES.group]: \"\",\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId\n  }));\n}\nconst PanelGroup = (0,react__WEBPACK_IMPORTED_MODULE_0__.forwardRef)((props, ref) => (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(PanelGroupWithForwardedRef, {\n  ...props,\n  forwardedRef: ref\n}));\nPanelGroupWithForwardedRef.displayName = \"PanelGroup\";\nPanelGroup.displayName = \"forwardRef(PanelGroup)\";\nfunction findPanelDataIndex(panelDataArray, panelData) {\n  return panelDataArray.findIndex(prevPanelData => prevPanelData === panelData || prevPanelData.id === panelData.id);\n}\nfunction panelDataHelper(panelDataArray, panelData, layout) {\n  const panelIndex = findPanelDataIndex(panelDataArray, panelData);\n  const isLastPanel = panelIndex === panelDataArray.length - 1;\n  const pivotIndices = isLastPanel ? [panelIndex - 1, panelIndex] : [panelIndex, panelIndex + 1];\n  const panelSize = layout[panelIndex];\n  return {\n    ...panelData.constraints,\n    panelSize,\n    pivotIndices\n  };\n}\n\n// https://www.w3.org/WAI/ARIA/apg/patterns/windowsplitter/\n\nfunction useWindowSplitterResizeHandlerBehavior({\n  disabled,\n  handleId,\n  resizeHandler,\n  panelGroupElement\n}) {\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null || panelGroupElement == null) {\n      return;\n    }\n    const handleElement = getResizeHandleElement(handleId, panelGroupElement);\n    if (handleElement == null) {\n      return;\n    }\n    const onKeyDown = event => {\n      if (event.defaultPrevented) {\n        return;\n      }\n      switch (event.key) {\n        case \"ArrowDown\":\n        case \"ArrowLeft\":\n        case \"ArrowRight\":\n        case \"ArrowUp\":\n        case \"End\":\n        case \"Home\":\n          {\n            event.preventDefault();\n            resizeHandler(event);\n            break;\n          }\n        case \"F6\":\n          {\n            event.preventDefault();\n            const groupId = handleElement.getAttribute(DATA_ATTRIBUTES.groupId);\n            assert(groupId, `No group element found for id \"${groupId}\"`);\n            const handles = getResizeHandleElementsForGroup(groupId, panelGroupElement);\n            const index = getResizeHandleElementIndex(groupId, handleId, panelGroupElement);\n            assert(index !== null, `No resize element found for id \"${handleId}\"`);\n            const nextIndex = event.shiftKey ? index > 0 ? index - 1 : handles.length - 1 : index + 1 < handles.length ? index + 1 : 0;\n            const nextHandle = handles[nextIndex];\n            nextHandle.focus();\n            break;\n          }\n      }\n    };\n    handleElement.addEventListener(\"keydown\", onKeyDown);\n    return () => {\n      handleElement.removeEventListener(\"keydown\", onKeyDown);\n    };\n  }, [panelGroupElement, disabled, handleId, resizeHandler]);\n}\n\nfunction PanelResizeHandle({\n  children = null,\n  className: classNameFromProps = \"\",\n  disabled = false,\n  hitAreaMargins,\n  id: idFromProps,\n  onBlur,\n  onClick,\n  onDragging,\n  onFocus,\n  onPointerDown,\n  onPointerUp,\n  style: styleFromProps = {},\n  tabIndex = 0,\n  tagName: Type = \"div\",\n  ...rest\n}) {\n  var _hitAreaMargins$coars, _hitAreaMargins$fine;\n  const elementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n\n  // Use a ref to guard against users passing inline props\n  const callbacksRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    onClick,\n    onDragging,\n    onPointerDown,\n    onPointerUp\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    callbacksRef.current.onClick = onClick;\n    callbacksRef.current.onDragging = onDragging;\n    callbacksRef.current.onPointerDown = onPointerDown;\n    callbacksRef.current.onPointerUp = onPointerUp;\n  });\n  const panelGroupContext = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  if (panelGroupContext === null) {\n    throw Error(`PanelResizeHandle components must be rendered within a PanelGroup container`);\n  }\n  const {\n    direction,\n    groupId,\n    registerResizeHandle: registerResizeHandleWithParentGroup,\n    startDragging,\n    stopDragging,\n    panelGroupElement\n  } = panelGroupContext;\n  const resizeHandleId = useUniqueId(idFromProps);\n  const [state, setState] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(\"inactive\");\n  const [isFocused, setIsFocused] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n  const [resizeHandler, setResizeHandler] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n  const committedValuesRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)({\n    state\n  });\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled) {\n      setResizeHandler(null);\n    } else {\n      const resizeHandler = registerResizeHandleWithParentGroup(resizeHandleId);\n      setResizeHandler(() => resizeHandler);\n    }\n  }, [disabled, resizeHandleId, registerResizeHandleWithParentGroup]);\n\n  // Extract hit area margins before passing them to the effect's dependency array\n  // so that inline object values won't trigger re-renders\n  const coarseHitAreaMargins = (_hitAreaMargins$coars = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.coarse) !== null && _hitAreaMargins$coars !== void 0 ? _hitAreaMargins$coars : 15;\n  const fineHitAreaMargins = (_hitAreaMargins$fine = hitAreaMargins === null || hitAreaMargins === void 0 ? void 0 : hitAreaMargins.fine) !== null && _hitAreaMargins$fine !== void 0 ? _hitAreaMargins$fine : 5;\n  (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)(() => {\n    if (disabled || resizeHandler == null) {\n      return;\n    }\n    const element = elementRef.current;\n    assert(element, \"Element ref not attached\");\n    let didMove = false;\n    const setResizeHandlerState = (action, isActive, event) => {\n      if (!isActive) {\n        setState(\"inactive\");\n        return;\n      }\n      switch (action) {\n        case \"down\":\n          {\n            setState(\"drag\");\n            didMove = false;\n            assert(event, 'Expected event to be defined for \"down\" action');\n            startDragging(resizeHandleId, event);\n            const {\n              onDragging,\n              onPointerDown\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(true);\n            onPointerDown === null || onPointerDown === void 0 ? void 0 : onPointerDown();\n            break;\n          }\n        case \"move\":\n          {\n            const {\n              state\n            } = committedValuesRef.current;\n            didMove = true;\n            if (state !== \"drag\") {\n              setState(\"hover\");\n            }\n            assert(event, 'Expected event to be defined for \"move\" action');\n            resizeHandler(event);\n            break;\n          }\n        case \"up\":\n          {\n            setState(\"hover\");\n            stopDragging();\n            const {\n              onClick,\n              onDragging,\n              onPointerUp\n            } = callbacksRef.current;\n            onDragging === null || onDragging === void 0 ? void 0 : onDragging(false);\n            onPointerUp === null || onPointerUp === void 0 ? void 0 : onPointerUp();\n            if (!didMove) {\n              onClick === null || onClick === void 0 ? void 0 : onClick();\n            }\n            break;\n          }\n      }\n    };\n    return registerResizeHandle(resizeHandleId, element, direction, {\n      coarse: coarseHitAreaMargins,\n      fine: fineHitAreaMargins\n    }, setResizeHandlerState);\n  }, [coarseHitAreaMargins, direction, disabled, fineHitAreaMargins, registerResizeHandleWithParentGroup, resizeHandleId, resizeHandler, startDragging, stopDragging]);\n  useWindowSplitterResizeHandlerBehavior({\n    disabled,\n    handleId: resizeHandleId,\n    resizeHandler,\n    panelGroupElement\n  });\n  const style = {\n    touchAction: \"none\",\n    userSelect: \"none\"\n  };\n  return (0,react__WEBPACK_IMPORTED_MODULE_0__.createElement)(Type, {\n    ...rest,\n    children,\n    className: classNameFromProps,\n    id: idFromProps,\n    onBlur: () => {\n      setIsFocused(false);\n      onBlur === null || onBlur === void 0 ? void 0 : onBlur();\n    },\n    onFocus: () => {\n      setIsFocused(true);\n      onFocus === null || onFocus === void 0 ? void 0 : onFocus();\n    },\n    ref: elementRef,\n    role: \"separator\",\n    style: {\n      ...style,\n      ...styleFromProps\n    },\n    tabIndex,\n    // CSS selectors\n    [DATA_ATTRIBUTES.groupDirection]: direction,\n    [DATA_ATTRIBUTES.groupId]: groupId,\n    [DATA_ATTRIBUTES.resizeHandle]: \"\",\n    [DATA_ATTRIBUTES.resizeHandleActive]: state === \"drag\" ? \"pointer\" : isFocused ? \"keyboard\" : undefined,\n    [DATA_ATTRIBUTES.resizeHandleEnabled]: !disabled,\n    [DATA_ATTRIBUTES.resizeHandleId]: resizeHandleId,\n    [DATA_ATTRIBUTES.resizeHandleState]: state\n  });\n}\nPanelResizeHandle.displayName = \"PanelResizeHandle\";\n\nfunction usePanelGroupContext() {\n  const context = (0,react__WEBPACK_IMPORTED_MODULE_0__.useContext)(PanelGroupContext);\n  return {\n    direction: context === null || context === void 0 ? void 0 : context.direction,\n    groupId: context === null || context === void 0 ? void 0 : context.groupId\n  };\n}\n\nfunction getPanelElement(id, scope = document) {\n  const element = scope.querySelector(`[data-panel-id=\"${id}\"]`);\n  if (element) {\n    return element;\n  }\n  return null;\n}\n\nfunction getPanelElementsForGroup(groupId, scope = document) {\n  return Array.from(scope.querySelectorAll(`[data-panel][data-panel-group-id=\"${groupId}\"]`));\n}\n\nfunction getIntersectingRectangle(rectOne, rectTwo, strict) {\n  if (!intersects(rectOne, rectTwo, strict)) {\n    return {\n      x: 0,\n      y: 0,\n      width: 0,\n      height: 0\n    };\n  }\n  return {\n    x: Math.max(rectOne.x, rectTwo.x),\n    y: Math.max(rectOne.y, rectTwo.y),\n    width: Math.min(rectOne.x + rectOne.width, rectTwo.x + rectTwo.width) - Math.max(rectOne.x, rectTwo.x),\n    height: Math.min(rectOne.y + rectOne.height, rectTwo.y + rectTwo.height) - Math.max(rectOne.y, rectTwo.y)\n  };\n}\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/react-resizable-panels/dist/react-resizable-panels.development.edge-light.js\n");

/***/ })

};
;