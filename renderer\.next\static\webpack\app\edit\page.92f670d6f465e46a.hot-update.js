"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/NoteEditor.tsx":
/*!***************************************!*\
  !*** ./src/components/NoteEditor.tsx ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   NoteEditor: () => (/* binding */ NoteEditor)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./src/components/ui/input.tsx\");\n/* harmony import */ var _components_ui_TagInput__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/TagInput */ \"(app-pages-browser)/./src/components/ui/TagInput.tsx\");\n/* harmony import */ var _CherryMarkdownEditor__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./CherryMarkdownEditor */ \"(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\");\n/* harmony import */ var _uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @uiw/react-markdown-preview */ \"(app-pages-browser)/./node_modules/@uiw/react-markdown-preview/esm/index.js\");\n/* harmony import */ var _components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/ui/dropdown-menu */ \"(app-pages-browser)/./src/components/ui/dropdown-menu.tsx\");\n/* harmony import */ var _components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/components/ui/dialog */ \"(app-pages-browser)/./src/components/ui/dialog.tsx\");\n/* harmony import */ var _components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/components/ui/alert-dialog */ \"(app-pages-browser)/./src/components/ui/alert-dialog.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./src/components/ui/textarea.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _lib_ai_service__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! @/lib/ai-service */ \"(app-pages-browser)/./src/lib/ai-service.ts\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/loader-circle.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/sparkles.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/save.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Loader2,Save,Sparkles,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _contexts_i18n__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! @/contexts/i18n */ \"(app-pages-browser)/./src/contexts/i18n.tsx\");\n/* __next_internal_client_entry_do_not_use__ NoteEditor auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nfunction NoteEditor(param) {\n    let { note, onSave, onDelete } = param;\n    _s();\n    const { t } = (0,_contexts_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18n)();\n    const [title, setTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [content, setContent] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [tags, setTags] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isEditing, setIsEditing] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast)();\n    const [aiLoading, setAiLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedText, setSelectedText] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [aiResult, setAiResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isResultDialogVisible, setIsResultDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [isExpandDialogVisible, setIsExpandDialogVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [expandPrompt, setExpandPrompt] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [suggestedTitle, setSuggestedTitle] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(\"\");\n    const [isTitleAlertVisible, setIsTitleAlertVisible] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const editorRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // 選取文字處理將由 CherryMarkdownEditor 元件處理\n    const handleSelectionChange = (selectedText)=>{\n        setSelectedText(selectedText);\n    };\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"NoteEditor.useEffect\": ()=>{\n            if (note) {\n                setTitle(note.title);\n                setContent(note.content);\n                setTags(Array.isArray(note.tags) ? note.tags : []);\n                setIsEditing(false);\n            } else {\n                setTitle(\"\");\n                setContent(\"\");\n                setTags([]);\n                setIsEditing(true);\n            }\n        }\n    }[\"NoteEditor.useEffect\"], [\n        note\n    ]);\n    const handleSave = ()=>{\n        onSave((note === null || note === void 0 ? void 0 : note.id) || null, title, content, tags);\n        setIsEditing(false);\n    };\n    const handleDelete = ()=>{\n        if (note) {\n            onDelete(note.id);\n        }\n    };\n    const handleAiAction = async (action, payload)=>{\n        setAiLoading(action);\n        try {\n            let result;\n            switch(action){\n                case \"summarize\":\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting summarize streaming\");\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.summarizeStream)(content, (chunk)=>{\n                        console.log(\"Received summarize chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Summarize streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"generateTitle\":\n                    let titleResult = \"\";\n                    console.log(\"Starting title generation streaming\");\n                    await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.generateTitleStream)(content, (chunk)=>{\n                        console.log(\"Received title chunk:\", chunk);\n                        titleResult += chunk;\n                    }).catch((error)=>{\n                        console.error(\"Title streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                        throw error;\n                    });\n                    setSuggestedTitle(titleResult.trim());\n                    setIsTitleAlertVisible(true);\n                    break;\n                case \"polish\":\n                    if (!selectedText) {\n                        toast({\n                            title: t(\"editor.selection_error\"),\n                            description: t(\"editor.select_text_first\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting polish streaming\");\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.polishStream)(selectedText, (chunk)=>{\n                        console.log(\"Received polish chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Polish streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"expand\":\n                    if (!payload || !payload.trim()) {\n                        toast({\n                            title: t(\"editor.input_error\"),\n                            description: t(\"editor.enter_valid_prompt\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsExpandDialogVisible(false);\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting expand streaming with payload:\", payload);\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.expandContentStream)(payload, (chunk)=>{\n                        console.log(\"Received expand chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"Expand streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"changeTone\":\n                    if (!selectedText) {\n                        toast({\n                            title: t(\"editor.selection_error\"),\n                            description: t(\"editor.select_tone_text_first\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    setAiResult(\"\"); // 清空之前的結果\n                    setIsResultDialogVisible(true);\n                    // 立即打開 result dialog 並開始 streaming\n                    console.log(\"Starting changeTone streaming with tone:\", payload);\n                    (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.changeToneStream)(selectedText, payload, (chunk)=>{\n                        console.log(\"Received changeTone chunk:\", chunk);\n                        setAiResult((prev)=>prev + chunk);\n                    }).catch((error)=>{\n                        console.error(\"ChangeTone streaming error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    });\n                    break;\n                case \"generateTags\":\n                    if (!content.trim()) {\n                        toast({\n                            title: t(\"editor.input_error\"),\n                            description: t(\"editor.content_required_for_tags\"),\n                            variant: \"destructive\"\n                        });\n                        return;\n                    }\n                    try {\n                        console.log(\"Starting tag generation\");\n                        const newTags = await (0,_lib_ai_service__WEBPACK_IMPORTED_MODULE_13__.generateTags)(content);\n                        const uniqueTags = [\n                            ...new Set([\n                                ...tags,\n                                ...newTags\n                            ])\n                        ];\n                        setTags(uniqueTags);\n                        toast({\n                            title: t(\"editor.tags_generated\"),\n                            description: t(\"editor.tags_added_successfully\"),\n                            variant: \"default\"\n                        });\n                    } catch (error) {\n                        console.error(\"Tag generation error:\", error);\n                        toast({\n                            title: t(\"editor.ai_operation_failed\"),\n                            description: error instanceof Error ? error.message : t(\"editor.unknown_error\"),\n                            variant: \"destructive\"\n                        });\n                    }\n                    break;\n            }\n        } catch (error) {\n            const message = error instanceof Error ? error.message : t(\"editor.unknown_error\");\n            toast({\n                title: t(\"editor.ai_operation_failed\"),\n                description: message,\n                variant: \"destructive\"\n            });\n        } finally{\n            setAiLoading(false);\n        }\n    };\n    const handleInsertResult = ()=>{\n        setContent(\"\".concat(content, \"\\n\\n---\\n\\n\").concat(aiResult));\n        setIsResultDialogVisible(false);\n    };\n    const handleReplaceResult = ()=>{\n        setContent(content.replace(selectedText, aiResult));\n        setIsResultDialogVisible(false);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col h-full\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex-shrink-0 pb-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-between\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                            value: title,\n                                            onChange: (e)=>setTitle(e.target.value),\n                                            placeholder: t(\"editor.title_placeholder\"),\n                                            className: \"h-auto max-w-2xl px-0 py-1 text-xl font-semibold bg-transparent border-none shadow-none focus-visible:ring-0\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 313,\n                                            columnNumber: 17\n                                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"text-xl font-semibold\",\n                                            children: title\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 320,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                        lineNumber: 311,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center space-x-2\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenu, {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuTrigger, {\n                                                        asChild: true,\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            variant: \"outline\",\n                                                            size: \"sm\",\n                                                            disabled: !!aiLoading,\n                                                            children: [\n                                                                aiLoading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2 animate-spin\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                    lineNumber: 328,\n                                                                    columnNumber: 23\n                                                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"w-4 h-4 mr-2\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                    lineNumber: 330,\n                                                                    columnNumber: 23\n                                                                }, this),\n                                                                t(\"editor.ai_tools\")\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 325,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuContent, {\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"summarize\"),\n                                                                children: t(\"editor.summarize\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 336,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"generateTitle\"),\n                                                                children: t(\"editor.generate_title\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 339,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 344,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"polish\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.polish_selected\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 345,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>setIsExpandDialogVisible(true),\n                                                                children: t(\"editor.expand_content\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 351,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"changeTone\", \"專業\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.change_tone_professional\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 356,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"changeTone\", \"休閒\"),\n                                                                disabled: !selectedText,\n                                                                children: t(\"editor.change_tone_casual\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 362,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuSeparator, {}, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 368,\n                                                                columnNumber: 19\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dropdown_menu__WEBPACK_IMPORTED_MODULE_8__.DropdownMenuItem, {\n                                                                onClick: ()=>handleAiAction(\"generateTags\"),\n                                                                disabled: !content.trim() || !!aiLoading,\n                                                                children: t(\"editor.generate_tags\")\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                                lineNumber: 369,\n                                                                columnNumber: 19\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 335,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 324,\n                                                columnNumber: 15\n                                            }, this),\n                                            isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: handleSave,\n                                                size: \"sm\",\n                                                variant: \"secondary\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 380,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.save\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 379,\n                                                columnNumber: 17\n                                            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                onClick: ()=>setIsEditing(true),\n                                                size: \"sm\",\n                                                variant: \"outline\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 389,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.edit\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 384,\n                                                columnNumber: 17\n                                            }, this),\n                                            note && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                variant: \"destructive\",\n                                                onClick: handleDelete,\n                                                size: \"sm\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                        className: \"w-4 h-4 mr-2\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                        lineNumber: 395,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    t(\"editor.delete\")\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 394,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                        lineNumber: 323,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 310,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"mt-2\",\n                                children: isEditing ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_TagInput__WEBPACK_IMPORTED_MODULE_5__.TagInput, {\n                                    value: tags,\n                                    onChange: setTags,\n                                    placeholder: t(\"editor.tags_placeholder\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 403,\n                                    columnNumber: 15\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-wrap gap-2\",\n                                    children: Array.isArray(tags) && tags.map((tag)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-3 py-1 text-sm rounded-full bg-secondary text-secondary-foreground\",\n                                            children: tag\n                                        }, tag, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                            lineNumber: 412,\n                                            columnNumber: 21\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 401,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                        lineNumber: 309,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex flex-col flex-1 min-h-0 pt-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative w-full h-full overflow-hidden\",\n                            style: {\n                                contain: \"layout style\",\n                                isolation: \"isolate\",\n                                position: \"relative\",\n                                height: \"100%\",\n                                maxHeight: \"100%\",\n                                overflow: \"hidden\"\n                            },\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_CherryMarkdownEditor__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                ref: editorRef,\n                                value: content,\n                                onChange: (value)=>setContent(value || \"\"),\n                                preview: isEditing ? \"edit\" : \"preview\",\n                                hideToolbar: !isEditing,\n                                className: \"h-full min-h-[400px]\",\n                                onSelectionChange: handleSelectionChange\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 424,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 308,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isResultDialogVisible,\n                onOpenChange: setIsResultDialogVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: t(\"editor.ai_result_title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 455,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 454,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 my-4 border rounded-md bg-muted max-h-[70vh] overflow-auto\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_uiw_react_markdown_preview__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                source: aiResult\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 458,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 457,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsResultDialogVisible(false),\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 461,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleInsertResult,\n                                    variant: \"secondary\",\n                                    children: t(\"editor.insert_at_end\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 467,\n                                    columnNumber: 13\n                                }, this),\n                                selectedText && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: handleReplaceResult,\n                                    variant: \"secondary\",\n                                    children: t(\"editor.replace_selected\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 469,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 460,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 453,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 449,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.Dialog, {\n                open: isExpandDialogVisible,\n                onOpenChange: setIsExpandDialogVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogHeader, {\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogTitle, {\n                                children: t(\"editor.expand_content_title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                lineNumber: 482,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 481,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_11__.Textarea, {\n                            placeholder: t(\"editor.expand_placeholder\"),\n                            value: expandPrompt,\n                            onChange: (e)=>setExpandPrompt(e.target.value),\n                            disabled: aiLoading === \"expand\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 484,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_dialog__WEBPACK_IMPORTED_MODULE_9__.DialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: ()=>setIsExpandDialogVisible(false),\n                                    disabled: aiLoading === \"expand\",\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 491,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    onClick: ()=>handleAiAction(\"expand\", expandPrompt),\n                                    variant: \"secondary\",\n                                    disabled: !expandPrompt.trim() || aiLoading === \"expand\",\n                                    children: aiLoading === \"expand\" ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Loader2_Save_Sparkles_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                className: \"w-4 h-4 mr-2 animate-spin\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                                lineNumber: 505,\n                                                columnNumber: 19\n                                            }, this),\n                                            t(\"editor.generating\")\n                                        ]\n                                    }, void 0, true) : t(\"editor.generate\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 498,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 490,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 480,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 476,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialog, {\n                open: isTitleAlertVisible,\n                onOpenChange: setIsTitleAlertVisible,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogContent, {\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogHeader, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogTitle, {\n                                    children: t(\"editor.suggested_title\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 523,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogDescription, {\n                                    children: t(\"editor.suggested_title_desc\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 524,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 522,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 font-semibold border rounded-md bg-muted\",\n                            children: suggestedTitle\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 528,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogFooter, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogCancel, {\n                                    children: t(\"editor.cancel\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 532,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_alert_dialog__WEBPACK_IMPORTED_MODULE_10__.AlertDialogAction, {\n                                    onClick: ()=>{\n                                        setTitle(suggestedTitle);\n                                        setIsTitleAlertVisible(false);\n                                    },\n                                    className: \"text-white bg-black hover:bg-gray-800\",\n                                    children: t(\"editor.replace\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                                    lineNumber: 533,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                            lineNumber: 531,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                    lineNumber: 521,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\NoteEditor.tsx\",\n                lineNumber: 517,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true);\n}\n_s(NoteEditor, \"ToC0ksbA5+BVBrsULPCDoVCYPX8=\", false, function() {\n    return [\n        _contexts_i18n__WEBPACK_IMPORTED_MODULE_14__.useI18n,\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_12__.useToast\n    ];\n});\n_c = NoteEditor;\nvar _c;\n$RefreshReg$(_c, \"NoteEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/NoteEditor.tsx\n"));

/***/ })

});