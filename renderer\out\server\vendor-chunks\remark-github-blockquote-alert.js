"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/remark-github-blockquote-alert";
exports.ids = ["vendor-chunks/remark-github-blockquote-alert"];
exports.modules = {

/***/ "(ssr)/./node_modules/remark-github-blockquote-alert/lib/index.js":
/*!******************************************************************!*\
  !*** ./node_modules/remark-github-blockquote-alert/lib/index.js ***!
  \******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getAlertIcon: () => (/* binding */ getAlertIcon),\n/* harmony export */   remarkAlert: () => (/* binding */ remarkAlert)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\nconst alertRegex = /^\\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)\\]/i;\nconst alertLegacyRegex = /^\\[!(NOTE|TIP|IMPORTANT|WARNING|CAUTION)(\\/.*)?\\]/i;\n/**\n * Alerts are a Markdown extension based on the blockquote syntax that you can use to emphasize critical information.\n * On GitHub, they are displayed with distinctive colors and icons to indicate the significance of the content.\n * https://docs.github.com/en/get-started/writing-on-github/getting-started-with-writing-and-formatting-on-github/basic-writing-and-formatting-syntax#alerts\n */\nconst remarkAlert = ({ legacyTitle = false, tagName = \"div\" } = {}) => {\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, \"blockquote\", (node, index, parent) => {\n            let alertType = '';\n            let title = '';\n            let isNext = true;\n            let child = node.children.map((item) => {\n                if (isNext && item.type === \"paragraph\") {\n                    const firstNode = item.children[0];\n                    const text = firstNode.type === 'text' ? firstNode.value : '';\n                    const reg = legacyTitle ? alertLegacyRegex : alertRegex;\n                    const match = text.match(reg);\n                    if (match) {\n                        isNext = false;\n                        alertType = match[1].toLocaleLowerCase();\n                        title = legacyTitle ? match[2] || alertType.toLocaleUpperCase() : alertType.toLocaleUpperCase();\n                        if (text.includes('\\n')) {\n                            item.children[0] = {\n                                type: 'text',\n                                value: text.replace(reg, '').replace(/^\\n+/, ''),\n                            };\n                        }\n                        if (!text.includes('\\n')) {\n                            const itemChild = [];\n                            item.children.forEach((item, idx) => {\n                                if (idx == 0)\n                                    return;\n                                if (idx == 1 && item.type === 'break') {\n                                    return;\n                                }\n                                itemChild.push(item);\n                            });\n                            item.children = [...itemChild];\n                        }\n                    }\n                }\n                return item;\n            });\n            if (!!alertType) {\n                node.data = {\n                    hName: tagName,\n                    hProperties: {\n                        class: `markdown-alert markdown-alert-${alertType}`,\n                        dir: 'auto'\n                    },\n                };\n                child.unshift({\n                    type: \"paragraph\",\n                    children: [\n                        getAlertIcon(alertType),\n                        {\n                            type: \"text\",\n                            value: title.replace(/^\\//, ''),\n                        }\n                    ],\n                    data: {\n                        hProperties: {\n                            class: \"markdown-alert-title\",\n                            dir: \"auto\"\n                        }\n                    }\n                });\n            }\n            node.children = [...child];\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (remarkAlert);\nfunction getAlertIcon(type) {\n    let pathD = pathData[type] ?? '';\n    return {\n        type: \"emphasis\",\n        data: {\n            hName: \"svg\",\n            hProperties: {\n                class: \"octicon\",\n                viewBox: '0 0 16 16',\n                width: '16',\n                height: '16',\n                ariaHidden: 'true',\n            },\n        },\n        children: [\n            {\n                type: \"emphasis\",\n                data: {\n                    hName: \"path\",\n                    hProperties: {\n                        d: pathD\n                    }\n                },\n                children: []\n            }\n        ]\n    };\n}\nconst pathData = {\n    note: 'M0 8a8 8 0 1 1 16 0A8 8 0 0 1 0 8Zm8-6.5a6.5 6.5 0 1 0 0 13 6.5 6.5 0 0 0 0-13ZM6.5 7.75A.75.75 0 0 1 7.25 7h1a.75.75 0 0 1 .75.75v2.75h.25a.75.75 0 0 1 0 1.5h-2a.75.75 0 0 1 0-1.5h.25v-2h-.25a.75.75 0 0 1-.75-.75ZM8 6a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z',\n    tip: 'M8 1.5c-2.363 0-4 1.69-4 3.75 0 .984.424 1.625.984 2.304l.214.253c.223.264.47.556.673.848.284.411.537.896.621 1.49a.75.75 0 0 1-1.484.211c-.04-.282-.163-.547-.37-.847a8.456 8.456 0 0 0-.542-.68c-.084-.1-.173-.205-.268-.32C3.201 7.75 2.5 6.766 2.5 5.25 2.5 2.31 4.863 0 8 0s5.5 2.31 5.5 5.25c0 1.516-.701 2.5-1.328 3.259-.095.115-.184.22-.268.319-.207.245-.383.453-.541.681-.208.3-.33.565-.37.847a.751.751 0 0 1-1.485-.212c.084-.593.337-1.078.621-1.489.203-.292.45-.584.673-.848.075-.088.147-.173.213-.253.561-.679.985-1.32.985-2.304 0-2.06-1.637-3.75-4-3.75ZM5.75 12h4.5a.75.75 0 0 1 0 1.5h-4.5a.75.75 0 0 1 0-1.5ZM6 15.25a.75.75 0 0 1 .75-.75h2.5a.75.75 0 0 1 0 1.5h-2.5a.75.75 0 0 1-.75-.75Z',\n    important: 'M0 1.75C0 .784.784 0 1.75 0h12.5C15.216 0 16 .784 16 1.75v9.5A1.75 1.75 0 0 1 14.25 13H8.06l-2.573 2.573A1.458 1.458 0 0 1 3 14.543V13H1.75A1.75 1.75 0 0 1 0 11.25Zm1.75-.25a.25.25 0 0 0-.25.25v9.5c0 .*************.25h2a.75.75 0 0 1 .75.75v2.19l2.72-2.72a.749.749 0 0 1 .53-.22h6.5a.25.25 0 0 0 .25-.25v-9.5a.25.25 0 0 0-.25-.25Zm7 2.25v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 9a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',\n    warning: 'M6.457 1.047c.659-1.234 2.427-1.234 3.086 0l6.082 11.378A1.75 1.75 0 0 1 14.082 15H1.918a1.75 1.75 0 0 1-1.543-2.575Zm1.763.707a.25.25 0 0 0-.44 0L1.698 13.132a.25.25 0 0 0 .22.368h12.164a.25.25 0 0 0 .22-.368Zm.53 3.996v2.5a.75.75 0 0 1-1.5 0v-2.5a.75.75 0 0 1 1.5 0ZM9 11a1 1 0 1 1-2 0 1 1 0 0 1 2 0Z',\n    caution: 'M4.47.22A.749.749 0 0 1 5 0h6c.199 0 .389.079.53.22l4.25 4.25c.141.14.22.331.22.53v6a.749.749 0 0 1-.22.53l-4.25 4.25A.749.749 0 0 1 11 16H5a.749.749 0 0 1-.53-.22L.22 11.53A.749.749 0 0 1 0 11V5c0-.199.079-.389.22-.53Zm.84 1.28L1.5 5.31v5.38l3.81 3.81h5.38l3.81-3.81V5.31L10.69 1.5ZM8 4a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 8 4Zm0 8a1 1 0 1 1 0-2 1 1 0 0 1 0 2Z',\n};\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/remark-github-blockquote-alert/lib/index.js\n");

/***/ })

};
;