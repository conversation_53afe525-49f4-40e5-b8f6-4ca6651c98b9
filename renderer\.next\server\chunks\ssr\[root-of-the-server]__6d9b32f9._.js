module.exports=[89578,a=>{a.v({className:"geist_a71539c9-module__T19VSG__className",variable:"geist_a71539c9-module__T19VSG__variable"})},35214,a=>{a.v({className:"geist_mono_8d43a2aa-module__8Li5zG__className",variable:"geist_mono_8d43a2aa-module__8Li5zG__variable"})},8431,a=>{"use strict";a.s(["ThemeProvider",()=>b]);let b=(0,a.i(15502).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/components/theme-provider.tsx <module evaluation>","ThemeProvider")},7681,a=>{"use strict";a.s(["ThemeProvider",()=>b]);let b=(0,a.i(15502).registerClientReference)(function(){throw Error("Attempted to call ThemeProvider() from the server but ThemeProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/components/theme-provider.tsx","ThemeProvider")},83112,a=>{"use strict";a.i(8431);var b=a.i(7681);a.n(b)},96790,a=>{"use strict";a.s(["I18nProvider",()=>c,"useI18n",()=>d]);var b=a.i(15502);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/contexts/i18n.tsx <module evaluation>","I18nProvider"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/contexts/i18n.tsx <module evaluation>","useI18n")},13151,a=>{"use strict";a.s(["I18nProvider",()=>c,"useI18n",()=>d]);var b=a.i(15502);let c=(0,b.registerClientReference)(function(){throw Error("Attempted to call I18nProvider() from the server but I18nProvider is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/contexts/i18n.tsx","I18nProvider"),d=(0,b.registerClientReference)(function(){throw Error("Attempted to call useI18n() from the server but useI18n is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/contexts/i18n.tsx","useI18n")},51770,a=>{"use strict";a.i(96790);var b=a.i(13151);a.n(b)},92674,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(15502).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/components/ui/toaster.tsx <module evaluation>","Toaster")},82017,a=>{"use strict";a.s(["Toaster",()=>b]);let b=(0,a.i(15502).registerClientReference)(function(){throw Error("Attempted to call Toaster() from the server but Toaster is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"[project]/renderer/src/components/ui/toaster.tsx","Toaster")},39256,a=>{"use strict";a.i(92674);var b=a.i(82017);a.n(b)},72870,a=>{"use strict";a.s(["default",()=>k,"metadata",()=>j],72870);var b=a.i(10031),c=a.i(89578);let d={className:c.default.className,style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"}};null!=c.default.variable&&(d.variable=c.default.variable);var e=a.i(35214);let f={className:e.default.className,style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"}};null!=e.default.variable&&(f.variable=e.default.variable);var g=a.i(83112),h=a.i(51770),i=a.i(39256);let j={title:"MyNote",description:"A simple note-taking app"};function k({children:a}){return(0,b.jsx)("html",{lang:"en",suppressHydrationWarning:!0,children:(0,b.jsx)("body",{className:`${d.variable} ${f.variable} min-h-screen bg-background font-sans antialiased`,children:(0,b.jsxs)(g.ThemeProvider,{attribute:"class",defaultTheme:"system",enableSystem:!0,disableTransitionOnChange:!0,children:[(0,b.jsx)(h.I18nProvider,{children:a}),(0,b.jsx)(i.Toaster,{})]})})})}}];

//# sourceMappingURL=%5Broot-of-the-server%5D__6d9b32f9._.js.map