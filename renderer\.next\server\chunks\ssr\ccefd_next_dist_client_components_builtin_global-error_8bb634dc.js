module.exports=[94371,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(15502);a.n(d("[project]/renderer/node_modules/next/dist/client/components/builtin/global-error.js <module evaluation>"))},60745,(a,b,c)=>{let{createClientModuleProxy:d}=a.r(15502);a.n(d("[project]/renderer/node_modules/next/dist/client/components/builtin/global-error.js"))},48219,a=>{"use strict";a.i(94371);var b=a.i(60745);a.n(b)}];

//# sourceMappingURL=ccefd_next_dist_client_components_builtin_global-error_8bb634dc.js.map