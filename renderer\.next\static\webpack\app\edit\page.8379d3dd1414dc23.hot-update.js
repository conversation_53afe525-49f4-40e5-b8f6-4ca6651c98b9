"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Fallback to @uiw/react-md-editor if Cherry fails\nconst MDEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_uiw_react-md-editor_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @uiw/react-md-editor */ \"(app-pages-browser)/./node_modules/@uiw/react-md-editor/esm/index.js\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\CherryMarkdownEditor.tsx -> \" + \"@uiw/react-md-editor\"\n        ]\n    },\n    ssr: false\n});\n_c = MDEditor;\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cherryLoadFailed, setCherryLoadFailed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 設置超時，如果 Cherry 載入時間過長則使用備用編輯器\n            const timeout = setTimeout({\n                \"CherryMarkdownEditor.useEffect.timeout\": ()=>{\n                    console.log(\"Cherry loading timeout, using fallback editor\");\n                    setCherryLoadFailed(true);\n                }\n            }[\"CherryMarkdownEditor.useEffect.timeout\"], 5000); // 5秒超時\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                            clearTimeout(timeout); // 成功載入，清除超時\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                            setCherryLoadFailed(true);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                        // Set failed state to use fallback editor\n                        setCherryLoadFailed(true);\n                        clearTimeout(timeout);\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    clearTimeout(timeout);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        console.log(\"Setting Cherry element styles\");\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                        cherryElement.style.width = '100%';\n                                        cherryElement.style.maxWidth = '100%';\n                                        cherryElement.style.contain = 'layout style';\n                                        cherryElement.style.display = 'flex';\n                                        cherryElement.style.flexDirection = preview === 'edit' ? 'column' : preview === 'preview' ? 'column' : 'row';\n                                        // Also fix child elements\n                                        const editor = cherryElement.querySelector('.cherry-editor');\n                                        const previewer = cherryElement.querySelector('.cherry-previewer');\n                                        if (editor) {\n                                            editor.style.position = 'relative';\n                                            editor.style.overflow = 'hidden';\n                                            editor.style.maxHeight = '100%';\n                                            editor.style.maxWidth = '100%';\n                                        }\n                                        if (previewer) {\n                                            previewer.style.position = 'relative';\n                                            previewer.style.overflow = 'hidden';\n                                            previewer.style.maxHeight = '100%';\n                                            previewer.style.maxWidth = '100%';\n                                        }\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 319,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 307,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，使用 MDEditor 作為備用編輯器\n    if (!CherryClass) {\n        if (cherryLoadFailed) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cherry-markdown-editor \".concat(className),\n                style: {\n                    height: \"100%\",\n                    width: \"100%\",\n                    maxHeight: \"100%\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    position: \"relative\",\n                    overflow: \"hidden\",\n                    contain: \"layout style\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MDEditor, {\n                    value: value,\n                    onChange: (val)=>onChange && onChange(val || \"\"),\n                    preview: preview === \"preview\" ? \"preview\" : preview === \"edit\" ? \"edit\" : \"live\",\n                    hideToolbar: hideToolbar,\n                    \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\",\n                    height: \"100%\",\n                    style: {\n                        height: \"100%\",\n                        maxHeight: \"100%\",\n                        overflow: \"hidden\",\n                        position: \"relative\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 341,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 328,\n                columnNumber: 11\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"載入編輯器中...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 371,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"正在載入 Cherry Markdown 編輯器...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                        lineNumber: 375,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 374,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 360,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 382,\n        columnNumber: 7\n    }, undefined);\n}, \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c2 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MDEditor\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c2, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});