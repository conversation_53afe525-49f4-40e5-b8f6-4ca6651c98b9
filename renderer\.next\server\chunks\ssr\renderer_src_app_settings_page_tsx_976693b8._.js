module.exports=[66879,a=>{"use strict";a.s(["default",()=>bm],66879);var b=a.i(28386),c=a.i(54436),d=a.i(11105),e=a.i(47346),f=a.i(23e3),g=a.i(68389),h=a.i(3332),i=a.i(41930),j=a.i(46563),k=a.i(27444),l=a.i(43503),m=a.i(64161),n=a.i(94120),o=a.i(42527),p=a.i(33616),q=a.i(56217),r=a.i(28432),s=a.i(9935),t=a.i(14053),u=a.i(73490),v=a.i(64296),w=a.i(62705),x=a.i(40686),y=a.i(57495),z=a.i(98445),A=a.i(9851),B=a.i(64249),C=a.i(57703),D=a.i(85502),E=a.i(6022),F=[" ","Enter","ArrowUp","ArrowDown"],G=[" ","Enter"],H="Select",[I,J,K]=(0,n.createCollection)(H),[L,M]=(0,p.createContextScope)(H,[K,v.createPopperScope]),N=(0,v.createPopperScope)(),[O,P]=L(H),[Q,R]=L(H),S=a=>{let{__scopeSelect:d,children:e,open:f,defaultOpen:g,onOpenChange:h,value:i,defaultValue:j,onValueChange:k,dir:l,name:m,autoComplete:n,disabled:o,required:p,form:r}=a,s=N(d),[t,w]=c.useState(null),[x,y]=c.useState(null),[z,B]=c.useState(!1),C=(0,q.useDirection)(l),[D,E]=(0,A.useControllableState)({prop:f,defaultProp:g??!1,onChange:h,caller:H}),[F,G]=(0,A.useControllableState)({prop:i,defaultProp:j,onChange:k,caller:H}),J=c.useRef(null),K=!t||r||!!t.closest("form"),[L,M]=c.useState(new Set),P=Array.from(L).map(a=>a.props.value).join(";");return(0,b.jsx)(v.Root,{...s,children:(0,b.jsxs)(O,{required:p,scope:d,trigger:t,onTriggerChange:w,valueNode:x,onValueNodeChange:y,valueNodeHasChildren:z,onValueNodeHasChildrenChange:B,contentId:(0,u.useId)(),value:F,onValueChange:G,open:D,onOpenChange:E,dir:C,triggerPointerDownPosRef:J,disabled:o,children:[(0,b.jsx)(I.Provider,{scope:d,children:(0,b.jsx)(Q,{scope:a.__scopeSelect,onNativeOptionAdd:c.useCallback(a=>{M(b=>new Set(b).add(a))},[]),onNativeOptionRemove:c.useCallback(a=>{M(b=>{let c=new Set(b);return c.delete(a),c})},[]),children:e})}),K?(0,b.jsxs)(aD,{"aria-hidden":!0,required:p,tabIndex:-1,name:m,autoComplete:n,value:F,onChange:a=>G(a.target.value),disabled:o,form:r,children:[void 0===F?(0,b.jsx)("option",{value:""}):null,Array.from(L)]},P):null]})})};S.displayName=H;var T="SelectTrigger",U=c.forwardRef((a,d)=>{let{__scopeSelect:e,disabled:f=!1,...g}=a,h=N(e),i=P(T,e),j=i.disabled||f,k=(0,o.useComposedRefs)(d,i.onTriggerChange),l=J(e),n=c.useRef("touch"),[p,q,r]=aF(a=>{let b=l().filter(a=>!a.disabled),c=b.find(a=>a.value===i.value),d=aG(b,a,c);void 0!==d&&i.onValueChange(d.value)}),s=a=>{j||(i.onOpenChange(!0),r()),a&&(i.triggerPointerDownPosRef.current={x:Math.round(a.pageX),y:Math.round(a.pageY)})};return(0,b.jsx)(v.Anchor,{asChild:!0,...h,children:(0,b.jsx)(x.Primitive.button,{type:"button",role:"combobox","aria-controls":i.contentId,"aria-expanded":i.open,"aria-required":i.required,"aria-autocomplete":"none",dir:i.dir,"data-state":i.open?"open":"closed",disabled:j,"data-disabled":j?"":void 0,"data-placeholder":aE(i.value)?"":void 0,...g,ref:k,onClick:(0,m.composeEventHandlers)(g.onClick,a=>{a.currentTarget.focus(),"mouse"!==n.current&&s(a)}),onPointerDown:(0,m.composeEventHandlers)(g.onPointerDown,a=>{n.current=a.pointerType;let b=a.target;b.hasPointerCapture(a.pointerId)&&b.releasePointerCapture(a.pointerId),0===a.button&&!1===a.ctrlKey&&"mouse"===a.pointerType&&(s(a),a.preventDefault())}),onKeyDown:(0,m.composeEventHandlers)(g.onKeyDown,a=>{let b=""!==p.current;a.ctrlKey||a.altKey||a.metaKey||1!==a.key.length||q(a.key),(!b||" "!==a.key)&&F.includes(a.key)&&(s(),a.preventDefault())})})})});U.displayName=T;var V="SelectValue",W=c.forwardRef((a,c)=>{let{__scopeSelect:d,className:e,style:f,children:g,placeholder:h="",...i}=a,j=P(V,d),{onValueNodeHasChildrenChange:k}=j,l=void 0!==g,m=(0,o.useComposedRefs)(c,j.onValueNodeChange);return(0,B.useLayoutEffect)(()=>{k(l)},[k,l]),(0,b.jsx)(x.Primitive.span,{...i,ref:m,style:{pointerEvents:"none"},children:aE(j.value)?(0,b.jsx)(b.Fragment,{children:h}):g})});W.displayName=V;var X=c.forwardRef((a,c)=>{let{__scopeSelect:d,children:e,...f}=a;return(0,b.jsx)(x.Primitive.span,{"aria-hidden":!0,...f,ref:c,children:e||"▼"})});X.displayName="SelectIcon";var Y=a=>(0,b.jsx)(w.Portal,{asChild:!0,...a});Y.displayName="SelectPortal";var Z="SelectContent",$=c.forwardRef((a,d)=>{let e=P(Z,a.__scopeSelect),[f,g]=c.useState();return((0,B.useLayoutEffect)(()=>{g(new DocumentFragment)},[]),e.open)?(0,b.jsx)(ac,{...a,ref:d}):f?k.createPortal((0,b.jsx)(_,{scope:a.__scopeSelect,children:(0,b.jsx)(I.Slot,{scope:a.__scopeSelect,children:(0,b.jsx)("div",{children:a.children})})}),f):null});$.displayName=Z;var[_,aa]=L(Z),ab=(0,y.createSlot)("SelectContent.RemoveScroll"),ac=c.forwardRef((a,d)=>{let{__scopeSelect:e,position:f="item-aligned",onCloseAutoFocus:g,onEscapeKeyDown:h,onPointerDownOutside:i,side:j,sideOffset:k,align:l,alignOffset:n,arrowPadding:p,collisionBoundary:q,collisionPadding:u,sticky:v,hideWhenDetached:w,avoidCollisions:x,...y}=a,z=P(Z,e),[A,B]=c.useState(null),[C,F]=c.useState(null),G=(0,o.useComposedRefs)(d,a=>B(a)),[H,I]=c.useState(null),[K,L]=c.useState(null),M=J(e),[N,O]=c.useState(!1),Q=c.useRef(!1);c.useEffect(()=>{if(A)return(0,D.hideOthers)(A)},[A]),(0,s.useFocusGuards)();let R=c.useCallback(a=>{let[b,...c]=M().map(a=>a.ref.current),[d]=c.slice(-1),e=document.activeElement;for(let c of a)if(c===e||(c?.scrollIntoView({block:"nearest"}),c===b&&C&&(C.scrollTop=0),c===d&&C&&(C.scrollTop=C.scrollHeight),c?.focus(),document.activeElement!==e))return},[M,C]),S=c.useCallback(()=>R([H,A]),[R,H,A]);c.useEffect(()=>{N&&S()},[N,S]);let{onOpenChange:T,triggerPointerDownPosRef:U}=z;c.useEffect(()=>{if(A){let a={x:0,y:0},b=b=>{a={x:Math.abs(Math.round(b.pageX)-(U.current?.x??0)),y:Math.abs(Math.round(b.pageY)-(U.current?.y??0))}},c=c=>{a.x<=10&&a.y<=10?c.preventDefault():A.contains(c.target)||T(!1),document.removeEventListener("pointermove",b),U.current=null};return null!==U.current&&(document.addEventListener("pointermove",b),document.addEventListener("pointerup",c,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",b),document.removeEventListener("pointerup",c,{capture:!0})}}},[A,T,U]),c.useEffect(()=>{let a=()=>T(!1);return window.addEventListener("blur",a),window.addEventListener("resize",a),()=>{window.removeEventListener("blur",a),window.removeEventListener("resize",a)}},[T]);let[V,W]=aF(a=>{let b=M().filter(a=>!a.disabled),c=b.find(a=>a.ref.current===document.activeElement),d=aG(b,a,c);d&&setTimeout(()=>d.ref.current.focus())}),X=c.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==z.value&&z.value===b||d)&&(I(a),d&&(Q.current=!0))},[z.value]),Y=c.useCallback(()=>A?.focus(),[A]),$=c.useCallback((a,b,c)=>{let d=!Q.current&&!c;(void 0!==z.value&&z.value===b||d)&&L(a)},[z.value]),aa="popper"===f?ae:ad,ac=aa===ae?{side:j,sideOffset:k,align:l,alignOffset:n,arrowPadding:p,collisionBoundary:q,collisionPadding:u,sticky:v,hideWhenDetached:w,avoidCollisions:x}:{};return(0,b.jsx)(_,{scope:e,content:A,viewport:C,onViewportChange:F,itemRefCallback:X,selectedItem:H,onItemLeave:Y,itemTextRefCallback:$,focusSelectedItem:S,selectedItemText:K,position:f,isPositioned:N,searchRef:V,children:(0,b.jsx)(E.RemoveScroll,{as:ab,allowPinchZoom:!0,children:(0,b.jsx)(t.FocusScope,{asChild:!0,trapped:z.open,onMountAutoFocus:a=>{a.preventDefault()},onUnmountAutoFocus:(0,m.composeEventHandlers)(g,a=>{z.trigger?.focus({preventScroll:!0}),a.preventDefault()}),children:(0,b.jsx)(r.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:h,onPointerDownOutside:i,onFocusOutside:a=>a.preventDefault(),onDismiss:()=>z.onOpenChange(!1),children:(0,b.jsx)(aa,{role:"listbox",id:z.contentId,"data-state":z.open?"open":"closed",dir:z.dir,onContextMenu:a=>a.preventDefault(),...y,...ac,onPlaced:()=>O(!0),ref:G,style:{display:"flex",flexDirection:"column",outline:"none",...y.style},onKeyDown:(0,m.composeEventHandlers)(y.onKeyDown,a=>{let b=a.ctrlKey||a.altKey||a.metaKey;if("Tab"===a.key&&a.preventDefault(),b||1!==a.key.length||W(a.key),["ArrowUp","ArrowDown","Home","End"].includes(a.key)){let b=M().filter(a=>!a.disabled).map(a=>a.ref.current);if(["ArrowUp","End"].includes(a.key)&&(b=b.slice().reverse()),["ArrowUp","ArrowDown"].includes(a.key)){let c=a.target,d=b.indexOf(c);b=b.slice(d+1)}setTimeout(()=>R(b)),a.preventDefault()}})})})})})})});ac.displayName="SelectContentImpl";var ad=c.forwardRef((a,d)=>{let{__scopeSelect:e,onPlaced:f,...g}=a,h=P(Z,e),i=aa(Z,e),[j,k]=c.useState(null),[m,n]=c.useState(null),p=(0,o.useComposedRefs)(d,a=>n(a)),q=J(e),r=c.useRef(!1),s=c.useRef(!0),{viewport:t,selectedItem:u,selectedItemText:v,focusSelectedItem:w}=i,y=c.useCallback(()=>{if(h.trigger&&h.valueNode&&j&&m&&t&&u&&v){let a=h.trigger.getBoundingClientRect(),b=m.getBoundingClientRect(),c=h.valueNode.getBoundingClientRect(),d=v.getBoundingClientRect();if("rtl"!==h.dir){let e=d.left-b.left,f=c.left-e,g=a.left-f,h=a.width+g,i=Math.max(h,b.width),k=window.innerWidth-10,m=(0,l.clamp)(f,[10,Math.max(10,k-i)]);j.style.minWidth=h+"px",j.style.left=m+"px"}else{let e=b.right-d.right,f=window.innerWidth-c.right-e,g=window.innerWidth-a.right-f,h=a.width+g,i=Math.max(h,b.width),k=window.innerWidth-10,m=(0,l.clamp)(f,[10,Math.max(10,k-i)]);j.style.minWidth=h+"px",j.style.right=m+"px"}let e=q(),g=window.innerHeight-20,i=t.scrollHeight,k=window.getComputedStyle(m),n=parseInt(k.borderTopWidth,10),o=parseInt(k.paddingTop,10),p=parseInt(k.borderBottomWidth,10),s=n+o+i+parseInt(k.paddingBottom,10)+p,w=Math.min(5*u.offsetHeight,s),x=window.getComputedStyle(t),y=parseInt(x.paddingTop,10),z=parseInt(x.paddingBottom,10),A=a.top+a.height/2-10,B=u.offsetHeight/2,C=n+o+(u.offsetTop+B);if(C<=A){let a=e.length>0&&u===e[e.length-1].ref.current;j.style.bottom="0px";let b=Math.max(g-A,B+(a?z:0)+(m.clientHeight-t.offsetTop-t.offsetHeight)+p);j.style.height=C+b+"px"}else{let a=e.length>0&&u===e[0].ref.current;j.style.top="0px";let b=Math.max(A,n+t.offsetTop+(a?y:0)+B);j.style.height=b+(s-C)+"px",t.scrollTop=C-A+t.offsetTop}j.style.margin="10px 0",j.style.minHeight=w+"px",j.style.maxHeight=g+"px",f?.(),requestAnimationFrame(()=>r.current=!0)}},[q,h.trigger,h.valueNode,j,m,t,u,v,h.dir,f]);(0,B.useLayoutEffect)(()=>y(),[y]);let[z,A]=c.useState();(0,B.useLayoutEffect)(()=>{m&&A(window.getComputedStyle(m).zIndex)},[m]);let C=c.useCallback(a=>{a&&!0===s.current&&(y(),w?.(),s.current=!1)},[y,w]);return(0,b.jsx)(af,{scope:e,contentWrapper:j,shouldExpandOnScrollRef:r,onScrollButtonChange:C,children:(0,b.jsx)("div",{ref:k,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:z},children:(0,b.jsx)(x.Primitive.div,{...g,ref:p,style:{boxSizing:"border-box",maxHeight:"100%",...g.style}})})})});ad.displayName="SelectItemAlignedPosition";var ae=c.forwardRef((a,c)=>{let{__scopeSelect:d,align:e="start",collisionPadding:f=10,...g}=a,h=N(d);return(0,b.jsx)(v.Content,{...h,...g,ref:c,align:e,collisionPadding:f,style:{boxSizing:"border-box",...g.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});ae.displayName="SelectPopperPosition";var[af,ag]=L(Z,{}),ah="SelectViewport",ai=c.forwardRef((a,d)=>{let{__scopeSelect:e,nonce:f,...g}=a,h=aa(ah,e),i=ag(ah,e),j=(0,o.useComposedRefs)(d,h.onViewportChange),k=c.useRef(0);return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:f}),(0,b.jsx)(I.Slot,{scope:e,children:(0,b.jsx)(x.Primitive.div,{"data-radix-select-viewport":"",role:"presentation",...g,ref:j,style:{position:"relative",flex:1,overflow:"hidden auto",...g.style},onScroll:(0,m.composeEventHandlers)(g.onScroll,a=>{let b=a.currentTarget,{contentWrapper:c,shouldExpandOnScrollRef:d}=i;if(d?.current&&c){let a=Math.abs(k.current-b.scrollTop);if(a>0){let d=window.innerHeight-20,e=Math.max(parseFloat(c.style.minHeight),parseFloat(c.style.height));if(e<d){let f=e+a,g=Math.min(d,f),h=f-g;c.style.height=g+"px","0px"===c.style.bottom&&(b.scrollTop=h>0?h:0,c.style.justifyContent="flex-end")}}}k.current=b.scrollTop})})})]})});ai.displayName=ah;var aj="SelectGroup",[ak,al]=L(aj);c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=(0,u.useId)();return(0,b.jsx)(ak,{scope:d,id:f,children:(0,b.jsx)(x.Primitive.div,{role:"group","aria-labelledby":f,...e,ref:c})})}).displayName=aj;var am="SelectLabel",an=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=al(am,d);return(0,b.jsx)(x.Primitive.div,{id:f.id,...e,ref:c})});an.displayName=am;var ao="SelectItem",[ap,aq]=L(ao),ar=c.forwardRef((a,d)=>{let{__scopeSelect:e,value:f,disabled:g=!1,textValue:h,...i}=a,j=P(ao,e),k=aa(ao,e),l=j.value===f,[n,p]=c.useState(h??""),[q,r]=c.useState(!1),s=(0,o.useComposedRefs)(d,a=>k.itemRefCallback?.(a,f,g)),t=(0,u.useId)(),v=c.useRef("touch"),w=()=>{g||(j.onValueChange(f),j.onOpenChange(!1))};if(""===f)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,b.jsx)(ap,{scope:e,value:f,disabled:g,textId:t,isSelected:l,onItemTextChange:c.useCallback(a=>{p(b=>b||(a?.textContent??"").trim())},[]),children:(0,b.jsx)(I.ItemSlot,{scope:e,value:f,disabled:g,textValue:n,children:(0,b.jsx)(x.Primitive.div,{role:"option","aria-labelledby":t,"data-highlighted":q?"":void 0,"aria-selected":l&&q,"data-state":l?"checked":"unchecked","aria-disabled":g||void 0,"data-disabled":g?"":void 0,tabIndex:g?void 0:-1,...i,ref:s,onFocus:(0,m.composeEventHandlers)(i.onFocus,()=>r(!0)),onBlur:(0,m.composeEventHandlers)(i.onBlur,()=>r(!1)),onClick:(0,m.composeEventHandlers)(i.onClick,()=>{"mouse"!==v.current&&w()}),onPointerUp:(0,m.composeEventHandlers)(i.onPointerUp,()=>{"mouse"===v.current&&w()}),onPointerDown:(0,m.composeEventHandlers)(i.onPointerDown,a=>{v.current=a.pointerType}),onPointerMove:(0,m.composeEventHandlers)(i.onPointerMove,a=>{v.current=a.pointerType,g?k.onItemLeave?.():"mouse"===v.current&&a.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,m.composeEventHandlers)(i.onPointerLeave,a=>{a.currentTarget===document.activeElement&&k.onItemLeave?.()}),onKeyDown:(0,m.composeEventHandlers)(i.onKeyDown,a=>{(k.searchRef?.current===""||" "!==a.key)&&(G.includes(a.key)&&w()," "===a.key&&a.preventDefault())})})})})});ar.displayName=ao;var as="SelectItemText",at=c.forwardRef((a,d)=>{let{__scopeSelect:e,className:f,style:g,...h}=a,i=P(as,e),j=aa(as,e),l=aq(as,e),m=R(as,e),[n,p]=c.useState(null),q=(0,o.useComposedRefs)(d,a=>p(a),l.onItemTextChange,a=>j.itemTextRefCallback?.(a,l.value,l.disabled)),r=n?.textContent,s=c.useMemo(()=>(0,b.jsx)("option",{value:l.value,disabled:l.disabled,children:r},l.value),[l.disabled,l.value,r]),{onNativeOptionAdd:t,onNativeOptionRemove:u}=m;return(0,B.useLayoutEffect)(()=>(t(s),()=>u(s)),[t,u,s]),(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)(x.Primitive.span,{id:l.textId,...h,ref:q}),l.isSelected&&i.valueNode&&!i.valueNodeHasChildren?k.createPortal(h.children,i.valueNode):null]})});at.displayName=as;var au="SelectItemIndicator",av=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return aq(au,d).isSelected?(0,b.jsx)(x.Primitive.span,{"aria-hidden":!0,...e,ref:c}):null});av.displayName=au;var aw="SelectScrollUpButton",ax=c.forwardRef((a,d)=>{let e=aa(aw,a.__scopeSelect),f=ag(aw,a.__scopeSelect),[g,h]=c.useState(!1),i=(0,o.useComposedRefs)(d,f.onScrollButtonChange);return(0,B.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){h(b.scrollTop>0)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(aA,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop-b.offsetHeight)}}):null});ax.displayName=aw;var ay="SelectScrollDownButton",az=c.forwardRef((a,d)=>{let e=aa(ay,a.__scopeSelect),f=ag(ay,a.__scopeSelect),[g,h]=c.useState(!1),i=(0,o.useComposedRefs)(d,f.onScrollButtonChange);return(0,B.useLayoutEffect)(()=>{if(e.viewport&&e.isPositioned){let a=function(){let a=b.scrollHeight-b.clientHeight;h(Math.ceil(b.scrollTop)<a)},b=e.viewport;return a(),b.addEventListener("scroll",a),()=>b.removeEventListener("scroll",a)}},[e.viewport,e.isPositioned]),g?(0,b.jsx)(aA,{...a,ref:i,onAutoScroll:()=>{let{viewport:a,selectedItem:b}=e;a&&b&&(a.scrollTop=a.scrollTop+b.offsetHeight)}}):null});az.displayName=ay;var aA=c.forwardRef((a,d)=>{let{__scopeSelect:e,onAutoScroll:f,...g}=a,h=aa("SelectScrollButton",e),i=c.useRef(null),j=J(e),k=c.useCallback(()=>{null!==i.current&&(window.clearInterval(i.current),i.current=null)},[]);return c.useEffect(()=>()=>k(),[k]),(0,B.useLayoutEffect)(()=>{let a=j().find(a=>a.ref.current===document.activeElement);a?.ref.current?.scrollIntoView({block:"nearest"})},[j]),(0,b.jsx)(x.Primitive.div,{"aria-hidden":!0,...g,ref:d,style:{flexShrink:0,...g.style},onPointerDown:(0,m.composeEventHandlers)(g.onPointerDown,()=>{null===i.current&&(i.current=window.setInterval(f,50))}),onPointerMove:(0,m.composeEventHandlers)(g.onPointerMove,()=>{h.onItemLeave?.(),null===i.current&&(i.current=window.setInterval(f,50))}),onPointerLeave:(0,m.composeEventHandlers)(g.onPointerLeave,()=>{k()})})}),aB=c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a;return(0,b.jsx)(x.Primitive.div,{"aria-hidden":!0,...e,ref:c})});aB.displayName="SelectSeparator";var aC="SelectArrow";c.forwardRef((a,c)=>{let{__scopeSelect:d,...e}=a,f=N(d),g=P(aC,d),h=aa(aC,d);return g.open&&"popper"===h.position?(0,b.jsx)(v.Arrow,{...f,...e,ref:c}):null}).displayName=aC;var aD=c.forwardRef(({__scopeSelect:a,value:d,...e},f)=>{let g=c.useRef(null),h=(0,o.useComposedRefs)(f,g),i=function(a){let b=c.useRef({value:a,previous:a});return c.useMemo(()=>(b.current.value!==a&&(b.current.previous=b.current.value,b.current.value=a),b.current.previous),[a])}(d);return c.useEffect(()=>{let a=g.current;if(!a)return;let b=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(i!==d&&b){let c=new Event("change",{bubbles:!0});b.call(a,d),a.dispatchEvent(c)}},[i,d]),(0,b.jsx)(x.Primitive.select,{...e,style:{...C.VISUALLY_HIDDEN_STYLES,...e.style},ref:h,defaultValue:d})});function aE(a){return""===a||void 0===a}function aF(a){let b=(0,z.useCallbackRef)(a),d=c.useRef(""),e=c.useRef(0),f=c.useCallback(a=>{let c=d.current+a;b(c),function a(b){d.current=b,window.clearTimeout(e.current),""!==b&&(e.current=window.setTimeout(()=>a(""),1e3))}(c)},[b]),g=c.useCallback(()=>{d.current="",window.clearTimeout(e.current)},[]);return c.useEffect(()=>()=>window.clearTimeout(e.current),[]),[d,f,g]}function aG(a,b,c){var d,e;let f=b.length>1&&Array.from(b).every(a=>a===b[0])?b[0]:b,g=c?a.indexOf(c):-1,h=(d=a,e=Math.max(g,0),d.map((a,b)=>d[(e+b)%d.length]));1===f.length&&(h=h.filter(a=>a!==c));let i=h.find(a=>a.textValue.toLowerCase().startsWith(f.toLowerCase()));return i!==c?i:void 0}aD.displayName="SelectBubbleInput";var aH=a.i(74797),aI=a.i(93007);let aJ=(0,aI.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),aK=(0,aI.default)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var aL=a.i(90334);let aM=c.forwardRef(({className:a,children:c,...d},e)=>(0,b.jsxs)(U,{ref:e,className:(0,aL.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",a),...d,children:[c,(0,b.jsx)(X,{asChild:!0,children:(0,b.jsx)(aJ,{className:"w-4 h-4 opacity-50"})})]}));aM.displayName=U.displayName;let aN=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(ax,{ref:d,className:(0,aL.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aK,{className:"w-4 h-4"})}));aN.displayName=ax.displayName;let aO=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(az,{ref:d,className:(0,aL.cn)("flex cursor-default items-center justify-center py-1",a),...c,children:(0,b.jsx)(aJ,{className:"w-4 h-4"})}));aO.displayName=az.displayName;let aP=c.forwardRef(({className:a,children:c,position:d="popper",...e},f)=>(0,b.jsx)(Y,{children:(0,b.jsxs)($,{ref:f,className:(0,aL.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border border-border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===d&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",a),position:d,...e,children:[(0,b.jsx)(aN,{}),(0,b.jsx)(ai,{className:(0,aL.cn)("p-1","popper"===d&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:c}),(0,b.jsx)(aO,{})]})}));aP.displayName=$.displayName,c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(an,{ref:d,className:(0,aL.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",a),...c})).displayName=an.displayName;let aQ=c.forwardRef(({className:a,children:c,...d},e)=>(0,b.jsxs)(ar,{ref:e,className:(0,aL.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",a),...d,children:[(0,b.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,b.jsx)(av,{children:(0,b.jsx)(aH.Check,{className:"w-4 h-4"})})}),(0,b.jsx)(at,{children:c})]}));aQ.displayName=ar.displayName,c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aB,{ref:d,className:(0,aL.cn)("-mx-1 my-1 h-px bg-muted",a),...c})).displayName=aB.displayName;var aR=c.forwardRef((a,c)=>(0,b.jsx)(x.Primitive.label,{...a,ref:c,onMouseDown:b=>{b.target.closest("button, input, select, textarea")||(a.onMouseDown?.(b),!b.defaultPrevented&&b.detail>1&&b.preventDefault())}}));aR.displayName="Label";let aS=(0,a.i(30718).cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),aT=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(aR,{ref:d,className:(0,aL.cn)(aS(),a),...c}));aT.displayName=aR.displayName;var aU=a.i(85718),aV=a.i(15209),aW="Tabs",[aX,aY]=(0,p.createContextScope)(aW,[aU.createRovingFocusGroupScope]),aZ=(0,aU.createRovingFocusGroupScope)(),[a$,a_]=aX(aW),a0=c.forwardRef((a,c)=>{let{__scopeTabs:d,value:e,onValueChange:f,defaultValue:g,orientation:h="horizontal",dir:i,activationMode:j="automatic",...k}=a,l=(0,q.useDirection)(i),[m,n]=(0,A.useControllableState)({prop:e,onChange:f,defaultProp:g??"",caller:aW});return(0,b.jsx)(a$,{scope:d,baseId:(0,u.useId)(),value:m,onValueChange:n,orientation:h,dir:l,activationMode:j,children:(0,b.jsx)(x.Primitive.div,{dir:l,"data-orientation":h,...k,ref:c})})});a0.displayName=aW;var a1="TabsList",a2=c.forwardRef((a,c)=>{let{__scopeTabs:d,loop:e=!0,...f}=a,g=a_(a1,d),h=aZ(d);return(0,b.jsx)(aU.Root,{asChild:!0,...h,orientation:g.orientation,dir:g.dir,loop:e,children:(0,b.jsx)(x.Primitive.div,{role:"tablist","aria-orientation":g.orientation,...f,ref:c})})});a2.displayName=a1;var a3="TabsTrigger",a4=c.forwardRef((a,c)=>{let{__scopeTabs:d,value:e,disabled:f=!1,...g}=a,h=a_(a3,d),i=aZ(d),j=a7(h.baseId,e),k=a8(h.baseId,e),l=e===h.value;return(0,b.jsx)(aU.Item,{asChild:!0,...i,focusable:!f,active:l,children:(0,b.jsx)(x.Primitive.button,{type:"button",role:"tab","aria-selected":l,"aria-controls":k,"data-state":l?"active":"inactive","data-disabled":f?"":void 0,disabled:f,id:j,...g,ref:c,onMouseDown:(0,m.composeEventHandlers)(a.onMouseDown,a=>{f||0!==a.button||!1!==a.ctrlKey?a.preventDefault():h.onValueChange(e)}),onKeyDown:(0,m.composeEventHandlers)(a.onKeyDown,a=>{[" ","Enter"].includes(a.key)&&h.onValueChange(e)}),onFocus:(0,m.composeEventHandlers)(a.onFocus,()=>{let a="manual"!==h.activationMode;l||f||!a||h.onValueChange(e)})})})});a4.displayName=a3;var a5="TabsContent",a6=c.forwardRef((a,d)=>{let{__scopeTabs:e,value:f,forceMount:g,children:h,...i}=a,j=a_(a5,e),k=a7(j.baseId,f),l=a8(j.baseId,f),m=f===j.value,n=c.useRef(m);return c.useEffect(()=>{let a=requestAnimationFrame(()=>n.current=!1);return()=>cancelAnimationFrame(a)},[]),(0,b.jsx)(aV.Presence,{present:g||m,children:({present:c})=>(0,b.jsx)(x.Primitive.div,{"data-state":m?"active":"inactive","data-orientation":j.orientation,role:"tabpanel","aria-labelledby":k,hidden:!c,id:l,tabIndex:0,...i,ref:d,style:{...a.style,animationDuration:n.current?"0s":void 0},children:c&&h})})});function a7(a,b){return`${a}-trigger-${b}`}function a8(a,b){return`${a}-content-${b}`}a6.displayName=a5;let a9=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(a2,{ref:d,className:(0,aL.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",a),...c}));a9.displayName=a2.displayName;let ba=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(a4,{ref:d,className:(0,aL.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",a),...c}));ba.displayName=a4.displayName;let bb=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(a6,{ref:d,className:(0,aL.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",a),...c}));bb.displayName=a6.displayName;var bc=a.i(33518),bd=a.i(74939);let be=(0,aI.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),bf=(0,aI.default)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),bg=(0,aI.default)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),bh=(0,aI.default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),bi=(0,aI.default)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),bj=(0,aI.default)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),bk=(0,aI.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var bl=a.i(1905);function bm(){let{t:a,setLocale:k,locale:l}=(0,bl.useI18n)(),[m,n]=(0,c.useState)(""),[o,p]=(0,c.useState)(""),[q,r]=(0,c.useState)("gemini"),[s,t]=(0,c.useState)("gemini-1.5-pro-latest"),[u,v]=(0,c.useState)("mistralai/mistral-7b-instruct"),[w,x]=(0,c.useState)("https://rainbow-gumption-2fc85c.netlify.app/"),[y,z]=(0,c.useState)(""),[A,B]=(0,c.useState)([]),[C,D]=(0,c.useState)([]),[E,F]=(0,c.useState)(!1),[G,H]=(0,c.useState)(!1),[I,J]=(0,c.useState)(!1),[K,L]=(0,c.useState)(!1),[M,N]=(0,c.useState)(!1),[O,P]=(0,c.useState)(!1),{toast:Q}=(0,h.useToast)();(0,c.useEffect)(()=>{let a=localStorage.getItem("gemini_api_key"),b=localStorage.getItem("openrouter_api_key"),c=localStorage.getItem("ai_provider"),d=localStorage.getItem("gemini_model"),e=localStorage.getItem("openrouter_model"),f=localStorage.getItem("gemini_api_base_url");a&&n(a),b&&p(b),c&&r(c),d&&t(d),e&&v(e),f&&x(f),(0,bc.getGeminiModels)().then(B)},[]);let R=(0,c.useCallback)(async()=>{if(!o)return void Q({title:a("settings.missing_api_key"),description:a("settings.enter_openrouter_api_key_first"),variant:"destructive"});F(!0);try{let a=await (0,bc.getOpenRouterModels)();D(a),a.length>0&&!u&&v(a[0].id)}catch(b){Q({title:a("settings.load_models_fail"),description:a("settings.load_models_fail_desc"),variant:"destructive"})}finally{F(!1)}},[o,u,Q,a]),T=async()=>{if(!m)return void Q({title:a("settings.validation_failed"),description:a("settings.missing_api_key"),variant:"destructive"});N(!0);try{let b=w||"https://rainbow-gumption-2fc85c.netlify.app/",c=s.startsWith("gemini-")?s:"gemini-2.5-pro";if((await fetch(`${b}v1/models/${c}:generateContent?key=${m}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"Hello"}]}]})})).ok)Q({title:a("settings.validation_success"),description:a("settings.validation_success_gemini")});else throw Error("API 金鑰無效")}catch(b){Q({title:a("settings.validation_failed"),description:a("settings.validation_failed_gemini"),variant:"destructive"})}finally{N(!1)}},U=async()=>{if(!o)return void Q({title:a("settings.validation_failed"),description:a("settings.missing_api_key"),variant:"destructive"});P(!0);try{if((await fetch("https://openrouter.ai/api/v1/models",{headers:{Authorization:`Bearer ${o}`}})).ok)Q({title:a("settings.validation_success"),description:a("settings.validation_success_openrouter")});else throw Error("API 金鑰無效")}catch(b){Q({title:a("settings.validation_failed"),description:a("settings.validation_failed_openrouter"),variant:"destructive"})}finally{P(!1)}},V=async()=>{H(!0);try{let b=await window.electron.exportData();if(b.success)Q({title:a("settings.export_success"),description:b.message});else throw Error(b.message)}catch(b){Q({title:a("settings.export_error"),description:b instanceof Error?b.message:String(b),variant:"destructive"})}finally{H(!1)}},X=async()=>{L(!1),J(!0);try{let b=await window.electron.importData();if(b.success)b.settings&&Object.entries(b.settings).forEach(([a,b])=>{"string"==typeof b&&localStorage.setItem(a,b)}),Q({title:a("settings.import_success"),description:b.message}),setTimeout(()=>{window.location.reload()},1e3);else throw Error(b.message)}catch(b){Q({title:a("settings.import_error"),description:b instanceof Error?b.message:String(b),variant:"destructive"})}finally{J(!1)}};return(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("div",{className:"min-h-screen bg-background",children:(0,b.jsx)("div",{className:"container py-10 mx-auto",children:(0,b.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,b.jsx)("div",{className:"mb-10",children:(0,b.jsxs)("div",{className:"flex items-center justify-between",children:[(0,b.jsxs)("div",{children:[(0,b.jsx)("h1",{className:"text-3xl font-semibold",children:a("settings.title")}),(0,b.jsx)("p",{className:"text-muted-foreground",children:a("settings.general_settings_description")})]}),(0,b.jsx)(j.default,{href:"/",children:(0,b.jsxs)(d.Button,{variant:"outline",children:[(0,b.jsx)(bk,{className:"w-4 h-4 mr-2"}),a("settings.back_to_home")]})})]})}),(0,b.jsxs)(a0,{defaultValue:"general",className:"w-full",children:[(0,b.jsxs)(a9,{className:"grid w-full grid-cols-3",children:[(0,b.jsxs)(ba,{value:"general",className:"flex items-center gap-2",children:[(0,b.jsx)(bf,{className:"w-4 h-4"}),a("settings.general_settings")]}),(0,b.jsxs)(ba,{value:"data",className:"flex items-center gap-2",children:[(0,b.jsx)(bg,{className:"w-4 h-4"}),a("settings.data_management")]}),(0,b.jsxs)(ba,{value:"ai",className:"flex items-center gap-2",children:[(0,b.jsx)(bi,{className:"w-4 h-4"}),a("settings.ai_settings")]})]}),(0,b.jsx)(bb,{value:"general",className:"space-y-6",children:(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{children:[(0,b.jsxs)(f.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(bf,{className:"w-5 h-5"}),a("settings.general_settings")]}),(0,b.jsx)(f.CardDescription,{children:a("settings.general_settings_description")})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"grid gap-4",children:(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"language",children:a("settings.language")}),(0,b.jsxs)(S,{value:l,onValueChange:a=>k(a),children:[(0,b.jsx)(aM,{className:"w-full",children:(0,b.jsx)(W,{placeholder:a("settings.select_language")})}),(0,b.jsxs)(aP,{children:[(0,b.jsx)(aQ,{value:"en",children:a("settings.english")}),(0,b.jsx)(aQ,{value:"zh",children:a("settings.traditional_chinese")})]})]})]})})})]})}),(0,b.jsx)(bb,{value:"data",className:"space-y-6",children:(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{children:[(0,b.jsxs)(f.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(bg,{className:"w-5 h-5"}),a("settings.data_management")]}),(0,b.jsx)(f.CardDescription,{children:a("settings.data_management_description")})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row",children:[(0,b.jsxs)(d.Button,{onClick:V,disabled:G,className:"flex items-center gap-2",children:[G?(0,b.jsx)(bd.Loader2,{className:"w-4 h-4 animate-spin"}):(0,b.jsx)(bg,{className:"w-4 h-4"}),G?a("settings.export_in_progress"):a("settings.export_data")]}),(0,b.jsxs)(d.Button,{onClick:()=>L(!0),disabled:I,variant:"outline",className:"flex items-center gap-2",children:[I?(0,b.jsx)(bd.Loader2,{className:"w-4 h-4 animate-spin"}):(0,b.jsx)(bh,{className:"w-4 h-4"}),I?a("settings.import_in_progress"):a("settings.import_data")]})]})})]})}),(0,b.jsxs)(bb,{value:"ai",className:"space-y-6",children:[(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{children:[(0,b.jsxs)(f.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(bi,{className:"w-5 h-5"}),a("settings.ai_settings")]}),(0,b.jsx)(f.CardDescription,{children:a("settings.ai_settings_description")})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsx)("div",{className:"grid gap-4",children:(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"ai-provider",children:a("settings.ai_provider")}),(0,b.jsxs)(S,{value:q,onValueChange:r,children:[(0,b.jsx)(aM,{children:(0,b.jsx)(W,{placeholder:a("settings.select_ai_provider")})}),(0,b.jsxs)(aP,{children:[(0,b.jsx)(aQ,{value:"gemini",children:"Google Gemini"}),(0,b.jsx)(aQ,{value:"openrouter",children:"OpenRouter"})]})]})]})})})]}),(0,b.jsxs)(f.Card,{children:[(0,b.jsxs)(f.CardHeader,{children:[(0,b.jsxs)(f.CardTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(bj,{className:"w-5 h-5"}),a("settings.model_key_settings")]}),(0,b.jsx)(f.CardDescription,{children:a("settings.model_key_settings_description")})]}),(0,b.jsx)(f.CardContent,{children:(0,b.jsxs)("div",{className:"grid gap-4",children:["gemini"===q&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"gemini-api-base-url",children:a("settings.gemini_api_endpoint")}),(0,b.jsx)(e.Input,{id:"gemini-api-base-url",type:"url",value:w,onChange:a=>x(a.target.value),placeholder:"https://rainbow-gumption-2fc85c.netlify.app/"}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:a("settings.custom_gemini_endpoint_desc")})]}),(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"gemini-model",children:a("settings.gemini_model")}),(0,b.jsxs)(S,{value:s.startsWith("gemini-")||"custom"===s?s:"custom",onValueChange:a=>{"custom"===a?t(y||"gemini-2.5-pro"):t(a)},children:[(0,b.jsx)(aM,{children:(0,b.jsx)(W,{placeholder:a("settings.select_gemini_model")})}),(0,b.jsxs)(aP,{children:[A.map(a=>(0,b.jsx)(aQ,{value:a.id,children:a.name},a.id)),(0,b.jsx)(aQ,{value:"custom",children:a("settings.custom_model")})]})]}),("custom"===s||!A.some(a=>a.id===s))&&(0,b.jsx)(e.Input,{type:"text",placeholder:a("settings.enter_custom_model_name"),value:s.startsWith("gemini-")?s:y,onChange:a=>{z(a.target.value),t(a.target.value)}})]}),(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"gemini-key",children:a("settings.gemini_api_key")}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(e.Input,{id:"gemini-key",type:"password",value:m,onChange:a=>n(a.target.value),placeholder:a("settings.enter_gemini_api_key"),className:"flex-1"}),(0,b.jsx)(d.Button,{onClick:T,disabled:M||!m,variant:"outline",size:"sm",children:M?(0,b.jsx)(bd.Loader2,{className:"w-4 h-4 animate-spin"}):a("settings.validate")})]})]})]}),"openrouter"===q&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"openrouter-key",children:a("settings.openrouter_api_key")}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsx)(e.Input,{id:"openrouter-key",type:"password",value:o,onChange:a=>p(a.target.value),placeholder:a("settings.enter_openrouter_api_key"),className:"flex-1"}),(0,b.jsx)(d.Button,{onClick:U,disabled:O||!o,variant:"outline",size:"sm",children:O?(0,b.jsx)(bd.Loader2,{className:"w-4 h-4 animate-spin"}):a("settings.validate")})]})]}),(0,b.jsxs)("div",{className:"grid gap-2",children:[(0,b.jsx)(aT,{htmlFor:"openrouter-model",children:a("settings.openrouter_model")}),(0,b.jsxs)("div",{className:"flex gap-2",children:[(0,b.jsxs)(S,{value:u,onValueChange:v,disabled:0===C.length,children:[(0,b.jsx)(aM,{className:"flex-1",children:(0,b.jsx)(W,{placeholder:a("settings.select_or_load_model")})}),(0,b.jsx)(aP,{children:C.map(a=>(0,b.jsx)(aQ,{value:a.id,children:a.name},a.id))})]}),(0,b.jsx)(d.Button,{onClick:R,disabled:E,variant:"outline",size:"sm",children:E?(0,b.jsx)(bd.Loader2,{className:"w-4 h-4 animate-spin"}):a("settings.load_models")})]}),(0,b.jsx)("p",{className:"text-sm text-muted-foreground",children:a("settings.load_models_instruction")})]})]})]})})]}),(0,b.jsx)("div",{className:"flex justify-end",children:(0,b.jsxs)(d.Button,{onClick:()=>{localStorage.setItem("gemini_api_key",m),localStorage.setItem("openrouter_api_key",o),localStorage.setItem("ai_provider",q),localStorage.setItem("gemini_model",s),localStorage.setItem("openrouter_model",u),localStorage.setItem("gemini_api_base_url",w),Q({title:a("settings.settings_saved_success")})},size:"default",children:[(0,b.jsx)(be,{className:"w-4 h-4 mr-2"}),a("settings.save_all_settings")]})})]})]})]})})}),(0,b.jsx)(i.Toaster,{}),(0,b.jsx)(g.AlertDialog,{open:K,onOpenChange:L,children:(0,b.jsxs)(g.AlertDialogContent,{children:[(0,b.jsxs)(g.AlertDialogHeader,{children:[(0,b.jsxs)(g.AlertDialogTitle,{className:"flex items-center gap-2",children:[(0,b.jsx)(bh,{className:"w-5 h-5"}),a("settings.import_data")]}),(0,b.jsx)(g.AlertDialogDescription,{children:a("settings.import_confirmation")})]}),(0,b.jsxs)(g.AlertDialogFooter,{children:[(0,b.jsx)(g.AlertDialogCancel,{children:a("editor.cancel")}),(0,b.jsx)(g.AlertDialogAction,{onClick:X,children:a("settings.continue")})]})]})})]})}}];

//# sourceMappingURL=renderer_src_app_settings_page_tsx_976693b8._.js.map