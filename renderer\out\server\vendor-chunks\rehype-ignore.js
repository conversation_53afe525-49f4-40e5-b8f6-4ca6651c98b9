"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-ignore";
exports.ids = ["vendor-chunks/rehype-ignore"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-ignore/lib/index.js":
/*!*************************************************!*\
  !*** ./node_modules/rehype-ignore/lib/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n\nconst rehypeIgnore = (options = {}) => {\n    const { openDelimiter = 'rehype:ignore:start', closeDelimiter = 'rehype:ignore:end' } = options;\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, (node, index, parent) => {\n            if (node.type === 'element' || node.type === 'root') {\n                // const start = node.children.findIndex((item) => item.type === 'comment' && item.value === openDelimiter);\n                // const end = node.children.findIndex((item) => item.type === 'comment' && item.value === closeDelimiter);\n                // if (start > -1 && end > -1) {\n                //   node.children = node.children.filter((_, idx) => idx < start || idx > end);\n                // }\n                let start = false;\n                node.children = node.children.filter((item) => {\n                    if (item.type === 'raw' || item.type === 'comment') {\n                        let str = (item.value || '').trim();\n                        str = str.replace(/^<!--(.*?)-->/, '$1');\n                        if (str === openDelimiter) {\n                            start = true;\n                            return false;\n                        }\n                        if (str === closeDelimiter) {\n                            start = false;\n                            return false;\n                        }\n                    }\n                    return !start;\n                });\n            }\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rehypeIgnore);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-ignore/lib/index.js\n");

/***/ })

};
;