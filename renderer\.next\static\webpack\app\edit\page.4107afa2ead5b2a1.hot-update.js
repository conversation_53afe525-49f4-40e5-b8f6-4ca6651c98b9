"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Fallback to @uiw/react-md-editor if Cherry fails\nconst MDEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_uiw_react-md-editor_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @uiw/react-md-editor */ \"(app-pages-browser)/./node_modules/@uiw/react-md-editor/esm/index.js\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\CherryMarkdownEditor.tsx -> \" + \"@uiw/react-md-editor\"\n        ]\n    },\n    ssr: false\n});\n_c = MDEditor;\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cherryLoadFailed, setCherryLoadFailed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                        // Set failed state to use fallback editor\n                        setCherryLoadFailed(true);\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 281,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 269,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，使用 MDEditor 作為備用編輯器\n    if (!CherryClass) {\n        if (cherryLoadFailed) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cherry-markdown-editor \".concat(className),\n                style: {\n                    height: \"100%\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MDEditor, {\n                    value: value,\n                    onChange: (val)=>onChange && onChange(val || \"\"),\n                    preview: preview === \"preview\" ? \"preview\" : preview === \"edit\" ? \"edit\" : \"live\",\n                    hideToolbar: hideToolbar,\n                    \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\",\n                    style: {\n                        height: \"100%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 290,\n                columnNumber: 11\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"載入編輯器中...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 315,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"正在載入 Cherry Markdown 編輯器...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 304,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 326,\n        columnNumber: 7\n    }, undefined);\n}, \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c2 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MDEditor\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c2, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});