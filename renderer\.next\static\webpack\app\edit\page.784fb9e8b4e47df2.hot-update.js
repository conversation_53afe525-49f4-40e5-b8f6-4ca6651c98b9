"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息\n    if (!isClient || !CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 272,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 260,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 278,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});