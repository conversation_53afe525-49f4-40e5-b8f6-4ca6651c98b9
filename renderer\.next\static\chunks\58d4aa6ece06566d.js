(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,45372,e=>{"use strict";e.s(["AlertDialog",()=>O,"AlertDialogAction",()=>M,"AlertDialogCancel",()=>J,"AlertDialogContent",()=>j,"AlertDialogDescription",()=>F,"AlertDialogFooter",()=>k,"AlertDialogHeader",()=>D,"AlertDialogTitle",()=>G],45372);var t=e.i(67857),n=e.i(24302),r=e.i(76825),o=e.i(51906),a=e.i(48424),i=e.i(61540),s=e.i(81692),l="AlertDialog",[c,u]=(0,r.createContextScope)(l,[a.createDialogScope]),d=(0,a.createDialogScope)(),p=e=>{let{__scopeAlertDialog:n,...r}=e,o=d(n);return(0,t.jsx)(a.Root,{...o,...r,modal:!0})};p.displayName=l,n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...o}=e,i=d(r);return(0,t.jsx)(a.Trigger,{...i,...o,ref:n})}).displayName="AlertDialogTrigger";var m=e=>{let{__scopeAlertDialog:n,...r}=e,o=d(n);return(0,t.jsx)(a.Portal,{...o,...r})};m.displayName="AlertDialogPortal";var f=n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...o}=e,i=d(r);return(0,t.jsx)(a.Overlay,{...i,...o,ref:n})});f.displayName="AlertDialogOverlay";var g="AlertDialogContent",[h,y]=c(g),v=(0,s.createSlottable)("AlertDialogContent"),w=n.forwardRef((e,r)=>{let{__scopeAlertDialog:s,children:l,...c}=e,u=d(s),p=n.useRef(null),m=(0,o.useComposedRefs)(r,p),f=n.useRef(null);return(0,t.jsx)(a.WarningProvider,{contentName:g,titleName:x,docsSlug:"alert-dialog",children:(0,t.jsx)(h,{scope:s,cancelRef:f,children:(0,t.jsxs)(a.Content,{role:"alertdialog",...u,...c,ref:m,onOpenAutoFocus:(0,i.composeEventHandlers)(c.onOpenAutoFocus,e=>{var t;e.preventDefault(),null==(t=f.current)||t.focus({preventScroll:!0})}),onPointerDownOutside:e=>e.preventDefault(),onInteractOutside:e=>e.preventDefault(),children:[(0,t.jsx)(v,{children:l}),(0,t.jsx)(N,{contentRef:p})]})})})});w.displayName=g;var x="AlertDialogTitle",A=n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...o}=e,i=d(r);return(0,t.jsx)(a.Title,{...i,...o,ref:n})});A.displayName=x;var b="AlertDialogDescription",S=n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...o}=e,i=d(r);return(0,t.jsx)(a.Description,{...i,...o,ref:n})});S.displayName=b;var T=n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...o}=e,i=d(r);return(0,t.jsx)(a.Close,{...i,...o,ref:n})});T.displayName="AlertDialogAction";var R="AlertDialogCancel",E=n.forwardRef((e,n)=>{let{__scopeAlertDialog:r,...i}=e,{cancelRef:s}=y(R,r),l=d(r),c=(0,o.useComposedRefs)(n,s);return(0,t.jsx)(a.Close,{...l,...i,ref:c})});E.displayName=R;var N=e=>{let{contentRef:t}=e,r="`".concat(g,"` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the `").concat(g,"` by passing a `").concat(b,"` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an `id` and passing the same value to the `aria-describedby` prop in `").concat(g,"`. If the description is confusing or duplicative for sighted users, you can use the `@radix-ui/react-visually-hidden` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog");return n.useEffect(()=>{var e;document.getElementById(null==(e=t.current)?void 0:e.getAttribute("aria-describedby"))||console.warn(r)},[r,t]),null},I=e.i(92072),C=e.i(28157);let O=p,P=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsx)(f,{className:(0,I.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",r),...o,ref:n})});P.displayName=f.displayName;let j=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsxs)(m,{children:[(0,t.jsx)(P,{}),(0,t.jsx)(w,{ref:n,className:(0,I.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",r),...o})]})});j.displayName=w.displayName;let D=e=>{let{className:n,...r}=e;return(0,t.jsx)("div",{className:(0,I.cn)("flex flex-col space-y-2 text-center sm:text-left",n),...r})};D.displayName="AlertDialogHeader";let k=e=>{let{className:n,...r}=e;return(0,t.jsx)("div",{className:(0,I.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",n),...r})};k.displayName="AlertDialogFooter";let G=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsx)(A,{ref:n,className:(0,I.cn)("text-lg font-semibold",r),...o})});G.displayName=A.displayName;let F=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsx)(S,{ref:n,className:(0,I.cn)("text-sm text-muted-foreground",r),...o})});F.displayName=S.displayName;let M=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsx)(T,{ref:n,className:(0,I.cn)((0,C.buttonVariants)(),r),...o})});M.displayName=T.displayName;let J=n.forwardRef((e,n)=>{let{className:r,...o}=e;return(0,t.jsx)(E,{ref:n,className:(0,I.cn)((0,C.buttonVariants)({variant:"outline"}),"mt-2 sm:mt-0",r),...o})});J.displayName=E.displayName},56611,e=>{"use strict";function t(e,t){let[n,r]=t;return Math.min(r,Math.max(n,e))}e.s(["clamp",()=>t])},53456,e=>{"use strict";e.s(["useDirection",()=>r]);var t=e.i(24302);e.i(67857);var n=t.createContext(void 0);function r(e){let r=t.useContext(n);return e||r||"ltr"}},17412,e=>{"use strict";e.s(["Check",()=>t],17412);let t=(0,e.i(93137).default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},63772,e=>{"use strict";e.s(["Item",()=>C,"Root",()=>I,"createRovingFocusGroupScope",()=>w]);var t=e.i(24302),n=e.i(61540),r=e.i(49566),o=e.i(51906),a=e.i(76825),i=e.i(36641),s=e.i(32054),l=e.i(83875),c=e.i(89199),u=e.i(53456),d=e.i(67857),p="rovingFocusGroup.onEntryFocus",m={bubbles:!1,cancelable:!0},f="RovingFocusGroup",[g,h,y]=(0,r.createCollection)(f),[v,w]=(0,a.createContextScope)(f,[y]),[x,A]=v(f),b=t.forwardRef((e,t)=>(0,d.jsx)(g.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(g.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,d.jsx)(S,{...e,ref:t})})}));b.displayName=f;var S=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:a,orientation:i,loop:g=!1,dir:y,currentTabStopId:v,defaultCurrentTabStopId:w,onCurrentTabStopIdChange:A,onEntryFocus:b,preventScrollOnEntryFocus:S=!1,...T}=e,R=t.useRef(null),E=(0,o.useComposedRefs)(r,R),I=(0,u.useDirection)(y),[C,O]=(0,c.useControllableState)({prop:v,defaultProp:null!=w?w:null,onChange:A,caller:f}),[P,j]=t.useState(!1),D=(0,l.useCallbackRef)(b),k=h(a),G=t.useRef(!1),[F,M]=t.useState(0);return t.useEffect(()=>{let e=R.current;if(e)return e.addEventListener(p,D),()=>e.removeEventListener(p,D)},[D]),(0,d.jsx)(x,{scope:a,orientation:i,dir:I,loop:g,currentTabStopId:C,onItemFocus:t.useCallback(e=>O(e),[O]),onItemShiftTab:t.useCallback(()=>j(!0),[]),onFocusableItemAdd:t.useCallback(()=>M(e=>e+1),[]),onFocusableItemRemove:t.useCallback(()=>M(e=>e-1),[]),children:(0,d.jsx)(s.Primitive.div,{tabIndex:P||0===F?-1:0,"data-orientation":i,...T,ref:E,style:{outline:"none",...e.style},onMouseDown:(0,n.composeEventHandlers)(e.onMouseDown,()=>{G.current=!0}),onFocus:(0,n.composeEventHandlers)(e.onFocus,e=>{let t=!G.current;if(e.target===e.currentTarget&&t&&!P){let t=new CustomEvent(p,m);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=k().filter(e=>e.focusable);N([e.find(e=>e.active),e.find(e=>e.id===C),...e].filter(Boolean).map(e=>e.ref.current),S)}}G.current=!1}),onBlur:(0,n.composeEventHandlers)(e.onBlur,()=>j(!1))})})}),T="RovingFocusGroupItem",R=t.forwardRef((e,r)=>{let{__scopeRovingFocusGroup:o,focusable:a=!0,active:l=!1,tabStopId:c,children:u,...p}=e,m=(0,i.useId)(),f=c||m,y=A(T,o),v=y.currentTabStopId===f,w=h(o),{onFocusableItemAdd:x,onFocusableItemRemove:b,currentTabStopId:S}=y;return t.useEffect(()=>{if(a)return x(),()=>b()},[a,x,b]),(0,d.jsx)(g.ItemSlot,{scope:o,id:f,focusable:a,active:l,children:(0,d.jsx)(s.Primitive.span,{tabIndex:v?0:-1,"data-orientation":y.orientation,...p,ref:r,onMouseDown:(0,n.composeEventHandlers)(e.onMouseDown,e=>{a?y.onItemFocus(f):e.preventDefault()}),onFocus:(0,n.composeEventHandlers)(e.onFocus,()=>y.onItemFocus(f)),onKeyDown:(0,n.composeEventHandlers)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void y.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return E[o]}(e,y.orientation,y.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=w().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=y.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>N(n))}}),children:"function"==typeof u?u({isCurrentTabStop:v,hasTabStop:null!=S}):u})})});R.displayName=T;var E={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function N(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var I=b,C=R},29841,6458,e=>{"use strict";e.s(["changeToneStream",()=>T,"expandContentStream",()=>p,"generateTags",()=>N,"generateTitleStream",()=>v,"getGeminiModels",()=>E,"getOpenRouterModels",()=>R,"polishStream",()=>A,"summarizeStream",()=>g],29841);var t=e.i(61620);let n="https://openrouter.ai/api/v1/chat/completions";function r(){return localStorage.getItem("gemini_api_base_url")||"https://rainbow-gumption-2fc85c.netlify.app/"}function o(e){return localStorage.getItem("".concat(e,"_api_key"))}function a(){return localStorage.getItem("ai_provider")||"gemini"}function i(){return localStorage.getItem("openrouter_model")||"mistralai/mistral-7b-instruct"}function s(){return(localStorage.getItem("gemini_model")||"gemini-2.5-pro").replace(/^models\//,"")}async function l(e){var n,r,o,a,i,s,l,c,u,d;let{url:p,headers:m,body:f,onChunk:g}=e,h=await fetch(p,{method:"POST",headers:m,body:f});if(!h.ok){let e=await h.text();console.error("Stream API Error:",e);try{let n=(null==(r=JSON.parse(e).error)?void 0:r.message)||"串流請求失敗。";throw(0,t.toast)({title:"串流請求失敗",description:n,variant:"destructive"}),Error(n)}catch(r){let n="串流請求失敗: ".concat(e);throw(0,t.toast)({title:"串流請求失敗",description:e,variant:"destructive"}),Error(n)}}let y=null==(n=h.body)?void 0:n.getReader();if(!y)throw Error("無法獲取 response reader");let v=new TextDecoder,w="";try{for(;;){let{done:e,value:t}=await y.read();if(e)break;let n=(w+=v.decode(t,{stream:!0})).split("\n");for(let e of(w=n.pop()||"",n))if(e.startsWith("data: ")){let t=e.slice(6);if("[DONE]"===t)continue;try{let e=JSON.parse(t),n=(null==(i=e.choices)||null==(a=i[0])||null==(o=a.delta)?void 0:o.content)||(null==(d=e.candidates)||null==(u=d[0])||null==(c=u.content)||null==(l=c.parts)||null==(s=l[0])?void 0:s.text);n&&(console.log("Stream chunk:",n),g(n))}catch(e){console.log("Stream parse error:",e,"data:",t)}}}}finally{y.releaseLock()}}async function c(e){let r=o("openrouter");if(!r)throw(0,t.toast)({title:"API 金鑰未設定",description:"請在設定中設定 OpenRouter API 金鑰。",variant:"destructive"}),Error("OpenRouter API 金鑰未設定。");let a=await fetch(n,{method:"POST",headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:e})});if(!a.ok){let e=await a.text();console.error("OpenRouter API Error:",e);try{var s;let n=(null==(s=JSON.parse(e).error)?void 0:s.message)||"OpenRouter API 請求失敗。";throw(0,t.toast)({title:"API 請求失敗",description:n,variant:"destructive"}),Error(n)}catch(r){let n="OpenRouter API 請求失敗: ".concat(e);throw(0,t.toast)({title:"API 請求失敗",description:e,variant:"destructive"}),Error(n)}}return(await a.json()).choices[0].message.content}async function u(e,t){let r=o("openrouter");if(!r)throw Error("OpenRouter API 金鑰未設定。");await l({url:n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:[{role:"system",content:"You are a creative writing expert. Based on the following prompt, create a detailed, engaging, and structurally complete text. Follow these creative principles:\n\nCREATIVE REQUIREMENTS:\n1. **Content Richness**: Add relevant details, examples, and explanations while maintaining the core theme\n2. **Structural Integrity**: Include introduction, main content, and conclusion sections\n3. **Logical Coherence**: Ensure natural transitions between sections with clear thinking\n4. **Reader Engagement**: Use vivid language and concrete descriptions\n5. **Practical Value**: Provide meaningful information or insights\n\nCREATIVE STRATEGY:\n- **Introduction**: Set the background, spark reader interest, and present the core theme\n- **Main Content**: Develop the theme in detail with specific examples, data, or cases\n- **Conclusion**: Summarize key points and provide suggestions or inspirational thoughts\n- **Appropriate Length**: Expand to 3-5 times the length of the original prompt\n\nSTYLE SUGGESTIONS:\n- Use first-person or second-person narrative to increase intimacy\n- Use rhetorical devices such as metaphors and contrasts when appropriate\n- Balance professionalism with readability\n\nBased on the original prompt, create a complete and attractive text:"},{role:"user",content:e}],stream:!0}),onChunk:t})}async function d(e,t){let n=o("gemini");if(!n)throw Error("Gemini API 金鑰未設定。");let a=s(),i=r();await l({url:"".concat(i,"v1/models/").concat(a,":streamGenerateContent?key=").concat(n,"&alt=sse"),headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"請作為一位創意寫作專家，根據以下提示詞創建一個詳細、引人入勝且結構完整的文本。請遵循以下創作原則：\n\n## 創作要求：\n1. **內容豐富性**：在保持核心主題的前提下，添加相關的細節、例子和說明\n2. **結構完整性**：包含引言、主要內容和結論三個部分\n3. **邏輯連貫性**：確保各部分之間的過渡自然，思路清晰\n4. **吸引讀者**：使用生動的語言和具體的描述\n5. **實用價值**：提供有實際意義的資訊或見解\n\n## 創作策略：\n- **引言部分**：設定背景，引起讀者興趣，提出核心主題\n- **主要內容**：詳細展開主題，提供具體例子、數據或案例\n- **結論部分**：總結要點，提供建議或啟發性思考\n- **適當長度**：擴展到原提示詞的 3-5 倍長度\n\n## 風格建議：\n- 使用第一人稱或第二人稱敘述，增加親切感\n- 適時使用修辭手法，如比喻、對比等\n- 保持語言的專業性和可讀性平衡\n\n## 原始提示詞：\n".concat(e,"\n\n請以此為基礎，創建一個完整而吸引人的文本：")}]}]}),onChunk:t})}async function p(e,t){let n=a();return(console.log("Starting streaming with provider:",n),"gemini"===n)?d(e,t):u(e,t)}async function m(e,t){let r=o("openrouter");if(!r)throw Error("OpenRouter API 金鑰未設定。");await l({url:n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:[{role:"system",content:"Summarize the following text."},{role:"user",content:e}],stream:!0}),onChunk:t})}async function f(e,t){let n=o("gemini");if(!n)throw Error("Gemini API 金鑰未設定。");let a=s(),i=r();await l({url:"".concat(i,"v1/models/").concat(a,":streamGenerateContent?key=").concat(n,"&alt=sse"),headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"請作為一位專業的內容總結專家，分析並總結以下筆記內容。請遵循以下原則：\n\n## 總結要求：\n1. **重點提取**：識別並保留最重要的資訊、關鍵概念和核心觀點\n2. **結構清晰**：使用簡潔的段落結構，確保邏輯連貫\n3. **長度適中**：總結長度約為原文的 1/3 到 1/4，重點突出，避免冗餘\n4. **客觀準確**：保持原文的原意，不添加個人解釋或外部資訊\n5. **實用價值**：確保總結具有實用性，可以幫助讀者快速了解原文要點\n\n## 總結格式：\n- 使用條列式或段落式結構\n- 保留重要的數據、日期、名稱等關鍵資訊\n- 如果原文有明確的結論或建議，請特別強調\n\n## 原文內容：\n".concat(e,"\n\n請提供一個結構清晰、重點突出且實用的總結：")}]}]}),onChunk:t})}async function g(e,t){let n=a();return(console.log("Starting summarize streaming with provider:",n),"gemini"===n)?f(e,t):m(e,t)}async function h(e,t){let r=o("openrouter");if(!r)throw Error("OpenRouter API 金鑰未設定。");await l({url:n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:[{role:"system",content:"Generate a concise title for the following text."},{role:"user",content:e.substring(0,200)}],stream:!0}),onChunk:t})}async function y(e,t){let n=o("gemini");if(!n)throw Error("Gemini API 金鑰未設定。");let a=s(),i=r();await l({url:"".concat(i,"v1/models/").concat(a,":streamGenerateContent?key=").concat(n,"&alt=sse"),headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"請作為一位專業的標題撰寫專家，根據以下內容生成一個吸引人且準確的標題。請遵循以下原則：\n\n## 標題要求：\n1. **簡潔有力**：標題長度控制在 5-15 個中文字，突出重點\n2. **吸引注意力**：使用能引起讀者興趣的關鍵詞或表述\n3. **準確反映內容**：標題必須準確代表原文的核心主題和主要觀點\n4. **SEO 友好**：考慮搜尋引擎優化，使用相關關鍵詞\n5. **獨特性**：避免使用常見的通用性標題\n\n## 標題風格建議：\n- 問題式標題：以疑問句形式吸引讀者\n- 結果式標題：強調內容帶來的價值或結果\n- 數字式標題：如果內容涉及步驟、方法或清單\n- 故事式標題：如果內容有敘事性質\n\n## 內容預覽：\n".concat(e.substring(0,300),"\n\n請生成 3 個不同的標題選項，並為每個標題簡要說明選擇的原因：")}]}]}),onChunk:t})}async function v(e,t){let n=a();return(console.log("Starting title generation streaming with provider:",n),"gemini"===n)?y(e,t):h(e,t)}async function w(e,t){let r=o("openrouter");if(!r)throw Error("OpenRouter API 金鑰未設定。");await l({url:n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(r),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:[{role:"system",content:"You are a professional text editor and writing consultant. Perform comprehensive polishing and improvement on the following text. Follow these editing principles:\n\nEDITING FOCUS:\n1. **Grammatical Correctness**: Correct all grammar errors, punctuation usage, and sentence structure issues\n2. **Expression Clarity**: Simplify complex sentences to make them clearer and easier to understand\n3. **Logical Coherence**: Ensure smooth logical flow between paragraphs and natural transitions between ideas\n4. **Word Precision**: Use more appropriate and professional vocabulary to replace vague or inaccurate expressions\n5. **Style Consistency**: Maintain consistency in overall style\n6. **Length Optimization**: Adjust length appropriately while preserving the original meaning, avoiding redundancy\n\nIMPROVEMENT STRATEGY:\n- Convert passive voice to active voice when appropriate\n- Merge duplicate or redundant sentences\n- Improve paragraph structure and sentence rhythm\n- Add necessary connecting words to improve fluency\n- Ensure correct use of professional terminology\n\nOUTPUT REQUIREMENTS:\nPlease provide the polished complete text and briefly explain the main improvement points at the end.\n\nORIGINAL CONTENT:"},{role:"user",content:e}],stream:!0}),onChunk:t})}async function x(e,t){let n=o("gemini");if(!n)throw Error("Gemini API 金鑰未設定。");let a=s(),i=r();await l({url:"".concat(i,"v1/models/").concat(a,":streamGenerateContent?key=").concat(n,"&alt=sse"),headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"請作為一位專業的文字編輯和寫作顧問，對以下文字進行全面的潤飾和改進。請遵循以下編輯原則：\n\n## 編輯重點：\n1. **語法正確性**：修正所有語法錯誤、標點符號使用和句子結構問題\n2. **表達清晰度**：簡化複雜的句子，使表達更清晰易懂\n3. **邏輯連貫性**：確保段落之間的邏輯流暢，想法之間的過渡自然\n4. **用詞精準性**：使用更恰當、專業的詞彙替代模糊或不準確的表達\n5. **風格一致性**：保持整體風格的一致性\n6. **長度優化**：在保持原意的前提下，適度調整長度，避免冗餘\n\n## 改進策略：\n- 修正被動語態為主動語態（適當時機）\n- 合併重複或多餘的句子\n- 改善段落結構和句子節奏\n- 增加必要的連接詞以改善流暢度\n- 確保專業術語使用正確\n\n## 輸出要求：\n請提供潤飾後的完整文字，並在文末簡要說明主要的改進點。\n\n## 原文內容：\n".concat(e,"\n\n請開始潤飾：")}]}]}),onChunk:t})}async function A(e,t){let n=a();return(console.log("Starting polish streaming with provider:",n),"gemini"===n)?x(e,t):w(e,t)}async function b(e,t,r){let a=o("openrouter");if(!a)throw Error("OpenRouter API 金鑰未設定。");let s="";switch(t){case"正式":s="Rewrite the text in a formal tone.";break;case"休閒":s="Rewrite the text in a casual and friendly tone.";break;case"專業":s="Rewrite the text in a professional and authoritative tone.";break;case"幽默":s="Rewrite the text in a witty and humorous tone."}await l({url:n,headers:{"Content-Type":"application/json",Authorization:"Bearer ".concat(a),"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:i(),messages:[{role:"system",content:s},{role:"user",content:e}],stream:!0}),onChunk:r})}async function S(e,t,n){let a=o("gemini");if(!a)throw Error("Gemini API 金鑰未設定。");let i="";switch(t){case"正式":i="請您扮演一位專業的公文與學術寫作專家。將以下文字改寫為正式、結構化且客觀的語氣，使其適用於官方文件或學術論文。請避免使用口語、縮寫和個人代名詞，並專注於清晰、精確與專業的表達。";break;case"休閒":i="請您扮演一位友善且健談的朋友。將以下文字改寫為輕鬆、休閒且友好的語氣。請適度使用生活化用語、個人化的例子或比喻，使其更平易近人、更容易閱讀。";break;case"專業":i="請您扮演一位行業內的頂尖專家。將以下文字改寫為專業、權威且知識豐富的語氣。請在適當之處使用行業術語，保持自信的口吻，並確保資訊呈現清晰、簡潔。";break;case"幽默":i="請您扮演一位機智且風趣的作家。將以下文字改寫為巧妙且幽默的語氣。請運用雙關語、反諷或輕鬆的例子，使內容更具娛樂性，同時仍能傳達核心訊息。"}let c=s(),u=r();await l({url:"".concat(u,"v1/models/").concat(c,":streamGenerateContent?key=").concat(a,"&alt=sse"),headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"".concat(i,"\n\n").concat(e)}]}]}),onChunk:n})}async function T(e,t,n){let r=a();return(console.log("Starting changeTone streaming with provider:",r),"gemini"===r)?S(e,t,n):b(e,t,n)}async function R(){try{let e=await fetch("https://openrouter.ai/api/v1/models");if(!e.ok)throw Error("無法獲取 OpenRouter 模型列表。");return(await e.json()).data.map(e=>({id:e.id,name:e.name||e.id}))}catch(e){return console.error("獲取 OpenRouter 模型失敗:",e),[]}}async function E(){return Promise.resolve([{id:"gemini-2.5-pro",name:"Gemini 2.5 Pro"},{id:"gemini-2.5-flash",name:"Gemini 2.5 Flash"},{id:"gemini-2.0-flash-exp",name:"Gemini 2.0 Flash Experimental"}])}async function N(e){let t,n=a(),i='請作為一位專業的內容分析師和分類專家，仔細閱讀以下筆記內容，並提取出最核心、最相關的關鍵字作為標籤。\n\n分析要求：\n1. **核心主題**：識別文章的主要主題和核心概念。\n2. **關鍵實體**：提取出重要的人物、地點、組織、技術或專有名詞。\n3. **內容分類**：判斷文章的性質，例如是「教學」、「評論」、「筆記」還是「想法」。\n4. **精簡化**：確保每個標籤都是簡潔且具代表性的詞彙。\n5. **數量控制**：提供 3 到 5 個最相關的標籤。\n\n請以 JSON 格式的陣列輸出，例如：["標籤一", "標籤二", "標籤三"]\n\n原文內容：\n'.concat(e,"\n\n請僅提供 JSON 格式的標籤陣列：");if("gemini"===n){let e=o("gemini");if(!e)throw Error("Gemini API 金鑰未設定。");let n=s(),a=r(),l=await fetch("".concat(a,"v1/models/").concat(n,":generateContent?key=").concat(e),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:i}]}]})});if(!l.ok)throw console.error("Gemini API Error:",await l.text()),Error("Gemini API 請求失敗。");t=(await l.json()).candidates[0].content.parts[0].text}else t=await c([{role:"system",content:'You are a professional content analyst. Extract 3 to 5 core keywords from the following text to be used as tags. Return them as a JSON array of strings. For example: ["Tag1", "Tag2", "Tag3"]'},{role:"user",content:e}]);try{let e=t.match(/(\[[\s\S]*\])/);if(e){let t=JSON.parse(e[0]);return Array.isArray(t)?t.map(e=>String(e)):[]}return t.split(",").map(e=>e.trim()).filter(Boolean)}catch(e){return console.error("Failed to parse tags:",e),t.split(",").map(e=>e.trim()).filter(Boolean)}}e.s(["Loader2",()=>I],6458);let I=(0,e.i(93137).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}]);