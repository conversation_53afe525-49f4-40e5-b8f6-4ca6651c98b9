"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/nth-check";
exports.ids = ["vendor-chunks/nth-check"];
exports.modules = {

/***/ "(ssr)/./node_modules/nth-check/lib/esm/compile.js":
/*!***************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/compile.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* binding */ compile),\n/* harmony export */   generate: () => (/* binding */ generate)\n/* harmony export */ });\n/* harmony import */ var boolbase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! boolbase */ \"(ssr)/./node_modules/boolbase/index.js\");\n\n/**\n * Returns a function that checks if an elements index matches the given rule\n * highly optimized to return the fastest solution.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A highly optimized function that returns whether an index matches the nth-check.\n * @example\n *\n * ```js\n * const check = nthCheck.compile([2, 3]);\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n * ```\n */\nfunction compile(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    const b = parsed[1] - 1;\n    /*\n     * When `b <= 0`, `a * n` won't be lead to any matches for `a < 0`.\n     * Besides, the specification states that no elements are\n     * matched when `a` and `b` are 0.\n     *\n     * `b < 0` here as we subtracted 1 from `b` above.\n     */\n    if (b < 0 && a <= 0)\n        return boolbase__WEBPACK_IMPORTED_MODULE_0__.falseFunc;\n    // When `a` is in the range -1..1, it matches any element (so only `b` is checked).\n    if (a === -1)\n        return (index) => index <= b;\n    if (a === 0)\n        return (index) => index === b;\n    // When `b <= 0` and `a === 1`, they match any element.\n    if (a === 1)\n        return b < 0 ? boolbase__WEBPACK_IMPORTED_MODULE_0__.trueFunc : (index) => index >= b;\n    /*\n     * Otherwise, modulo can be used to check if there is a match.\n     *\n     * Modulo doesn't care about the sign, so let's use `a`s absolute value.\n     */\n    const absA = Math.abs(a);\n    // Get `b mod a`, + a if this is negative.\n    const bMod = ((b % absA) + absA) % absA;\n    return a > 1\n        ? (index) => index >= b && index % absA === bMod\n        : (index) => index <= b && index % absA === bMod;\n}\n/**\n * Returns a function that produces a monotonously increasing sequence of indices.\n *\n * If the sequence has an end, the returned function will return `null` after\n * the last index in the sequence.\n *\n * @param parsed A tuple [a, b], as returned by `parse`.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing (2n+3)</caption>\n *\n * ```js\n * const gen = nthCheck.generate([2, 3])\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value (-2n+10)</caption>\n *\n * ```js\n *\n * const gen = nthCheck.generate([-2, 5]);\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction generate(parsed) {\n    const a = parsed[0];\n    // Subtract 1 from `b`, to convert from one- to zero-indexed.\n    let b = parsed[1] - 1;\n    let n = 0;\n    // Make sure to always return an increasing sequence\n    if (a < 0) {\n        const aPos = -a;\n        // Get `b mod a`\n        const minValue = ((b % aPos) + aPos) % aPos;\n        return () => {\n            const val = minValue + aPos * n++;\n            return val > b ? null : val;\n        };\n    }\n    if (a === 0)\n        return b < 0\n            ? // There are no result — always return `null`\n                () => null\n            : // Return `b` exactly once\n                () => (n++ === 0 ? b : null);\n    if (b < 0) {\n        b += a * Math.ceil(-b / a);\n    }\n    return () => a * n++ + b;\n}\n//# sourceMappingURL=compile.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nth-check/lib/esm/compile.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nth-check/lib/esm/index.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/index.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compile: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.compile),\n/* harmony export */   \"default\": () => (/* binding */ nthCheck),\n/* harmony export */   generate: () => (/* reexport safe */ _compile_js__WEBPACK_IMPORTED_MODULE_1__.generate),\n/* harmony export */   parse: () => (/* reexport safe */ _parse_js__WEBPACK_IMPORTED_MODULE_0__.parse),\n/* harmony export */   sequence: () => (/* binding */ sequence)\n/* harmony export */ });\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/nth-check/lib/esm/parse.js\");\n/* harmony import */ var _compile_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./compile.js */ \"(ssr)/./node_modules/nth-check/lib/esm/compile.js\");\n\n\n\n/**\n * Parses and compiles a formula to a highly optimized function.\n * Combination of {@link parse} and {@link compile}.\n *\n * If the formula doesn't match any elements,\n * it returns [`boolbase`](https://github.com/fb55/boolbase)'s `falseFunc`.\n * Otherwise, a function accepting an _index_ is returned, which returns\n * whether or not the passed _index_ matches the formula.\n *\n * Note: The nth-rule starts counting at `1`, the returned function at `0`.\n *\n * @param formula The formula to compile.\n * @example\n * const check = nthCheck(\"2n+3\");\n *\n * check(0); // `false`\n * check(1); // `false`\n * check(2); // `true`\n * check(3); // `false`\n * check(4); // `true`\n * check(5); // `false`\n * check(6); // `true`\n */\nfunction nthCheck(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.compile)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n/**\n * Parses and compiles a formula to a generator that produces a sequence of indices.\n * Combination of {@link parse} and {@link generate}.\n *\n * @param formula The formula to compile.\n * @returns A function that produces a sequence of indices.\n * @example <caption>Always increasing</caption>\n *\n * ```js\n * const gen = nthCheck.sequence('2n+3')\n *\n * gen() // `1`\n * gen() // `3`\n * gen() // `5`\n * gen() // `8`\n * gen() // `11`\n * ```\n *\n * @example <caption>With end value</caption>\n *\n * ```js\n *\n * const gen = nthCheck.sequence('-2n+5');\n *\n * gen() // 0\n * gen() // 2\n * gen() // 4\n * gen() // null\n * ```\n */\nfunction sequence(formula) {\n    return (0,_compile_js__WEBPACK_IMPORTED_MODULE_1__.generate)((0,_parse_js__WEBPACK_IMPORTED_MODULE_0__.parse)(formula));\n}\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nth-check/lib/esm/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/nth-check/lib/esm/parse.js":
/*!*************************************************!*\
  !*** ./node_modules/nth-check/lib/esm/parse.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n// Following http://www.w3.org/TR/css3-selectors/#nth-child-pseudo\n// Whitespace as per https://www.w3.org/TR/selectors-3/#lex is \" \\t\\r\\n\\f\"\nconst whitespace = new Set([9, 10, 12, 13, 32]);\nconst ZERO = \"0\".charCodeAt(0);\nconst NINE = \"9\".charCodeAt(0);\n/**\n * Parses an expression.\n *\n * @throws An `Error` if parsing fails.\n * @returns An array containing the integer step size and the integer offset of the nth rule.\n * @example nthCheck.parse(\"2n+3\"); // returns [2, 3]\n */\nfunction parse(formula) {\n    formula = formula.trim().toLowerCase();\n    if (formula === \"even\") {\n        return [2, 0];\n    }\n    else if (formula === \"odd\") {\n        return [2, 1];\n    }\n    // Parse [ ['-'|'+']? INTEGER? {N} [ S* ['-'|'+'] S* INTEGER ]?\n    let idx = 0;\n    let a = 0;\n    let sign = readSign();\n    let number = readNumber();\n    if (idx < formula.length && formula.charAt(idx) === \"n\") {\n        idx++;\n        a = sign * (number !== null && number !== void 0 ? number : 1);\n        skipWhitespace();\n        if (idx < formula.length) {\n            sign = readSign();\n            skipWhitespace();\n            number = readNumber();\n        }\n        else {\n            sign = number = 0;\n        }\n    }\n    // Throw if there is anything else\n    if (number === null || idx < formula.length) {\n        throw new Error(`n-th rule couldn't be parsed ('${formula}')`);\n    }\n    return [a, sign * number];\n    function readSign() {\n        if (formula.charAt(idx) === \"-\") {\n            idx++;\n            return -1;\n        }\n        if (formula.charAt(idx) === \"+\") {\n            idx++;\n        }\n        return 1;\n    }\n    function readNumber() {\n        const start = idx;\n        let value = 0;\n        while (idx < formula.length &&\n            formula.charCodeAt(idx) >= ZERO &&\n            formula.charCodeAt(idx) <= NINE) {\n            value = value * 10 + (formula.charCodeAt(idx) - ZERO);\n            idx++;\n        }\n        // Return `null` if we didn't read anything.\n        return idx === start ? null : value;\n    }\n    function skipWhitespace() {\n        while (idx < formula.length &&\n            whitespace.has(formula.charCodeAt(idx))) {\n            idx++;\n        }\n    }\n}\n//# sourceMappingURL=parse.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/nth-check/lib/esm/parse.js\n");

/***/ })

};
;