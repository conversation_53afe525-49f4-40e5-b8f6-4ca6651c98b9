"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Fallback to @uiw/react-md-editor if Cherry fails\nconst MDEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(_c = ()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_uiw_react-md-editor_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @uiw/react-md-editor */ \"(app-pages-browser)/./node_modules/@uiw/react-md-editor/esm/index.js\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\CherryMarkdownEditor.tsx -> \" + \"@uiw/react-md-editor\"\n        ]\n    },\n    ssr: false\n});\n_c1 = MDEditor;\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c2 = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cherryLoadFailed, setCherryLoadFailed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 279,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 267,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，提供簡單的 textarea 編輯器\n    if (!CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"簡易編輯器 (Cherry Markdown 載入失敗)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 298,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: value,\n                    onChange: (e)=>onChange && onChange(e.target.value),\n                    style: {\n                        flex: 1,\n                        border: \"none\",\n                        outline: \"none\",\n                        padding: \"16px\",\n                        resize: \"none\",\n                        backgroundColor: \"transparent\",\n                        color: \"hsl(var(--foreground))\",\n                        fontFamily: \"monospace\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.5\"\n                    },\n                    placeholder: \"在此輸入 Markdown 內容...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 301,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 287,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 323,\n        columnNumber: 7\n    }, undefined);\n}, \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c3 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1, _c2, _c3;\n$RefreshReg$(_c, \"MDEditor$dynamic\");\n$RefreshReg$(_c1, \"MDEditor\");\n$RefreshReg$(_c2, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c3, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});