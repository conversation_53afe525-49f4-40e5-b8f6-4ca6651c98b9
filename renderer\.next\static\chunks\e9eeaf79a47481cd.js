(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,29369,e=>{e.v(JSON.parse('{"settings":{"title":"Settings","back_to_home":"Back to Home","general_settings":"General Settings","general_settings_description":"Customize your application experience","language":"Language","select_language":"Select Language","english":"English","traditional_chinese":"繁體中文","data_management":"Data Management","data_management_description":"Backup and restore your data","export_data":"Export Data","export_in_progress":"Exporting...","export_success":"Data exported successfully","export_error":"Failed to export data","import_data":"Import Data","import_in_progress":"Importing...","import_success":"Data imported successfully","import_error":"Failed to import data","import_confirmation":"Are you sure you want to import data? This will overwrite all current notes and settings.","ai_settings":"AI Settings","ai_settings_description":"Choose your AI assistant service","ai_provider":"AI Provider","select_ai_provider":"Select an AI Provider","gemini_model":"Gemini Model","select_gemini_model":"Select a Gemini Model","gemini_api_key":"Gemini API Key","enter_gemini_api_key":"Enter your Gemini API Key","openrouter_api_key":"OpenRouter API Key","enter_openrouter_api_key":"Enter your OpenRouter API Key","openrouter_model":"OpenRouter Model","load_models":"Load Models","loading_models":"Loading...","save_all_settings":"Save All Settings","settings_saved_success":"Settings saved successfully.","missing_api_key":"Missing API Key","enter_openrouter_api_key_first":"Please enter your OpenRouter API key first.","load_models_fail":"Failed to load models","load_models_fail_desc":"Could not fetch model list from OpenRouter.","model_key_settings":"Model & API Key Settings","model_key_settings_description":"Configure models and API credentials","select_or_load_model":"Select or Load Model","load_models_instruction":"Enter your API key, then click \'Load Models\' to fetch available models.","continue":"Continue","custom_prompts":"Custom Prompts","custom_prompts_description":"Create and manage your custom AI prompt templates to make AI process text according to your needs.","add_custom_prompt":"Add Prompt","edit_custom_prompt":"Edit Prompt","delete_custom_prompt":"Delete Prompt","prompt_name":"Prompt Name","prompt_description":"Description","prompt_content":"Prompt Content","prompt_category":"Category","select_category":"Select Category","save_prompt":"Save Prompt","create_prompt":"Create Prompt","no_custom_prompts":"No custom prompts yet","create_first_prompt":"Click \'Add Prompt\' to create your first prompt template","translate":"Translate","translate_to":"Translate to","source_language":"Source Language","target_language":"Target Language","auto_detect":"Auto Detect","translating":"Translating...","extract_todos":"Extract Todos","extracting_todos":"Extracting todos...","no_todos_found":"No todos found","todo_high_priority":"High Priority","todo_medium_priority":"Medium Priority","todo_low_priority":"Low Priority","todo_completed":"Completed","todo_pending":"Pending","custom_model":"Custom Model","enter_custom_model":"Enter custom model name","api_base_url":"API Base URL","enter_api_base_url":"Enter custom API base URL","validate_key":"Validate","validating":"Validating...","validation_success":"Validation successful","validation_failed":"Validation failed","custom_prompts_tab":"Custom Prompts","custom_prompts_management":"Custom Prompt Management","your_prompts":"Your Prompts","add_prompt":"Add Prompt","edit":"Edit","delete":"Delete","edit_prompt":"Edit Prompt","create_new_prompt":"Create New Prompt","edit_existing_prompt":"Modify existing custom prompt","create_new_prompt_template":"Create a new custom prompt template","enter_prompt_name":"Enter prompt name","description":"Description","describe_prompt_purpose":"Briefly describe the purpose of this prompt","category":"Category","category_examples":"For example: writing, analysis, translation, etc.","enter_prompt_content":"Enter your custom prompt content...","cancel":"Cancel","update":"Update","create":"Create","gemini_api_endpoint":"Gemini API Endpoint","custom_gemini_endpoint_desc":"Custom Gemini API endpoint. If left empty, the default endpoint will be used.","enter_custom_model_name":"Enter custom model name (e.g.: gemini-2.5-pro)","validate":"Validate","input_error":"Input Error","fill_name_and_content":"Please fill in the prompt name and content","success":"Success","prompt_created":"Custom prompt created","create_failed":"Creation Failed","cannot_create_prompt":"Cannot create custom prompt","prompt_updated":"Custom prompt updated","update_failed":"Update Failed","cannot_update_prompt":"Cannot update custom prompt","prompt_deleted":"Custom prompt deleted","delete_failed":"Deletion Failed","cannot_delete_prompt":"Cannot delete custom prompt","validation_success_gemini":"Gemini API key is valid","validation_failed_gemini":"Gemini API key is invalid or network error","validation_success_openrouter":"OpenRouter API key is valid","validation_failed_openrouter":"OpenRouter API key is invalid or network error"},"home":{"title":"MyNote","new_note":"New Note","ai_settings":"Settings","search_notes":"Search notes...","tags":"Tags","search_tags":"Search tags...","no_tags_found":"No tags found.","no_title":"No Title","no_content":"No additional content","loading":"Loading..."},"editor":{"title_placeholder":"Enter note title...","tags_placeholder":"Add tags...","ai_tools":"AI Tools","summarize":"Summarize Note","generate_title":"Generate Title","polish_selected":"Polish Selected Text","expand_content":"Expand Content","change_tone_professional":"Change Tone (Professional)","change_tone_casual":"Change Tone (Casual)","translate_text":"Translate Text","extract_todos":"Extract Todos","execute_custom_prompt":"Execute Custom Prompt","save":"Save","edit":"Edit","delete":"Delete","ai_result_title":"AI Processing Result","cancel":"Cancel","insert_at_end":"Insert at End of Note","replace_selected":"Replace Selected Content","expand_content_title":"Expand Content","expand_placeholder":"Enter a brief prompt or sentence...","generate":"Generate","generating":"Generating...","suggested_title":"Suggested Title","suggested_title_desc":"AI suggests using the following title. Do you want to replace the current title?","replace":"Replace","ai_operation_failed":"AI Operation Failed","unknown_error":"An unknown error occurred","selection_error":"Selection Error","select_text_first":"Please select text to polish first.","select_tone_text_first":"Please select text to change tone first.","input_error":"Input Error","enter_valid_prompt":"Please enter a valid expansion prompt.","generate_tags":"Generate Tags","tags_generated":"Tags Generated","tags_added_successfully":"Tags added successfully","content_required_for_tags":"Content is required to generate tags"},"note_list":{"search_notes":"Search notes...","no_notes_found":"No matching notes found","no_notes":"No notes yet","try_different_search":"Try using different search terms","create_first_note":"Click the button above to create your first note","no_title":"No Title","no_content":"No additional content"},"edit":{"error":"Error","electron_api_unavailable":"Electron API unavailable","load_failed":"Load Failed","cannot_load_notes":"Cannot load note list","cannot_load_note_content":"Cannot load note content","save_success":"Save Success","note_saved":"Note saved","save_failed":"Save Failed","cannot_save_note":"Cannot save note","delete_success":"Delete Success","note_deleted":"Note deleted","delete_failed":"Delete Failed","cannot_delete_note":"Cannot delete note","new_note":"New Note","loading":"Loading...","welcome_title":"Welcome to MyNote","welcome_description":"Select a note from the left sidebar to start editing, or create a new note."}}'))},20104,e=>{e.v(JSON.parse('{"settings":{"title":"設定","back_to_home":"返回主頁","general_settings":"通用設定","general_settings_description":"自訂您的應用程式體驗","language":"語言","select_language":"選擇語言","english":"English","traditional_chinese":"繁體中文","data_management":"資料管理","data_management_description":"備份與恢復您的資料","export_data":"匯出資料","export_in_progress":"正在匯出...","export_success":"資料匯出成功","export_error":"資料匯出失敗","import_data":"匯入資料","import_in_progress":"正在匯入...","import_success":"資料匯入成功","import_error":"資料匯入失敗","import_confirmation":"您確定要匯入資料嗎？這將會覆寫所有目前的筆記與設定。","ai_settings":"AI 設定","ai_settings_description":"選擇您的 AI 助手服務","ai_provider":"AI 供應商","select_ai_provider":"選擇一個 AI 供應商","gemini_model":"Gemini 模型","select_gemini_model":"選擇一個 Gemini 模型","gemini_api_key":"Gemini API 金鑰","enter_gemini_api_key":"請輸入您的 Gemini API 金鑰","openrouter_api_key":"OpenRouter API 金鑰","enter_openrouter_api_key":"請輸入您的 OpenRouter API 金鑰","openrouter_model":"OpenRouter 模型","load_models":"載入模型","loading_models":"載入中...","save_all_settings":"儲存所有設定","settings_saved_success":"設定已成功儲存。","missing_api_key":"缺少 API 金鑰","enter_openrouter_api_key_first":"請先輸入您的 OpenRouter API 金鑰。","load_models_fail":"模型載入失敗","load_models_fail_desc":"無法從 OpenRouter 獲取模型列表。","model_key_settings":"模型與金鑰設定","model_key_settings_description":"配置模型與 API 憑證","select_or_load_model":"選擇或載入模型","load_models_instruction":"輸入金鑰後，點擊「載入模型」以獲取可用模型列表。","continue":"繼續","custom_prompts":"自訂提示詞","custom_prompts_description":"建立和管理您的自訂 AI 提示詞範本，讓 AI 按照您的需求處理文字。","add_custom_prompt":"新增提示詞","edit_custom_prompt":"編輯提示詞","delete_custom_prompt":"刪除提示詞","prompt_name":"提示詞名稱","prompt_description":"提示詞描述","prompt_content":"提示詞內容","prompt_category":"分類","select_category":"選擇分類","save_prompt":"儲存提示詞","create_prompt":"建立提示詞","no_custom_prompts":"目前還沒有自訂提示詞","create_first_prompt":"點擊「新增提示詞」來建立您的第一個提示詞範本","translate":"翻譯","translate_to":"翻譯成","source_language":"原文語言","target_language":"目標語言","auto_detect":"自動檢測","translating":"翻譯中...","extract_todos":"擷取待辦事項","extracting_todos":"擷取待辦事項中...","no_todos_found":"沒有找到待辦事項","todo_high_priority":"高優先級","todo_medium_priority":"中優先級","todo_low_priority":"低優先級","todo_completed":"已完成","todo_pending":"待處理","custom_model":"自訂模型","enter_custom_model":"輸入自訂模型名稱","api_base_url":"API 端點","enter_api_base_url":"輸入自訂 API 端點","validate_key":"驗證","validating":"驗證中...","validation_success":"驗證成功","validation_failed":"驗證失敗","custom_prompts_tab":"自訂提示詞","custom_prompts_management":"自訂提示詞管理","your_prompts":"您的提示詞","add_prompt":"新增提示詞","edit":"編輯","delete":"刪除","edit_prompt":"編輯提示詞","create_new_prompt":"建立新提示詞","edit_existing_prompt":"修改現有的自訂提示詞","create_new_prompt_template":"建立一個新的自訂提示詞範本","enter_prompt_name":"輸入提示詞名稱","description":"描述","describe_prompt_purpose":"簡要描述這個提示詞的用途","category":"分類","category_examples":"例如：寫作、分析、翻譯等","enter_prompt_content":"輸入您的自訂提示詞內容...","cancel":"取消","update":"更新","create":"建立","gemini_api_endpoint":"Gemini API 端點","custom_gemini_endpoint_desc":"自訂 Gemini API 端點。如果留空將使用預設端點。","enter_custom_model_name":"輸入自訂模型名稱 (例如: gemini-2.5-pro)","validate":"驗證","input_error":"輸入錯誤","fill_name_and_content":"請填寫提示詞名稱和內容","success":"成功","prompt_created":"自訂提示詞已建立","create_failed":"建立失敗","cannot_create_prompt":"無法建立自訂提示詞","prompt_updated":"自訂提示詞已更新","update_failed":"更新失敗","cannot_update_prompt":"無法更新自訂提示詞","prompt_deleted":"自訂提示詞已刪除","delete_failed":"刪除失敗","cannot_delete_prompt":"無法刪除自訂提示詞","validation_success_gemini":"Gemini API 金鑰有效","validation_failed_gemini":"Gemini API 金鑰無效或網路錯誤","validation_success_openrouter":"OpenRouter API 金鑰有效","validation_failed_openrouter":"OpenRouter API 金鑰無效或網路錯誤"},"home":{"title":"MyNote","new_note":"新增筆記","ai_settings":"設定","search_notes":"搜尋筆記...","tags":"標籤","search_tags":"搜尋標籤...","no_tags_found":"找不到標籤。","no_title":"無標題","no_content":"沒有額外內容","loading":"載入中..."},"editor":{"title_placeholder":"輸入筆記標題...","tags_placeholder":"新增標籤...","ai_tools":"AI 工具","summarize":"總結筆記","generate_title":"生成標題","polish_selected":"潤飾選定文字","expand_content":"擴展內容","change_tone_professional":"改變語氣 (專業)","change_tone_casual":"改變語氣 (休閒)","translate_text":"翻譯文字","extract_todos":"擷取待辦事項","execute_custom_prompt":"執行自訂提示詞","save":"儲存","edit":"編輯","delete":"刪除","ai_result_title":"AI 處理結果","cancel":"取消","insert_at_end":"插入到筆記結尾","replace_selected":"替換選取內容","expand_content_title":"擴展內容","expand_placeholder":"輸入一個簡短的提示詞或句子...","generate":"生成","generating":"生成中...","suggested_title":"建議標題","suggested_title_desc":"AI 建議使用以下標題，您是否要替換目前的標題？","replace":"替換","ai_operation_failed":"AI 操作失敗","unknown_error":"發生未知錯誤","selection_error":"選擇錯誤","select_text_first":"請先選擇要潤飾的文字。","select_tone_text_first":"請先選擇要改變語氣的文字。","input_error":"輸入錯誤","enter_valid_prompt":"請輸入有效的擴展提示詞。","generate_tags":"生成標籤","tags_generated":"標籤已生成","tags_added_successfully":"標籤已成功添加","content_required_for_tags":"需要內容才能生成標籤"},"note_list":{"search_notes":"搜尋筆記...","no_notes_found":"沒有找到匹配的筆記","no_notes":"還沒有筆記","try_different_search":"嘗試使用不同的搜尋詞","create_first_note":"點擊上方按鈕新增你的第一篇筆記","no_title":"無標題","no_content":"沒有額外內容"},"edit":{"error":"錯誤","electron_api_unavailable":"Electron API 不可用","load_failed":"載入失敗","cannot_load_notes":"無法載入筆記列表","cannot_load_note_content":"無法載入筆記內容","save_success":"儲存成功","note_saved":"筆記已儲存","save_failed":"儲存失敗","cannot_save_note":"無法儲存筆記","delete_success":"刪除成功","note_deleted":"筆記已刪除","delete_failed":"刪除失敗","cannot_delete_note":"無法刪除筆記","new_note":"新增筆記","loading":"載入中...","welcome_title":"歡迎使用 MyNote","welcome_description":"從左側選擇一篇筆記開始編輯，或建立一篇新筆記。"}}'))},73222,e=>{"use strict";e.s(["ThemeProvider",()=>l,"useTheme",()=>_]);var t=e.i(24302),o=(e,t,o,n,a,r,i,s)=>{let _=document.documentElement,l=["light","dark"];function d(t){var o;(Array.isArray(e)?e:[e]).forEach(e=>{let o="class"===e,n=o&&r?a.map(e=>r[e]||e):a;o?(_.classList.remove(...n),_.classList.add(r&&r[t]?r[t]:t)):_.setAttribute(e,t)}),o=t,s&&l.includes(o)&&(_.style.colorScheme=o)}if(n)d(n);else try{let e=localStorage.getItem(t)||o,n=i&&"system"===e?window.matchMedia("(prefers-color-scheme: dark)").matches?"dark":"light":e;d(n)}catch(e){}},n=["light","dark"],a="(prefers-color-scheme: dark)",r="undefined"==typeof window,i=t.createContext(void 0),s={setTheme:e=>{},themes:[]},_=()=>{var e;return null!=(e=t.useContext(i))?e:s},l=e=>t.useContext(i)?t.createElement(t.Fragment,null,e.children):t.createElement(c,{...e}),d=["light","dark"],c=e=>{let{forcedTheme:o,disableTransitionOnChange:r=!1,enableSystem:s=!0,enableColorScheme:_=!0,storageKey:l="theme",themes:c=d,defaultTheme:y=s?"system":"light",attribute:f="data-theme",value:h,children:v,nonce:P,scriptProps:A}=e,[I,C]=t.useState(()=>m(l,y)),[x,w]=t.useState(()=>"system"===I?g():I),S=h?Object.values(h):c,k=t.useCallback(e=>{let t=e;if(!t)return;"system"===e&&s&&(t=g());let o=h?h[t]:t,a=r?u(P):null,i=document.documentElement,l=e=>{"class"===e?(i.classList.remove(...S),o&&i.classList.add(o)):e.startsWith("data-")&&(o?i.setAttribute(e,o):i.removeAttribute(e))};if(Array.isArray(f)?f.forEach(l):l(f),_){let e=n.includes(y)?y:null,o=n.includes(t)?t:e;i.style.colorScheme=o}null==a||a()},[P]),E=t.useCallback(e=>{let t="function"==typeof e?e(I):e;C(t);try{localStorage.setItem(l,t)}catch(e){}},[I]),b=t.useCallback(e=>{w(g(e)),"system"===I&&s&&!o&&k("system")},[I,o]);t.useEffect(()=>{let e=window.matchMedia(a);return e.addListener(b),b(e),()=>e.removeListener(b)},[b]),t.useEffect(()=>{let e=e=>{e.key===l&&(e.newValue?C(e.newValue):E(y))};return window.addEventListener("storage",e),()=>window.removeEventListener("storage",e)},[E]),t.useEffect(()=>{k(null!=o?o:I)},[o,I]);let T=t.useMemo(()=>({theme:I,setTheme:E,forcedTheme:o,resolvedTheme:"system"===I?x:I,themes:s?[...c,"system"]:c,systemTheme:s?x:void 0}),[I,E,o,x,s,c]);return t.createElement(i.Provider,{value:T},t.createElement(p,{forcedTheme:o,storageKey:l,attribute:f,enableSystem:s,enableColorScheme:_,defaultTheme:y,value:h,themes:c,nonce:P,scriptProps:A}),v)},p=t.memo(e=>{let{forcedTheme:n,storageKey:a,attribute:r,enableSystem:i,enableColorScheme:s,defaultTheme:_,value:l,themes:d,nonce:c,scriptProps:p}=e,m=JSON.stringify([r,a,_,n,d,l,i,s]).slice(1,-1);return t.createElement("script",{...p,suppressHydrationWarning:!0,nonce:"undefined"==typeof window?c:"",dangerouslySetInnerHTML:{__html:"(".concat(o.toString(),")(").concat(m,")")}})}),m=(e,t)=>{let o;if(!r){try{o=localStorage.getItem(e)||void 0}catch(e){}return o||t}},u=e=>{let t=document.createElement("style");return e&&t.setAttribute("nonce",e),t.appendChild(document.createTextNode("*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}")),document.head.appendChild(t),()=>{window.getComputedStyle(document.body),setTimeout(()=>{document.head.removeChild(t)},1)}},g=e=>(e||(e=window.matchMedia(a)),e.matches?"dark":"light")},23647,e=>{"use strict";e.s(["ThemeProvider",()=>n]);var t=e.i(67857),o=e.i(73222);function n(e){let{children:n,...a}=e;return(0,t.jsx)(o.ThemeProvider,{...a,children:n})}}]);