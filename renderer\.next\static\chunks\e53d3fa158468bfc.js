(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,52531,e=>{"use strict";let t;e.s(["EditorContext",()=>l,"MarkdownUtil",()=>tP,"TextAreaCommandOrchestrator",()=>ty,"TextAreaTextApi",()=>tC,"bold",()=>eW,"checkedListCommand",()=>e2,"code",()=>eZ,"codeBlock",()=>eF,"codeEdit",()=>e0,"codeLive",()=>e4,"codePreview",()=>e6,"commands",()=>tH,"comment",()=>eV,"default",()=>tR,"divider",()=>eK,"executeCommand",()=>eD,"fullscreen",()=>eY,"getBreaksNeededForEmptyLineAfter",()=>ez,"getBreaksNeededForEmptyLineBefore",()=>eO,"getCommands",()=>tg,"getExtraCommands",()=>tx,"getStateFromTextArea",()=>tv,"getSurroundingWord",()=>eB,"group",()=>e$,"heading",()=>tt,"heading1",()=>e8,"heading2",()=>tr,"heading3",()=>ta,"heading4",()=>tc,"heading5",()=>tl,"heading6",()=>tu,"headingExecute",()=>te,"help",()=>th,"hr",()=>e_,"image",()=>eQ,"insertBeforeEachLine",()=>eq,"insertTextAtPosition",()=>eR,"issue",()=>tf,"italic",()=>eX,"link",()=>eG,"orderedListCommand",()=>e3,"quote",()=>e5,"reducer",()=>s,"selectLine",()=>eP,"selectWord",()=>eH,"strikethrough",()=>e7,"table",()=>tm,"title",()=>tn,"title1",()=>e9,"title2",()=>ti,"title3",()=>to,"title4",()=>ts,"title5",()=>td,"title6",()=>tp,"unorderedListCommand",()=>e1],52531),e.s(["default",()=>tR],22856),e.s(["default",()=>tU],58309);var n,r,i=e.i(90771),a=e.i(3880),o=e.i(24302),c=e.i(21104);function s(e,t){return(0,i.default)({},e,t)}e.s(["EditorContext",()=>l,"reducer",()=>s],19932);var l=o.default.createContext({markdown:""}),d=e.i(67857);function u(e){var{prefixCls:t,groupName:n,commands:r,children:a}=e||{},{barPopup:c={}}=(0,o.useContext)(l);return(0,o.useMemo)(()=>(0,d.jsx)("div",{className:t+"-toolbar-child "+(n&&c[n]?"active":""),onClick:e=>e.stopPropagation(),children:Array.isArray(r)?(0,d.jsx)(m,(0,i.default)({commands:r},e,{isChild:!0})):a}),[r,c,n,t])}function p(e){var{prefixCls:t,overflow:n}=e,{fullscreen:r,preview:a,barPopup:c={},components:s,commandOrchestrator:p,dispatch:m}=(0,o.useContext)(l),f=(0,o.useRef)("");function h(t,n){if(m){var a={barPopup:(0,i.default)({},c)};"preview"===t.keyCommand&&(a.preview=t.value),"fullscreen"===t.keyCommand&&(a.fullscreen=!r),e.commands&&"group"===t.keyCommand?e.commands.forEach(e=>{n===e.groupName?a.barPopup[n]=!0:e.keyCommand&&(a.barPopup[e.groupName]=!1)}):(n||t.parent)&&Object.keys(a.barPopup||{}).forEach(e=>{a.barPopup[e]=!1}),Object.keys(a).length&&m((0,i.default)({},a)),p&&p.executeCommand(t)}}return(0,o.useEffect)(()=>{document&&n&&(r?document.body.style.overflow="hidden":(f.current||(f.current=window.getComputedStyle(document.body,null).overflow),document.body.style.overflow=f.current))},[r,f,n]),(0,d.jsx)("ul",{children:(e.commands||[]).map((e,l)=>{if("divider"===e.keyCommand)return(0,d.jsx)("li",(0,i.default)({},e.liProps,{className:t+"-toolbar-divider"}),l);if(!e.keyCommand)return(0,d.jsx)(o.Fragment,{},l);var f=r&&"fullscreen"===e.keyCommand||"preview"===e.keyCommand&&a===e.value,g=e.children&&"function"==typeof e.children?e.children({getState:()=>p.getState(),textApi:p?p.textApi:void 0,close:()=>h({},e.groupName),execute:()=>h({execute:e.execute}),dispatch:m}):void 0,x=c&&a&&"preview"===a&&!/(preview|fullscreen)/.test(e.keyCommand),v=(null==s?void 0:s.toolbar)||e.render,C=v&&"function"==typeof v?v(e,!!x,h,l):null;return(0,d.jsxs)("li",(0,i.default)({},e.liProps,{className:f?"active":"",children:[C&&o.default.isValidElement(C)&&C,!C&&!e.buttonProps&&e.icon,!C&&e.buttonProps&&o.default.createElement("button",(0,i.default)({type:"button",key:l,disabled:x,"data-name":e.name},e.buttonProps,{onClick:t=>{t.stopPropagation(),h(e,e.groupName)}}),e.icon),e.children&&(0,d.jsx)(u,{overflow:n,groupName:e.groupName,prefixCls:t,children:g,commands:Array.isArray(e.children)?e.children:void 0})]}),l)})})}function m(e){void 0===e&&(e={});var{prefixCls:t,isChild:n,className:r}=e,{commands:a,extraCommands:c}=(0,o.useContext)(l);return(0,d.jsxs)("div",{className:t+"-toolbar "+r,children:[(0,d.jsx)(p,(0,i.default)({},e,{commands:e.commands||a||[]})),!n&&(0,d.jsx)(p,(0,i.default)({},e,{commands:c||[]}))]})}function f(e){var{hideToolbar:t,toolbarBottom:n,placement:r,overflow:i,prefixCls:a}=e;return t||"bottom"===r&&!n||"top"===r&&n?null:(0,d.jsx)(m,{prefixCls:a,overflow:i,className:n?"bottom":""})}function h(e,t,n,r,a){void 0===t&&(t=[]);var o=function e(t,n){return void 0===t&&(t=[]),void 0===n&&(n={}),t.forEach(t=>{t.children&&Array.isArray(t.children)?n=(0,i.default)({},n,e(t.children||[])):t.keyCommand&&t.shortcuts&&t.execute&&(n[t.shortcuts.toLocaleLowerCase()]=t)}),n}(t||[]),c=[];if(e.altKey&&c.push("alt"),e.shiftKey&&c.push("shift"),e.metaKey&&c.push("cmd"),e.ctrlKey&&c.push("ctrl"),c.length>0&&!/(control|alt|meta|shift)/.test(e.key.toLocaleLowerCase())&&c.push(e.key.toLocaleLowerCase()),/escape/.test(e.key.toLocaleLowerCase())&&c.push("escape"),!(c.length<1)){var s=o[c.join("+")]?o[c.join("+")]:void 0;if(Object.keys(o).forEach(e=>{e.split("+").every(e=>/ctrlcmd/.test(e)?c.includes("ctrl")||c.includes("cmd"):c.includes(e))&&(s=o[e])}),s&&n){e.stopPropagation(),e.preventDefault(),n.executeCommand(s,r,a,c);return}}}var g=e.i(92516),x=e.i(38760),v=e.i(36122),C=e.i(154),y=e.i(93655);let b={abandonedHeadElementChild:{reason:"Unexpected metadata element after head",description:"Unexpected element after head. Expected the element before `</head>`",url:!1},abruptClosingOfEmptyComment:{reason:"Unexpected abruptly closed empty comment",description:"Unexpected `>` or `->`. Expected `-->` to close comments"},abruptDoctypePublicIdentifier:{reason:"Unexpected abruptly closed public identifier",description:"Unexpected `>`. Expected a closing `\"` or `'` after the public identifier"},abruptDoctypeSystemIdentifier:{reason:"Unexpected abruptly closed system identifier",description:"Unexpected `>`. Expected a closing `\"` or `'` after the identifier identifier"},absenceOfDigitsInNumericCharacterReference:{reason:"Unexpected non-digit at start of numeric character reference",description:"Unexpected `%c`. Expected `[0-9]` for decimal references or `[0-9a-fA-F]` for hexadecimal references"},cdataInHtmlContent:{reason:"Unexpected CDATA section in HTML",description:"Unexpected `<![CDATA[` in HTML. Remove it, use a comment, or encode special characters instead"},characterReferenceOutsideUnicodeRange:{reason:"Unexpected too big numeric character reference",description:"Unexpectedly high character reference. Expected character references to be at most hexadecimal 10ffff (or decimal 1114111)"},closingOfElementWithOpenChildElements:{reason:"Unexpected closing tag with open child elements",description:"Unexpectedly closing tag. Expected other tags to be closed first",url:!1},controlCharacterInInputStream:{reason:"Unexpected control character",description:"Unexpected control character `%x`. Expected a non-control code point, 0x00, or ASCII whitespace"},controlCharacterReference:{reason:"Unexpected control character reference",description:"Unexpectedly control character in reference. Expected a non-control code point, 0x00, or ASCII whitespace"},disallowedContentInNoscriptInHead:{reason:"Disallowed content inside `<noscript>` in `<head>`",description:"Unexpected text character `%c`. Only use text in `<noscript>`s in `<body>`",url:!1},duplicateAttribute:{reason:"Unexpected duplicate attribute",description:"Unexpectedly double attribute. Expected attributes to occur only once"},endTagWithAttributes:{reason:"Unexpected attribute on closing tag",description:"Unexpected attribute. Expected `>` instead"},endTagWithTrailingSolidus:{reason:"Unexpected slash at end of closing tag",description:"Unexpected `%c-1`. Expected `>` instead"},endTagWithoutMatchingOpenElement:{reason:"Unexpected unopened end tag",description:"Unexpected end tag. Expected no end tag or another end tag",url:!1},eofBeforeTagName:{reason:"Unexpected end of file",description:"Unexpected end of file. Expected tag name instead"},eofInCdata:{reason:"Unexpected end of file in CDATA",description:"Unexpected end of file. Expected `]]>` to close the CDATA"},eofInComment:{reason:"Unexpected end of file in comment",description:"Unexpected end of file. Expected `-->` to close the comment"},eofInDoctype:{reason:"Unexpected end of file in doctype",description:"Unexpected end of file. Expected a valid doctype (such as `<!doctype html>`)"},eofInElementThatCanContainOnlyText:{reason:"Unexpected end of file in element that can only contain text",description:"Unexpected end of file. Expected text or a closing tag",url:!1},eofInScriptHtmlCommentLikeText:{reason:"Unexpected end of file in comment inside script",description:"Unexpected end of file. Expected `-->` to close the comment"},eofInTag:{reason:"Unexpected end of file in tag",description:"Unexpected end of file. Expected `>` to close the tag"},incorrectlyClosedComment:{reason:"Incorrectly closed comment",description:"Unexpected `%c-1`. Expected `-->` to close the comment"},incorrectlyOpenedComment:{reason:"Incorrectly opened comment",description:"Unexpected `%c`. Expected `<!--` to open the comment"},invalidCharacterSequenceAfterDoctypeName:{reason:"Invalid sequence after doctype name",description:"Unexpected sequence at `%c`. Expected `public` or `system`"},invalidFirstCharacterOfTagName:{reason:"Invalid first character in tag name",description:"Unexpected `%c`. Expected an ASCII letter instead"},misplacedDoctype:{reason:"Misplaced doctype",description:"Unexpected doctype. Expected doctype before head",url:!1},misplacedStartTagForHeadElement:{reason:"Misplaced `<head>` start tag",description:"Unexpected start tag `<head>`. Expected `<head>` directly after doctype",url:!1},missingAttributeValue:{reason:"Missing attribute value",description:"Unexpected `%c-1`. Expected an attribute value or no `%c-1` instead"},missingDoctype:{reason:"Missing doctype before other content",description:"Expected a `<!doctype html>` before anything else",url:!1},missingDoctypeName:{reason:"Missing doctype name",description:"Unexpected doctype end at `%c`. Expected `html` instead"},missingDoctypePublicIdentifier:{reason:"Missing public identifier in doctype",description:"Unexpected `%c`. Expected identifier for `public` instead"},missingDoctypeSystemIdentifier:{reason:"Missing system identifier in doctype",description:'Unexpected `%c`. Expected identifier for `system` instead (suggested: `"about:legacy-compat"`)'},missingEndTagName:{reason:"Missing name in end tag",description:"Unexpected `%c`. Expected an ASCII letter instead"},missingQuoteBeforeDoctypePublicIdentifier:{reason:"Missing quote before public identifier in doctype",description:"Unexpected `%c`. Expected `\"` or `'` instead"},missingQuoteBeforeDoctypeSystemIdentifier:{reason:"Missing quote before system identifier in doctype",description:"Unexpected `%c`. Expected `\"` or `'` instead"},missingSemicolonAfterCharacterReference:{reason:"Missing semicolon after character reference",description:"Unexpected `%c`. Expected `;` instead"},missingWhitespaceAfterDoctypePublicKeyword:{reason:"Missing whitespace after public identifier in doctype",description:"Unexpected `%c`. Expected ASCII whitespace instead"},missingWhitespaceAfterDoctypeSystemKeyword:{reason:"Missing whitespace after system identifier in doctype",description:"Unexpected `%c`. Expected ASCII whitespace instead"},missingWhitespaceBeforeDoctypeName:{reason:"Missing whitespace before doctype name",description:"Unexpected `%c`. Expected ASCII whitespace instead"},missingWhitespaceBetweenAttributes:{reason:"Missing whitespace between attributes",description:"Unexpected `%c`. Expected ASCII whitespace instead"},missingWhitespaceBetweenDoctypePublicAndSystemIdentifiers:{reason:"Missing whitespace between public and system identifiers in doctype",description:"Unexpected `%c`. Expected ASCII whitespace instead"},nestedComment:{reason:"Unexpected nested comment",description:"Unexpected `<!--`. Expected `-->`"},nestedNoscriptInHead:{reason:"Unexpected nested `<noscript>` in `<head>`",description:"Unexpected `<noscript>`. Expected a closing tag or a meta element",url:!1},nonConformingDoctype:{reason:"Unexpected non-conforming doctype declaration",description:'Expected `<!doctype html>` or `<!doctype html system "about:legacy-compat">`',url:!1},nonVoidHtmlElementStartTagWithTrailingSolidus:{reason:"Unexpected trailing slash on start tag of non-void element",description:"Unexpected `/`. Expected `>` instead"},noncharacterCharacterReference:{reason:"Unexpected noncharacter code point referenced by character reference",description:"Unexpected code point. Do not use noncharacters in HTML"},noncharacterInInputStream:{reason:"Unexpected noncharacter character",description:"Unexpected code point `%x`. Do not use noncharacters in HTML"},nullCharacterReference:{reason:"Unexpected NULL character referenced by character reference",description:"Unexpected code point. Do not use NULL characters in HTML"},openElementsLeftAfterEof:{reason:"Unexpected end of file",description:"Unexpected end of file. Expected closing tag instead",url:!1},surrogateCharacterReference:{reason:"Unexpected surrogate character referenced by character reference",description:"Unexpected code point. Do not use lone surrogate characters in HTML"},surrogateInInputStream:{reason:"Unexpected surrogate character",description:"Unexpected code point `%x`. Do not use lone surrogate characters in HTML"},unexpectedCharacterAfterDoctypeSystemIdentifier:{reason:"Invalid character after system identifier in doctype",description:"Unexpected character at `%c`. Expected `>`"},unexpectedCharacterInAttributeName:{reason:"Unexpected character in attribute name",description:"Unexpected `%c`. Expected whitespace, `/`, `>`, `=`, or probably an ASCII letter"},unexpectedCharacterInUnquotedAttributeValue:{reason:"Unexpected character in unquoted attribute value",description:"Unexpected `%c`. Quote the attribute value to include it"},unexpectedEqualsSignBeforeAttributeName:{reason:"Unexpected equals sign before attribute name",description:"Unexpected `%c`. Add an attribute name before it"},unexpectedNullCharacter:{reason:"Unexpected NULL character",description:"Unexpected code point `%x`. Do not use NULL characters in HTML"},unexpectedQuestionMarkInsteadOfTagName:{reason:"Unexpected question mark instead of tag name",description:"Unexpected `%c`. Expected an ASCII letter instead"},unexpectedSolidusInTag:{reason:"Unexpected slash in tag",description:"Unexpected `%c-1`. Expected it followed by `>` or in a quoted attribute value"},unknownNamedCharacterReference:{reason:"Unexpected unknown named character reference",description:"Unexpected character reference. Expected known named character references"}},L=/-[a-z]/g,w=/%c(?:([-+])(\d+))?/g,E=/%x/g,A={2:!0,1:!1,0:null},N={};function S(e){return e.charAt(1).toUpperCase()}var T=e.i(98889),k=e.i(92624),j=e.i(37570);let U=/["&'<>`]/g,M=/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,I=/[\x01-\t\v\f\x0E-\x1F\x7F\x81\x8D\x8F\x90\x9D\xA0-\uFFFF]/g,R=/[|\\{}()[\]^$+*?.]/g,H=new WeakMap,P=/[\dA-Fa-f]/,O=/\d/;var z=e.i(28544);let B={nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",fnof:"ƒ",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",bull:"•",hellip:"…",prime:"′",Prime:"″",oline:"‾",frasl:"⁄",weierp:"℘",image:"ℑ",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",lang:"〈",rang:"〉",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",quot:'"',amp:"&",lt:"<",gt:">",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",circ:"ˆ",tilde:"˜",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",permil:"‰",lsaquo:"‹",rsaquo:"›",euro:"€"},D=["cent","copy","divide","gt","lt","not","para","times"],q={}.hasOwnProperty,W={};for(t in B)q.call(B,t)&&(W[B[t]]=t);let F=/[^\dA-Za-z]/;function Z(e,t,n){let r,i=function(e,t,n){let r="&#x"+e.toString(16).toUpperCase();return n&&t&&!P.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);if((n.useNamedReferences||n.useShortestReferences)&&(r=function(e,t,n,r){let i=String.fromCharCode(e);if(q.call(W,i)){let e=W[i],a="&"+e;return n&&z.characterEntitiesLegacy.includes(e)&&!D.includes(e)&&(!r||t&&61!==t&&F.test(String.fromCharCode(t)))?a:a+";"}return""}(e,t,n.omitOptionalSemicolons,n.attribute)),(n.useShortestReferences||!r)&&n.useShortestReferences){let r=function(e,t,n){let r="&#"+String(e);return n&&t&&!O.test(String.fromCharCode(t))?r:r+";"}(e,t,n.omitOptionalSemicolons);r.length<i.length&&(i=r)}return r&&(!n.useShortestReferences||r.length<i.length)?r:i}function V(e,t){let n;var r,i=e,a=Object.assign({format:Z},t);if(i=i.replace(a.subset?(r=a.subset,(n=H.get(r))||(n=function(e){let t=[],n=-1;for(;++n<e.length;)t.push(e[n].replace(R,"\\$&"));return RegExp("(?:"+t.join("|")+")","g")}(r),H.set(r,n)),n):U,o),a.subset||a.escapeOnly)return i;return i.replace(M,function(e,t,n){return a.format((e.charCodeAt(0)-55296)*1024+e.charCodeAt(1)-56320+65536,n.charCodeAt(t+2),a)}).replace(I,o);function o(e,t,n){return a.format(e.charCodeAt(0),n.charCodeAt(t+1),a)}}let K=/^>|^->|<!--|-->|--!>|<!-$/g,Y=[">"],$=["<",">"];var _=e.i(18735),Q=e.i(16944),X=e.i(31617),G=e.i(33361),J=e.i(30634);let ee=er(1),et=er(-1),en=[];function er(e){return function(t,n,r){let i=t?t.children:en,a=(n||0)+e,o=i[a];if(!r)for(;o&&(0,J.whitespace)(o);)a+=e,o=i[a];return o}}let ei={}.hasOwnProperty;function ea(e){return function(t,n,r){return ei.call(e,t.tagName)&&e[t.tagName](t,n,r)}}let eo=ea({body:function(e,t,n){let r=ee(n,t);return!r||"comment"!==r.type},caption:ec,colgroup:ec,dd:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName)},dt:function(e,t,n){let r=ee(n,t);return!!(r&&"element"===r.type&&("dt"===r.tagName||"dd"===r.tagName))},head:ec,html:function(e,t,n){let r=ee(n,t);return!r||"comment"!==r.type},li:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&"li"===r.tagName},optgroup:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&"optgroup"===r.tagName},option:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&("option"===r.tagName||"optgroup"===r.tagName)},p:function(e,t,n){let r=ee(n,t);return r?"element"===r.type&&("address"===r.tagName||"article"===r.tagName||"aside"===r.tagName||"blockquote"===r.tagName||"details"===r.tagName||"div"===r.tagName||"dl"===r.tagName||"fieldset"===r.tagName||"figcaption"===r.tagName||"figure"===r.tagName||"footer"===r.tagName||"form"===r.tagName||"h1"===r.tagName||"h2"===r.tagName||"h3"===r.tagName||"h4"===r.tagName||"h5"===r.tagName||"h6"===r.tagName||"header"===r.tagName||"hgroup"===r.tagName||"hr"===r.tagName||"main"===r.tagName||"menu"===r.tagName||"nav"===r.tagName||"ol"===r.tagName||"p"===r.tagName||"pre"===r.tagName||"section"===r.tagName||"table"===r.tagName||"ul"===r.tagName):!n||"element"!==n.type||"a"!==n.tagName&&"audio"!==n.tagName&&"del"!==n.tagName&&"ins"!==n.tagName&&"map"!==n.tagName&&"noscript"!==n.tagName&&"video"!==n.tagName},rp:es,rt:es,tbody:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName)},td:el,tfoot:function(e,t,n){return!ee(n,t)},th:el,thead:function(e,t,n){let r=ee(n,t);return!!(r&&"element"===r.type&&("tbody"===r.tagName||"tfoot"===r.tagName))},tr:function(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&"tr"===r.tagName}});function ec(e,t,n){let r=ee(n,t,!0);return!r||"comment"!==r.type&&!("text"===r.type&&(0,J.whitespace)(r.value.charAt(0)))}function es(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&("rp"===r.tagName||"rt"===r.tagName)}function el(e,t,n){let r=ee(n,t);return!r||"element"===r.type&&("td"===r.tagName||"th"===r.tagName)}let ed=ea({body:function(e){let t=ee(e,-1,!0);return!t||"comment"!==t.type&&!("text"===t.type&&(0,J.whitespace)(t.value.charAt(0)))&&("element"!==t.type||"meta"!==t.tagName&&"link"!==t.tagName&&"script"!==t.tagName&&"style"!==t.tagName&&"template"!==t.tagName)},colgroup:function(e,t,n){let r=et(n,t),i=ee(e,-1,!0);return!(n&&r&&"element"===r.type&&"colgroup"===r.tagName&&eo(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"col"===i.tagName)},head:function(e){let t=new Set;for(let n of e.children)if("element"===n.type&&("base"===n.tagName||"title"===n.tagName)){if(t.has(n.tagName))return!1;t.add(n.tagName)}let n=e.children[0];return!n||"element"===n.type},html:function(e){let t=ee(e,-1);return!t||"comment"!==t.type},tbody:function(e,t,n){let r=et(n,t),i=ee(e,-1);return!(n&&r&&"element"===r.type&&("thead"===r.tagName||"tbody"===r.tagName)&&eo(r,n.children.indexOf(r),n))&&!!(i&&"element"===i.type&&"tr"===i.tagName)}}),eu={name:[["	\n\f\r &/=>".split(""),"	\n\f\r \"&'/=>`".split("")],["\0	\n\f\r \"&'/<=>".split(""),"\0	\n\f\r \"&'/<=>`".split("")]],unquoted:[["	\n\f\r &>".split(""),"\0	\n\f\r \"&'<=>`".split("")],["\0	\n\f\r \"&'<=>`".split(""),"\0	\n\f\r \"&'<=>`".split("")]],single:[["&'".split(""),"\"&'`".split("")],["\0&'".split(""),"\0\"&'`".split("")]],double:[['"&'.split(""),"\"&'`".split("")],['\0"&'.split(""),"\0\"&'`".split("")]]},ep=["<","&"];function em(e,t,n,r){return n&&"element"===n.type&&("script"===n.tagName||"style"===n.tagName)?e.value:V(e.value,Object.assign({},r.settings.characterReferences,{subset:ep}))}let ef=(0,j.zwitch)("type",{invalid:function(e){throw Error("Expected node, not `"+e+"`")},unknown:function(e){throw Error("Cannot compile unknown node `"+e.type+"`")},handlers:{comment:function(e,t,n,r){return r.settings.bogusComments?"<?"+V(e.value,Object.assign({},r.settings.characterReferences,{subset:Y}))+">":"<!--"+e.value.replace(K,function(e){return V(e,Object.assign({},r.settings.characterReferences,{subset:$}))})+"-->"},doctype:function(e,t,n,r){return"<!"+(r.settings.upperDoctype?"DOCTYPE":"doctype")+(r.settings.tightDoctype?"":" ")+"html>"},element:function(e,t,n,r){let i,a=r.schema,o="svg"!==a.space&&r.settings.omitOptionalTags,c="svg"===a.space?r.settings.closeEmptyElements:r.settings.voids.includes(e.tagName.toLowerCase()),s=[];"html"===a.space&&"svg"===e.tagName&&(r.schema=k.svg);let l=function(e,t){let n,r=[],i=-1;if(t){for(n in t)if(null!==t[n]&&void 0!==t[n]){let i=function(e,t,n){let r,i=(0,X.find)(e.schema,t),a=e.settings.allowParseErrors&&"html"===e.schema.space?0:1,o=+!e.settings.allowDangerousCharacters,c=e.quote;if(i.overloadedBoolean&&(n===i.attribute||""===n)?n=!0:(i.boolean||i.overloadedBoolean)&&("string"!=typeof n||n===i.attribute||""===n)&&(n=!!n),null==n||!1===n||"number"==typeof n&&Number.isNaN(n))return"";let s=V(i.attribute,Object.assign({},e.settings.characterReferences,{subset:eu.name[a][o]}));return!0===n||(n=Array.isArray(n)?(i.commaSeparated?Q.stringify:G.stringify)(n,{padLeft:!e.settings.tightCommaSeparatedLists}):String(n),e.settings.collapseEmptyAttributes&&!n)?s:(e.settings.preferUnquoted&&(r=V(n,Object.assign({},e.settings.characterReferences,{attribute:!0,subset:eu.unquoted[a][o]}))),r!==n&&(e.settings.quoteSmart&&(0,_.ccount)(n,c)>(0,_.ccount)(n,e.alternative)&&(c=e.alternative),r=c+V(n,Object.assign({},e.settings.characterReferences,{subset:("'"===c?eu.single:eu.double)[a][o],attribute:!0}))+c),s+(r?"="+r:r))}(e,n,t[n]);i&&r.push(i)}}for(;++i<r.length;){let t=e.settings.tightAttributes?r[i].charAt(r[i].length-1):void 0;i!==r.length-1&&'"'!==t&&"'"!==t&&(r[i]+=" ")}return r.join("")}(r,e.properties),d=r.all("html"===a.space&&"template"===e.tagName?e.content:e);return r.schema=a,d&&(c=!1),!l&&o&&ed(e,t,n)||(s.push("<",e.tagName,l?" "+l:""),c&&("svg"===a.space||r.settings.closeSelfClosing)&&(i=l.charAt(l.length-1),(!r.settings.tightSelfClosing||"/"===i||i&&'"'!==i&&"'"!==i)&&s.push(" "),s.push("/")),s.push(">")),s.push(d),c||o&&eo(e,t,n)||s.push("</"+e.tagName+">"),s.join("")},raw:function(e,t,n,r){return r.settings.allowDangerousHtml?e.value:em(e,t,n,r)},root:function(e,t,n,r){return r.all(e)},text:em}}),eh={},eg={},ex=[];function ev(e,t,n){return ef(e,t,n,this)}function eC(e){let t=[],n=e&&e.children||ex,r=-1;for(;++r<n.length;)t[r]=this.one(n[r],r,e);return t.join("")}let ey=(0,e.i(2457).unified)().use(function(e){let{emitParseErrors:t,...n}={...this.data("settings"),...e};this.parser=function(e,r){return function(e,t){let n=t||N,r=n.onerror,i=e instanceof C.VFile?e:new C.VFile(e),a=n.fragment?v.parseFragment:v.parse,o=String(i),c=a(o,{sourceCodeLocationInfo:!0,onParseError:n.onerror?function(e){let t=e.code,a=t.replace(L,S),c=n[a],s=null==c||c,l="number"==typeof s?s:+!!s;if(l){let n=b[a];(0,g.ok)(n,"expected known error from `parse5`");let o=new y.VFileMessage(d(n.reason),{place:{start:{line:e.startLine,column:e.startCol,offset:e.startOffset},end:{line:e.endLine,column:e.endCol,offset:e.endOffset}},ruleId:t,source:"hast-util-from-html"});i.path&&(o.file=i.path,o.name=i.path+":"+o.name),o.fatal=A[l],o.note=d(n.description),o.url=!1===n.url?void 0:"https://html.spec.whatwg.org/multipage/parsing.html#parse-error-"+t,(0,g.ok)(r,"`internalOnerror` is not passed if `onerror` is not set"),r(o)}function d(t){return t.replace(w,function(t,n,r){var i;let a=(r?Number.parseInt(r,10):0)*("-"===n?-1:1);return"`"===(i=o.charAt(e.startOffset+a))?"` ` `":i}).replace(E,function(){return"0x"+o.charCodeAt(e.startOffset).toString(16).toUpperCase()})}}:null,scriptingEnabled:!1});return(0,x.fromParse5)(c,{file:i,space:n.space,verbose:n.verbose})}(e,{...n,onerror:t?function(e){r.path&&(e.name=r.path+":"+e.name,e.file=r.path),r.messages.push(e)}:void 0})}}).use(function(e){let t={...this.data("settings"),...e};this.compiler=function(e){let n=t||eh,r=n.quote||'"';if('"'!==r&&"'"!==r)throw Error("Invalid quote `"+r+"`, expected `'` or `\"`");return({one:ev,all:eC,settings:{omitOptionalTags:n.omitOptionalTags||!1,allowParseErrors:n.allowParseErrors||!1,allowDangerousCharacters:n.allowDangerousCharacters||!1,quoteSmart:n.quoteSmart||!1,preferUnquoted:n.preferUnquoted||!1,tightAttributes:n.tightAttributes||!1,upperDoctype:n.upperDoctype||!1,tightDoctype:n.tightDoctype||!1,bogusComments:n.bogusComments||!1,tightCommaSeparatedLists:n.tightCommaSeparatedLists||!1,tightSelfClosing:n.tightSelfClosing||!1,collapseEmptyAttributes:n.collapseEmptyAttributes||!1,allowDangerousHtml:n.allowDangerousHtml||!1,voids:n.voids||T.htmlVoidElements,characterReferences:n.characterReferences||eg,closeSelfClosing:n.closeSelfClosing||!1,closeEmptyElements:n.closeEmptyElements||!1},schema:"svg"===n.space?k.svg:k.html,quote:r,alternative:'"'===r?"'":'"'}).one(Array.isArray(e)?{type:"root",children:e}:e,void 0,void 0)}}).freeze();var eb=e.i(93984),eL=e.i(22228),ew=e.i(4027),eE=e.i(39363);e.i(98994);var eA=e.i(57558);function eN(){eN=function(e,t){return new n(e,void 0,t)};var e=RegExp.prototype,t=new WeakMap;function n(e,r,i){var a=new RegExp(e,r);return t.set(a,i||t.get(e)),eS(a,n.prototype)}function r(e,n){var r=t.get(n);return Object.keys(r).reduce(function(t,n){var i=r[n];if("number"==typeof i)t[n]=e[i];else{for(var a=0;void 0===e[i[a]]&&a+1<i.length;)a++;t[n]=e[i[a]]}return t},Object.create(null))}return function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Super expression must either be null or a function");e.prototype=Object.create(t&&t.prototype,{constructor:{value:e,writable:!0,configurable:!0}}),Object.defineProperty(e,"prototype",{writable:!1}),t&&eS(e,t)}(n,RegExp),n.prototype.exec=function(t){var n=e.exec.call(this,t);if(n){n.groups=r(n,this);var i=n.indices;i&&(i.groups=r(i,this))}return n},n.prototype[Symbol.replace]=function(n,i){if("string"==typeof i){var a=t.get(this);return e[Symbol.replace].call(this,n,i.replace(/\$<([^>]+)>/g,function(e,t){var n=a[t];return"$"+(Array.isArray(n)?n.join("$"):n)}))}if("function"==typeof i){var o=this;return e[Symbol.replace].call(this,n,function(){var e=arguments;return"object"!=typeof e[e.length-1]&&(e=[].slice.call(e)).push(r(e,o)),i.apply(this,e)})}return e[Symbol.replace].call(this,n,i)},eN.apply(this,arguments)}function eS(e,t){return(eS=Object.setPrototypeOf?Object.setPrototypeOf.bind():function(e,t){return e.__proto__=t,e})(e,t)}function eT(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=Array(t);n<t;n++)r[n]=e[n];return r}function ek(e,t){var n="undefined"!=typeof Symbol&&e[Symbol.iterator]||e["@@iterator"];if(n)return(n=n.call(e)).next.bind(n);if(Array.isArray(e)||(n=function(e,t){if(e){if("string"==typeof e)return eT(e,void 0);var n=Object.prototype.toString.call(e).slice(8,-1);return"Object"===n&&e.constructor&&(n=e.constructor.name),"Map"===n||"Set"===n?Array.from(e):"Arguments"===n||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(n)?eT(e,void 0):void 0}}(e))||t&&e&&"number"==typeof e.length){n&&(e=n);var r=0;return function(){return r>=e.length?{done:!0}:{done:!1,value:e[r++]}}}throw TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}e.i(20756);var ej=function(e){return function(t){return void 0===t&&(t={}),function(e,t){if(t&&!e.registered(t))throw Error('The default language "'+t+'" is not registered with refractor.')}(e,t.defaultLanguage),function(e){(0,eb.visit)(e,"element",n)};function n(n,r,i){var a,o;if(i&&"pre"===i.tagName&&"code"===n.tagName){var c=(null==n||null==(a=n.data)?void 0:a.meta)||(null==n||null==(o=n.properties)?void 0:o.metastring)||"";n.properties.className?"boolean"==typeof n.properties.className?n.properties.className=[]:Array.isArray(n.properties.className)||(n.properties.className=[n.properties.className]):n.properties.className=[];var s,l,d=function(e){for(var t,n=ek(e.properties.className);!(t=n()).done;){var r=t.value;if("language-"===r.slice(0,9))return r.slice(9).toLowerCase()}return null}(n);if(!d&&t.defaultLanguage&&n.properties.className.push("language-"+(d=t.defaultLanguage)),n.properties.className.push("code-highlight"),d)try{m=null!=(p=d)&&p.includes("diff-")?d.split("-")[1]:d,s=e.highlight((0,eL.toString)(n),m),i.properties.className=(i.properties.className||[]).concat("language-"+m)}catch(e){if(!t.ignoreMissing||!/Unknown language/.test(e.message))throw e;s=n}else s=n;s.children=(l=1,function e(t){return t.reduce(function(t,n){if("text"===n.type){var r=n.value,i=(r.match(/\n/g)||"").length;if(0===i)n.position={start:{line:l,column:1},end:{line:l,column:1}},t.push(n);else for(var a,o=r.split("\n"),c=ek(o.entries());!(a=c()).done;){var s=a.value,d=s[0],u=s[1];t.push({type:"text",value:d===o.length-1?u:u+"\n",position:{start:{line:l+d,column:1},end:{line:l+d,column:1}}})}return l+=i,t}if(Object.prototype.hasOwnProperty.call(n,"children")){var p=l;return n.children=e(n.children),t.push(n),n.position={start:{line:p,column:1},end:{line:l,column:1}},t}return t.push(n),t},[])})(s.children),s.position=s.children.length>0?{start:{line:s.children[0].position.start.line,column:0},end:{line:s.children[s.children.length-1].position.end.line,column:0}}:{start:{line:0,column:0},end:{line:0,column:0}};for(var u,p,m,f,h=function(e){var t=/{([\d,-]+)}/,n=e.split(",").map(function(e){return e.trim()}).join();if(t.test(n)){var r=t.exec(n)[1],i=(0,eE.default)(r);return function(e){return i.includes(e+1)}}return function(){return!1}}(c),g=(u=eN(/showLineNumbers=(\d+)/i,{lines:1})).test(c)?Number(u.exec(c).groups.lines):1,x=function(e){for(var t=Array(e),n=0;n<e;n++)t[n]={type:"element",tagName:"span",properties:{className:[]},children:[]};return t}(s.position.end.line),v=["showlinenumbers=false",'showlinenumbers="false"',"showlinenumbers={false}"],C=ek(x.entries());!(f=C()).done;)!function(){var e,n,r=f.value,i=r[0],a=r[1];a.properties.className=["code-line"],a.children=(0,ew.filter)(s,function(e){return e.position.start.line<=i+1&&e.position.end.line>=i+1}).children,(c.toLowerCase().includes("showlinenumbers")||!0===t.showLineNumbers||"object"==typeof t.showLineNumbers&&t.showLineNumbers.includes(d))&&!v.some(function(e){return c.toLowerCase().includes(e)})&&(a.properties.line=[(i+g).toString()],a.properties.className.push("line-number")),h(i)&&a.properties.className.push("highlight-line"),("diff"===d||null!=(e=d)&&e.includes("diff-"))&&"-"===(0,eL.toString)(a).substring(0,1)?a.properties.className.push("deleted"):("diff"===d||null!=(n=d)&&n.includes("diff-"))&&"+"===(0,eL.toString)(a).substring(0,1)&&a.properties.className.push("inserted")}();x.length>0&&""===(0,eL.toString)(x[x.length-1]).trim()&&x.pop(),n.children=x}}}},eU=(ej(eA.refractor),ej(eA.refractor));function eM(e){var t,r,{prefixCls:i}=e,{markdown:a="",highlightEnable:c,dispatch:s}=(0,o.useContext)(l),u=o.default.createRef();if((0,o.useEffect)(()=>{u.current&&s&&s({textareaPre:u.current})},[]),!a)return(0,d.jsx)("pre",{ref:u,className:i+"-text-pre wmde-markdown-color"});var p='<pre class="language-markdown '+i+'-text-pre wmde-markdown-color"><code class="language-markdown">'+String.raw(n||(t=["",""],r||(r=t.slice(0)),t.raw=r,n=t),a).replace(/[<&"]/g,e=>({"<":"&lt;",">":"&gt;","&":"&amp;",'"':"&quot;"})[e])+"\n</code></pre>";if(c)try{p=ey().data("settings",{fragment:!0}).use(eU,{ignoreMissing:!0}).processSync(p).toString()}catch(e){}return o.default.createElement("div",{className:"wmde-markdown-color",dangerouslySetInnerHTML:{__html:p||""}})}function eI(e){if("TEXTAREA"!==e.nodeName)return!1;if(void 0===r){var t=document.createElement("textarea");t.value="1",r=!!t.firstChild}return r}function eR(e,t){if(e.focus(),document.selection){var n=document.selection.createRange();n.text=t,n.collapse(!1),n.select();return}if(!(""!==t?document.execCommand&&document.execCommand("insertText",!1,t):document.execCommand&&document.execCommand("delete",!1))){var r=e.selectionStart,i=e.selectionEnd;if("function"==typeof e.setRangeText)e.setRangeText(t);else{var a=document.createRange(),o=document.createTextNode(t);if(eI(e)){var c=e.firstChild;if(c){for(var s=0,l=null,d=null;c&&(null===l||null===d);){var u=c.nodeValue.length;r>=s&&r<=s+u&&a.setStart(l=c,r-s),i>=s&&i<=s+u&&a.setEnd(d=c,i-s),s+=u,c=c.nextSibling}r!==i&&a.deleteContents()}else e.appendChild(o)}if(eI(e)&&"#text"===a.commonAncestorContainer.nodeName)a.insertNode(o);else{var p=e.value;e.value=p.slice(0,r)+t+p.slice(i)}}e.setSelectionRange(r+t.length,r+t.length);var m=document.createEvent("UIEvent");m.initEvent("input",!0,!1),e.dispatchEvent(m)}}function eH(e){var{text:t,selection:n,prefix:r,suffix:i=r}=e,a=n;if(t&&t.length&&n.start===n.end&&(a=eB(t,n.start)),a.start>=r.length&&a.end<=t.length-i.length){var o=t.slice(a.start-r.length,a.end+i.length);if(o.startsWith(r)&&o.endsWith(i))return{start:a.start-r.length,end:a.end+i.length}}return a}function eP(e){var{text:t,selection:n}=e,r=t.slice(0,n.start).lastIndexOf("\n")+1,i=t.slice(n.end).indexOf("\n")+n.end;return i===n.end-1&&(i=t.length),{start:r,end:i}}function eO(e,t){if(void 0===e&&(e=""),0===t)return 0;for(var n=2,r=!0,i=t-1;i>=0&&n>=0;i--)switch(e.charCodeAt(i)){case 32:continue;case 10:n--,r=!1;break;default:return n}return r?0:n}function ez(e,t){if(void 0===e&&(e=""),t===e.length-1)return 0;for(var n=2,r=!0,i=t;i<e.length&&n>=0;i++)switch(e.charCodeAt(i)){case 32:continue;case 10:n--,r=!1;break;default:return n}return r?0:n}function eB(e,t){if(!e)throw Error("Argument 'text' should be truthy");for(var n=e=>" "===e||10===e.charCodeAt(0),r=0,i=e.length,a=t;a-1>-1;a--)if(n(e[a-1])){r=a;break}for(var o=t;o<e.length;o++)if(n(e[o])){i=o;break}return{start:r,end:i}}function eD(e){var{api:t,selectedText:n,selection:r,prefix:i,suffix:a=i}=e;n.length>=i.length+a.length&&n.startsWith(i)&&n.endsWith(a)?(t.replaceSelection(n.slice(i.length,a.length?-a.length:void 0)),t.setSelectionRange({start:r.start-i.length,end:r.end-i.length})):(t.replaceSelection(""+i+n+a),t.setSelectionRange({start:r.start+i.length,end:r.end+i.length}))}function eq(e,t){var n=e.split(/\n/),r=0;return{modifiedText:n.map((e,n)=>{if("string"==typeof t)return e.startsWith(t)?(r-=t.length,e.slice(t.length)):(r+=t.length,t+e);if("function"==typeof t){if(e.startsWith(t(e,n)))return r-=t(e,n).length,e.slice(t(e,n).length);var i=t(e,n);return r+=i.length,t(e,n)+e}throw Error("insertion is expected to be either a string or a function")}).join("\n"),insertionLength:r}}e.s(["TextAreaCommandOrchestrator",()=>ty,"TextAreaTextApi",()=>tC,"getCommands",()=>tg,"getExtraCommands",()=>tx,"getStateFromTextArea",()=>tv],71431),e.s(["insertTextAtPosition",()=>eR],64334),e.s(["executeCommand",()=>eD,"getBreaksNeededForEmptyLineAfter",()=>ez,"getBreaksNeededForEmptyLineBefore",()=>eO,"getSurroundingWord",()=>eB,"insertBeforeEachLine",()=>eq,"selectLine",()=>eP,"selectWord",()=>eH],61251);var eW={name:"bold",keyCommand:"bold",shortcuts:"ctrlcmd+b",prefix:"**",buttonProps:{"aria-label":"Add bold text (ctrl + b)",title:"Add bold text (ctrl + b)"},icon:(0,d.jsx)("svg",{role:"img",width:"12",height:"12",viewBox:"0 0 384 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M304.793 243.891c33.639-18.537 53.657-54.16 53.657-95.693 0-48.236-26.25-87.626-68.626-104.179C265.138 34.01 240.849 32 209.661 32H24c-8.837 0-16 7.163-16 16v33.049c0 8.837 7.163 16 16 16h33.113v318.53H24c-8.837 0-16 7.163-16 16V464c0 8.837 7.163 16 16 16h195.69c24.203 0 44.834-1.289 66.866-7.584C337.52 457.193 376 410.647 376 350.014c0-52.168-26.573-91.684-71.207-106.123zM142.217 100.809h67.444c16.294 0 27.536 2.019 37.525 6.717 15.828 8.479 24.906 26.502 24.906 49.446 0 35.029-20.32 56.79-53.029 56.79h-76.846V100.809zm112.642 305.475c-10.14 4.056-22.677 4.907-31.409 4.907h-81.233V281.943h84.367c39.645 0 63.057 25.38 63.057 63.057.001 28.425-13.66 52.483-34.782 61.284z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix})}},eF={name:"codeBlock",keyCommand:"codeBlock",shortcuts:"ctrlcmd+shift+j",prefix:"```",buttonProps:{"aria-label":"Insert Code Block (ctrl + shift + j)",title:"Insert Code Block (ctrl + shift +j)"},icon:(0,d.jsx)("svg",{width:"13",height:"13",role:"img",viewBox:"0 0 156 156",children:(0,d.jsx)("path",{fill:"currentColor",d:"M110.85 120.575 43.7 120.483333 43.7083334 110.091667 110.85 110.191667 110.841667 120.583333 110.85 120.575ZM85.1333334 87.1916666 43.625 86.7083332 43.7083334 76.3166666 85.2083334 76.7916666 85.1333334 87.1916666 85.1333334 87.1916666ZM110.841667 53.4166666 43.7 53.3166666 43.7083334 42.925 110.85 43.025 110.841667 53.4166666ZM36 138C27.2916666 138 20.75 136.216667 16.4 132.666667 12.1333334 129.2 10 124.308333 10 118L10 95.3333332C10 91.0666666 9.25 88.1333332 7.7333334 86.5333332 6.3166668 84.8416666 3.7333334 84 0 84L0 72C3.7333334 72 6.3083334 71.2 7.7333334 69.6 9.2416668 67.9083334 10 64.9333334 10 60.6666666L10 38C10 31.775 12.1333334 26.8833334 16.4 23.3333332 20.7583334 19.7749998 27.2916666 18 36 18L40.6666668 18 40.6666668 30 36 30C34.0212222 29.9719277 32.1263151 30.7979128 30.8 32.2666666 29.3605875 33.8216362 28.5938182 35.8823287 28.6666668 38L28.6666668 60.6666666C28.6666668 67.5083332 26.6666668 72.4 22.6666668 75.3333332 20.9317416 76.7274684 18.8640675 77.6464347 16.6666668 78 18.8916668 78.35 20.8916668 79.2416666 22.6666668 80.6666666 26.6666668 83.95 28.6666668 88.8416666 28.6666668 95.3333332L28.6666668 118C28.6666668 120.308333 29.3750002 122.216667 30.8 123.733333 32.2166666 125.241667 33.9583334 126 36 126L40.6666668 126 40.6666668 138 36 138 36 138ZM114.116667 126 118.783333 126C120.833333 126 122.566667 125.241667 123.983333 123.733333 125.422746 122.178364 126.189515 120.117671 126.116667 118L126.116667 95.3333332C126.116667 88.8333332 128.116667 83.9499998 132.116667 80.6666666 133.9 79.2416666 135.9 78.35 138.116667 78 135.919156 77.6468047 133.851391 76.7277979 132.116667 75.3333332 128.116667 72.3999998 126.116667 67.5 126.116667 60.6666666L126.116667 38C126.189515 35.8823287 125.422746 33.8216361 123.983333 32.2666666 122.657018 30.7979128 120.762111 29.9719277 118.783333 30L114.116667 30 114.116667 18 118.783333 18C127.5 18 133.983333 19.775 138.25 23.3333332 142.608333 26.8833332 144.783333 31.7749998 144.783333 38L144.783333 60.6666666C144.783333 64.9333332 145.5 67.9083332 146.916667 69.6 148.433333 71.2 151.05 72 154.783333 72L154.783333 84C151.05 84 148.433333 84.8333334 146.916667 86.5333332 145.5 88.1333332 144.783333 91.0666666 144.783333 95.3333332L144.783333 118C144.783333 124.308333 142.616667 129.2 138.25 132.666667 133.983333 136.216667 127.5 138 118.783333 138L114.116667 138 114.116667 126 114.116667 126Z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:"```\n",suffix:"\n```"}),r=t.setSelectionRange(n),i="\n```\n",a="\n```\n";r.selectedText.length>=i.length+a.length-2&&r.selectedText.startsWith(i)&&r.selectedText.endsWith(a)?(i="```\n",a="\n```"):((r.selection.start>=1&&"\n"===e.text.slice(r.selection.start-1,r.selection.start)||0===r.selection.start)&&(i="```\n"),(r.selection.end<=e.text.length-1&&"\n"===e.text.slice(r.selection.end,r.selection.end+1)||r.selection.end===e.text.length)&&(a="\n```"));var o=eH({text:e.text,selection:e.selection,prefix:i,suffix:a}),c=t.setSelectionRange(o);eD({api:t,selectedText:c.selectedText,selection:e.selection,prefix:i,suffix:a})}},eZ={name:"code",keyCommand:"code",shortcuts:"ctrlcmd+j",prefix:"`",buttonProps:{"aria-label":"Insert code (ctrl + j)",title:"Insert code (ctrl + j)"},icon:(0,d.jsx)("svg",{width:"14",height:"14",role:"img",viewBox:"0 0 640 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M278.9 511.5l-61-17.7c-6.4-1.8-10-8.5-8.2-14.9L346.2 8.7c1.8-6.4 8.5-10 14.9-8.2l61 17.7c6.4 1.8 10 8.5 8.2 14.9L293.8 503.3c-1.9 6.4-8.5 10.1-14.9 8.2zm-114-112.2l43.5-46.4c4.6-4.9 4.3-12.7-.8-17.2L117 256l90.6-79.7c5.1-4.5 5.5-12.3.8-17.2l-43.5-46.4c-4.5-4.8-12.1-5.1-17-.5L3.8 247.2c-5.1 4.7-5.1 12.8 0 17.5l144.1 135.1c4.9 4.6 12.5 4.4 17-.5zm327.2.6l144.1-135.1c5.1-4.7 5.1-12.8 0-17.5L492.1 112.1c-4.8-4.5-12.4-4.3-17 .5L431.6 159c-4.6 4.9-4.3 12.7.8 17.2L523 256l-90.6 79.7c-5.1 4.5-5.5 12.3-.8 17.2l43.5 46.4c4.5 4.9 12.1 5.1 17 .6z"})}),execute:(e,t)=>{if(-1===e.selectedText.indexOf("\n")){var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix})}else eF.execute(e,t)}},eV={name:"comment",keyCommand:"comment",shortcuts:"ctrlcmd+/",prefix:"<!-- ",suffix:" -->",buttonProps:{"aria-label":"Insert comment (ctrl + /)",title:"Insert comment (ctrl + /)"},icon:(0,d.jsx)("svg",{height:"1em",width:"1em",viewBox:"0 0 25 25",children:(0,d.jsxs)("g",{fill:"none",fillRule:"evenodd",children:[(0,d.jsx)("polygon",{points:".769 .727 24.981 .727 24.981 24.727 .769 24.727"}),(0,d.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"3",d:"M12.625,23.8787879 L8.125,19.6969697 L5.125,19.6969697 C2.63971863,19.6969697 0.625,17.8247059 0.625,15.5151515 L0.625,7.15151515 C0.625,4.84196074 2.63971863,2.96969697 5.125,2.96969697 L20.125,2.96969697 C22.6102814,2.96969697 24.625,4.84196074 24.625,7.15151515 L24.625,15.5151515 C24.625,17.8247059 22.6102814,19.6969697 20.125,19.6969697 L17.125,19.6969697 L12.625,23.8787879"}),(0,d.jsx)("path",{stroke:"currentColor",strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"3",d:"M10.625,8.54545455 L7.25,11.3333333 L10.625,14.1212121 M15.6875,8.54545455 L19.0625,11.3333333 L15.6875,14.1212121"})]})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix})}},eK={keyCommand:"divider"},eY={name:"fullscreen",keyCommand:"fullscreen",shortcuts:"ctrlcmd+0",value:"fullscreen",buttonProps:{"aria-label":"Toggle fullscreen (ctrl + 0)",title:"Toggle fullscreen (ctrl+ 0)"},icon:(0,d.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:(0,d.jsx)("path",{fill:"currentColor",d:"M118 171.133334L118 342.200271C118 353.766938 126.675 365.333605 141.133333 365.333605L382.634614 365.333605C394.201281 365.333605 405.767948 356.658605 405.767948 342.200271L405.767948 171.133334C405.767948 159.566667 397.092948 148 382.634614 148L141.133333 148C126.674999 148 117.999999 156.675 118 171.133334zM465.353591 413.444444L370 413.444444 370 471.222222 474.0221 471.222222C500.027624 471.222222 520.254143 451 520.254143 425L520.254143 321 462.464089 321 462.464089 413.444444 465.353591 413.444444zM471.0221 43L367 43 367 100.777778 462.353591 100.777778 462.353591 196.111111 520.143647 196.111111 520.143647 89.2222219C517.254144 63.2222219 497.027624 43 471.0221 43zM57.7900547 100.777778L153.143646 100.777778 153.143646 43 46.2320439 43C20.2265191 43 0 63.2222219 0 89.2222219L0 193.222222 57.7900547 193.222222 57.7900547 100.777778zM57.7900547 321L0 321 0 425C0 451 20.2265191 471.222222 46.2320439 471.222223L150.254143 471.222223 150.254143 413.444445 57.7900547 413.444445 57.7900547 321z"})}),execute:(e,t,n,r,i)=>{t.textArea.focus(),i&&n&&r&&n({fullscreen:!r.fullscreen})}};e.s(["group",()=>e$],7481);var e$=(e,t)=>{var n=(0,i.default)({children:e,icon:(0,d.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:(0,d.jsx)("path",{fill:"currentColor",d:"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z"})}),execute:()=>{}},t,{keyCommand:"group"});return Array.isArray(n.children)&&(n.children=n.children.map(e=>{var t=(0,i.default)({},(function(e){if(null==e)throw TypeError("Cannot destructure "+e)}(e),e));return t.parent=n,(0,i.default)({},t)})),n},e_={name:"hr",keyCommand:"hr",shortcuts:"ctrlcmd+h",prefix:"\n\n---\n",suffix:"",buttonProps:{"aria-label":"Insert HR (ctrl + h)",title:"Insert HR (ctrl + h)"},icon:(0,d.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 175 175",children:(0,d.jsx)("path",{fill:"currentColor",d:"M0,129 L175,129 L175,154 L0,154 L0,129 Z M3,9 L28.2158203,9 L28.2158203,47.9824219 L55.7695313,47.9824219 L55.7695313,9 L81.0966797,9 L81.0966797,107.185547 L55.7695313,107.185547 L55.7695313,68.0214844 L28.2158203,68.0214844 L28.2158203,107.185547 L3,107.185547 L3,9 Z M93.1855469,100.603516 L93.1855469,19 L135.211914,19 C143.004922,19 148.960917,19.6679621 153.080078,21.0039063 C157.199239,22.3398504 160.520495,24.8168764 163.043945,28.4350586 C165.567395,32.0532407 166.829102,36.459935 166.829102,41.6552734 C166.829102,46.1826398 165.864267,50.0883625 163.93457,53.3725586 C162.004873,56.6567547 159.351579,59.3193257 155.974609,61.3603516 C153.822255,62.6591862 150.872089,63.7353473 147.124023,64.5888672 C150.129898,65.5908253 152.319329,66.5927684 153.692383,67.5947266 C154.620122,68.2626987 155.965323,69.6913953 157.728027,71.8808594 C159.490731,74.0703234 160.668942,75.7587831 161.262695,76.9462891 L173,100.603516 L144.953125,100.603516 L131.482422,75.6660156 C129.775382,72.4374839 128.253913,70.3408251 126.917969,69.3759766 C125.0996,68.1142515 123.040051,67.4833984 120.739258,67.4833984 L118.512695,67.4833984 L118.512695,100.603516 L93.1855469,100.603516 Z M118.512695,52.0644531 L129.144531,52.0644531 C130.294928,52.0644531 132.521468,51.6933631 135.824219,50.9511719 C137.494149,50.6171858 138.857905,49.7636787 139.915527,48.390625 C140.97315,47.0175713 141.501953,45.4404386 141.501953,43.6591797 C141.501953,41.0244009 140.667001,39.0019602 138.99707,37.5917969 C137.32714,36.1816336 134.191429,35.4765625 129.589844,35.4765625 L117.512695,35.4765625 L118.512695,52.0644531 Z",transform:"translate(0 9)"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);r.selectedText.length>=e.command.prefix.length&&r.selectedText.startsWith(e.command.prefix)||(r=t.setSelectionRange({start:e.selection.start,end:e.selection.start})),eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix})}},eQ={name:"image",keyCommand:"image",shortcuts:"ctrlcmd+k",prefix:"![image](",suffix:")",buttonProps:{"aria-label":"Add image (ctrl + k)",title:"Add image (ctrl + k)"},icon:(0,d.jsx)("svg",{width:"13",height:"13",viewBox:"0 0 20 20",children:(0,d.jsx)("path",{fill:"currentColor",d:"M15 9c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm4-7H1c-.55 0-1 .45-1 1v14c0 .55.45 1 1 1h18c.55 0 1-.45 1-1V3c0-.55-.45-1-1-1zm-1 13l-6-5-2 2-4-5-4 8V4h16v11z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);r.selectedText.includes("http")||r.selectedText.includes("www")?eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}):(n=eH({text:e.text,selection:e.selection,prefix:"![",suffix:"]()"}),0===(r=t.setSelectionRange(n)).selectedText.length?eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:"![image",suffix:"](url)"}):eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:"![",suffix:"]()"}))}},eX={name:"italic",keyCommand:"italic",shortcuts:"ctrlcmd+i",prefix:"*",buttonProps:{"aria-label":"Add italic text (ctrl + i)",title:"Add italic text (ctrl + i)"},icon:(0,d.jsx)("svg",{"data-name":"italic",width:"12",height:"12",role:"img",viewBox:"0 0 320 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M204.758 416h-33.849l62.092-320h40.725a16 16 0 0 0 15.704-12.937l6.242-32C297.599 41.184 290.034 32 279.968 32H120.235a16 16 0 0 0-15.704 12.937l-6.242 32C96.362 86.816 103.927 96 113.993 96h33.846l-62.09 320H46.278a16 16 0 0 0-15.704 12.935l-6.245 32C22.402 470.815 29.967 480 40.034 480h158.479a16 16 0 0 0 15.704-12.935l6.245-32c1.927-9.88-5.638-19.065-15.704-19.065z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix})}},eG={name:"link",keyCommand:"link",shortcuts:"ctrlcmd+l",prefix:"[",suffix:"](url)",buttonProps:{"aria-label":"Add a link (ctrl + l)",title:"Add a link (ctrl + l)"},icon:(0,d.jsx)("svg",{"data-name":"italic",width:"12",height:"12",role:"img",viewBox:"0 0 520 520",children:(0,d.jsx)("path",{fill:"currentColor",d:"M331.751196,182.121107 C392.438214,241.974735 391.605313,337.935283 332.11686,396.871226 C332.005129,396.991316 331.873084,397.121413 331.751196,397.241503 L263.493918,464.491645 C203.291404,523.80587 105.345257,523.797864 45.151885,464.491645 C-15.0506283,405.187427 -15.0506283,308.675467 45.151885,249.371249 L82.8416853,212.237562 C92.836501,202.39022 110.049118,208.9351 110.56511,222.851476 C111.223305,240.5867 114.451306,258.404985 120.407566,275.611815 C122.424812,281.438159 120.983487,287.882964 116.565047,292.23621 L103.272145,305.332975 C74.8052033,333.379887 73.9123737,379.047937 102.098973,407.369054 C130.563883,435.969378 177.350591,436.139505 206.033884,407.879434 L274.291163,340.6393 C302.9257,312.427264 302.805844,266.827265 274.291163,238.733318 C270.531934,235.036561 266.74528,232.16442 263.787465,230.157924 C259.544542,227.2873 256.928256,222.609848 256.731165,217.542518 C256.328935,206.967633 260.13184,196.070508 268.613213,187.714278 L289.998463,166.643567 C295.606326,161.118448 304.403592,160.439942 310.906317,164.911276 C318.353355,170.034591 325.328531,175.793397 331.751196,182.121107 Z M240.704978,55.4828366 L172.447607,122.733236 C172.325719,122.853326 172.193674,122.983423 172.081943,123.103513 C117.703294,179.334654 129.953294,261.569283 185.365841,328.828764 C191.044403,335.721376 198.762988,340.914712 206.209732,346.037661 C212.712465,350.509012 221.510759,349.829503 227.117615,344.305363 L248.502893,323.234572 C256.984277,314.87831 260.787188,303.981143 260.384957,293.406218 C260.187865,288.338869 257.571576,283.661398 253.328648,280.790763 C250.370829,278.78426 246.58417,275.912107 242.824936,272.215337 C214.310216,244.121282 206.209732,204.825874 229.906702,179.334654 L298.164073,112.094263 C326.847404,83.8340838 373.633159,84.0042113 402.099123,112.604645 C430.285761,140.92587 429.393946,186.594095 400.92595,214.641114 L387.63303,227.737929 C383.214584,232.091191 381.773257,238.536021 383.790506,244.362388 C389.746774,261.569283 392.974779,279.387637 393.632975,297.122928 C394.149984,311.039357 411.361608,317.584262 421.356437,307.736882 L459.046288,270.603053 C519.249898,211.29961 519.249898,114.787281 459.047304,55.4828366 C398.853851,-3.82360914 300.907572,-3.83161514 240.704978,55.4828366 Z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);r.selectedText.includes("http")||r.selectedText.includes("www")?(n=eH({text:e.text,selection:e.selection,prefix:"[](",suffix:")"}),r=t.setSelectionRange(n),eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:"[](",suffix:")"})):0===r.selectedText.length?eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:"[title",suffix:"](url)"}):eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix})}},eJ=(e,t,n)=>{var r=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),i=t.setSelectionRange(r),a=eO(i.text,i.selection.start),o=Array(a+1).join("\n"),c=Array(ez(i.text,i.selection.end)+1).join("\n"),{modifiedText:s,insertionLength:l}=eq(i.selectedText,n);if(l<0){var d=i.selection.start,u=i.selection.end;i.selection.start>0&&"\n"===e.text.slice(i.selection.start-1,i.selection.start)&&(d-=1),i.selection.end<e.text.length-1&&"\n"===e.text.slice(i.selection.end,i.selection.end+1)&&(u+=1),t.setSelectionRange({start:d,end:u}),t.replaceSelection(""+s),t.setSelectionRange({start:d,end:d+s.length})}else{t.replaceSelection(""+o+s+c);var p=i.selection.start+a,m=p+s.length;t.setSelectionRange({start:p,end:m})}},e1={name:"unordered-list",keyCommand:"list",shortcuts:"ctrl+shift+u",prefix:"- ",buttonProps:{"aria-label":"Add unordered list (ctrl + shift + u)",title:"Add unordered list (ctrl + shift + u)"},icon:(0,d.jsx)("svg",{"data-name":"unordered-list",width:"12",height:"12",viewBox:"0 0 512 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M96 96c0 26.51-21.49 48-48 48S0 122.51 0 96s21.49-48 48-48 48 21.49 48 48zM48 208c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm0 160c-26.51 0-48 21.49-48 48s21.49 48 48 48 48-21.49 48-48-21.49-48-48-48zm96-236h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"})}),execute:(e,t)=>{eJ(e,t,"- ")}},e3={name:"ordered-list",keyCommand:"list",shortcuts:"ctrl+shift+o",prefix:"1. ",buttonProps:{"aria-label":"Add ordered list (ctrl + shift + o)",title:"Add ordered list (ctrl + shift + o)"},icon:(0,d.jsx)("svg",{"data-name":"ordered-list",width:"12",height:"12",role:"img",viewBox:"0 0 512 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M3.263 139.527c0-7.477 3.917-11.572 11.573-11.572h15.131V88.078c0-5.163.534-10.503.534-10.503h-.356s-1.779 2.67-2.848 3.738c-4.451 4.273-10.504 4.451-15.666-1.068l-5.518-6.231c-5.342-5.341-4.984-11.216.534-16.379l21.72-19.938C32.815 33.602 36.732 32 42.785 32H54.89c7.656 0 11.749 3.916 11.749 11.572v84.384h15.488c7.655 0 11.572 4.094 11.572 11.572v8.901c0 7.477-3.917 11.572-11.572 11.572H14.836c-7.656 0-11.573-4.095-11.573-11.572v-8.902zM2.211 304.591c0-47.278 50.955-56.383 50.955-69.165 0-7.18-5.954-8.755-9.28-8.755-3.153 0-6.479 1.051-9.455 3.852-5.079 4.903-10.507 7.004-16.111 2.451l-8.579-6.829c-5.779-4.553-7.18-9.805-2.803-15.409C13.592 201.981 26.025 192 47.387 192c19.437 0 44.476 10.506 44.476 39.573 0 38.347-46.753 46.402-48.679 56.909h39.049c7.529 0 11.557 4.027 11.557 11.382v8.755c0 7.354-4.028 11.382-11.557 11.382h-67.94c-7.005 0-12.083-4.028-12.083-11.382v-4.028zM5.654 454.61l5.603-9.28c3.853-6.654 9.105-7.004 15.584-3.152 4.903 2.101 9.63 3.152 14.359 3.152 10.155 0 14.358-3.502 14.358-8.23 0-6.654-5.604-9.106-15.934-9.106h-4.728c-5.954 0-9.28-2.101-12.258-7.88l-1.05-1.926c-2.451-4.728-1.226-9.806 2.801-14.884l5.604-7.004c6.829-8.405 12.257-13.483 12.257-13.483v-.35s-4.203 1.051-12.608 1.051H16.685c-7.53 0-11.383-4.028-11.383-11.382v-8.755c0-7.53 3.853-11.382 11.383-11.382h58.484c7.529 0 11.382 4.027 11.382 11.382v3.327c0 5.778-1.401 9.806-5.079 14.183l-17.509 20.137c19.611 5.078 28.716 20.487 28.716 34.845 0 21.363-14.358 44.126-48.503 44.126-16.636 0-28.192-4.728-35.896-9.455-5.779-4.202-6.304-9.805-2.626-15.934zM144 132h352c8.837 0 16-7.163 16-16V76c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16zm0 160h352c8.837 0 16-7.163 16-16v-40c0-8.837-7.163-16-16-16H144c-8.837 0-16 7.163-16 16v40c0 8.837 7.163 16 16 16z"})}),execute:(e,t)=>{eJ(e,t,(e,t)=>t+1+". ")}},e2={name:"checked-list",keyCommand:"list",shortcuts:"ctrl+shift+c",prefix:"- [ ] ",buttonProps:{"aria-label":"Add checked list (ctrl + shift + c)",title:"Add checked list (ctrl + shift + c)"},icon:(0,d.jsx)("svg",{"data-name":"checked-list",width:"12",height:"12",role:"img",viewBox:"0 0 512 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M208 132h288c8.8 0 16-7.2 16-16V76c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zm0 160h288c8.8 0 16-7.2 16-16v-40c0-8.8-7.2-16-16-16H208c-8.8 0-16 7.2-16 16v40c0 8.8 7.2 16 16 16zM64 368c-26.5 0-48.6 21.5-48.6 48s22.1 48 48.6 48 48-21.5 48-48-21.5-48-48-48zm92.5-299l-72.2 72.2-15.6 15.6c-4.7 4.7-12.9 4.7-17.6 0L3.5 109.4c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.3c4.7-4.7 12.3-4.7 17 0l17 16.5c4.6 4.7 4.6 12.3-.1 17zm0 159.6l-72.2 72.2-15.7 15.7c-4.7 4.7-12.9 4.7-17.6 0L3.5 269c-4.7-4.7-4.7-12.3 0-17l15.7-15.7c4.7-4.7 12.3-4.7 17 0l22.7 22.1 63.7-63.7c4.7-4.7 12.3-4.7 17 0l17 17c4.6 4.6 4.6 12.2-.1 16.9z"})}),execute:(e,t)=>{eJ(e,t,(e,t)=>"- [ ] ")}},e6={name:"preview",keyCommand:"preview",value:"preview",shortcuts:"ctrlcmd+9",buttonProps:{"aria-label":"Preview code (ctrl + 9)",title:"Preview code (ctrl + 9)"},icon:(0,d.jsxs)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:[(0,d.jsx)("polygon",{fill:"currentColor",points:"0 71.293 0 122 38.023 123 38.023 398 0 397 0 449.707 91.023 450.413 91.023 72.293"}),(0,d.jsx)("polygon",{fill:"currentColor",points:"148.023 72.293 520 71.293 520 122 200.023 124 200.023 397 520 396 520 449.707 148.023 450.413"})]}),execute:(e,t,n,r,i)=>{t.textArea.focus(),i&&n&&r&&n({preview:"preview"})}},e0={name:"edit",keyCommand:"preview",value:"edit",shortcuts:"ctrlcmd+7",buttonProps:{"aria-label":"Edit code (ctrl + 7)",title:"Edit code (ctrl + 7)"},icon:(0,d.jsxs)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:[(0,d.jsx)("polygon",{fill:"currentColor",points:"0 71.293 0 122 319 122 319 397 0 397 0 449.707 372 449.413 372 71.293"}),(0,d.jsx)("polygon",{fill:"currentColor",points:"429 71.293 520 71.293 520 122 481 123 481 396 520 396 520 449.707 429 449.413"})]}),execute:(e,t,n,r,i)=>{t.textArea.focus(),i&&n&&r&&n({preview:"edit"})}},e4={name:"live",keyCommand:"preview",value:"live",shortcuts:"ctrlcmd+8",buttonProps:{"aria-label":"Live code (ctrl + 8)",title:"Live code (ctrl + 8)"},icon:(0,d.jsxs)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:[(0,d.jsx)("polygon",{fill:"currentColor",points:"0 71.293 0 122 179 122 179 397 0 397 0 449.707 232 449.413 232 71.293"}),(0,d.jsx)("polygon",{fill:"currentColor",points:"289 71.293 520 71.293 520 122 341 123 341 396 520 396 520 449.707 289 449.413"})]}),execute:(e,t,n,r,i)=>{t.textArea.focus(),i&&n&&r&&n({preview:"live"})}},e5={name:"quote",keyCommand:"quote",shortcuts:"ctrlcmd+q",prefix:"> ",buttonProps:{"aria-label":"Insert a quote (ctrl + q)",title:"Insert a quote (ctrl + q)"},icon:(0,d.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:(0,d.jsx)("path",{fill:"currentColor",d:"M520,95.75 L520,225.75 C520,364.908906 457.127578,437.050625 325.040469,472.443125 C309.577578,476.586875 294.396016,464.889922 294.396016,448.881641 L294.396016,414.457031 C294.396016,404.242891 300.721328,395.025078 310.328125,391.554687 C377.356328,367.342187 414.375,349.711094 414.375,274.5 L341.25,274.5 C314.325781,274.5 292.5,252.674219 292.5,225.75 L292.5,95.75 C292.5,68.8257812 314.325781,47 341.25,47 L471.25,47 C498.174219,47 520,68.8257812 520,95.75 Z M178.75,47 L48.75,47 C21.8257813,47 0,68.8257812 0,95.75 L0,225.75 C0,252.674219 21.8257813,274.5 48.75,274.5 L121.875,274.5 C121.875,349.711094 84.8563281,367.342187 17.828125,391.554687 C8.22132813,395.025078 1.89601563,404.242891 1.89601563,414.457031 L1.89601563,448.881641 C1.89601563,464.889922 17.0775781,476.586875 32.5404687,472.443125 C164.627578,437.050625 227.5,364.908906 227.5,225.75 L227.5,95.75 C227.5,68.8257812 205.674219,47 178.75,47 Z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),r=t.setSelectionRange(n),i=eO(r.text,r.selection.start),a=Array(i+1).join("\n"),o=Array(ez(r.text,r.selection.end)+1).join("\n"),c=eq(r.selectedText,e.command.prefix);t.replaceSelection(""+a+c.modifiedText+o);var s=r.selection.start+i,l=s+c.modifiedText.length;t.setSelectionRange({start:s,end:l})}},e7={name:"strikethrough",keyCommand:"strikethrough",shortcuts:"ctrl+shift+x",buttonProps:{"aria-label":"Add strikethrough text (ctrl + shift + x)",title:"Add strikethrough text (ctrl + shift + x)"},prefix:"~~",icon:(0,d.jsx)("svg",{"data-name":"strikethrough",width:"12",height:"12",role:"img",viewBox:"0 0 512 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M496 288H16c-8.837 0-16-7.163-16-16v-32c0-8.837 7.163-16 16-16h480c8.837 0 16 7.163 16 16v32c0 8.837-7.163 16-16 16zm-214.666 16c27.258 12.937 46.524 28.683 46.524 56.243 0 33.108-28.977 53.676-75.621 53.676-32.325 0-76.874-12.08-76.874-44.271V368c0-8.837-7.164-16-16-16H113.75c-8.836 0-16 7.163-16 16v19.204c0 66.845 77.717 101.82 154.487 101.82 88.578 0 162.013-45.438 162.013-134.424 0-19.815-3.618-36.417-10.143-50.6H281.334zm-30.952-96c-32.422-13.505-56.836-28.946-56.836-59.683 0-33.92 30.901-47.406 64.962-47.406 42.647 0 64.962 16.593 64.962 32.985V136c0 8.837 7.164 16 16 16h45.613c8.836 0 16-7.163 16-16v-30.318c0-52.438-71.725-79.875-142.575-79.875-85.203 0-150.726 40.972-150.726 125.646 0 22.71 4.665 41.176 12.777 56.547h129.823z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix})}},e8={name:"heading1",keyCommand:"heading1",shortcuts:"ctrlcmd+1",prefix:"# ",suffix:"",buttonProps:{"aria-label":"Insert Heading 1 (ctrl + 1)",title:"Insert Heading 1 (ctrl + 1)"},icon:(0,d.jsx)("div",{style:{fontSize:18,textAlign:"left"},children:"Heading 1"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},e9=e8;function te(e){var{state:t,api:n,prefix:r,suffix:i=r}=e,a=eP({text:t.text,selection:t.selection}),o=n.setSelectionRange(a);eD({api:n,selectedText:o.selectedText,selection:t.selection,prefix:r,suffix:i})}var tt=(0,i.default)({},e8,{icon:(0,d.jsx)("svg",{width:"12",height:"12",viewBox:"0 0 520 520",children:(0,d.jsx)("path",{fill:"currentColor",d:"M15.7083333,468 C7.03242448,468 0,462.030833 0,454.666667 L0,421.333333 C0,413.969167 7.03242448,408 15.7083333,408 L361.291667,408 C369.967576,408 377,413.969167 377,421.333333 L377,454.666667 C377,462.030833 369.967576,468 361.291667,468 L15.7083333,468 Z M21.6666667,366 C9.69989583,366 0,359.831861 0,352.222222 L0,317.777778 C0,310.168139 9.69989583,304 21.6666667,304 L498.333333,304 C510.300104,304 520,310.168139 520,317.777778 L520,352.222222 C520,359.831861 510.300104,366 498.333333,366 L21.6666667,366 Z M136.835938,64 L136.835937,126 L107.25,126 L107.25,251 L40.75,251 L40.75,126 L-5.68434189e-14,126 L-5.68434189e-14,64 L136.835938,64 Z M212,64 L212,251 L161.648438,251 L161.648438,64 L212,64 Z M378,64 L378,126 L343.25,126 L343.25,251 L281.75,251 L281.75,126 L238,126 L238,64 L378,64 Z M449.047619,189.550781 L520,189.550781 L520,251 L405,251 L405,64 L449.047619,64 L449.047619,189.550781 Z"})})}),tn=tt,tr={name:"heading2",keyCommand:"heading2",shortcuts:"ctrlcmd+2",prefix:"## ",suffix:"",buttonProps:{"aria-label":"Insert Heading 2 (ctrl + 2)",title:"Insert Heading 2 (ctrl + 2)"},icon:(0,d.jsx)("div",{style:{fontSize:16,textAlign:"left"},children:"Heading 2"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},ti=tr,ta={name:"heading3",keyCommand:"heading3",shortcuts:"ctrlcmd+3",prefix:"### ",suffix:"",buttonProps:{"aria-label":"Insert Heading 3 (ctrl + 3)",title:"Insert Heading 3 (ctrl + 3)"},icon:(0,d.jsx)("div",{style:{fontSize:15,textAlign:"left"},children:"Heading 3"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},to=ta,tc={name:"heading4",keyCommand:"heading4",shortcuts:"ctrlcmd+4",prefix:"#### ",suffix:"",buttonProps:{"aria-label":"Insert Heading 4 (ctrl + 4)",title:"Insert Heading 4 (ctrl + 4)"},icon:(0,d.jsx)("div",{style:{fontSize:14,textAlign:"left"},children:"Heading 4"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},ts=tc,tl={name:"heading5",keyCommand:"heading5",shortcuts:"ctrlcmd+5",prefix:"##### ",suffix:"",buttonProps:{"aria-label":"Insert Heading 5 (ctrl + 5)",title:"Insert Heading 5 (ctrl + 5)"},icon:(0,d.jsx)("div",{style:{fontSize:12,textAlign:"left"},children:"Heading 5"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},td=tl,tu={name:"heading6",keyCommand:"heading6",shortcuts:"ctrlcmd+6",prefix:"###### ",suffix:"",buttonProps:{"aria-label":"Insert Heading 6 (ctrl + 6)",title:"Insert Heading 6 (ctrl + 6)"},icon:(0,d.jsx)("div",{style:{fontSize:12,textAlign:"left"},children:"Heading 6"}),execute:(e,t)=>{te({state:e,api:t,prefix:e.command.prefix,suffix:e.command.suffix})}},tp=tu,tm={name:"table",keyCommand:"table",prefix:"\n| Header | Header |\n|--------|--------|\n| Cell | Cell |\n| Cell | Cell |\n| Cell | Cell |\n\n",suffix:"",buttonProps:{"aria-label":"Add table",title:"Add table"},icon:(0,d.jsx)("svg",{role:"img",width:"12",height:"12",viewBox:"0 0 512 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M64 256V160H224v96H64zm0 64H224v96H64V320zm224 96V320H448v96H288zM448 256H288V160H448v96zM64 32C28.7 32 0 60.7 0 96V416c0 35.3 28.7 64 64 64H448c35.3 0 64-28.7 64-64V96c0-35.3-28.7-64-64-64H64z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);r.selectedText.length>=e.command.prefix.length+e.command.suffix.length&&r.selectedText.startsWith(e.command.prefix)||(r=t.setSelectionRange({start:e.selection.start,end:e.selection.start})),eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix})}},tf={name:"issue",keyCommand:"issue",prefix:"#",suffix:"",buttonProps:{"aria-label":"Add issue",title:"Add issue"},icon:(0,d.jsx)("svg",{role:"img",width:"12",height:"12",viewBox:"0 0 448 512",children:(0,d.jsx)("path",{fill:"currentColor",d:"M181.3 32.4c17.4 2.9 29.2 19.4 26.3 36.8L197.8 128l95.1 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3s29.2 19.4 26.3 36.8L357.8 128l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0L325.8 320l58.2 0c17.7 0 32 14.3 32 32s-14.3 32-32 32l-68.9 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8l9.8-58.7-95.1 0-11.5 69.3c-2.9 17.4-19.4 29.2-36.8 26.3s-29.2-19.4-26.3-36.8L90.2 384 32 384c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 21.3-128L64 192c-17.7 0-32-14.3-32-32s14.3-32 32-32l68.9 0 11.5-69.3c2.9-17.4 19.4-29.2 36.8-26.3zM187.1 192L165.8 320l95.1 0 21.3-128-95.1 0z"})}),execute:(e,t)=>{var n=eH({text:e.text,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix}),r=t.setSelectionRange(n);eD({api:t,selectedText:r.selectedText,selection:e.selection,prefix:e.command.prefix,suffix:e.command.suffix})}},th={name:"help",keyCommand:"help",buttonProps:{"aria-label":"Open help",title:"Open help"},icon:(0,d.jsx)("svg",{viewBox:"0 0 16 16",width:"12px",height:"12px",children:(0,d.jsx)("path",{d:"M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8Zm.9 13H7v-1.8h1.9V13Zm-.1-3.6v.5H7.1v-.6c.2-2.1 2-1.9 1.9-3.2.1-.7-.3-1.1-1-1.1-.8 0-1.2.7-1.2 1.6H5c0-1.7 1.2-3 2.9-3 2.3 0 3 1.4 3 2.3.1 2.3-1.9 2-2.1 3.5Z",fill:"currentColor"})}),execute:()=>{window.open("https://www.markdownguide.org/basic-syntax/","_blank","noreferrer")}},tg=()=>[eW,eX,e7,e_,e$([e9,ti,to,ts,td,tp],{name:"title",groupName:"title",buttonProps:{"aria-label":"Insert title",title:"Insert title"}}),eK,eG,e5,eZ,eF,eV,eQ,tm,eK,e1,e3,e2,eK,th],tx=()=>[e0,e4,e6,eK,eY];function tv(e){var t;return{selection:{start:e.selectionStart,end:e.selectionEnd},text:e.value,selectedText:null==(t=e.value)?void 0:t.slice(e.selectionStart,e.selectionEnd)}}class tC{replaceSelection(e){return eR(this.textArea,e),tv(this.textArea)}setSelectionRange(e){return this.textArea.focus(),this.textArea.selectionStart=e.start,this.textArea.selectionEnd=e.end,tv(this.textArea)}constructor(e){this.textArea=void 0,this.textArea=e}}class ty{getState(){return!!this.textArea&&tv(this.textArea)}executeCommand(e,t,n,r){e.execute&&e.execute((0,i.default)({command:e},tv(this.textArea)),this.textApi,t,n,r)}constructor(e){this.textArea=void 0,this.textApi=void 0,this.textArea=e,this.textApi=new tC(e)}}function tb(e){e.stopPropagation(),e.preventDefault()}function tL(e,t){tb(e);var n=e.target,r=new tC(n),i={start:n.selectionStart,end:n.selectionEnd};if(i=eP({text:n.value,selection:i}),(!(t<0)||!(i.start<=0))&&(!(t>0)||!(i.end>=n.value.length))){var a=n.value.slice(i.start,i.end);if(t<0){var o=eP({text:n.value,selection:{start:i.start-1,end:i.start-1}}),c=n.value.slice(o.start,o.end);r.setSelectionRange({start:o.start,end:i.end}),eR(n,a+"\n"+c),r.setSelectionRange({start:o.start,end:o.start+a.length})}else{var s=eP({text:n.value,selection:{start:i.end+1,end:i.end+1}}),l=n.value.slice(s.start,s.end);r.setSelectionRange({start:i.start,end:s.end}),eR(n,l+"\n"+a),r.setSelectionRange({start:s.end-a.length,end:s.end})}}}var tw=["prefixCls","onChange"],tE=["markdown","commands","fullscreen","preview","highlightEnable","extraCommands","tabSize","defaultTabEnable","autoFocusEnd","textareaWarp","dispatch"];function tA(e){var{prefixCls:t,onChange:n}=e,r=(0,a.default)(e,tw),c=(0,o.useContext)(l),{markdown:s,commands:u,fullscreen:p,preview:m,highlightEnable:f,extraCommands:g,tabSize:x,defaultTabEnable:v,autoFocusEnd:C,textareaWarp:y,dispatch:b}=c;(0,a.default)(c,tE);var L=o.default.useRef(null),w=o.default.useRef(),E=o.default.useRef({fullscreen:p,preview:m});(0,o.useEffect)(()=>{E.current={fullscreen:p,preview:m,highlightEnable:f}},[p,m,f]),(0,o.useEffect)(()=>{if(L.current&&b){var e=new ty(L.current);w.current=e,b({textarea:L.current,commandOrchestrator:e})}},[]),(0,o.useEffect)(()=>{if(C&&L.current&&y){L.current.focus();var e=L.current.value.length;L.current.setSelectionRange(e,e),setTimeout(()=>{y&&(y.scrollTop=y.scrollHeight),L.current&&(L.current.scrollTop=L.current.scrollHeight)},0)}},[y]);var A=e=>{!function(e,t,n){void 0===t&&(t=2),void 0===n&&(n=!1);var r=e.target,i=r.value.substr(0,r.selectionStart).split("\n"),a=i[i.length-1],o=new tC(r);if(!n&&e.code&&"tab"===e.code.toLowerCase()){tb(e);var c=Array(t+1).join("  ");if(r.selectionStart===r.selectionEnd)return eR(r,c);var s=r.value.substring(0,r.selectionStart).split("\n"),l=r.value.substring(0,r.selectionEnd).split("\n"),d=[];l.forEach((e,t)=>{e!==s[t]&&d.push(e)});var u=d.join("\n"),p=r.value.substring(r.selectionStart,r.selectionEnd),m=r.value.substring(0,r.selectionStart).length;o.setSelectionRange({start:r.value.indexOf(u),end:r.selectionEnd});var f=eq(u,e.shiftKey?"":c).modifiedText;e.shiftKey&&(f=f.split("\n").map(e=>e.replace(RegExp("^"+c),"")).join("\n")),o.replaceSelection(f);var h=e.shiftKey?-t:t,g=e.shiftKey?-d.length*t:d.length*t;o.setSelectionRange({start:m+h,end:m+p.length+g})}else if(13===e.keyCode&&"enter"===e.code.toLowerCase()&&(/^(-|\*)\s/.test(a)||/^\d+.\s/.test(a))&&!e.shiftKey){tb(e);var x="\n- ";a.startsWith("*")&&(x="\n* "),(a.startsWith("- [ ]")||a.startsWith("- [X]")||a.startsWith("- [x]"))&&(x="\n- [ ] "),/^\d+.\s/.test(a)&&(x="\n"+(parseInt(a)+1)+". "),eR(r,x)}else if(e.code&&"keyd"===e.code.toLowerCase()&&e.ctrlKey){tb(e);var v={start:r.selectionStart,end:r.selectionEnd},C=v;v=eP({text:r.value,selection:v});var y=r.value.slice(v.start,v.end);o.setSelectionRange({start:v.end,end:v.end}),eR(r,"\n"+y),o.setSelectionRange({start:C.start,end:C.end})}else e.code&&"arrowup"===e.code.toLowerCase()&&e.altKey?tL(e,-1):e.code&&"arrowdown"===e.code.toLowerCase()&&e.altKey&&tL(e,1)}(e,x,v),h(e,[...u||[],...g||[]],w.current,b,E.current)};return(0,o.useEffect)(()=>(L.current&&L.current.addEventListener("keydown",A),()=>{L.current&&L.current.removeEventListener("keydown",A)}),[]),(0,d.jsx)("textarea",(0,i.default)({autoComplete:"off",autoCorrect:"off",autoCapitalize:"off",spellCheck:!1},r,{ref:L,className:t+"-text-input "+(r.className?r.className:""),value:s,onChange:e=>{b&&b({markdown:e.target.value}),n&&n(e)}}))}var tN=["prefixCls","className","onScroll","renderTextarea"];function tS(e){var t=e||{},{prefixCls:n,className:r,onScroll:c,renderTextarea:s}=t,u=(0,a.default)(t,tN),{markdown:p,scrollTop:m,commands:f,minHeight:g,highlightEnable:x,extraCommands:v,dispatch:C}=(0,o.useContext)(l),y=o.default.useRef(null),b=o.default.useRef(),L=o.default.createRef();return(0,o.useEffect)(()=>{var e={};L.current&&(e.textareaWarp=L.current||void 0,L.current.scrollTop=m||0),C&&C((0,i.default)({},e))},[]),(0,o.useEffect)(()=>{if(y.current&&C){var e=new ty(y.current);b.current=e,C({textarea:y.current,commandOrchestrator:e})}},[]),(0,d.jsx)("div",{ref:L,className:n+"-area "+(r||""),onScroll:c,children:(0,d.jsx)("div",{className:n+"-text",style:{minHeight:g},children:s?o.default.cloneElement(s((0,i.default)({},u,{value:p,autoComplete:"off",autoCorrect:"off",spellCheck:"false",autoCapitalize:"off",className:n+"-text-input",style:{WebkitTextFillColor:"inherit",overflow:"auto"}}),{dispatch:C,onChange:u.onChange,shortcuts:h,useContext:{commands:f,extraCommands:v,commandOrchestrator:b.current}}),{ref:y}):(0,d.jsxs)(o.Fragment,{children:[x&&(0,d.jsx)(eM,{prefixCls:n}),(0,d.jsx)(tA,(0,i.default)({prefixCls:n},u,{style:x?{}:{WebkitTextFillColor:"initial",overflow:"auto"}}))]})})})}let tT=e=>{var{prefixCls:t,onChange:n}=e||{},r=(0,o.useRef)(null),i=(0,o.useRef)(),a=(0,o.useRef)(e.height);function c(t){if(i.current){var r,a=t.clientY||(null==(r=t.changedTouches[0])?void 0:r.clientY),o=i.current.height+a-i.current.dragY;o>=e.minHeight&&o<=e.maxHeight&&n&&n(i.current.height+(a-i.current.dragY))}}function s(){var e,t;i.current=void 0,document.removeEventListener("mousemove",c),document.removeEventListener("mouseup",s),null==(e=r.current)||e.removeEventListener("touchmove",c),null==(t=r.current)||t.removeEventListener("touchend",s)}function l(e){e.preventDefault();var t,n,o,l=e.clientY||(null==(t=e.changedTouches[0])?void 0:t.clientY);i.current={height:a.current,dragY:l},document.addEventListener("mousemove",c),document.addEventListener("mouseup",s),null==(n=r.current)||n.addEventListener("touchmove",c,{passive:!1}),null==(o=r.current)||o.addEventListener("touchend",s,{passive:!1})}(0,o.useEffect)(()=>{a.current!==e.height&&(a.current=e.height)},[e.height]),(0,o.useEffect)(()=>{if(document){var e,t;null==(e=r.current)||e.addEventListener("touchstart",l,{passive:!1}),null==(t=r.current)||t.addEventListener("mousedown",l)}return()=>{if(document){var e;null==(e=r.current)||e.removeEventListener("touchstart",l),document.removeEventListener("mousemove",c)}}},[]);var u=(0,o.useMemo)(()=>(0,d.jsx)("svg",{viewBox:"0 0 512 512",height:"100%",children:(0,d.jsx)("path",{fill:"currentColor",d:"M304 256c0 26.5-21.5 48-48 48s-48-21.5-48-48 21.5-48 48-48 48 21.5 48 48zm120-48c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48zm-336 0c-26.5 0-48 21.5-48 48s21.5 48 48 48 48-21.5 48-48-21.5-48-48-48z"})}),[]);return(0,d.jsx)("div",{className:t+"-bar",ref:r,children:u})};var tk=["prefixCls","className","value","commands","commandsFilter","direction","extraCommands","height","enableScroll","visibleDragbar","highlightEnable","preview","fullscreen","overflow","previewOptions","textareaProps","maxHeight","minHeight","autoFocus","autoFocusEnd","tabSize","defaultTabEnable","onChange","onStatistics","onHeightChange","hideToolbar","toolbarBottom","components","renderTextarea"],tj=o.default.forwardRef((e,t)=>{var n=e||{},{prefixCls:r="w-md-editor",className:u,value:p,commands:m=tg(),commandsFilter:h,direction:g,extraCommands:x=tx(),height:v=200,enableScroll:C=!0,visibleDragbar:y="boolean"!=typeof e.visiableDragbar||e.visiableDragbar,highlightEnable:b=!0,preview:L="live",fullscreen:w=!1,overflow:E=!0,previewOptions:A={},textareaProps:N,maxHeight:S=1200,minHeight:T=100,autoFocus:k,autoFocusEnd:j=!1,tabSize:U=2,defaultTabEnable:M=!1,onChange:I,onStatistics:R,onHeightChange:H,hideToolbar:P,toolbarBottom:O=!1,components:z,renderTextarea:B}=n,D=(0,a.default)(n,tk),q=m.map(e=>h?h(e,!1):e).filter(Boolean),W=x.map(e=>h?h(e,!0):e).filter(Boolean),[F,Z]=(0,o.useReducer)(s,{markdown:p,preview:L,components:z,height:v,minHeight:T,highlightEnable:b,tabSize:U,defaultTabEnable:M,scrollTop:0,scrollTopPreview:0,commands:q,extraCommands:W,fullscreen:w,barPopup:{}}),V=(0,o.useRef)(null),K=(0,o.useRef)(null),Y=(0,o.useRef)(C);(0,o.useImperativeHandle)(t,()=>(0,i.default)({},F,{container:V.current,dispatch:Z})),(0,o.useMemo)(()=>Y.current=C,[C]),(0,o.useEffect)(()=>{var e={};V.current&&(e.container=V.current||void 0),e.markdown=p||"",e.barPopup={},Z&&Z((0,i.default)({},F,e))},[]);var $=[u,"wmde-markdown-var",g?r+"-"+g:null,r,F.preview?r+"-show-"+F.preview:null,F.fullscreen?r+"-fullscreen":null].filter(Boolean).join(" ").trim();(0,o.useMemo)(()=>p!==F.markdown&&Z({markdown:p||""}),[p,F.markdown]),(0,o.useMemo)(()=>L!==F.preview&&Z({preview:L}),[L]),(0,o.useMemo)(()=>U!==F.tabSize&&Z({tabSize:U}),[U]),(0,o.useMemo)(()=>b!==F.highlightEnable&&Z({highlightEnable:b}),[b]),(0,o.useMemo)(()=>k!==F.autoFocus&&Z({autoFocus:k}),[k]),(0,o.useMemo)(()=>j!==F.autoFocusEnd&&Z({autoFocusEnd:j}),[j]),(0,o.useMemo)(()=>w!==F.fullscreen&&Z({fullscreen:w}),[w]),(0,o.useMemo)(()=>v!==F.height&&Z({height:v}),[v]),(0,o.useMemo)(()=>v!==F.height&&H&&H(F.height,v,F),[v,H,F]),(0,o.useMemo)(()=>m!==F.commands&&Z({commands:q}),[e.commands]),(0,o.useMemo)(()=>x!==F.extraCommands&&Z({extraCommands:W}),[e.extraCommands]);var _=(0,o.useRef)(),Q=(0,o.useRef)("preview"),X=(0,o.useRef)(!1);(0,o.useMemo)(()=>{_.current=F.textareaWarp,F.textareaWarp&&(F.textareaWarp.addEventListener("mouseover",()=>{Q.current="text"}),F.textareaWarp.addEventListener("mouseleave",()=>{Q.current="preview"}))},[F.textareaWarp]);var G=(e,t)=>{if(Y.current){var n=_.current,r=K.current?K.current:void 0;if(X.current||(Q.current=t,X.current=!0),n&&r){var i=(n.scrollHeight-n.offsetHeight)/(r.scrollHeight-r.offsetHeight);e.target===n&&"text"===Q.current&&(r.scrollTop=n.scrollTop/i),e.target===r&&"preview"===Q.current&&(n.scrollTop=r.scrollTop*i);var a=0;"text"===Q.current?a=n.scrollTop||0:"preview"===Q.current&&(a=r.scrollTop||0),Z({scrollTop:a})}}},J=r+"-preview "+(A.className||""),ee=e=>G(e,"preview"),et=(0,o.useMemo)(()=>(0,d.jsx)("div",{ref:K,className:J,children:(0,d.jsx)(c.default,(0,i.default)({},A,{onScroll:ee,source:F.markdown||""}))}),[J,A,F.markdown]),en=(null==z?void 0:z.preview)&&(null==z?void 0:z.preview(F.markdown||"",F,Z));en&&o.default.isValidElement(en)&&(et=(0,d.jsx)("div",{className:J,ref:K,onScroll:ee,children:en}));var er=(0,i.default)({},D.style,{height:F.height||"100%"});return(0,d.jsx)(l.Provider,{value:(0,i.default)({},F,{dispatch:Z}),children:(0,d.jsxs)("div",(0,i.default)({ref:V,className:$},D,{onClick:()=>Z({barPopup:(0,i.default)({},function(e){return void 0===e&&(e={}),Object.keys(e).forEach(t=>{e[t]=!1}),e}(F.barPopup))}),style:er,children:[(0,d.jsx)(f,{hideToolbar:P,toolbarBottom:O,prefixCls:r,overflow:E,placement:"top"}),(0,d.jsxs)("div",{className:r+"-content",children:[/(edit|live)/.test(F.preview||"")&&(0,d.jsx)(tS,(0,i.default)({className:r+"-input",prefixCls:r,autoFocus:k},N,{onChange:e=>{if(I&&I(e.target.value,e,F),N&&N.onChange&&N.onChange(e),F.textarea&&F.textarea instanceof HTMLTextAreaElement&&R){var t=new ty(F.textarea).getState()||{};R((0,i.default)({},t,{lineCount:e.target.value.split("\n").length,length:e.target.value.length}))}},renderTextarea:(null==z?void 0:z.textarea)||B,onScroll:e=>G(e,"text")})),/(live|preview)/.test(F.preview||"")&&et]}),y&&!F.fullscreen&&(0,d.jsx)(tT,{prefixCls:r,height:F.height,maxHeight:S,minHeight:T,onChange:e=>Z({height:e})}),(0,d.jsx)(f,{hideToolbar:P,toolbarBottom:O,prefixCls:r,overflow:E,placement:"bottom"})]}))})});tj.Markdown=c.default;let tU=tj;e.s(["TextAreaCommandOrchestrator",()=>ty,"TextAreaTextApi",()=>tC,"bold",()=>eW,"checkedListCommand",()=>e2,"code",()=>eZ,"codeBlock",()=>eF,"codeEdit",()=>e0,"codeLive",()=>e4,"codePreview",()=>e6,"comment",()=>eV,"divider",()=>eK,"fullscreen",()=>eY,"getCommands",()=>tg,"getExtraCommands",()=>tx,"getStateFromTextArea",()=>tv,"group",()=>e$,"heading",()=>tt,"heading1",()=>e8,"heading2",()=>tr,"heading3",()=>ta,"heading4",()=>tc,"heading5",()=>tl,"heading6",()=>tu,"help",()=>th,"hr",()=>e_,"image",()=>eQ,"issue",()=>tf,"italic",()=>eX,"link",()=>eG,"orderedListCommand",()=>e3,"quote",()=>e5,"strikethrough",()=>e7,"table",()=>tm,"title",()=>tn,"title1",()=>e9,"title2",()=>ti,"title3",()=>to,"title4",()=>ts,"title5",()=>td,"title6",()=>tp,"unorderedListCommand",()=>e1],52915),e.i(71431);var tM=e.i(52915),tI=e.i(61251);e.s([],79218);let tR=tU;e.i(22856);var tH=tM,tP=tI;e.i(7481),e.i(64334),e.i(58309),e.i(19932),e.i(79218)},54787,e=>{e.n(e.i(52531))}]);