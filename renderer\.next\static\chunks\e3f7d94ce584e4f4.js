(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,3626,e=>{"use strict";e.s(["default",()=>tp],3626);var t=e.i(67857),a=e.i(24302),r=e.i(28157),n=e.i(91457),s=e.i(96496),i=e.i(45372),l=e.i(61620),o=e.i(30438),d=e.i(79431),c=e.i(79238),u=e.i(56611),p=e.i(61540),m=e.i(49566),h=e.i(51906),f=e.i(76825),g=e.i(53456),v=e.i(80435),x=e.i(40971),y=e.i(89920),j=e.i(36641),w=e.i(36838),b=e.i(87804),C=e.i(32054),_=e.i(81692),N=e.i(83875),S=e.i(89199),k=e.i(3961),R=e.i(56281),E=e.i(15119),I=e.i(67084),P=[" ","Enter","ArrowUp","ArrowDown"],T=[" ","Enter"],D="Select",[H,M,L]=(0,m.createCollection)(D),[A,F]=(0,f.createContextScope)(D,[L,w.createPopperScope]),V=(0,w.createPopperScope)(),[B,O]=A(D),[z,K]=A(D),W=e=>{let{__scopeSelect:r,children:n,open:s,defaultOpen:i,onOpenChange:l,value:o,defaultValue:d,onValueChange:c,dir:u,name:p,autoComplete:m,disabled:h,required:f,form:v}=e,x=V(r),[y,b]=a.useState(null),[C,_]=a.useState(null),[N,k]=a.useState(!1),R=(0,g.useDirection)(u),[E,I]=(0,S.useControllableState)({prop:s,defaultProp:null!=i&&i,onChange:l,caller:D}),[P,T]=(0,S.useControllableState)({prop:o,defaultProp:d,onChange:c,caller:D}),M=a.useRef(null),L=!y||v||!!y.closest("form"),[A,F]=a.useState(new Set),O=Array.from(A).map(e=>e.props.value).join(";");return(0,t.jsx)(w.Root,{...x,children:(0,t.jsxs)(B,{required:f,scope:r,trigger:y,onTriggerChange:b,valueNode:C,onValueNodeChange:_,valueNodeHasChildren:N,onValueNodeHasChildrenChange:k,contentId:(0,j.useId)(),value:P,onValueChange:T,open:E,onOpenChange:I,dir:R,triggerPointerDownPosRef:M,disabled:h,children:[(0,t.jsx)(H.Provider,{scope:r,children:(0,t.jsx)(z,{scope:e.__scopeSelect,onNativeOptionAdd:a.useCallback(e=>{F(t=>new Set(t).add(e))},[]),onNativeOptionRemove:a.useCallback(e=>{F(t=>{let a=new Set(t);return a.delete(e),a})},[]),children:n})}),L?(0,t.jsxs)(eE,{"aria-hidden":!0,required:f,tabIndex:-1,name:p,autoComplete:m,value:P,onChange:e=>T(e.target.value),disabled:h,form:v,children:[void 0===P?(0,t.jsx)("option",{value:""}):null,Array.from(A)]},O):null]})})};W.displayName=D;var U="SelectTrigger",q=a.forwardRef((e,r)=>{let{__scopeSelect:n,disabled:s=!1,...i}=e,l=V(n),o=O(U,n),d=o.disabled||s,c=(0,h.useComposedRefs)(r,o.onTriggerChange),u=M(n),m=a.useRef("touch"),[f,g,v]=eP(e=>{let t=u().filter(e=>!e.disabled),a=t.find(e=>e.value===o.value),r=eT(t,e,a);void 0!==r&&o.onValueChange(r.value)}),x=e=>{d||(o.onOpenChange(!0),v()),e&&(o.triggerPointerDownPosRef.current={x:Math.round(e.pageX),y:Math.round(e.pageY)})};return(0,t.jsx)(w.Anchor,{asChild:!0,...l,children:(0,t.jsx)(C.Primitive.button,{type:"button",role:"combobox","aria-controls":o.contentId,"aria-expanded":o.open,"aria-required":o.required,"aria-autocomplete":"none",dir:o.dir,"data-state":o.open?"open":"closed",disabled:d,"data-disabled":d?"":void 0,"data-placeholder":eI(o.value)?"":void 0,...i,ref:c,onClick:(0,p.composeEventHandlers)(i.onClick,e=>{e.currentTarget.focus(),"mouse"!==m.current&&x(e)}),onPointerDown:(0,p.composeEventHandlers)(i.onPointerDown,e=>{m.current=e.pointerType;let t=e.target;t.hasPointerCapture(e.pointerId)&&t.releasePointerCapture(e.pointerId),0===e.button&&!1===e.ctrlKey&&"mouse"===e.pointerType&&(x(e),e.preventDefault())}),onKeyDown:(0,p.composeEventHandlers)(i.onKeyDown,e=>{let t=""!==f.current;e.ctrlKey||e.altKey||e.metaKey||1!==e.key.length||g(e.key),(!t||" "!==e.key)&&P.includes(e.key)&&(x(),e.preventDefault())})})})});q.displayName=U;var G="SelectValue",Y=a.forwardRef((e,a)=>{let{__scopeSelect:r,className:n,style:s,children:i,placeholder:l="",...o}=e,d=O(G,r),{onValueNodeHasChildrenChange:c}=d,u=void 0!==i,p=(0,h.useComposedRefs)(a,d.onValueNodeChange);return(0,k.useLayoutEffect)(()=>{c(u)},[c,u]),(0,t.jsx)(C.Primitive.span,{...o,ref:p,style:{pointerEvents:"none"},children:eI(d.value)?(0,t.jsx)(t.Fragment,{children:l}):i})});Y.displayName=G;var X=a.forwardRef((e,a)=>{let{__scopeSelect:r,children:n,...s}=e;return(0,t.jsx)(C.Primitive.span,{"aria-hidden":!0,...s,ref:a,children:n||"▼"})});X.displayName="SelectIcon";var J=e=>(0,t.jsx)(b.Portal,{asChild:!0,...e});J.displayName="SelectPortal";var Z="SelectContent",Q=a.forwardRef((e,r)=>{let n=O(Z,e.__scopeSelect),[s,i]=a.useState();return((0,k.useLayoutEffect)(()=>{i(new DocumentFragment)},[]),n.open)?(0,t.jsx)(ea,{...e,ref:r}):s?c.createPortal((0,t.jsx)($,{scope:e.__scopeSelect,children:(0,t.jsx)(H.Slot,{scope:e.__scopeSelect,children:(0,t.jsx)("div",{children:e.children})})}),s):null});Q.displayName=Z;var[$,ee]=A(Z),et=(0,_.createSlot)("SelectContent.RemoveScroll"),ea=a.forwardRef((e,r)=>{let{__scopeSelect:n,position:s="item-aligned",onCloseAutoFocus:i,onEscapeKeyDown:l,onPointerDownOutside:o,side:d,sideOffset:c,align:u,alignOffset:m,arrowPadding:f,collisionBoundary:g,collisionPadding:j,sticky:w,hideWhenDetached:b,avoidCollisions:C,..._}=e,N=O(Z,n),[S,k]=a.useState(null),[R,P]=a.useState(null),T=(0,h.useComposedRefs)(r,e=>k(e)),[D,H]=a.useState(null),[L,A]=a.useState(null),F=M(n),[V,B]=a.useState(!1),z=a.useRef(!1);a.useEffect(()=>{if(S)return(0,E.hideOthers)(S)},[S]),(0,x.useFocusGuards)();let K=a.useCallback(e=>{let[t,...a]=F().map(e=>e.ref.current),[r]=a.slice(-1),n=document.activeElement;for(let a of e)if(a===n||(null==a||a.scrollIntoView({block:"nearest"}),a===t&&R&&(R.scrollTop=0),a===r&&R&&(R.scrollTop=R.scrollHeight),null==a||a.focus(),document.activeElement!==n))return},[F,R]),W=a.useCallback(()=>K([D,S]),[K,D,S]);a.useEffect(()=>{V&&W()},[V,W]);let{onOpenChange:U,triggerPointerDownPosRef:q}=N;a.useEffect(()=>{if(S){let e={x:0,y:0},t=t=>{var a,r,n,s;e={x:Math.abs(Math.round(t.pageX)-(null!=(n=null==(a=q.current)?void 0:a.x)?n:0)),y:Math.abs(Math.round(t.pageY)-(null!=(s=null==(r=q.current)?void 0:r.y)?s:0))}},a=a=>{e.x<=10&&e.y<=10?a.preventDefault():S.contains(a.target)||U(!1),document.removeEventListener("pointermove",t),q.current=null};return null!==q.current&&(document.addEventListener("pointermove",t),document.addEventListener("pointerup",a,{capture:!0,once:!0})),()=>{document.removeEventListener("pointermove",t),document.removeEventListener("pointerup",a,{capture:!0})}}},[S,U,q]),a.useEffect(()=>{let e=()=>U(!1);return window.addEventListener("blur",e),window.addEventListener("resize",e),()=>{window.removeEventListener("blur",e),window.removeEventListener("resize",e)}},[U]);let[G,Y]=eP(e=>{let t=F().filter(e=>!e.disabled),a=t.find(e=>e.ref.current===document.activeElement),r=eT(t,e,a);r&&setTimeout(()=>r.ref.current.focus())}),X=a.useCallback((e,t,a)=>{let r=!z.current&&!a;(void 0!==N.value&&N.value===t||r)&&(H(e),r&&(z.current=!0))},[N.value]),J=a.useCallback(()=>null==S?void 0:S.focus(),[S]),Q=a.useCallback((e,t,a)=>{let r=!z.current&&!a;(void 0!==N.value&&N.value===t||r)&&A(e)},[N.value]),ee="popper"===s?en:er,ea=ee===en?{side:d,sideOffset:c,align:u,alignOffset:m,arrowPadding:f,collisionBoundary:g,collisionPadding:j,sticky:w,hideWhenDetached:b,avoidCollisions:C}:{};return(0,t.jsx)($,{scope:n,content:S,viewport:R,onViewportChange:P,itemRefCallback:X,selectedItem:D,onItemLeave:J,itemTextRefCallback:Q,focusSelectedItem:W,selectedItemText:L,position:s,isPositioned:V,searchRef:G,children:(0,t.jsx)(I.RemoveScroll,{as:et,allowPinchZoom:!0,children:(0,t.jsx)(y.FocusScope,{asChild:!0,trapped:N.open,onMountAutoFocus:e=>{e.preventDefault()},onUnmountAutoFocus:(0,p.composeEventHandlers)(i,e=>{var t;null==(t=N.trigger)||t.focus({preventScroll:!0}),e.preventDefault()}),children:(0,t.jsx)(v.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:!0,onEscapeKeyDown:l,onPointerDownOutside:o,onFocusOutside:e=>e.preventDefault(),onDismiss:()=>N.onOpenChange(!1),children:(0,t.jsx)(ee,{role:"listbox",id:N.contentId,"data-state":N.open?"open":"closed",dir:N.dir,onContextMenu:e=>e.preventDefault(),..._,...ea,onPlaced:()=>B(!0),ref:T,style:{display:"flex",flexDirection:"column",outline:"none",..._.style},onKeyDown:(0,p.composeEventHandlers)(_.onKeyDown,e=>{let t=e.ctrlKey||e.altKey||e.metaKey;if("Tab"===e.key&&e.preventDefault(),t||1!==e.key.length||Y(e.key),["ArrowUp","ArrowDown","Home","End"].includes(e.key)){let t=F().filter(e=>!e.disabled).map(e=>e.ref.current);if(["ArrowUp","End"].includes(e.key)&&(t=t.slice().reverse()),["ArrowUp","ArrowDown"].includes(e.key)){let a=e.target,r=t.indexOf(a);t=t.slice(r+1)}setTimeout(()=>K(t)),e.preventDefault()}})})})})})})});ea.displayName="SelectContentImpl";var er=a.forwardRef((e,r)=>{let{__scopeSelect:n,onPlaced:s,...i}=e,l=O(Z,n),o=ee(Z,n),[d,c]=a.useState(null),[p,m]=a.useState(null),f=(0,h.useComposedRefs)(r,e=>m(e)),g=M(n),v=a.useRef(!1),x=a.useRef(!0),{viewport:y,selectedItem:j,selectedItemText:w,focusSelectedItem:b}=o,_=a.useCallback(()=>{if(l.trigger&&l.valueNode&&d&&p&&y&&j&&w){let e=l.trigger.getBoundingClientRect(),t=p.getBoundingClientRect(),a=l.valueNode.getBoundingClientRect(),r=w.getBoundingClientRect();if("rtl"!==l.dir){let n=r.left-t.left,s=a.left-n,i=e.left-s,l=e.width+i,o=Math.max(l,t.width),c=window.innerWidth-10,p=(0,u.clamp)(s,[10,Math.max(10,c-o)]);d.style.minWidth=l+"px",d.style.left=p+"px"}else{let n=t.right-r.right,s=window.innerWidth-a.right-n,i=window.innerWidth-e.right-s,l=e.width+i,o=Math.max(l,t.width),c=window.innerWidth-10,p=(0,u.clamp)(s,[10,Math.max(10,c-o)]);d.style.minWidth=l+"px",d.style.right=p+"px"}let n=g(),i=window.innerHeight-20,o=y.scrollHeight,c=window.getComputedStyle(p),m=parseInt(c.borderTopWidth,10),h=parseInt(c.paddingTop,10),f=parseInt(c.borderBottomWidth,10),x=m+h+o+parseInt(c.paddingBottom,10)+f,b=Math.min(5*j.offsetHeight,x),C=window.getComputedStyle(y),_=parseInt(C.paddingTop,10),N=parseInt(C.paddingBottom,10),S=e.top+e.height/2-10,k=j.offsetHeight/2,R=m+h+(j.offsetTop+k);if(R<=S){let e=n.length>0&&j===n[n.length-1].ref.current;d.style.bottom="0px";let t=Math.max(i-S,k+(e?N:0)+(p.clientHeight-y.offsetTop-y.offsetHeight)+f);d.style.height=R+t+"px"}else{let e=n.length>0&&j===n[0].ref.current;d.style.top="0px";let t=Math.max(S,m+y.offsetTop+(e?_:0)+k);d.style.height=t+(x-R)+"px",y.scrollTop=R-S+y.offsetTop}d.style.margin="".concat(10,"px 0"),d.style.minHeight=b+"px",d.style.maxHeight=i+"px",null==s||s(),requestAnimationFrame(()=>v.current=!0)}},[g,l.trigger,l.valueNode,d,p,y,j,w,l.dir,s]);(0,k.useLayoutEffect)(()=>_(),[_]);let[N,S]=a.useState();(0,k.useLayoutEffect)(()=>{p&&S(window.getComputedStyle(p).zIndex)},[p]);let R=a.useCallback(e=>{e&&!0===x.current&&(_(),null==b||b(),x.current=!1)},[_,b]);return(0,t.jsx)(es,{scope:n,contentWrapper:d,shouldExpandOnScrollRef:v,onScrollButtonChange:R,children:(0,t.jsx)("div",{ref:c,style:{display:"flex",flexDirection:"column",position:"fixed",zIndex:N},children:(0,t.jsx)(C.Primitive.div,{...i,ref:f,style:{boxSizing:"border-box",maxHeight:"100%",...i.style}})})})});er.displayName="SelectItemAlignedPosition";var en=a.forwardRef((e,a)=>{let{__scopeSelect:r,align:n="start",collisionPadding:s=10,...i}=e,l=V(r);return(0,t.jsx)(w.Content,{...l,...i,ref:a,align:n,collisionPadding:s,style:{boxSizing:"border-box",...i.style,"--radix-select-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-select-content-available-width":"var(--radix-popper-available-width)","--radix-select-content-available-height":"var(--radix-popper-available-height)","--radix-select-trigger-width":"var(--radix-popper-anchor-width)","--radix-select-trigger-height":"var(--radix-popper-anchor-height)"}})});en.displayName="SelectPopperPosition";var[es,ei]=A(Z,{}),el="SelectViewport",eo=a.forwardRef((e,r)=>{let{__scopeSelect:n,nonce:s,...i}=e,l=ee(el,n),o=ei(el,n),d=(0,h.useComposedRefs)(r,l.onViewportChange),c=a.useRef(0);return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("style",{dangerouslySetInnerHTML:{__html:"[data-radix-select-viewport]{scrollbar-width:none;-ms-overflow-style:none;-webkit-overflow-scrolling:touch;}[data-radix-select-viewport]::-webkit-scrollbar{display:none}"},nonce:s}),(0,t.jsx)(H.Slot,{scope:n,children:(0,t.jsx)(C.Primitive.div,{"data-radix-select-viewport":"",role:"presentation",...i,ref:d,style:{position:"relative",flex:1,overflow:"hidden auto",...i.style},onScroll:(0,p.composeEventHandlers)(i.onScroll,e=>{let t=e.currentTarget,{contentWrapper:a,shouldExpandOnScrollRef:r}=o;if((null==r?void 0:r.current)&&a){let e=Math.abs(c.current-t.scrollTop);if(e>0){let r=window.innerHeight-20,n=Math.max(parseFloat(a.style.minHeight),parseFloat(a.style.height));if(n<r){let s=n+e,i=Math.min(r,s),l=s-i;a.style.height=i+"px","0px"===a.style.bottom&&(t.scrollTop=l>0?l:0,a.style.justifyContent="flex-end")}}}c.current=t.scrollTop})})})]})});eo.displayName=el;var ed="SelectGroup",[ec,eu]=A(ed);a.forwardRef((e,a)=>{let{__scopeSelect:r,...n}=e,s=(0,j.useId)();return(0,t.jsx)(ec,{scope:r,id:s,children:(0,t.jsx)(C.Primitive.div,{role:"group","aria-labelledby":s,...n,ref:a})})}).displayName=ed;var ep="SelectLabel",em=a.forwardRef((e,a)=>{let{__scopeSelect:r,...n}=e,s=eu(ep,r);return(0,t.jsx)(C.Primitive.div,{id:s.id,...n,ref:a})});em.displayName=ep;var eh="SelectItem",[ef,eg]=A(eh),ev=a.forwardRef((e,r)=>{let{__scopeSelect:n,value:s,disabled:i=!1,textValue:l,...o}=e,d=O(eh,n),c=ee(eh,n),u=d.value===s,[m,f]=a.useState(null!=l?l:""),[g,v]=a.useState(!1),x=(0,h.useComposedRefs)(r,e=>{var t;return null==(t=c.itemRefCallback)?void 0:t.call(c,e,s,i)}),y=(0,j.useId)(),w=a.useRef("touch"),b=()=>{i||(d.onValueChange(s),d.onOpenChange(!1))};if(""===s)throw Error("A <Select.Item /> must have a value prop that is not an empty string. This is because the Select value can be set to an empty string to clear the selection and show the placeholder.");return(0,t.jsx)(ef,{scope:n,value:s,disabled:i,textId:y,isSelected:u,onItemTextChange:a.useCallback(e=>{f(t=>{var a;return t||(null!=(a=null==e?void 0:e.textContent)?a:"").trim()})},[]),children:(0,t.jsx)(H.ItemSlot,{scope:n,value:s,disabled:i,textValue:m,children:(0,t.jsx)(C.Primitive.div,{role:"option","aria-labelledby":y,"data-highlighted":g?"":void 0,"aria-selected":u&&g,"data-state":u?"checked":"unchecked","aria-disabled":i||void 0,"data-disabled":i?"":void 0,tabIndex:i?void 0:-1,...o,ref:x,onFocus:(0,p.composeEventHandlers)(o.onFocus,()=>v(!0)),onBlur:(0,p.composeEventHandlers)(o.onBlur,()=>v(!1)),onClick:(0,p.composeEventHandlers)(o.onClick,()=>{"mouse"!==w.current&&b()}),onPointerUp:(0,p.composeEventHandlers)(o.onPointerUp,()=>{"mouse"===w.current&&b()}),onPointerDown:(0,p.composeEventHandlers)(o.onPointerDown,e=>{w.current=e.pointerType}),onPointerMove:(0,p.composeEventHandlers)(o.onPointerMove,e=>{if(w.current=e.pointerType,i){var t;null==(t=c.onItemLeave)||t.call(c)}else"mouse"===w.current&&e.currentTarget.focus({preventScroll:!0})}),onPointerLeave:(0,p.composeEventHandlers)(o.onPointerLeave,e=>{if(e.currentTarget===document.activeElement){var t;null==(t=c.onItemLeave)||t.call(c)}}),onKeyDown:(0,p.composeEventHandlers)(o.onKeyDown,e=>{var t;((null==(t=c.searchRef)?void 0:t.current)===""||" "!==e.key)&&(T.includes(e.key)&&b()," "===e.key&&e.preventDefault())})})})})});ev.displayName=eh;var ex="SelectItemText",ey=a.forwardRef((e,r)=>{let{__scopeSelect:n,className:s,style:i,...l}=e,o=O(ex,n),d=ee(ex,n),u=eg(ex,n),p=K(ex,n),[m,f]=a.useState(null),g=(0,h.useComposedRefs)(r,e=>f(e),u.onItemTextChange,e=>{var t;return null==(t=d.itemTextRefCallback)?void 0:t.call(d,e,u.value,u.disabled)}),v=null==m?void 0:m.textContent,x=a.useMemo(()=>(0,t.jsx)("option",{value:u.value,disabled:u.disabled,children:v},u.value),[u.disabled,u.value,v]),{onNativeOptionAdd:y,onNativeOptionRemove:j}=p;return(0,k.useLayoutEffect)(()=>(y(x),()=>j(x)),[y,j,x]),(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)(C.Primitive.span,{id:u.textId,...l,ref:g}),u.isSelected&&o.valueNode&&!o.valueNodeHasChildren?c.createPortal(l.children,o.valueNode):null]})});ey.displayName=ex;var ej="SelectItemIndicator",ew=a.forwardRef((e,a)=>{let{__scopeSelect:r,...n}=e;return eg(ej,r).isSelected?(0,t.jsx)(C.Primitive.span,{"aria-hidden":!0,...n,ref:a}):null});ew.displayName=ej;var eb="SelectScrollUpButton",eC=a.forwardRef((e,r)=>{let n=ee(eb,e.__scopeSelect),s=ei(eb,e.__scopeSelect),[i,l]=a.useState(!1),o=(0,h.useComposedRefs)(r,s.onScrollButtonChange);return(0,k.useLayoutEffect)(()=>{if(n.viewport&&n.isPositioned){let e=function(){l(t.scrollTop>0)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,t.jsx)(eS,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop-t.offsetHeight)}}):null});eC.displayName=eb;var e_="SelectScrollDownButton",eN=a.forwardRef((e,r)=>{let n=ee(e_,e.__scopeSelect),s=ei(e_,e.__scopeSelect),[i,l]=a.useState(!1),o=(0,h.useComposedRefs)(r,s.onScrollButtonChange);return(0,k.useLayoutEffect)(()=>{if(n.viewport&&n.isPositioned){let e=function(){let e=t.scrollHeight-t.clientHeight;l(Math.ceil(t.scrollTop)<e)},t=n.viewport;return e(),t.addEventListener("scroll",e),()=>t.removeEventListener("scroll",e)}},[n.viewport,n.isPositioned]),i?(0,t.jsx)(eS,{...e,ref:o,onAutoScroll:()=>{let{viewport:e,selectedItem:t}=n;e&&t&&(e.scrollTop=e.scrollTop+t.offsetHeight)}}):null});eN.displayName=e_;var eS=a.forwardRef((e,r)=>{let{__scopeSelect:n,onAutoScroll:s,...i}=e,l=ee("SelectScrollButton",n),o=a.useRef(null),d=M(n),c=a.useCallback(()=>{null!==o.current&&(window.clearInterval(o.current),o.current=null)},[]);return a.useEffect(()=>()=>c(),[c]),(0,k.useLayoutEffect)(()=>{var e;let t=d().find(e=>e.ref.current===document.activeElement);null==t||null==(e=t.ref.current)||e.scrollIntoView({block:"nearest"})},[d]),(0,t.jsx)(C.Primitive.div,{"aria-hidden":!0,...i,ref:r,style:{flexShrink:0,...i.style},onPointerDown:(0,p.composeEventHandlers)(i.onPointerDown,()=>{null===o.current&&(o.current=window.setInterval(s,50))}),onPointerMove:(0,p.composeEventHandlers)(i.onPointerMove,()=>{var e;null==(e=l.onItemLeave)||e.call(l),null===o.current&&(o.current=window.setInterval(s,50))}),onPointerLeave:(0,p.composeEventHandlers)(i.onPointerLeave,()=>{c()})})}),ek=a.forwardRef((e,a)=>{let{__scopeSelect:r,...n}=e;return(0,t.jsx)(C.Primitive.div,{"aria-hidden":!0,...n,ref:a})});ek.displayName="SelectSeparator";var eR="SelectArrow";a.forwardRef((e,a)=>{let{__scopeSelect:r,...n}=e,s=V(r),i=O(eR,r),l=ee(eR,r);return i.open&&"popper"===l.position?(0,t.jsx)(w.Arrow,{...s,...n,ref:a}):null}).displayName=eR;var eE=a.forwardRef((e,r)=>{let{__scopeSelect:n,value:s,...i}=e,l=a.useRef(null),o=(0,h.useComposedRefs)(r,l),d=function(e){let t=a.useRef({value:e,previous:e});return a.useMemo(()=>(t.current.value!==e&&(t.current.previous=t.current.value,t.current.value=e),t.current.previous),[e])}(s);return a.useEffect(()=>{let e=l.current;if(!e)return;let t=Object.getOwnPropertyDescriptor(window.HTMLSelectElement.prototype,"value").set;if(d!==s&&t){let a=new Event("change",{bubbles:!0});t.call(e,s),e.dispatchEvent(a)}},[d,s]),(0,t.jsx)(C.Primitive.select,{...i,style:{...R.VISUALLY_HIDDEN_STYLES,...i.style},ref:o,defaultValue:s})});function eI(e){return""===e||void 0===e}function eP(e){let t=(0,N.useCallbackRef)(e),r=a.useRef(""),n=a.useRef(0),s=a.useCallback(e=>{let a=r.current+e;t(a),function e(t){r.current=t,window.clearTimeout(n.current),""!==t&&(n.current=window.setTimeout(()=>e(""),1e3))}(a)},[t]),i=a.useCallback(()=>{r.current="",window.clearTimeout(n.current)},[]);return a.useEffect(()=>()=>window.clearTimeout(n.current),[]),[r,s,i]}function eT(e,t,a){var r,n;let s=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=a?e.indexOf(a):-1,l=(r=e,n=Math.max(i,0),r.map((e,t)=>r[(n+t)%r.length]));1===s.length&&(l=l.filter(e=>e!==a));let o=l.find(e=>e.textValue.toLowerCase().startsWith(s.toLowerCase()));return o!==a?o:void 0}eE.displayName="SelectBubbleInput";var eD=e.i(17412),eH=e.i(93137);let eM=(0,eH.default)("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),eL=(0,eH.default)("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]);var eA=e.i(92072);let eF=a.forwardRef((e,a)=>{let{className:r,children:n,...s}=e;return(0,t.jsxs)(q,{ref:a,className:(0,eA.cn)("flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background data-[placeholder]:text-muted-foreground focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 [&>span]:line-clamp-1",r),...s,children:[n,(0,t.jsx)(X,{asChild:!0,children:(0,t.jsx)(eM,{className:"w-4 h-4 opacity-50"})})]})});eF.displayName=q.displayName;let eV=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(eC,{ref:a,className:(0,eA.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,t.jsx)(eL,{className:"w-4 h-4"})})});eV.displayName=eC.displayName;let eB=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(eN,{ref:a,className:(0,eA.cn)("flex cursor-default items-center justify-center py-1",r),...n,children:(0,t.jsx)(eM,{className:"w-4 h-4"})})});eB.displayName=eN.displayName;let eO=a.forwardRef((e,a)=>{let{className:r,children:n,position:s="popper",...i}=e;return(0,t.jsx)(J,{children:(0,t.jsxs)(Q,{ref:a,className:(0,eA.cn)("relative z-50 max-h-[--radix-select-content-available-height] min-w-[8rem] overflow-y-auto overflow-x-hidden rounded-md border border-border bg-popover text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-select-content-transform-origin]","popper"===s&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1",r),position:s,...i,children:[(0,t.jsx)(eV,{}),(0,t.jsx)(eo,{className:(0,eA.cn)("p-1","popper"===s&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"),children:n}),(0,t.jsx)(eB,{})]})})});eO.displayName=Q.displayName,a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(em,{ref:a,className:(0,eA.cn)("py-1.5 pl-8 pr-2 text-sm font-semibold",r),...n})}).displayName=em.displayName;let ez=a.forwardRef((e,a)=>{let{className:r,children:n,...s}=e;return(0,t.jsxs)(ev,{ref:a,className:(0,eA.cn)("relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",r),...s,children:[(0,t.jsx)("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:(0,t.jsx)(ew,{children:(0,t.jsx)(eD.Check,{className:"w-4 h-4"})})}),(0,t.jsx)(ey,{children:n})]})});ez.displayName=ev.displayName,a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(ek,{ref:a,className:(0,eA.cn)("-mx-1 my-1 h-px bg-muted",r),...n})}).displayName=ek.displayName;var eK=a.forwardRef((e,a)=>(0,t.jsx)(C.Primitive.label,{...e,ref:a,onMouseDown:t=>{var a;t.target.closest("button, input, select, textarea")||(null==(a=e.onMouseDown)||a.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));eK.displayName="Label";let eW=(0,e.i(68316).cva)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"),eU=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(eK,{ref:a,className:(0,eA.cn)(eW(),r),...n})});eU.displayName=eK.displayName;var eq=e.i(63772),eG=e.i(22859),eY="Tabs",[eX,eJ]=(0,f.createContextScope)(eY,[eq.createRovingFocusGroupScope]),eZ=(0,eq.createRovingFocusGroupScope)(),[eQ,e$]=eX(eY),e0=a.forwardRef((e,a)=>{let{__scopeTabs:r,value:n,onValueChange:s,defaultValue:i,orientation:l="horizontal",dir:o,activationMode:d="automatic",...c}=e,u=(0,g.useDirection)(o),[p,m]=(0,S.useControllableState)({prop:n,onChange:s,defaultProp:null!=i?i:"",caller:eY});return(0,t.jsx)(eQ,{scope:r,baseId:(0,j.useId)(),value:p,onValueChange:m,orientation:l,dir:u,activationMode:d,children:(0,t.jsx)(C.Primitive.div,{dir:u,"data-orientation":l,...c,ref:a})})});e0.displayName=eY;var e1="TabsList",e2=a.forwardRef((e,a)=>{let{__scopeTabs:r,loop:n=!0,...s}=e,i=e$(e1,r),l=eZ(r);return(0,t.jsx)(eq.Root,{asChild:!0,...l,orientation:i.orientation,dir:i.dir,loop:n,children:(0,t.jsx)(C.Primitive.div,{role:"tablist","aria-orientation":i.orientation,...s,ref:a})})});e2.displayName=e1;var e4="TabsTrigger",e5=a.forwardRef((e,a)=>{let{__scopeTabs:r,value:n,disabled:s=!1,...i}=e,l=e$(e4,r),o=eZ(r),d=e7(l.baseId,n),c=e9(l.baseId,n),u=n===l.value;return(0,t.jsx)(eq.Item,{asChild:!0,...o,focusable:!s,active:u,children:(0,t.jsx)(C.Primitive.button,{type:"button",role:"tab","aria-selected":u,"aria-controls":c,"data-state":u?"active":"inactive","data-disabled":s?"":void 0,disabled:s,id:d,...i,ref:a,onMouseDown:(0,p.composeEventHandlers)(e.onMouseDown,e=>{s||0!==e.button||!1!==e.ctrlKey?e.preventDefault():l.onValueChange(n)}),onKeyDown:(0,p.composeEventHandlers)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&l.onValueChange(n)}),onFocus:(0,p.composeEventHandlers)(e.onFocus,()=>{let e="manual"!==l.activationMode;u||s||!e||l.onValueChange(n)})})})});e5.displayName=e4;var e3="TabsContent",e6=a.forwardRef((e,r)=>{let{__scopeTabs:n,value:s,forceMount:i,children:l,...o}=e,d=e$(e3,n),c=e7(d.baseId,s),u=e9(d.baseId,s),p=s===d.value,m=a.useRef(p);return a.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,t.jsx)(eG.Presence,{present:i||p,children:a=>{let{present:n}=a;return(0,t.jsx)(C.Primitive.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":c,hidden:!n,id:u,tabIndex:0,...o,ref:r,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&l})}})});function e7(e,t){return"".concat(e,"-trigger-").concat(t)}function e9(e,t){return"".concat(e,"-content-").concat(t)}e6.displayName=e3;let e8=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(e2,{ref:a,className:(0,eA.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",r),...n})});e8.displayName=e2.displayName;let te=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(e5,{ref:a,className:(0,eA.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",r),...n})});te.displayName=e5.displayName;let tt=a.forwardRef((e,a)=>{let{className:r,...n}=e;return(0,t.jsx)(e6,{ref:a,className:(0,eA.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",r),...n})});tt.displayName=e6.displayName;var ta=e.i(29841),tr=e.i(6458);let tn=(0,eH.default)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),ts=(0,eH.default)("globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]),ti=(0,eH.default)("download",[["path",{d:"M12 15V3",key:"m9g1x1"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["path",{d:"m7 10 5 5 5-5",key:"brsn70"}]]),tl=(0,eH.default)("upload",[["path",{d:"M12 3v12",key:"1x0j5s"}],["path",{d:"m17 8-5-5-5 5",key:"7q97r8"}],["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}]]),to=(0,eH.default)("brain",[["path",{d:"M12 18V5",key:"adv99a"}],["path",{d:"M15 13a4.17 4.17 0 0 1-3-4 4.17 4.17 0 0 1-3 4",key:"1e3is1"}],["path",{d:"M17.598 6.5A3 3 0 1 0 12 5a3 3 0 1 0-5.598 1.5",key:"1gqd8o"}],["path",{d:"M17.997 5.125a4 4 0 0 1 2.526 5.77",key:"iwvgf7"}],["path",{d:"M18 18a4 4 0 0 0 2-7.464",key:"efp6ie"}],["path",{d:"M19.967 17.483A4 4 0 1 1 12 18a4 4 0 1 1-7.967-.517",key:"1gq6am"}],["path",{d:"M6 18a4 4 0 0 1-2-7.464",key:"k1g0md"}],["path",{d:"M6.003 5.125a4 4 0 0 0-2.526 5.77",key:"q97ue3"}]]),td=(0,eH.default)("key",[["path",{d:"m15.5 7.5 2.3 2.3a1 1 0 0 0 1.4 0l2.1-2.1a1 1 0 0 0 0-1.4L19 4",key:"g0fldk"}],["path",{d:"m21 2-9.6 9.6",key:"1j0ho8"}],["circle",{cx:"7.5",cy:"15.5",r:"5.5",key:"yqb3hr"}]]),tc=(0,eH.default)("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]);var tu=e.i(47244);function tp(){let{t:e,setLocale:c,locale:u}=(0,tu.useI18n)(),[p,m]=(0,a.useState)(""),[h,f]=(0,a.useState)(""),[g,v]=(0,a.useState)("gemini"),[x,y]=(0,a.useState)("gemini-1.5-pro-latest"),[j,w]=(0,a.useState)("mistralai/mistral-7b-instruct"),[b,C]=(0,a.useState)("https://rainbow-gumption-2fc85c.netlify.app/"),[_,N]=(0,a.useState)(""),[S,k]=(0,a.useState)([]),[R,E]=(0,a.useState)([]),[I,P]=(0,a.useState)(!1),[T,D]=(0,a.useState)(!1),[H,M]=(0,a.useState)(!1),[L,A]=(0,a.useState)(!1),[F,V]=(0,a.useState)(!1),[B,O]=(0,a.useState)(!1),{toast:z}=(0,l.useToast)();(0,a.useEffect)(()=>{let e=localStorage.getItem("gemini_api_key"),t=localStorage.getItem("openrouter_api_key"),a=localStorage.getItem("ai_provider"),r=localStorage.getItem("gemini_model"),n=localStorage.getItem("openrouter_model"),s=localStorage.getItem("gemini_api_base_url");e&&m(e),t&&f(t),a&&v(a),r&&y(r),n&&w(n),s&&C(s),(0,ta.getGeminiModels)().then(k)},[]);let K=(0,a.useCallback)(async()=>{if(!h)return void z({title:e("settings.missing_api_key"),description:e("settings.enter_openrouter_api_key_first"),variant:"destructive"});P(!0);try{let e=await (0,ta.getOpenRouterModels)();E(e),e.length>0&&!j&&w(e[0].id)}catch(t){z({title:e("settings.load_models_fail"),description:e("settings.load_models_fail_desc"),variant:"destructive"})}finally{P(!1)}},[h,j,z,e]),U=async()=>{if(!p)return void z({title:e("settings.validation_failed"),description:e("settings.missing_api_key"),variant:"destructive"});V(!0);try{let t=b||"https://rainbow-gumption-2fc85c.netlify.app/",a=x.startsWith("gemini-")?x:"gemini-2.5-pro";if((await fetch("".concat(t,"v1/models/").concat(a,":generateContent?key=").concat(p),{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:"Hello"}]}]})})).ok)z({title:e("settings.validation_success"),description:e("settings.validation_success_gemini")});else throw Error("API 金鑰無效")}catch(t){z({title:e("settings.validation_failed"),description:e("settings.validation_failed_gemini"),variant:"destructive"})}finally{V(!1)}},q=async()=>{if(!h)return void z({title:e("settings.validation_failed"),description:e("settings.missing_api_key"),variant:"destructive"});O(!0);try{if((await fetch("https://openrouter.ai/api/v1/models",{headers:{Authorization:"Bearer ".concat(h)}})).ok)z({title:e("settings.validation_success"),description:e("settings.validation_success_openrouter")});else throw Error("API 金鑰無效")}catch(t){z({title:e("settings.validation_failed"),description:e("settings.validation_failed_openrouter"),variant:"destructive"})}finally{O(!1)}},G=async()=>{D(!0);try{let t=await window.electron.exportData();if(t.success)z({title:e("settings.export_success"),description:t.message});else throw Error(t.message)}catch(t){z({title:e("settings.export_error"),description:t instanceof Error?t.message:String(t),variant:"destructive"})}finally{D(!1)}},X=async()=>{A(!1),M(!0);try{let t=await window.electron.importData();if(t.success)t.settings&&Object.entries(t.settings).forEach(e=>{let[t,a]=e;"string"==typeof a&&localStorage.setItem(t,a)}),z({title:e("settings.import_success"),description:t.message}),setTimeout(()=>{window.location.reload()},1e3);else throw Error(t.message)}catch(t){z({title:e("settings.import_error"),description:t instanceof Error?t.message:String(t),variant:"destructive"})}finally{M(!1)}};return(0,t.jsxs)(t.Fragment,{children:[(0,t.jsx)("div",{className:"min-h-screen bg-background",children:(0,t.jsx)("div",{className:"container py-10 mx-auto",children:(0,t.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,t.jsx)("div",{className:"mb-10",children:(0,t.jsxs)("div",{className:"flex items-center justify-between",children:[(0,t.jsxs)("div",{children:[(0,t.jsx)("h1",{className:"text-3xl font-semibold",children:e("settings.title")}),(0,t.jsx)("p",{className:"text-muted-foreground",children:e("settings.general_settings_description")})]}),(0,t.jsx)(d.default,{href:"/",children:(0,t.jsxs)(r.Button,{variant:"outline",children:[(0,t.jsx)(tc,{className:"w-4 h-4 mr-2"}),e("settings.back_to_home")]})})]})}),(0,t.jsxs)(e0,{defaultValue:"general",className:"w-full",children:[(0,t.jsxs)(e8,{className:"grid w-full grid-cols-3",children:[(0,t.jsxs)(te,{value:"general",className:"flex items-center gap-2",children:[(0,t.jsx)(ts,{className:"w-4 h-4"}),e("settings.general_settings")]}),(0,t.jsxs)(te,{value:"data",className:"flex items-center gap-2",children:[(0,t.jsx)(ti,{className:"w-4 h-4"}),e("settings.data_management")]}),(0,t.jsxs)(te,{value:"ai",className:"flex items-center gap-2",children:[(0,t.jsx)(to,{className:"w-4 h-4"}),e("settings.ai_settings")]})]}),(0,t.jsx)(tt,{value:"general",className:"space-y-6",children:(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(ts,{className:"w-5 h-5"}),e("settings.general_settings")]}),(0,t.jsx)(s.CardDescription,{children:e("settings.general_settings_description")})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"grid gap-4",children:(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"language",children:e("settings.language")}),(0,t.jsxs)(W,{value:u,onValueChange:e=>c(e),children:[(0,t.jsx)(eF,{className:"w-full",children:(0,t.jsx)(Y,{placeholder:e("settings.select_language")})}),(0,t.jsxs)(eO,{children:[(0,t.jsx)(ez,{value:"en",children:e("settings.english")}),(0,t.jsx)(ez,{value:"zh",children:e("settings.traditional_chinese")})]})]})]})})})]})}),(0,t.jsx)(tt,{value:"data",className:"space-y-6",children:(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(ti,{className:"w-5 h-5"}),e("settings.data_management")]}),(0,t.jsx)(s.CardDescription,{children:e("settings.data_management_description")})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"flex flex-col gap-4 sm:flex-row",children:[(0,t.jsxs)(r.Button,{onClick:G,disabled:T,className:"flex items-center gap-2",children:[T?(0,t.jsx)(tr.Loader2,{className:"w-4 h-4 animate-spin"}):(0,t.jsx)(ti,{className:"w-4 h-4"}),T?e("settings.export_in_progress"):e("settings.export_data")]}),(0,t.jsxs)(r.Button,{onClick:()=>A(!0),disabled:H,variant:"outline",className:"flex items-center gap-2",children:[H?(0,t.jsx)(tr.Loader2,{className:"w-4 h-4 animate-spin"}):(0,t.jsx)(tl,{className:"w-4 h-4"}),H?e("settings.import_in_progress"):e("settings.import_data")]})]})})]})}),(0,t.jsxs)(tt,{value:"ai",className:"space-y-6",children:[(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(to,{className:"w-5 h-5"}),e("settings.ai_settings")]}),(0,t.jsx)(s.CardDescription,{children:e("settings.ai_settings_description")})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsx)("div",{className:"grid gap-4",children:(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"ai-provider",children:e("settings.ai_provider")}),(0,t.jsxs)(W,{value:g,onValueChange:v,children:[(0,t.jsx)(eF,{children:(0,t.jsx)(Y,{placeholder:e("settings.select_ai_provider")})}),(0,t.jsxs)(eO,{children:[(0,t.jsx)(ez,{value:"gemini",children:"Google Gemini"}),(0,t.jsx)(ez,{value:"openrouter",children:"OpenRouter"})]})]})]})})})]}),(0,t.jsxs)(s.Card,{children:[(0,t.jsxs)(s.CardHeader,{children:[(0,t.jsxs)(s.CardTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(td,{className:"w-5 h-5"}),e("settings.model_key_settings")]}),(0,t.jsx)(s.CardDescription,{children:e("settings.model_key_settings_description")})]}),(0,t.jsx)(s.CardContent,{children:(0,t.jsxs)("div",{className:"grid gap-4",children:["gemini"===g&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"gemini-api-base-url",children:e("settings.gemini_api_endpoint")}),(0,t.jsx)(n.Input,{id:"gemini-api-base-url",type:"url",value:b,onChange:e=>C(e.target.value),placeholder:"https://rainbow-gumption-2fc85c.netlify.app/"}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e("settings.custom_gemini_endpoint_desc")})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"gemini-model",children:e("settings.gemini_model")}),(0,t.jsxs)(W,{value:x.startsWith("gemini-")||"custom"===x?x:"custom",onValueChange:e=>{"custom"===e?y(_||"gemini-2.5-pro"):y(e)},children:[(0,t.jsx)(eF,{children:(0,t.jsx)(Y,{placeholder:e("settings.select_gemini_model")})}),(0,t.jsxs)(eO,{children:[S.map(e=>(0,t.jsx)(ez,{value:e.id,children:e.name},e.id)),(0,t.jsx)(ez,{value:"custom",children:e("settings.custom_model")})]})]}),("custom"===x||!S.some(e=>e.id===x))&&(0,t.jsx)(n.Input,{type:"text",placeholder:e("settings.enter_custom_model_name"),value:x.startsWith("gemini-")?x:_,onChange:e=>{N(e.target.value),y(e.target.value)}})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"gemini-key",children:e("settings.gemini_api_key")}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.Input,{id:"gemini-key",type:"password",value:p,onChange:e=>m(e.target.value),placeholder:e("settings.enter_gemini_api_key"),className:"flex-1"}),(0,t.jsx)(r.Button,{onClick:U,disabled:F||!p,variant:"outline",size:"sm",children:F?(0,t.jsx)(tr.Loader2,{className:"w-4 h-4 animate-spin"}):e("settings.validate")})]})]})]}),"openrouter"===g&&(0,t.jsxs)(t.Fragment,{children:[(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"openrouter-key",children:e("settings.openrouter_api_key")}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsx)(n.Input,{id:"openrouter-key",type:"password",value:h,onChange:e=>f(e.target.value),placeholder:e("settings.enter_openrouter_api_key"),className:"flex-1"}),(0,t.jsx)(r.Button,{onClick:q,disabled:B||!h,variant:"outline",size:"sm",children:B?(0,t.jsx)(tr.Loader2,{className:"w-4 h-4 animate-spin"}):e("settings.validate")})]})]}),(0,t.jsxs)("div",{className:"grid gap-2",children:[(0,t.jsx)(eU,{htmlFor:"openrouter-model",children:e("settings.openrouter_model")}),(0,t.jsxs)("div",{className:"flex gap-2",children:[(0,t.jsxs)(W,{value:j,onValueChange:w,disabled:0===R.length,children:[(0,t.jsx)(eF,{className:"flex-1",children:(0,t.jsx)(Y,{placeholder:e("settings.select_or_load_model")})}),(0,t.jsx)(eO,{children:R.map(e=>(0,t.jsx)(ez,{value:e.id,children:e.name},e.id))})]}),(0,t.jsx)(r.Button,{onClick:K,disabled:I,variant:"outline",size:"sm",children:I?(0,t.jsx)(tr.Loader2,{className:"w-4 h-4 animate-spin"}):e("settings.load_models")})]}),(0,t.jsx)("p",{className:"text-sm text-muted-foreground",children:e("settings.load_models_instruction")})]})]})]})})]}),(0,t.jsx)("div",{className:"flex justify-end",children:(0,t.jsxs)(r.Button,{onClick:()=>{localStorage.setItem("gemini_api_key",p),localStorage.setItem("openrouter_api_key",h),localStorage.setItem("ai_provider",g),localStorage.setItem("gemini_model",x),localStorage.setItem("openrouter_model",j),localStorage.setItem("gemini_api_base_url",b),z({title:e("settings.settings_saved_success")})},size:"default",children:[(0,t.jsx)(tn,{className:"w-4 h-4 mr-2"}),e("settings.save_all_settings")]})})]})]})]})})}),(0,t.jsx)(o.Toaster,{}),(0,t.jsx)(i.AlertDialog,{open:L,onOpenChange:A,children:(0,t.jsxs)(i.AlertDialogContent,{children:[(0,t.jsxs)(i.AlertDialogHeader,{children:[(0,t.jsxs)(i.AlertDialogTitle,{className:"flex items-center gap-2",children:[(0,t.jsx)(tl,{className:"w-5 h-5"}),e("settings.import_data")]}),(0,t.jsx)(i.AlertDialogDescription,{children:e("settings.import_confirmation")})]}),(0,t.jsxs)(i.AlertDialogFooter,{children:[(0,t.jsx)(i.AlertDialogCancel,{children:e("editor.cancel")}),(0,t.jsx)(i.AlertDialogAction,{onClick:X,children:e("settings.continue")})]})]})})]})}}]);