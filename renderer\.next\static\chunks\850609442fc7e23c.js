(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,47244,e=>{"use strict";e.s(["I18nProvider",()=>a,"useI18n",()=>l]);var t=e.i(67857),r=e.i(24302),o=e.i(29369),n=e.i(20104);let s={en:o.default,zh:n.default},i=(0,r.createContext)(void 0);function a(e){let{children:o}=e,[n,a]=(0,r.useState)("zh");(0,r.useEffect)(()=>{let e=localStorage.getItem("locale");e&&s[e]&&a(e)},[]);let l=(0,r.useCallback)(e=>{let t=e.split("."),r=s[n];for(let o of t)if(void 0===(r=null==r?void 0:r[o])){let r=s.en;for(let e of t)r=null==r?void 0:r[e];return r||e}return r||e},[n]);return(0,t.jsx)(i.Provider,{value:{locale:n,setLocale:e=>{a(e),localStorage.setItem("locale",e)},t:l},children:o})}function l(){let e=(0,r.useContext)(i);if(void 0===e)throw Error("useI18n must be used within an I18nProvider");return e}},61620,e=>{"use strict";e.s(["toast",()=>l,"useToast",()=>c]);var t=e.i(24302);let r=0,o=new Map,n=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),a({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},s=[],i={toasts:[]};function a(e){i=((e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?n(r):e.toasts.forEach(e=>{n(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}})(i,e),s.forEach(e=>{e(i)})}function l(e){let{...t}=e,o=(r=(r+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>a({type:"DISMISS_TOAST",toastId:o});return a({type:"ADD_TOAST",toast:{...t,id:o,open:!0,onOpenChange:e=>{e||n()}}}),{id:o,dismiss:n,update:e=>a({type:"UPDATE_TOAST",toast:{...e,id:o}})}}function c(){let[e,r]=t.useState(i);return t.useEffect(()=>(s.push(r),()=>{let e=s.indexOf(r);e>-1&&s.splice(e,1)}),[e]),{...e,toast:l,dismiss:e=>a({type:"DISMISS_TOAST",toastId:e})}}},61540,e=>{"use strict";function t(e,t){let{checkForDefaultPrevented:r=!0}=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{};return function(o){if(null==e||e(o),!1===r||!o.defaultPrevented)return null==t?void 0:t(o)}}e.s(["composeEventHandlers",()=>t]),"undefined"!=typeof window&&window.document&&window.document.createElement},51906,e=>{"use strict";e.s(["composeRefs",()=>o,"useComposedRefs",()=>n]);var t=e.i(24302);function r(e,t){if("function"==typeof e)return e(t);null!=e&&(e.current=t)}function o(){for(var e=arguments.length,t=Array(e),o=0;o<e;o++)t[o]=arguments[o];return e=>{let o=!1,n=t.map(t=>{let n=r(t,e);return o||"function"!=typeof n||(o=!0),n});if(o)return()=>{for(let e=0;e<n.length;e++){let o=n[e];"function"==typeof o?o():r(t[e],null)}}}}function n(){for(var e=arguments.length,r=Array(e),n=0;n<e;n++)r[n]=arguments[n];return t.useCallback(o(...r),r)}},8531,30940,66450,e=>{"use strict";function t(e,t,r){if(!t.has(e))throw TypeError("attempted to "+r+" private field on non-instance");return t.get(e)}function r(e,r){var o=t(e,r,"get");return o.get?o.get.call(e):o.value}function o(e,t,r){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,r)}function n(e,r,o){var n=t(e,r,"set");if(n.set)n.set.call(e,o);else{if(!n.writable)throw TypeError("attempted to set read only private field");n.value=o}return o}e.s(["_",()=>r],8531),e.s(["_",()=>o],30940),e.s(["_",()=>n],66450)},76825,e=>{"use strict";e.s(["createContext",()=>o,"createContextScope",()=>n]);var t=e.i(24302),r=e.i(67857);function o(e,o){let n=t.createContext(o),s=e=>{let{children:o,...s}=e,i=t.useMemo(()=>s,Object.values(s));return(0,r.jsx)(n.Provider,{value:i,children:o})};return s.displayName=e+"Provider",[s,function(r){let s=t.useContext(n);if(s)return s;if(void 0!==o)return o;throw Error("`".concat(r,"` must be used within `").concat(e,"`"))}]}function n(e){let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:[],n=[],s=()=>{let r=n.map(e=>t.createContext(e));return function(o){let n=(null==o?void 0:o[e])||r;return t.useMemo(()=>({["__scope".concat(e)]:{...o,[e]:n}}),[o,n])}};return s.scopeName=e,[function(o,s){let i=t.createContext(s),a=n.length;n=[...n,s];let l=o=>{var n;let{scope:s,children:l,...c}=o,d=(null==s||null==(n=s[e])?void 0:n[a])||i,u=t.useMemo(()=>c,Object.values(c));return(0,r.jsx)(d.Provider,{value:u,children:l})};return l.displayName=o+"Provider",[l,function(r,n){var l;let c=(null==n||null==(l=n[e])?void 0:l[a])||i,d=t.useContext(c);if(d)return d;if(void 0!==s)return s;throw Error("`".concat(r,"` must be used within `").concat(o,"`"))}]},function(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];let n=r[0];if(1===r.length)return n;let s=()=>{let e=r.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(r){let o=e.reduce((e,t)=>{let{useScope:o,scopeName:n}=t,s=o(r)["__scope".concat(n)];return{...e,...s}},{});return t.useMemo(()=>({["__scope".concat(n.scopeName)]:o}),[o])}};return s.scopeName=n.scopeName,s}(s,...o)]}},81692,e=>{"use strict";e.s(["Slot",()=>s,"createSlot",()=>n,"createSlottable",()=>a]);var t=e.i(24302),r=e.i(51906),o=e.i(67857);function n(e){let n=function(e){let o=t.forwardRef((e,o)=>{let{children:n,...s}=e;if(t.isValidElement(n)){var i,a,l;let e,c,d=(c=(e=null==(a=Object.getOwnPropertyDescriptor((i=n).props,"ref"))?void 0:a.get)&&"isReactWarning"in e&&e.isReactWarning)?i.ref:(c=(e=null==(l=Object.getOwnPropertyDescriptor(i,"ref"))?void 0:l.get)&&"isReactWarning"in e&&e.isReactWarning)?i.props.ref:i.props.ref||i.ref,u=function(e,t){let r={...t};for(let o in t){let n=e[o],s=t[o];/^on[A-Z]/.test(o)?n&&s?r[o]=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let o=s(...t);return n(...t),o}:n&&(r[o]=n):"style"===o?r[o]={...n,...s}:"className"===o&&(r[o]=[n,s].filter(Boolean).join(" "))}return{...e,...r}}(s,n.props);return n.type!==t.Fragment&&(u.ref=o?(0,r.composeRefs)(o,d):d),t.cloneElement(n,u)}return t.Children.count(n)>1?t.Children.only(null):null});return o.displayName="".concat(e,".SlotClone"),o}(e),s=t.forwardRef((e,r)=>{let{children:s,...i}=e,a=t.Children.toArray(s),c=a.find(l);if(c){let e=c.props.children,s=a.map(r=>r!==c?r:t.Children.count(e)>1?t.Children.only(null):t.isValidElement(e)?e.props.children:null);return(0,o.jsx)(n,{...i,ref:r,children:t.isValidElement(e)?t.cloneElement(e,void 0,s):null})}return(0,o.jsx)(n,{...i,ref:r,children:s})});return s.displayName="".concat(e,".Slot"),s}var s=n("Slot"),i=Symbol("radix.slottable");function a(e){let t=e=>{let{children:t}=e;return(0,o.jsx)(o.Fragment,{children:t})};return t.displayName="".concat(e,".Slottable"),t.__radixId=i,t}function l(e){return t.isValidElement(e)&&"function"==typeof e.type&&"__radixId"in e.type&&e.type.__radixId===i}},49566,e=>{"use strict";e.s(["createCollection",()=>d]);var t,r=e.i(8531),o=e.i(30940),n=e.i(66450),s=e.i(24302),i=e.i(76825),a=e.i(51906),l=e.i(81692),c=e.i(67857);function d(e){let t=e+"CollectionProvider",[r,o]=(0,i.createContextScope)(t),[n,d]=r(t,{collectionRef:{current:null},itemMap:new Map}),u=e=>{let{scope:t,children:r}=e,o=s.default.useRef(null),i=s.default.useRef(new Map).current;return(0,c.jsx)(n,{scope:t,itemMap:i,collectionRef:o,children:r})};u.displayName=t;let f=e+"CollectionSlot",p=(0,l.createSlot)(f),m=s.default.forwardRef((e,t)=>{let{scope:r,children:o}=e,n=d(f,r),s=(0,a.useComposedRefs)(t,n.collectionRef);return(0,c.jsx)(p,{ref:s,children:o})});m.displayName=f;let v=e+"CollectionItemSlot",h="data-radix-collection-item",b=(0,l.createSlot)(v),g=s.default.forwardRef((e,t)=>{let{scope:r,children:o,...n}=e,i=s.default.useRef(null),l=(0,a.useComposedRefs)(t,i),u=d(v,r);return s.default.useEffect(()=>(u.itemMap.set(i,{ref:i,...n}),()=>void u.itemMap.delete(i))),(0,c.jsx)(b,{...{[h]:""},ref:l,children:o})});return g.displayName=v,[{Provider:u,Slot:m,ItemSlot:g},function(t){let r=d(e+"CollectionConsumer",t);return s.default.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(h,"]")));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},o]}var u=new WeakMap;function f(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let r=function(e,t){let r=e.length,o=p(t),n=o>=0?o:r+o;return n<0||n>=r?-1:n}(e,t);return -1===r?void 0:e[r]}function p(e){return e!=e||0===e?0:Math.trunc(e)}t=new WeakMap,class e extends Map{set(e,o){return u.get(this)&&(this.has(e)?(0,r._)(this,t)[(0,r._)(this,t).indexOf(e)]=e:(0,r._)(this,t).push(e)),super.set(e,o),this}insert(e,o,n){let s,i=this.has(o),a=(0,r._)(this,t).length,l=p(e),c=l>=0?l:a+l,d=c<0||c>=a?-1:c;if(d===this.size||i&&d===this.size-1||-1===d)return this.set(o,n),this;let u=this.size+ +!i;l<0&&c++;let f=[...(0,r._)(this,t)],m=!1;for(let e=c;e<u;e++)if(c===e){let t=f[e];f[e]===o&&(t=f[e+1]),i&&this.delete(o),s=this.get(t),this.set(o,n)}else{m||f[e-1]!==o||(m=!0);let t=f[m?e:e-1],r=s;s=this.get(t),this.delete(t),this.set(t,r)}return this}with(t,r,o){let n=new e(this);return n.insert(t,r,o),n}before(e){let o=(0,r._)(this,t).indexOf(e)-1;if(!(o<0))return this.entryAt(o)}setBefore(e,o,n){let s=(0,r._)(this,t).indexOf(e);return -1===s?this:this.insert(s,o,n)}after(e){let o=(0,r._)(this,t).indexOf(e);if(-1!==(o=-1===o||o===this.size-1?-1:o+1))return this.entryAt(o)}setAfter(e,o,n){let s=(0,r._)(this,t).indexOf(e);return -1===s?this:this.insert(s+1,o,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return(0,n._)(this,t,[]),super.clear()}delete(e){let o=super.delete(e);return o&&(0,r._)(this,t).splice((0,r._)(this,t).indexOf(e),1),o}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let o=f((0,r._)(this,t),e);if(void 0!==o)return this.get(o)}entryAt(e){let o=f((0,r._)(this,t),e);if(void 0!==o)return[o,this.get(o)]}indexOf(e){return(0,r._)(this,t).indexOf(e)}keyAt(e){return f((0,r._)(this,t),e)}from(e,t){let r=this.indexOf(e);if(-1===r)return;let o=r+t;return o<0&&(o=0),o>=this.size&&(o=this.size-1),this.at(o)}keyFrom(e,t){let r=this.indexOf(e);if(-1===r)return;let o=r+t;return o<0&&(o=0),o>=this.size&&(o=this.size-1),this.keyAt(o)}find(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return o;r++}}findIndex(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return r;r++}return -1}filter(t,r){let o=[],n=0;for(let e of this)Reflect.apply(t,r,[e,n,this])&&o.push(e),n++;return new e(o)}map(t,r){let o=[],n=0;for(let e of this)o.push([e[0],Reflect.apply(t,r,[e,n,this])]),n++;return new e(o)}reduce(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[o,n]=t,s=0,i=null!=n?n:this.at(0);for(let e of this)i=0===s&&1===t.length?e:Reflect.apply(o,this,[i,e,s,this]),s++;return i}reduceRight(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];let[o,n]=t,s=null!=n?n:this.at(-1);for(let e=this.size-1;e>=0;e--){let r=this.at(e);s=e===this.size-1&&1===t.length?r:Reflect.apply(o,this,[s,r,e,this])}return s}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let r=this.keyAt(e),o=this.get(r);t.set(r,o)}return t}toSpliced(){for(var t=arguments.length,r=Array(t),o=0;o<t;o++)r[o]=arguments[o];let n=[...this.entries()];return n.splice(...r),new e(n)}slice(t,r){let o=new e,n=this.size-1;if(void 0===t)return o;t<0&&(t+=this.size),void 0!==r&&r>0&&(n=r-1);for(let e=t;e<=n;e++){let t=this.keyAt(e),r=this.get(t);o.set(t,r)}return o}every(e,t){let r=0;for(let o of this){if(!Reflect.apply(e,t,[o,r,this]))return!1;r++}return!0}some(e,t){let r=0;for(let o of this){if(Reflect.apply(e,t,[o,r,this]))return!0;r++}return!1}constructor(e){super(e),(0,o._)(this,t,{writable:!0,value:void 0}),(0,n._)(this,t,[...super.keys()]),u.set(this,!0)}}},32054,e=>{"use strict";e.s(["Primitive",()=>s,"dispatchDiscreteCustomEvent",()=>i]);var t=e.i(24302),r=e.i(79238),o=e.i(81692),n=e.i(67857),s=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,r)=>{let s=(0,o.createSlot)("Primitive.".concat(r)),i=t.forwardRef((e,t)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,n.jsx)(o?s:r,{...i,ref:t})});return i.displayName="Primitive.".concat(r),{...e,[r]:i}},{});function i(e,t){e&&r.flushSync(()=>e.dispatchEvent(t))}},83875,e=>{"use strict";e.s(["useCallbackRef",()=>r]);var t=e.i(24302);function r(e){let r=t.useRef(e);return t.useEffect(()=>{r.current=e}),t.useMemo(()=>function(){for(var e,t=arguments.length,o=Array(t),n=0;n<t;n++)o[n]=arguments[n];return null==(e=r.current)?void 0:e.call(r,...o)},[])}},80435,e=>{"use strict";e.s(["Branch",()=>v,"DismissableLayer",()=>d,"Root",()=>m],80435);var t,r=e.i(24302),o=e.i(61540),n=e.i(32054),s=e.i(51906),i=e.i(83875),a=e.i(67857),l="dismissableLayer.update",c=r.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=r.forwardRef((e,d)=>{var u,m;let{disableOutsidePointerEvents:v=!1,onEscapeKeyDown:h,onPointerDownOutside:b,onFocusOutside:g,onInteractOutside:y,onDismiss:w,...x}=e,k=r.useContext(c),[E,C]=r.useState(null),T=null!=(m=null==E?void 0:E.ownerDocument)?m:null==(u=globalThis)?void 0:u.document,[,R]=r.useState({}),N=(0,s.useComposedRefs)(d,e=>C(e)),P=Array.from(k.layers),[S]=[...k.layersWithOutsidePointerEventsDisabled].slice(-1),j=P.indexOf(S),A=E?P.indexOf(E):-1,z=k.layersWithOutsidePointerEventsDisabled.size>0,O=A>=j,D=function(e){var t;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,i.useCallbackRef)(e),s=r.useRef(!1),a=r.useRef(()=>{});return r.useEffect(()=>{let e=e=>{if(e.target&&!s.current){let t=function(){p("dismissableLayer.pointerDownOutside",n,r,{discrete:!0})},r={originalEvent:e};"touch"===e.pointerType?(o.removeEventListener("click",a.current),a.current=t,o.addEventListener("click",a.current,{once:!0})):t()}else o.removeEventListener("click",a.current);s.current=!1},t=window.setTimeout(()=>{o.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),o.removeEventListener("pointerdown",e),o.removeEventListener("click",a.current)}},[o,n]),{onPointerDownCapture:()=>s.current=!0}}(e=>{let t=e.target,r=[...k.branches].some(e=>e.contains(t));O&&!r&&(null==b||b(e),null==y||y(e),e.defaultPrevented||null==w||w())},T),L=function(e){var t;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,i.useCallbackRef)(e),s=r.useRef(!1);return r.useEffect(()=>{let e=e=>{e.target&&!s.current&&p("dismissableLayer.focusOutside",n,{originalEvent:e},{discrete:!1})};return o.addEventListener("focusin",e),()=>o.removeEventListener("focusin",e)},[o,n]),{onFocusCapture:()=>s.current=!0,onBlurCapture:()=>s.current=!1}}(e=>{let t=e.target;![...k.branches].some(e=>e.contains(t))&&(null==g||g(e),null==y||y(e),e.defaultPrevented||null==w||w())},T);return!function(e){var t;let o=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,n=(0,i.useCallbackRef)(e);r.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return o.addEventListener("keydown",e,{capture:!0}),()=>o.removeEventListener("keydown",e,{capture:!0})},[n,o])}(e=>{A===k.layers.size-1&&(null==h||h(e),!e.defaultPrevented&&w&&(e.preventDefault(),w()))},T),r.useEffect(()=>{if(E)return v&&(0===k.layersWithOutsidePointerEventsDisabled.size&&(t=T.body.style.pointerEvents,T.body.style.pointerEvents="none"),k.layersWithOutsidePointerEventsDisabled.add(E)),k.layers.add(E),f(),()=>{v&&1===k.layersWithOutsidePointerEventsDisabled.size&&(T.body.style.pointerEvents=t)}},[E,T,v,k]),r.useEffect(()=>()=>{E&&(k.layers.delete(E),k.layersWithOutsidePointerEventsDisabled.delete(E),f())},[E,k]),r.useEffect(()=>{let e=()=>R({});return document.addEventListener(l,e),()=>document.removeEventListener(l,e)},[]),(0,a.jsx)(n.Primitive.div,{...x,ref:N,style:{pointerEvents:z?O?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.composeEventHandlers)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,o.composeEventHandlers)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,o.composeEventHandlers)(e.onPointerDownCapture,D.onPointerDownCapture)})});d.displayName="DismissableLayer";var u=r.forwardRef((e,t)=>{let o=r.useContext(c),i=r.useRef(null),l=(0,s.useComposedRefs)(t,i);return r.useEffect(()=>{let e=i.current;if(e)return o.branches.add(e),()=>{o.branches.delete(e)}},[o.branches]),(0,a.jsx)(n.Primitive.div,{...e,ref:l})});function f(){let e=new CustomEvent(l);document.dispatchEvent(e)}function p(e,t,r,o){let{discrete:s}=o,i=r.originalEvent.target,a=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),s?(0,n.dispatchDiscreteCustomEvent)(i,a):i.dispatchEvent(a)}u.displayName="DismissableLayerBranch";var m=d,v=u},3961,e=>{"use strict";e.s(["useLayoutEffect",()=>o]);var t,r=e.i(24302),o=(null==(t=globalThis)?void 0:t.document)?r.useLayoutEffect:()=>{}},87804,e=>{"use strict";e.s(["Portal",()=>i]);var t=e.i(24302),r=e.i(79238),o=e.i(32054),n=e.i(3961),s=e.i(67857),i=t.forwardRef((e,i)=>{var a,l;let{container:c,...d}=e,[u,f]=t.useState(!1);(0,n.useLayoutEffect)(()=>f(!0),[]);let p=c||u&&(null==(l=globalThis)||null==(a=l.document)?void 0:a.body);return p?r.default.createPortal((0,s.jsx)(o.Primitive.div,{...d,ref:i}),p):null});i.displayName="Portal"},22859,e=>{"use strict";e.s(["Presence",()=>n]);var t=e.i(24302),r=e.i(51906),o=e.i(3961),n=e=>{let{present:n,children:i}=e,a=function(e){var r,n;let[i,a]=t.useState(),l=t.useRef(null),c=t.useRef(e),d=t.useRef("none"),[u,f]=(r=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},t.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},r));return t.useEffect(()=>{let e=s(l.current);d.current="mounted"===u?e:"none"},[u]),(0,o.useLayoutEffect)(()=>{let t=l.current,r=c.current;if(r!==e){let o=d.current,n=s(t);e?f("MOUNT"):"none"===n||(null==t?void 0:t.display)==="none"?f("UNMOUNT"):r&&o!==n?f("ANIMATION_OUT"):f("UNMOUNT"),c.current=e}},[e,f]),(0,o.useLayoutEffect)(()=>{if(i){var e;let t,r=null!=(e=i.ownerDocument.defaultView)?e:window,o=e=>{let o=s(l.current).includes(CSS.escape(e.animationName));if(e.target===i&&o&&(f("ANIMATION_END"),!c.current)){let e=i.style.animationFillMode;i.style.animationFillMode="forwards",t=r.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=e)})}},n=e=>{e.target===i&&(d.current=s(l.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",o),i.addEventListener("animationend",o),()=>{r.clearTimeout(t),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",o),i.removeEventListener("animationend",o)}}f("ANIMATION_END")},[i,f]),{isPresent:["mounted","unmountSuspended"].includes(u),ref:t.useCallback(e=>{l.current=e?getComputedStyle(e):null,a(e)},[])}}(n),l="function"==typeof i?i({present:a.isPresent}):t.Children.only(i),c=(0,r.useComposedRefs)(a.ref,function(e){var t,r;let o=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,n=o&&"isReactWarning"in o&&o.isReactWarning;return n?e.ref:(n=(o=null==(r=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:r.get)&&"isReactWarning"in o&&o.isReactWarning)?e.props.ref:e.props.ref||e.ref}(l));return"function"==typeof i||a.isPresent?t.cloneElement(l,{ref:c}):null};function s(e){return(null==e?void 0:e.animationName)||"none"}n.displayName="Presence"},89199,e=>{"use strict";e.s(["useControllableState",()=>n],89199);var t=e.i(24302),r=e.i(3961);t[" useEffectEvent ".trim().toString()],t[" useInsertionEffect ".trim().toString()];var o=t[" useInsertionEffect ".trim().toString()]||r.useLayoutEffect;function n(e){let{prop:r,defaultProp:n,onChange:s=()=>{},caller:i}=e,[a,l,c]=function(e){let{defaultProp:r,onChange:n}=e,[s,i]=t.useState(r),a=t.useRef(s),l=t.useRef(n);return o(()=>{l.current=n},[n]),t.useEffect(()=>{if(a.current!==s){var e;null==(e=l.current)||e.call(l,s),a.current=s}},[s,a]),[s,i,l]}({defaultProp:n,onChange:s}),d=void 0!==r,u=d?r:a;{let e=t.useRef(void 0!==r);t.useEffect(()=>{let t=e.current;if(t!==d){let e=d?"controlled":"uncontrolled";console.warn("".concat(i," is changing from ").concat(t?"controlled":"uncontrolled"," to ").concat(e,". Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component."))}e.current=d},[d,i])}return[u,t.useCallback(e=>{if(d){let o="function"==typeof e?e(r):e;if(o!==r){var t;null==(t=c.current)||t.call(c,o)}}else l(e)},[d,r,l,c])]}Symbol("RADIX:SYNC_STATE")},29243,56281,e=>{"use strict";e.s(["Action",()=>ee,"Close",()=>et,"Description",()=>Q,"Provider",()=>q,"Root",()=>Z,"Title",()=>J,"Viewport",()=>Y],29243);var t=e.i(24302),r=e.i(79238),o=e.i(61540),n=e.i(51906),s=e.i(49566),i=e.i(76825),a=e.i(80435),l=e.i(87804),c=e.i(22859),d=e.i(32054),u=e.i(83875),f=e.i(89199),p=e.i(3961);e.s(["VISUALLY_HIDDEN_STYLES",()=>v,"VisuallyHidden",()=>h],56281);var m=e.i(67857),v=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),h=t.forwardRef((e,t)=>(0,m.jsx)(d.Primitive.span,{...e,ref:t,style:{...v,...e.style}}));h.displayName="VisuallyHidden";var b="ToastProvider",[g,y,w]=(0,s.createCollection)("Toast"),[x,k]=(0,i.createContextScope)("Toast",[w]),[E,C]=x(b),T=e=>{let{__scopeToast:r,label:o="Notification",duration:n=5e3,swipeDirection:s="right",swipeThreshold:i=50,children:a}=e,[l,c]=t.useState(null),[d,u]=t.useState(0),f=t.useRef(!1),p=t.useRef(!1);return o.trim()||console.error("Invalid prop `label` supplied to `".concat(b,"`. Expected non-empty `string`.")),(0,m.jsx)(g.Provider,{scope:r,children:(0,m.jsx)(E,{scope:r,label:o,duration:n,swipeDirection:s,swipeThreshold:i,toastCount:d,viewport:l,onViewportChange:c,onToastAdd:t.useCallback(()=>u(e=>e+1),[]),onToastRemove:t.useCallback(()=>u(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:f,isClosePausedRef:p,children:a})})};T.displayName=b;var R="ToastViewport",N=["F8"],P="toast.viewportPause",S="toast.viewportResume",j=t.forwardRef((e,r)=>{let{__scopeToast:o,hotkey:s=N,label:i="Notifications ({hotkey})",...l}=e,c=C(R,o),u=y(o),f=t.useRef(null),p=t.useRef(null),v=t.useRef(null),h=t.useRef(null),b=(0,n.useComposedRefs)(r,h,c.onViewportChange),w=s.join("+").replace(/Key/g,"").replace(/Digit/g,""),x=c.toastCount>0;t.useEffect(()=>{let e=e=>{var t;0!==s.length&&s.every(t=>e[t]||e.code===t)&&(null==(t=h.current)||t.focus())};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[s]),t.useEffect(()=>{let e=f.current,t=h.current;if(x&&e&&t){let r=()=>{if(!c.isClosePausedRef.current){let e=new CustomEvent(P);t.dispatchEvent(e),c.isClosePausedRef.current=!0}},o=()=>{if(c.isClosePausedRef.current){let e=new CustomEvent(S);t.dispatchEvent(e),c.isClosePausedRef.current=!1}},n=t=>{e.contains(t.relatedTarget)||o()},s=()=>{e.contains(document.activeElement)||o()};return e.addEventListener("focusin",r),e.addEventListener("focusout",n),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",s),window.addEventListener("blur",r),window.addEventListener("focus",o),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",n),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",s),window.removeEventListener("blur",r),window.removeEventListener("focus",o)}}},[x,c.isClosePausedRef]);let k=t.useCallback(e=>{let{tabbingDirection:t}=e,r=u().map(e=>{let r=e.ref.current,o=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===t?o:o.reverse()});return("forwards"===t?r.reverse():r).flat()},[u]);return t.useEffect(()=>{let e=h.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){var o,n,s;let r=document.activeElement,i=t.shiftKey;if(t.target===e&&i){null==(o=p.current)||o.focus();return}let a=k({tabbingDirection:i?"backwards":"forwards"}),l=a.findIndex(e=>e===r);X(a.slice(l+1))?t.preventDefault():i?null==(n=p.current)||n.focus():null==(s=v.current)||s.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[u,k]),(0,m.jsxs)(a.Branch,{ref:f,role:"region","aria-label":i.replace("{hotkey}",w),tabIndex:-1,style:{pointerEvents:x?void 0:"none"},children:[x&&(0,m.jsx)(z,{ref:p,onFocusFromOutsideViewport:()=>{X(k({tabbingDirection:"forwards"}))}}),(0,m.jsx)(g.Slot,{scope:o,children:(0,m.jsx)(d.Primitive.ol,{tabIndex:-1,...l,ref:b})}),x&&(0,m.jsx)(z,{ref:v,onFocusFromOutsideViewport:()=>{X(k({tabbingDirection:"backwards"}))}})]})});j.displayName=R;var A="ToastFocusProxy",z=t.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:o,...n}=e,s=C(A,r);return(0,m.jsx)(h,{tabIndex:0,...n,ref:t,style:{position:"fixed"},onFocus:e=>{var t;let r=e.relatedTarget;(null==(t=s.viewport)?void 0:t.contains(r))||o()}})});z.displayName=A;var O="Toast",D=t.forwardRef((e,t)=>{let{forceMount:r,open:n,defaultOpen:s,onOpenChange:i,...a}=e,[l,d]=(0,f.useControllableState)({prop:n,defaultProp:null==s||s,onChange:i,caller:O});return(0,m.jsx)(c.Presence,{present:r||l,children:(0,m.jsx)(I,{open:l,...a,ref:t,onClose:()=>d(!1),onPause:(0,u.useCallbackRef)(e.onPause),onResume:(0,u.useCallbackRef)(e.onResume),onSwipeStart:(0,o.composeEventHandlers)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,o.composeEventHandlers)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y","".concat(r,"px"))}),onSwipeCancel:(0,o.composeEventHandlers)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,o.composeEventHandlers)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x","".concat(t,"px")),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y","".concat(r,"px")),d(!1)})})})});D.displayName=O;var[L,M]=x(O,{onClose(){}}),I=t.forwardRef((e,s)=>{let{__scopeToast:i,type:l="foreground",duration:c,open:f,onClose:p,onEscapeKeyDown:v,onPause:h,onResume:b,onSwipeStart:y,onSwipeMove:w,onSwipeCancel:x,onSwipeEnd:k,...E}=e,T=C(O,i),[R,N]=t.useState(null),j=(0,n.useComposedRefs)(s,e=>N(e)),A=t.useRef(null),z=t.useRef(null),D=c||T.duration,M=t.useRef(0),I=t.useRef(D),F=t.useRef(0),{onToastAdd:W,onToastRemove:U}=T,V=(0,u.useCallbackRef)(()=>{var e;(null==R?void 0:R.contains(document.activeElement))&&(null==(e=T.viewport)||e.focus()),p()}),H=t.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(F.current),M.current=new Date().getTime(),F.current=window.setTimeout(V,e))},[V]);t.useEffect(()=>{let e=T.viewport;if(e){let t=()=>{H(I.current),null==b||b()},r=()=>{let e=new Date().getTime()-M.current;I.current=I.current-e,window.clearTimeout(F.current),null==h||h()};return e.addEventListener(P,r),e.addEventListener(S,t),()=>{e.removeEventListener(P,r),e.removeEventListener(S,t)}}},[T.viewport,D,h,b,H]),t.useEffect(()=>{f&&!T.isClosePausedRef.current&&H(D)},[f,D,T.isClosePausedRef,H]),t.useEffect(()=>(W(),()=>U()),[W,U]);let K=t.useMemo(()=>R?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{var o;if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),(o=t).nodeType===o.ELEMENT_NODE){let o=t.ariaHidden||t.hidden||"none"===t.style.display,n=""===t.dataset.radixToastAnnounceExclude;if(!o)if(n){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}),r}(R):null,[R]);return T.viewport?(0,m.jsxs)(m.Fragment,{children:[K&&(0,m.jsx)(_,{__scopeToast:i,role:"status","aria-live":"foreground"===l?"assertive":"polite",children:K}),(0,m.jsx)(L,{scope:i,onClose:V,children:r.createPortal((0,m.jsx)(g.ItemSlot,{scope:i,children:(0,m.jsx)(a.Root,{asChild:!0,onEscapeKeyDown:(0,o.composeEventHandlers)(v,()=>{T.isFocusedToastEscapeKeyDownRef.current||V(),T.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,m.jsx)(d.Primitive.li,{tabIndex:0,"data-state":f?"open":"closed","data-swipe-direction":T.swipeDirection,...E,ref:j,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,o.composeEventHandlers)(e.onKeyDown,e=>{"Escape"===e.key&&(null==v||v(e.nativeEvent),e.nativeEvent.defaultPrevented||(T.isFocusedToastEscapeKeyDownRef.current=!0,V()))}),onPointerDown:(0,o.composeEventHandlers)(e.onPointerDown,e=>{0===e.button&&(A.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,o.composeEventHandlers)(e.onPointerMove,e=>{if(!A.current)return;let t=e.clientX-A.current.x,r=e.clientY-A.current.y,o=!!z.current,n=["left","right"].includes(T.swipeDirection),s=["left","up"].includes(T.swipeDirection)?Math.min:Math.max,i=n?s(0,t):0,a=n?0:s(0,r),l="touch"===e.pointerType?10:2,c={x:i,y:a},d={originalEvent:e,delta:c};o?(z.current=c,G("toast.swipeMove",w,d,{discrete:!1})):$(c,T.swipeDirection,l)?(z.current=c,G("toast.swipeStart",y,d,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>l||Math.abs(r)>l)&&(A.current=null)}),onPointerUp:(0,o.composeEventHandlers)(e.onPointerUp,e=>{let t=z.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),z.current=null,A.current=null,t){let r=e.currentTarget,o={originalEvent:e,delta:t};$(t,T.swipeDirection,T.swipeThreshold)?G("toast.swipeEnd",k,o,{discrete:!0}):G("toast.swipeCancel",x,o,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),T.viewport)})]}):null}),_=e=>{let{__scopeToast:r,children:o,...n}=e,s=C(O,r),[i,a]=t.useState(!1),[c,d]=t.useState(!1);return function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:()=>{},t=(0,u.useCallbackRef)(e);(0,p.useLayoutEffect)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),t.useEffect(()=>{let e=window.setTimeout(()=>d(!0),1e3);return()=>window.clearTimeout(e)},[]),c?null:(0,m.jsx)(l.Portal,{asChild:!0,children:(0,m.jsx)(h,{...n,children:i&&(0,m.jsxs)(m.Fragment,{children:[s.label," ",o]})})})},F=t.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,m.jsx)(d.Primitive.div,{...o,ref:t})});F.displayName="ToastTitle";var W=t.forwardRef((e,t)=>{let{__scopeToast:r,...o}=e;return(0,m.jsx)(d.Primitive.div,{...o,ref:t})});W.displayName="ToastDescription";var U="ToastAction",V=t.forwardRef((e,t)=>{let{altText:r,...o}=e;return r.trim()?(0,m.jsx)(B,{altText:r,asChild:!0,children:(0,m.jsx)(K,{...o,ref:t})}):(console.error("Invalid prop `altText` supplied to `".concat(U,"`. Expected non-empty `string`.")),null)});V.displayName=U;var H="ToastClose",K=t.forwardRef((e,t)=>{let{__scopeToast:r,...n}=e,s=M(H,r);return(0,m.jsx)(B,{asChild:!0,children:(0,m.jsx)(d.Primitive.button,{type:"button",...n,ref:t,onClick:(0,o.composeEventHandlers)(e.onClick,s.onClose)})})});K.displayName=H;var B=t.forwardRef((e,t)=>{let{__scopeToast:r,altText:o,...n}=e;return(0,m.jsx)(d.Primitive.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":o||void 0,...n,ref:t})});function G(e,t,r,o){let{discrete:n}=o,s=r.originalEvent.currentTarget,i=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&s.addEventListener(e,t,{once:!0}),n?(0,d.dispatchDiscreteCustomEvent)(s,i):s.dispatchEvent(i)}var $=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:0,o=Math.abs(e.x),n=Math.abs(e.y),s=o>n;return"left"===t||"right"===t?s&&o>r:!s&&n>r};function X(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var q=T,Y=j,Z=D,J=F,Q=W,ee=V,et=K},68316,87813,e=>{"use strict";function t(){for(var e,t,r=0,o="",n=arguments.length;r<n;r++)(e=arguments[r])&&(t=function e(t){var r,o,n="";if("string"==typeof t||"number"==typeof t)n+=t;else if("object"==typeof t)if(Array.isArray(t)){var s=t.length;for(r=0;r<s;r++)t[r]&&(o=e(t[r]))&&(n&&(n+=" "),n+=o)}else for(o in t)t[o]&&(n&&(n+=" "),n+=o);return n}(e))&&(o&&(o+=" "),o+=t);return o}e.s(["cva",()=>n],68316),e.s(["clsx",()=>t],87813);let r=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,o=t,n=(e,t)=>n=>{var s;if((null==t?void 0:t.variants)==null)return o(e,null==n?void 0:n.class,null==n?void 0:n.className);let{variants:i,defaultVariants:a}=t,l=Object.keys(i).map(e=>{let t=null==n?void 0:n[e],o=null==a?void 0:a[e];if(null===t)return null;let s=r(t)||r(o);return i[e][s]}),c=n&&Object.entries(n).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return o(e,l,null==t||null==(s=t.compoundVariants)?void 0:s.reduce((e,t)=>{let{class:r,className:o,...n}=t;return Object.entries(n).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...c}[t]):({...a,...c})[t]===r})?[...e,r,o]:e},[]),null==n?void 0:n.class,null==n?void 0:n.className)}},17388,93137,e=>{"use strict";e.s(["X",()=>a],17388),e.s(["default",()=>i],93137);var t=e.i(24302);let r=e=>{let t=e.replace(/^([A-Z])|[\s-_]+(\w)/g,(e,t,r)=>r?r.toUpperCase():t.toLowerCase());return t.charAt(0).toUpperCase()+t.slice(1)},o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&""!==e.trim()&&r.indexOf(e)===t).join(" ").trim()};var n={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let s=(0,t.forwardRef)((e,r)=>{let{color:s="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:l,className:c="",children:d,iconNode:u,...f}=e;return(0,t.createElement)("svg",{ref:r,...n,width:i,height:i,stroke:s,strokeWidth:l?24*Number(a)/Number(i):a,className:o("lucide",c),...!d&&!(e=>{for(let t in e)if(t.startsWith("aria-")||"role"===t||"title"===t)return!0})(f)&&{"aria-hidden":"true"},...f},[...u.map(e=>{let[r,o]=e;return(0,t.createElement)(r,o)}),...Array.isArray(d)?d:[d]])}),i=(e,n)=>{let i=(0,t.forwardRef)((i,a)=>{let{className:l,...c}=i;return(0,t.createElement)(s,{ref:a,iconNode:n,className:o("lucide-".concat(r(e).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()),"lucide-".concat(e),l),...c})});return i.displayName=r(e),i},a=i("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},92072,e=>{"use strict";e.s(["cn",()=>ee],92072);var t=e.i(87813);let r=(e,t)=>{var o;if(0===e.length)return t.classGroupId;let n=e[0],s=t.nextPart.get(n),i=s?r(e.slice(1),s):void 0;if(i)return i;if(0===t.validators.length)return;let a=e.join("-");return null==(o=t.validators.find(e=>{let{validator:t}=e;return t(a)}))?void 0:o.classGroupId},o=/^\[(.+)\]$/,n=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:s(t,e)).classGroupId=r;return}if("function"==typeof e)return i(e)?void n(e(o),t,r,o):void t.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach(e=>{let[i,a]=e;n(a,s(t,i),r,o)})})},s=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},i=e=>e.isThemeGetter,a=/\s+/;function l(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=c(e))&&(o&&(o+=" "),o+=t);return o}let c=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=c(e[o]))&&(r&&(r+=" "),r+=t);return r},d=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},u=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,f=/^\((?:(\w[\w-]*):)?(.+)\)$/i,p=/^\d+\/\d+$/,m=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,v=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,h=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,b=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,g=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,y=e=>p.test(e),w=e=>!!e&&!Number.isNaN(Number(e)),x=e=>!!e&&Number.isInteger(Number(e)),k=e=>e.endsWith("%")&&w(e.slice(0,-1)),E=e=>m.test(e),C=()=>!0,T=e=>v.test(e)&&!h.test(e),R=()=>!1,N=e=>b.test(e),P=e=>g.test(e),S=e=>!A(e)&&!I(e),j=e=>K(e,X,R),A=e=>u.test(e),z=e=>K(e,q,T),O=e=>K(e,Y,w),D=e=>K(e,G,R),L=e=>K(e,$,P),M=e=>K(e,J,N),I=e=>f.test(e),_=e=>B(e,q),F=e=>B(e,Z),W=e=>B(e,G),U=e=>B(e,X),V=e=>B(e,$),H=e=>B(e,J,!0),K=(e,t,r)=>{let o=u.exec(e);return!!o&&(o[1]?t(o[1]):r(o[2]))},B=function(e,t){let r=arguments.length>2&&void 0!==arguments[2]&&arguments[2],o=f.exec(e);return!!o&&(o[1]?t(o[1]):r)},G=e=>"position"===e||"percentage"===e,$=e=>"image"===e||"url"===e,X=e=>"length"===e||"size"===e||"bg-size"===e,q=e=>"length"===e,Y=e=>"number"===e,Z=e=>"family-name"===e,J=e=>"shadow"===e;Symbol.toStringTag;let Q=function(e){let t,s,i;for(var c=arguments.length,d=Array(c>1?c-1:0),u=1;u<c;u++)d[u-1]=arguments[u];let f=function(a){let l;return s=(t={cache:(e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,n=(n,s)=>{r.set(n,s),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(n(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):n(e,t)}}})((l=d.reduce((e,t)=>t(e),e())).cacheSize),parseClassName:(e=>{let{prefix:t,experimentalParseClassName:r}=e,o=e=>{let t,r,o=[],n=0,s=0,i=0;for(let r=0;r<e.length;r++){let a=e[r];if(0===n&&0===s){if(":"===a){o.push(e.slice(i,r)),i=r+1;continue}if("/"===a){t=r;continue}}"["===a?n++:"]"===a?n--:"("===a?s++:")"===a&&s--}let a=0===o.length?e:e.substring(i),l=(r=a).endsWith("!")?r.substring(0,r.length-1):r.startsWith("!")?r.substring(1):r;return{modifiers:o,hasImportantModifier:l!==a,baseClassName:l,maybePostfixModifierPosition:t&&t>i?t-i:void 0}};if(t){let e=t+":",r=o;o=t=>t.startsWith(e)?r(t.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:t,maybePostfixModifierPosition:void 0}}if(r){let e=o;o=t=>r({className:t,parseClassName:e})}return o})(l),sortModifiers:(e=>{let t=Object.fromEntries(e.orderSensitiveModifiers.map(e=>[e,!0]));return e=>{if(e.length<=1)return e;let r=[],o=[];return e.forEach(e=>{"["===e[0]||t[e]?(r.push(...o.sort(),e),o=[]):o.push(e)}),r.push(...o.sort()),r}})(l),...(e=>{let t=(e=>{let{theme:t,classGroups:r}=e,o={nextPart:new Map,validators:[]};for(let e in r)n(r[e],o,e,t);return o})(e),{conflictingClassGroups:s,conflictingClassGroupModifiers:i}=e;return{getClassGroupId:e=>{let n=e.split("-");return""===n[0]&&1!==n.length&&n.shift(),r(n,t)||(e=>{if(o.test(e)){let t=o.exec(e)[1],r=null==t?void 0:t.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}})(e)},getConflictingClassGroupIds:(e,t)=>{let r=s[e]||[];return t&&i[e]?[...r,...i[e]]:r}}})(l)}).cache.get,i=t.cache.set,f=p,p(a)};function p(e){let r=s(e);if(r)return r;let o=((e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:n,sortModifiers:s}=t,i=[],l=e.trim().split(a),c="";for(let e=l.length-1;e>=0;e-=1){let t=l[e],{isExternal:a,modifiers:d,hasImportantModifier:u,baseClassName:f,maybePostfixModifierPosition:p}=r(t);if(a){c=t+(c.length>0?" "+c:c);continue}let m=!!p,v=o(m?f.substring(0,p):f);if(!v){if(!m||!(v=o(f))){c=t+(c.length>0?" "+c:c);continue}m=!1}let h=s(d).join(":"),b=u?h+"!":h,g=b+v;if(i.includes(g))continue;i.push(g);let y=n(v,m);for(let e=0;e<y.length;++e){let t=y[e];i.push(b+t)}c=t+(c.length>0?" "+c:c)}return c})(e,t);return i(e,o),o}return function(){return f(l.apply(null,arguments))}}(()=>{let e=d("color"),t=d("font"),r=d("text"),o=d("font-weight"),n=d("tracking"),s=d("leading"),i=d("breakpoint"),a=d("container"),l=d("spacing"),c=d("radius"),u=d("shadow"),f=d("inset-shadow"),p=d("text-shadow"),m=d("drop-shadow"),v=d("blur"),h=d("perspective"),b=d("aspect"),g=d("ease"),T=d("animate"),R=()=>["auto","avoid","all","avoid-page","page","left","right","column"],N=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],P=()=>[...N(),I,A],K=()=>["auto","hidden","clip","visible","scroll"],B=()=>["auto","contain","none"],G=()=>[I,A,l],$=()=>[y,"full","auto",...G()],X=()=>[x,"none","subgrid",I,A],q=()=>["auto",{span:["full",x,I,A]},x,I,A],Y=()=>[x,"auto",I,A],Z=()=>["auto","min","max","fr",I,A],J=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],Q=()=>["start","end","center","stretch","center-safe","end-safe"],ee=()=>["auto",...G()],et=()=>[y,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...G()],er=()=>[e,I,A],eo=()=>[...N(),W,D,{position:[I,A]}],en=()=>["no-repeat",{repeat:["","x","y","space","round"]}],es=()=>["auto","cover","contain",U,j,{size:[I,A]}],ei=()=>[k,_,z],ea=()=>["","none","full",c,I,A],el=()=>["",w,_,z],ec=()=>["solid","dashed","dotted","double"],ed=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],eu=()=>[w,k,W,D],ef=()=>["","none",v,I,A],ep=()=>["none",w,I,A],em=()=>["none",w,I,A],ev=()=>[w,I,A],eh=()=>[y,"full",...G()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[E],breakpoint:[E],color:[C],container:[E],"drop-shadow":[E],ease:["in","out","in-out"],font:[S],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[E],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[E],shadow:[E],spacing:["px",w],text:[E],"text-shadow":[E],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",y,A,I,b]}],container:["container"],columns:[{columns:[w,A,I,a]}],"break-after":[{"break-after":R()}],"break-before":[{"break-before":R()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:P()}],overflow:[{overflow:K()}],"overflow-x":[{"overflow-x":K()}],"overflow-y":[{"overflow-y":K()}],overscroll:[{overscroll:B()}],"overscroll-x":[{"overscroll-x":B()}],"overscroll-y":[{"overscroll-y":B()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:$()}],"inset-x":[{"inset-x":$()}],"inset-y":[{"inset-y":$()}],start:[{start:$()}],end:[{end:$()}],top:[{top:$()}],right:[{right:$()}],bottom:[{bottom:$()}],left:[{left:$()}],visibility:["visible","invisible","collapse"],z:[{z:[x,"auto",I,A]}],basis:[{basis:[y,"full","auto",a,...G()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[w,y,"auto","initial","none",A]}],grow:[{grow:["",w,I,A]}],shrink:[{shrink:["",w,I,A]}],order:[{order:[x,"first","last","none",I,A]}],"grid-cols":[{"grid-cols":X()}],"col-start-end":[{col:q()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":X()}],"row-start-end":[{row:q()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:G()}],"gap-x":[{"gap-x":G()}],"gap-y":[{"gap-y":G()}],"justify-content":[{justify:[...J(),"normal"]}],"justify-items":[{"justify-items":[...Q(),"normal"]}],"justify-self":[{"justify-self":["auto",...Q()]}],"align-content":[{content:["normal",...J()]}],"align-items":[{items:[...Q(),{baseline:["","last"]}]}],"align-self":[{self:["auto",...Q(),{baseline:["","last"]}]}],"place-content":[{"place-content":J()}],"place-items":[{"place-items":[...Q(),"baseline"]}],"place-self":[{"place-self":["auto",...Q()]}],p:[{p:G()}],px:[{px:G()}],py:[{py:G()}],ps:[{ps:G()}],pe:[{pe:G()}],pt:[{pt:G()}],pr:[{pr:G()}],pb:[{pb:G()}],pl:[{pl:G()}],m:[{m:ee()}],mx:[{mx:ee()}],my:[{my:ee()}],ms:[{ms:ee()}],me:[{me:ee()}],mt:[{mt:ee()}],mr:[{mr:ee()}],mb:[{mb:ee()}],ml:[{ml:ee()}],"space-x":[{"space-x":G()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":G()}],"space-y-reverse":["space-y-reverse"],size:[{size:et()}],w:[{w:[a,"screen",...et()]}],"min-w":[{"min-w":[a,"screen","none",...et()]}],"max-w":[{"max-w":[a,"screen","none","prose",{screen:[i]},...et()]}],h:[{h:["screen","lh",...et()]}],"min-h":[{"min-h":["screen","lh","none",...et()]}],"max-h":[{"max-h":["screen","lh",...et()]}],"font-size":[{text:["base",r,_,z]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[o,I,O]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",k,A]}],"font-family":[{font:[F,A,t]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[n,I,A]}],"line-clamp":[{"line-clamp":[w,"none",I,O]}],leading:[{leading:[s,...G()]}],"list-image":[{"list-image":["none",I,A]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",I,A]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:er()}],"text-color":[{text:er()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...ec(),"wavy"]}],"text-decoration-thickness":[{decoration:[w,"from-font","auto",I,z]}],"text-decoration-color":[{decoration:er()}],"underline-offset":[{"underline-offset":[w,"auto",I,A]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:G()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",I,A]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",I,A]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:eo()}],"bg-repeat":[{bg:en()}],"bg-size":[{bg:es()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},x,I,A],radial:["",I,A],conic:[x,I,A]},V,L]}],"bg-color":[{bg:er()}],"gradient-from-pos":[{from:ei()}],"gradient-via-pos":[{via:ei()}],"gradient-to-pos":[{to:ei()}],"gradient-from":[{from:er()}],"gradient-via":[{via:er()}],"gradient-to":[{to:er()}],rounded:[{rounded:ea()}],"rounded-s":[{"rounded-s":ea()}],"rounded-e":[{"rounded-e":ea()}],"rounded-t":[{"rounded-t":ea()}],"rounded-r":[{"rounded-r":ea()}],"rounded-b":[{"rounded-b":ea()}],"rounded-l":[{"rounded-l":ea()}],"rounded-ss":[{"rounded-ss":ea()}],"rounded-se":[{"rounded-se":ea()}],"rounded-ee":[{"rounded-ee":ea()}],"rounded-es":[{"rounded-es":ea()}],"rounded-tl":[{"rounded-tl":ea()}],"rounded-tr":[{"rounded-tr":ea()}],"rounded-br":[{"rounded-br":ea()}],"rounded-bl":[{"rounded-bl":ea()}],"border-w":[{border:el()}],"border-w-x":[{"border-x":el()}],"border-w-y":[{"border-y":el()}],"border-w-s":[{"border-s":el()}],"border-w-e":[{"border-e":el()}],"border-w-t":[{"border-t":el()}],"border-w-r":[{"border-r":el()}],"border-w-b":[{"border-b":el()}],"border-w-l":[{"border-l":el()}],"divide-x":[{"divide-x":el()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":el()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...ec(),"hidden","none"]}],"divide-style":[{divide:[...ec(),"hidden","none"]}],"border-color":[{border:er()}],"border-color-x":[{"border-x":er()}],"border-color-y":[{"border-y":er()}],"border-color-s":[{"border-s":er()}],"border-color-e":[{"border-e":er()}],"border-color-t":[{"border-t":er()}],"border-color-r":[{"border-r":er()}],"border-color-b":[{"border-b":er()}],"border-color-l":[{"border-l":er()}],"divide-color":[{divide:er()}],"outline-style":[{outline:[...ec(),"none","hidden"]}],"outline-offset":[{"outline-offset":[w,I,A]}],"outline-w":[{outline:["",w,_,z]}],"outline-color":[{outline:er()}],shadow:[{shadow:["","none",u,H,M]}],"shadow-color":[{shadow:er()}],"inset-shadow":[{"inset-shadow":["none",f,H,M]}],"inset-shadow-color":[{"inset-shadow":er()}],"ring-w":[{ring:el()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:er()}],"ring-offset-w":[{"ring-offset":[w,z]}],"ring-offset-color":[{"ring-offset":er()}],"inset-ring-w":[{"inset-ring":el()}],"inset-ring-color":[{"inset-ring":er()}],"text-shadow":[{"text-shadow":["none",p,H,M]}],"text-shadow-color":[{"text-shadow":er()}],opacity:[{opacity:[w,I,A]}],"mix-blend":[{"mix-blend":[...ed(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ed()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[w]}],"mask-image-linear-from-pos":[{"mask-linear-from":eu()}],"mask-image-linear-to-pos":[{"mask-linear-to":eu()}],"mask-image-linear-from-color":[{"mask-linear-from":er()}],"mask-image-linear-to-color":[{"mask-linear-to":er()}],"mask-image-t-from-pos":[{"mask-t-from":eu()}],"mask-image-t-to-pos":[{"mask-t-to":eu()}],"mask-image-t-from-color":[{"mask-t-from":er()}],"mask-image-t-to-color":[{"mask-t-to":er()}],"mask-image-r-from-pos":[{"mask-r-from":eu()}],"mask-image-r-to-pos":[{"mask-r-to":eu()}],"mask-image-r-from-color":[{"mask-r-from":er()}],"mask-image-r-to-color":[{"mask-r-to":er()}],"mask-image-b-from-pos":[{"mask-b-from":eu()}],"mask-image-b-to-pos":[{"mask-b-to":eu()}],"mask-image-b-from-color":[{"mask-b-from":er()}],"mask-image-b-to-color":[{"mask-b-to":er()}],"mask-image-l-from-pos":[{"mask-l-from":eu()}],"mask-image-l-to-pos":[{"mask-l-to":eu()}],"mask-image-l-from-color":[{"mask-l-from":er()}],"mask-image-l-to-color":[{"mask-l-to":er()}],"mask-image-x-from-pos":[{"mask-x-from":eu()}],"mask-image-x-to-pos":[{"mask-x-to":eu()}],"mask-image-x-from-color":[{"mask-x-from":er()}],"mask-image-x-to-color":[{"mask-x-to":er()}],"mask-image-y-from-pos":[{"mask-y-from":eu()}],"mask-image-y-to-pos":[{"mask-y-to":eu()}],"mask-image-y-from-color":[{"mask-y-from":er()}],"mask-image-y-to-color":[{"mask-y-to":er()}],"mask-image-radial":[{"mask-radial":[I,A]}],"mask-image-radial-from-pos":[{"mask-radial-from":eu()}],"mask-image-radial-to-pos":[{"mask-radial-to":eu()}],"mask-image-radial-from-color":[{"mask-radial-from":er()}],"mask-image-radial-to-color":[{"mask-radial-to":er()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":N()}],"mask-image-conic-pos":[{"mask-conic":[w]}],"mask-image-conic-from-pos":[{"mask-conic-from":eu()}],"mask-image-conic-to-pos":[{"mask-conic-to":eu()}],"mask-image-conic-from-color":[{"mask-conic-from":er()}],"mask-image-conic-to-color":[{"mask-conic-to":er()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:eo()}],"mask-repeat":[{mask:en()}],"mask-size":[{mask:es()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",I,A]}],filter:[{filter:["","none",I,A]}],blur:[{blur:ef()}],brightness:[{brightness:[w,I,A]}],contrast:[{contrast:[w,I,A]}],"drop-shadow":[{"drop-shadow":["","none",m,H,M]}],"drop-shadow-color":[{"drop-shadow":er()}],grayscale:[{grayscale:["",w,I,A]}],"hue-rotate":[{"hue-rotate":[w,I,A]}],invert:[{invert:["",w,I,A]}],saturate:[{saturate:[w,I,A]}],sepia:[{sepia:["",w,I,A]}],"backdrop-filter":[{"backdrop-filter":["","none",I,A]}],"backdrop-blur":[{"backdrop-blur":ef()}],"backdrop-brightness":[{"backdrop-brightness":[w,I,A]}],"backdrop-contrast":[{"backdrop-contrast":[w,I,A]}],"backdrop-grayscale":[{"backdrop-grayscale":["",w,I,A]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[w,I,A]}],"backdrop-invert":[{"backdrop-invert":["",w,I,A]}],"backdrop-opacity":[{"backdrop-opacity":[w,I,A]}],"backdrop-saturate":[{"backdrop-saturate":[w,I,A]}],"backdrop-sepia":[{"backdrop-sepia":["",w,I,A]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":G()}],"border-spacing-x":[{"border-spacing-x":G()}],"border-spacing-y":[{"border-spacing-y":G()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",I,A]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[w,"initial",I,A]}],ease:[{ease:["linear","initial",g,I,A]}],delay:[{delay:[w,I,A]}],animate:[{animate:["none",T,I,A]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[h,I,A]}],"perspective-origin":[{"perspective-origin":P()}],rotate:[{rotate:ep()}],"rotate-x":[{"rotate-x":ep()}],"rotate-y":[{"rotate-y":ep()}],"rotate-z":[{"rotate-z":ep()}],scale:[{scale:em()}],"scale-x":[{"scale-x":em()}],"scale-y":[{"scale-y":em()}],"scale-z":[{"scale-z":em()}],"scale-3d":["scale-3d"],skew:[{skew:ev()}],"skew-x":[{"skew-x":ev()}],"skew-y":[{"skew-y":ev()}],transform:[{transform:[I,A,"","none","gpu","cpu"]}],"transform-origin":[{origin:P()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:eh()}],"translate-x":[{"translate-x":eh()}],"translate-y":[{"translate-y":eh()}],"translate-z":[{"translate-z":eh()}],"translate-none":["translate-none"],accent:[{accent:er()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:er()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",I,A]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":G()}],"scroll-mx":[{"scroll-mx":G()}],"scroll-my":[{"scroll-my":G()}],"scroll-ms":[{"scroll-ms":G()}],"scroll-me":[{"scroll-me":G()}],"scroll-mt":[{"scroll-mt":G()}],"scroll-mr":[{"scroll-mr":G()}],"scroll-mb":[{"scroll-mb":G()}],"scroll-ml":[{"scroll-ml":G()}],"scroll-p":[{"scroll-p":G()}],"scroll-px":[{"scroll-px":G()}],"scroll-py":[{"scroll-py":G()}],"scroll-ps":[{"scroll-ps":G()}],"scroll-pe":[{"scroll-pe":G()}],"scroll-pt":[{"scroll-pt":G()}],"scroll-pr":[{"scroll-pr":G()}],"scroll-pb":[{"scroll-pb":G()}],"scroll-pl":[{"scroll-pl":G()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",I,A]}],fill:[{fill:["none",...er()]}],"stroke-w":[{stroke:[w,_,z,O]}],stroke:[{stroke:["none",...er()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function ee(){for(var e=arguments.length,r=Array(e),o=0;o<e;o++)r[o]=arguments[o];return Q((0,t.clsx)(r))}},30438,e=>{"use strict";e.s(["Toaster",()=>v],30438);var t=e.i(67857),r=e.i(61620),o=e.i(24302),n=e.i(29243),s=e.i(68316),i=e.i(17388),a=e.i(92072);let l=n.Provider,c=o.forwardRef((e,r)=>{let{className:o,...s}=e;return(0,t.jsx)(n.Viewport,{ref:r,className:(0,a.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",o),...s})});c.displayName=n.Viewport.displayName;let d=(0,s.cva)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),u=o.forwardRef((e,r)=>{let{className:o,variant:s,...i}=e;return(0,t.jsx)(n.Root,{ref:r,className:(0,a.cn)(d({variant:s}),o),...i})});u.displayName=n.Root.displayName,o.forwardRef((e,r)=>{let{className:o,...s}=e;return(0,t.jsx)(n.Action,{ref:r,className:(0,a.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground",o),...s})}).displayName=n.Action.displayName;let f=o.forwardRef((e,r)=>{let{className:o,...s}=e;return(0,t.jsx)(n.Close,{ref:r,className:(0,a.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50",o),"toast-close":"",...s,children:(0,t.jsx)(i.X,{className:"w-4 h-4"})})});f.displayName=n.Close.displayName;let p=o.forwardRef((e,r)=>{let{className:o,...s}=e;return(0,t.jsx)(n.Title,{ref:r,className:(0,a.cn)("text-sm font-semibold",o),...s})});p.displayName=n.Title.displayName;let m=o.forwardRef((e,r)=>{let{className:o,...s}=e;return(0,t.jsx)(n.Description,{ref:r,className:(0,a.cn)("text-sm opacity-90",o),...s})});function v(){let{toasts:e}=(0,r.useToast)();return(0,t.jsxs)(l,{children:[e.map(function(e){let{id:r,title:o,description:n,action:s,...i}=e;return(0,t.jsxs)(u,{...i,children:[(0,t.jsxs)("div",{className:"grid gap-1",children:[o&&(0,t.jsx)(p,{children:o}),n&&(0,t.jsx)(m,{children:n})]}),s,(0,t.jsx)(f,{})]},r)}),(0,t.jsx)(c,{})]})}m.displayName=n.Description.displayName}]);