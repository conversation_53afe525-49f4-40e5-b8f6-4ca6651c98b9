"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Fallback to @uiw/react-md-editor if Cherry fails\nconst MDEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_uiw_react-md-editor_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @uiw/react-md-editor */ \"(app-pages-browser)/./node_modules/@uiw/react-md-editor/esm/index.js\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\CherryMarkdownEditor.tsx -> \" + \"@uiw/react-md-editor\"\n        ]\n    },\n    ssr: false\n});\n_c = MDEditor;\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cherryLoadFailed, setCherryLoadFailed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 設置超時，如果 Cherry 載入時間過長則使用備用編輯器\n            const timeout = setTimeout({\n                \"CherryMarkdownEditor.useEffect.timeout\": ()=>{\n                    console.log(\"Cherry loading timeout, using fallback editor\");\n                    setCherryLoadFailed(true);\n                }\n            }[\"CherryMarkdownEditor.useEffect.timeout\"], 5000); // 5秒超時\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                            clearTimeout(timeout); // 成功載入，清除超時\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                            setCherryLoadFailed(true);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                        // Set failed state to use fallback editor\n                        setCherryLoadFailed(true);\n                        clearTimeout(timeout);\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    clearTimeout(timeout);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 294,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 282,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，使用 MDEditor 作為備用編輯器\n    if (!CherryClass) {\n        if (cherryLoadFailed) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cherry-markdown-editor \".concat(className),\n                style: {\n                    height: \"100%\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MDEditor, {\n                    value: value,\n                    onChange: (val)=>onChange && onChange(val || \"\"),\n                    preview: preview === \"preview\" ? \"preview\" : preview === \"edit\" ? \"edit\" : \"live\",\n                    hideToolbar: hideToolbar,\n                    \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\",\n                    style: {\n                        height: \"100%\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 304,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 303,\n                columnNumber: 11\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"載入編輯器中...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 328,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"正在載入 Cherry Markdown 編輯器...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 331,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 317,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 339,\n        columnNumber: 7\n    }, undefined);\n}, \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c2 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MDEditor\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c2, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});