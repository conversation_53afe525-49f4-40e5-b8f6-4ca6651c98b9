"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/app/edit/page.tsx":
/*!*******************************!*\
  !*** ./src/app/edit/page.tsx ***!
  \*******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ EditPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_NoteList__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/NoteList */ \"(app-pages-browser)/./src/components/NoteList.tsx\");\n/* harmony import */ var _components_NoteEditor__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/NoteEditor */ \"(app-pages-browser)/./src/components/NoteEditor.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./src/hooks/use-toast.ts\");\n/* harmony import */ var _components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/toaster */ \"(app-pages-browser)/./src/components/ui/toaster.tsx\");\n/* harmony import */ var _components_theme_toggle__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/components/theme-toggle */ \"(app-pages-browser)/./src/components/theme-toggle.tsx\");\n/* harmony import */ var _barrel_optimize_names_PanelLeftClose_PanelLeftOpen_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=PanelLeftClose,PanelLeftOpen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-close.js\");\n/* harmony import */ var _barrel_optimize_names_PanelLeftClose_PanelLeftOpen_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=PanelLeftClose,PanelLeftOpen!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/panel-left-open.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_9___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_9__);\n/* harmony import */ var _contexts_i18n__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! @/contexts/i18n */ \"(app-pages-browser)/./src/contexts/i18n.tsx\");\n/* harmony import */ var _components_ui_resizable__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! @/components/ui/resizable */ \"(app-pages-browser)/./src/components/ui/resizable.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\n\n\nfunction EditPageContent() {\n    _s();\n    const { t } = (0,_contexts_i18n__WEBPACK_IMPORTED_MODULE_10__.useI18n)();\n    const [notes, setNotes] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [selectedNote, setSelectedNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isNewNote, setIsNewNote] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [sidebarOpen, setSidebarOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast)();\n    const searchParams = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams)();\n    const loadNotes = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"EditPageContent.useCallback[loadNotes]\": async ()=>{\n            var _window_electron;\n            if (typeof ((_window_electron = window.electron) === null || _window_electron === void 0 ? void 0 : _window_electron.getNotes) !== \"function\") {\n                setLoading(false);\n                toast({\n                    title: t(\"edit.error\"),\n                    description: t(\"edit.electron_api_unavailable\"),\n                    variant: \"destructive\"\n                });\n                return;\n            }\n            try {\n                const allNotes = await window.electron.getNotes();\n                const noteSummaries = allNotes.map({\n                    \"EditPageContent.useCallback[loadNotes].noteSummaries\": (note)=>({\n                            id: note.id,\n                            title: note.title,\n                            content: note.content,\n                            tags: note.tags\n                        })\n                }[\"EditPageContent.useCallback[loadNotes].noteSummaries\"]);\n                setNotes(noteSummaries);\n                const noteId = searchParams.get(\"id\");\n                if (noteId) {\n                    handleSelectNote(noteId);\n                } else {\n                    setIsNewNote(true);\n                }\n            } catch (error) {\n                const message = error instanceof Error ? error.message : t(\"edit.cannot_load_notes\");\n                toast({\n                    title: t(\"edit.load_failed\"),\n                    description: message,\n                    variant: \"destructive\"\n                });\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"EditPageContent.useCallback[loadNotes]\"], [\n        toast,\n        searchParams\n    ]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"EditPageContent.useEffect\": ()=>{\n            loadNotes();\n        }\n    }[\"EditPageContent.useEffect\"], [\n        loadNotes\n    ]);\n    const handleSelectNote = async (id)=>{\n        try {\n            const note = await window.electron.getNote(id);\n            setSelectedNote(note);\n            setIsNewNote(false);\n        } catch (error) {\n            const message = error instanceof Error ? error.message : t(\"edit.cannot_load_note_content\");\n            toast({\n                title: t(\"edit.load_failed\"),\n                description: message,\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleSaveNote = async (id, title, content, tags)=>{\n        try {\n            let savedNote;\n            if (id) {\n                savedNote = await window.electron.updateNote(id, title, content, tags);\n            } else {\n                savedNote = await window.electron.createNote(title, content, tags);\n            }\n            setSelectedNote(savedNote);\n            setIsNewNote(false);\n            // 更新筆記列表狀態，而不是重新載入\n            setNotes((prevNotes)=>{\n                const noteIndex = prevNotes.findIndex((n)=>n.id === savedNote.id);\n                if (noteIndex > -1) {\n                    // 更新現有筆記\n                    const newNotes = [\n                        ...prevNotes\n                    ];\n                    newNotes[noteIndex] = {\n                        id: savedNote.id,\n                        title: savedNote.title,\n                        content: savedNote.content,\n                        tags: savedNote.tags\n                    };\n                    return newNotes;\n                } else {\n                    // 新增筆記\n                    return [\n                        {\n                            id: savedNote.id,\n                            title: savedNote.title,\n                            content: savedNote.content,\n                            tags: savedNote.tags\n                        },\n                        ...prevNotes\n                    ];\n                }\n            });\n            toast({\n                title: t(\"edit.save_success\"),\n                description: t(\"edit.note_saved\")\n            });\n        } catch (error) {\n            const message = error instanceof Error ? error.message : t(\"edit.cannot_save_note\");\n            toast({\n                title: t(\"edit.save_failed\"),\n                description: message,\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleDeleteNote = async (id)=>{\n        try {\n            await window.electron.deleteNote(id);\n            setSelectedNote(null);\n            await loadNotes();\n            toast({\n                title: t(\"edit.delete_success\"),\n                description: t(\"edit.note_deleted\")\n            });\n        } catch (error) {\n            const message = error instanceof Error ? error.message : t(\"edit.cannot_delete_note\");\n            toast({\n                title: t(\"edit.delete_failed\"),\n                description: message,\n                variant: \"destructive\"\n            });\n        }\n    };\n    const handleNewNote = ()=>{\n        setSelectedNote(null);\n        setIsNewNote(true);\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center h-screen\",\n            children: t(\"edit.loading\")\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n            lineNumber: 173,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex h-screen bg-background text-foreground\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"\\n            \".concat(sidebarOpen ? \"translate-x-0\" : \"-translate-x-full\", \"\\n            fixed inset-y-0 left-0 z-50 w-80 bg-card/95 backdrop-blur supports-[backdrop-filter]:bg-card/60 border-r shadow-lg transition-transform duration-300 ease-in-out\\n          \"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col h-full p-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-between mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                            href: \"/\",\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"24\",\n                                                    height: \"24\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"w-6 h-6\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 215\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"9 22 9 12 15 12 15 22\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 190,\n                                                            columnNumber: 279\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 190,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                    className: \"text-2xl font-bold\",\n                                                    children: \"MyNote\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_8__.ThemeToggle, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 194,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                    variant: \"ghost\",\n                                                    size: \"icon\",\n                                                    onClick: ()=>setSidebarOpen(false),\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PanelLeftClose_PanelLeftOpen_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                        className: \"w-5 h-5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 195,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 193,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                    variant: \"outline\",\n                                    onClick: handleNewNote,\n                                    className: \"mb-4\",\n                                    children: t(\"edit.new_note\")\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex-1 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoteList__WEBPACK_IMPORTED_MODULE_4__.NoteList, {\n                                        notes: notes,\n                                        onSelectNote: (id)=>{\n                                            handleSelectNote(id);\n                                            setSidebarOpen(false);\n                                        },\n                                        selectedNoteId: selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 208,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 207,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                        lineNumber: 181,\n                        columnNumber: 9\n                    }, this),\n                    sidebarOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 z-40 bg-black/20 backdrop-blur-sm\",\n                        onClick: ()=>setSidebarOpen(false)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                        lineNumber: 220,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                lineNumber: 180,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"hidden w-full h-full md:flex\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_11__.ResizablePanelGroup, {\n                    direction: \"horizontal\",\n                    className: \"h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_11__.ResizablePanel, {\n                            defaultSize: 25,\n                            minSize: 20,\n                            maxSize: 40,\n                            className: \"min-w-[280px]\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col h-full bg-card/30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between p-4 bg-card/50\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_9___default()), {\n                                                href: \"/\",\n                                                className: \"flex items-center gap-2 transition-opacity hover:opacity-80\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        xmlns: \"http://www.w3.org/2000/svg\",\n                                                        width: \"24\",\n                                                        height: \"24\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        strokeWidth: \"2\",\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        className: \"w-6 h-6 text-primary\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                d: \"m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 230\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                                points: \"9 22 9 12 15 12 15 22\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                                lineNumber: 240,\n                                                                columnNumber: 294\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 240,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                                        className: \"text-xl font-bold text-transparent bg-gradient-to-r from-primary to-primary/70 bg-clip-text\",\n                                                        children: \"MyNote\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                        lineNumber: 241,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                lineNumber: 239,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_8__.ThemeToggle, {}, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                lineNumber: 243,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"p-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                            variant: \"default\",\n                                            onClick: handleNewNote,\n                                            className: \"w-full shadow-sm bg-primary hover:bg-primary/90 text-primary-foreground\",\n                                            size: \"sm\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"16\",\n                                                    height: \"16\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"mr-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 214\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"14 2 14 8 20 8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 301\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M12 18v-6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 346\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M9 15h6\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 253,\n                                                            columnNumber: 373\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 253,\n                                                    columnNumber: 19\n                                                }, this),\n                                                t(\"edit.new_note\")\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 247,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 246,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1 p-2 overflow-hidden\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoteList__WEBPACK_IMPORTED_MODULE_4__.NoteList, {\n                                            notes: notes,\n                                            onSelectNote: handleSelectNote,\n                                            selectedNoteId: selectedNote === null || selectedNote === void 0 ? void 0 : selectedNote.id\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 259,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 258,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 231,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_11__.ResizableHandle, {\n                            withHandle: true,\n                            className: \"resizable-handle-custom\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_resizable__WEBPACK_IMPORTED_MODULE_11__.ResizablePanel, {\n                            defaultSize: 75,\n                            minSize: 50,\n                            className: \"relative\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                                className: \"absolute inset-0 flex flex-col overflow-hidden\",\n                                children: selectedNote || isNewNote ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col h-full p-6 overflow-hidden\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoteEditor__WEBPACK_IMPORTED_MODULE_5__.NoteEditor, {\n                                        note: selectedNote,\n                                        onSave: handleSaveNote,\n                                        onDelete: handleDeleteNote\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 276,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 275,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center h-full\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4 text-center\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                                    width: \"32\",\n                                                    height: \"32\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    strokeWidth: \"2\",\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    className: \"text-primary\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 226\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                                            points: \"14 2 14 8 20 8\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                            lineNumber: 286,\n                                                            columnNumber: 313\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                    lineNumber: 286,\n                                                    columnNumber: 23\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                lineNumber: 285,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                                className: \"text-2xl font-semibold\",\n                                                children: t(\"edit.welcome_title\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                lineNumber: 288,\n                                                columnNumber: 21\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"max-w-md text-muted-foreground\",\n                                                children: t(\"edit.welcome_description\")\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                                lineNumber: 289,\n                                                columnNumber: 21\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                        lineNumber: 284,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 283,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                    lineNumber: 229,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                lineNumber: 228,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"md:hidden fixed top-0 left-0 right-0 z-30 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-between p-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                            variant: \"ghost\",\n                            size: \"icon\",\n                            onClick: ()=>setSidebarOpen(!sidebarOpen),\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_PanelLeftClose_PanelLeftOpen_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                className: \"w-5 h-5\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 303,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-lg font-semibold\",\n                            children: \"MyNote\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 310,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_toggle__WEBPACK_IMPORTED_MODULE_8__.ThemeToggle, {}, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                            lineNumber: 311,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                    lineNumber: 302,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                lineNumber: 301,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full h-full pt-16 md:hidden\",\n                children: selectedNote || isNewNote ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"h-full p-4 flex flex-col\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_NoteEditor__WEBPACK_IMPORTED_MODULE_5__.NoteEditor, {\n                        note: selectedNote,\n                        onSave: handleSaveNote,\n                        onDelete: handleDeleteNote\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                        lineNumber: 319,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                    lineNumber: 318,\n                    columnNumber: 11\n                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center h-full p-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4 text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center justify-center w-16 h-16 mx-auto rounded-full bg-primary/10\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    xmlns: \"http://www.w3.org/2000/svg\",\n                                    width: \"32\",\n                                    height: \"32\",\n                                    viewBox: \"0 0 24 24\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    strokeWidth: \"2\",\n                                    strokeLinecap: \"round\",\n                                    strokeLinejoin: \"round\",\n                                    className: \"text-primary\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            d: \"M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 220\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"polyline\", {\n                                            points: \"14 2 14 8 20 8\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                            lineNumber: 329,\n                                            columnNumber: 307\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"text-2xl font-semibold\",\n                                children: t(\"edit.welcome_title\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 331,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"max-w-md text-muted-foreground\",\n                                children: t(\"edit.welcome_description\")\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                        lineNumber: 327,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                    lineNumber: 326,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                lineNumber: 316,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_toaster__WEBPACK_IMPORTED_MODULE_7__.Toaster, {}, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n                lineNumber: 340,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n        lineNumber: 178,\n        columnNumber: 5\n    }, this);\n}\n_s(EditPageContent, \"Cq5umuyp+fabl3UXO5VYmSCZ7K0=\", false, function() {\n    return [\n        _contexts_i18n__WEBPACK_IMPORTED_MODULE_10__.useI18n,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_6__.useToast,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useSearchParams\n    ];\n});\n_c = EditPageContent;\nfunction EditPage() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react__WEBPACK_IMPORTED_MODULE_1__.Suspense, {\n        fallback: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            children: \"載入中...\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n            lineNumber: 347,\n            columnNumber: 25\n        }, void 0),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(EditPageContent, {}, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n            lineNumber: 348,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\app\\\\edit\\\\page.tsx\",\n        lineNumber: 347,\n        columnNumber: 5\n    }, this);\n}\n_c1 = EditPage;\nvar _c, _c1;\n$RefreshReg$(_c, \"EditPageContent\");\n$RefreshReg$(_c1, \"EditPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/edit/page.tsx\n"));

/***/ })

});