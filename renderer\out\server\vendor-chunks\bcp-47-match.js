"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/bcp-47-match";
exports.ids = ["vendor-chunks/bcp-47-match"];
exports.modules = {

/***/ "(ssr)/./node_modules/bcp-47-match/index.js":
/*!********************************************!*\
  !*** ./node_modules/bcp-47-match/index.js ***!
  \********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   basicFilter: () => (/* binding */ basicFilter),\n/* harmony export */   extendedFilter: () => (/* binding */ extendedFilter),\n/* harmony export */   lookup: () => (/* binding */ lookup)\n/* harmony export */ });\n/**\n * See <https://tools.ietf.org/html/rfc4647#section-3.1>\n * for more info on the algorithms.\n */\n\n/**\n * @typedef {string} Tag\n *   BCP-47 tag.\n * @typedef {Array<Tag>} Tags\n *   List of BCP-47 tags.\n * @typedef {string} Range\n *   RFC 4647 range.\n * @typedef {Array<Range>} Ranges\n *   List of RFC 4647 range.\n *\n * @callback Check\n *   An internal check.\n * @param {Tag} tag\n *   BCP-47 tag.\n * @param {Range} range\n *   RFC 4647 range.\n * @returns {boolean}\n *   Whether the range matches the tag.\n *\n * @typedef {FilterOrLookup<true>} Filter\n *   Filter: yields all tags that match a range.\n * @typedef {FilterOrLookup<false>} Lookup\n *   Lookup: yields the best tag that matches a range.\n */\n\n/**\n * @template {boolean} IsFilter\n *   Whether to filter or perform a lookup.\n * @callback FilterOrLookup\n *   A check.\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {IsFilter extends true ? Tags : Tag|undefined}\n *   Result.\n */\n\n/**\n * Factory to perform a filter or a lookup.\n *\n * This factory creates a function that accepts a list of tags and a list of\n * ranges, and contains logic to exit early for lookups.\n * `check` just has to deal with one tag and one range.\n * This match function iterates over ranges, and for each range,\n * iterates over tags.\n * That way, earlier ranges matching any tag have precedence over later ranges.\n *\n * @template {boolean} IsFilter\n * @param {Check} check\n *   A check.\n * @param {IsFilter} filter\n *   Whether to filter or perform a lookup.\n * @returns {FilterOrLookup<IsFilter>}\n *   Filter or lookup.\n */\nfunction factory(check, filter) {\n  /**\n   * @param {Tag|Tags} tags\n   *   One or more BCP-47 tags.\n   * @param {Range|Ranges|undefined} [ranges='*']\n   *   One or more RFC 4647 ranges.\n   * @returns {IsFilter extends true ? Tags : Tag|undefined}\n   *   Result.\n   */\n  return function (tags, ranges) {\n    let left = cast(tags, 'tag')\n    const right = cast(\n      ranges === null || ranges === undefined ? '*' : ranges,\n      'range'\n    )\n    /** @type {Tags} */\n    const matches = []\n    let rightIndex = -1\n\n    while (++rightIndex < right.length) {\n      const range = right[rightIndex].toLowerCase()\n\n      // Ignore wildcards in lookup mode.\n      if (!filter && range === '*') continue\n\n      let leftIndex = -1\n      /** @type {Tags} */\n      const next = []\n\n      while (++leftIndex < left.length) {\n        if (check(left[leftIndex].toLowerCase(), range)) {\n          // Exit if this is a lookup and we have a match.\n          if (!filter) {\n            return /** @type {IsFilter extends true ? Tags : Tag|undefined} */ (\n              left[leftIndex]\n            )\n          }\n\n          matches.push(left[leftIndex])\n        } else {\n          next.push(left[leftIndex])\n        }\n      }\n\n      left = next\n    }\n\n    // If this is a filter, return the list.  If it’s a lookup, we didn’t find\n    // a match, so return `undefined`.\n    return /** @type {IsFilter extends true ? Tags : Tag|undefined} */ (\n      filter ? matches : undefined\n    )\n  }\n}\n\n/**\n * Basic Filtering (Section 3.3.1) matches a language priority list consisting\n * of basic language ranges (Section 2.1) to sets of language tags.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tags}\n *   List of BCP-47 tags.\n */\nconst basicFilter = factory(function (tag, range) {\n  return range === '*' || tag === range || tag.includes(range + '-')\n}, true)\n\n/**\n * Extended Filtering (Section 3.3.2) matches a language priority list\n * consisting of extended language ranges (Section 2.2) to sets of language\n * tags.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tags}\n *   List of BCP-47 tags.\n */\nconst extendedFilter = factory(function (tag, range) {\n  // 3.3.2.1\n  const left = tag.split('-')\n  const right = range.split('-')\n  let leftIndex = 0\n  let rightIndex = 0\n\n  // 3.3.2.2\n  if (right[rightIndex] !== '*' && left[leftIndex] !== right[rightIndex]) {\n    return false\n  }\n\n  leftIndex++\n  rightIndex++\n\n  // 3.3.2.3\n  while (rightIndex < right.length) {\n    // 3.3.2.3.A\n    if (right[rightIndex] === '*') {\n      rightIndex++\n      continue\n    }\n\n    // 3.3.2.3.B\n    if (!left[leftIndex]) return false\n\n    // 3.3.2.3.C\n    if (left[leftIndex] === right[rightIndex]) {\n      leftIndex++\n      rightIndex++\n      continue\n    }\n\n    // 3.3.2.3.D\n    if (left[leftIndex].length === 1) return false\n\n    // 3.3.2.3.E\n    leftIndex++\n  }\n\n  // 3.3.2.4\n  return true\n}, true)\n\n/**\n * Lookup (Section 3.4) matches a language priority list consisting of basic\n * language ranges to sets of language tags to find the one exact language tag\n * that best matches the range.\n *\n * @param {Tag|Tags} tags\n *   One or more BCP-47 tags.\n * @param {Range|Ranges|undefined} [ranges='*']\n *   One or more RFC 4647 ranges.\n * @returns {Tag|undefined}\n *   BCP-47 tag.\n */\nconst lookup = factory(function (tag, range) {\n  let right = range\n\n  /* eslint-disable-next-line no-constant-condition */\n  while (true) {\n    if (right === '*' || tag === right) return true\n\n    let index = right.lastIndexOf('-')\n\n    if (index < 0) return false\n\n    if (right.charAt(index - 2) === '-') index -= 2\n\n    right = right.slice(0, index)\n  }\n}, false)\n\n/**\n * Validate tags or ranges, and cast them to arrays.\n *\n * @param {string|Array<string>} values\n * @param {string} name\n * @returns {Array<string>}\n */\nfunction cast(values, name) {\n  const value = values && typeof values === 'string' ? [values] : values\n\n  if (!value || typeof value !== 'object' || !('length' in value)) {\n    throw new Error(\n      'Invalid ' + name + ' `' + value + '`, expected non-empty string'\n    )\n  }\n\n  return value\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/bcp-47-match/index.js\n");

/***/ })

};
;