"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/character-entities-legacy";
exports.ids = ["vendor-chunks/character-entities-legacy"];
exports.modules = {

/***/ "(ssr)/./node_modules/character-entities-legacy/index.js":
/*!*********************************************************!*\
  !*** ./node_modules/character-entities-legacy/index.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   characterEntitiesLegacy: () => (/* binding */ characterEntitiesLegacy)\n/* harmony export */ });\n/**\n * List of legacy HTML named character references that don’t need a trailing semicolon.\n *\n * @type {Array<string>}\n */\nconst characterEntitiesLegacy = [\n  'AElig',\n  'AMP',\n  'Aacute',\n  'Acirc',\n  'Agrave',\n  'Aring',\n  'Atilde',\n  'Auml',\n  'COPY',\n  'Ccedil',\n  'ETH',\n  'Eacute',\n  'Ecirc',\n  'Egrave',\n  'Euml',\n  'GT',\n  'Iacute',\n  'Icirc',\n  'Igrave',\n  'Iuml',\n  'LT',\n  'Ntilde',\n  'Oacute',\n  'Ocirc',\n  'Ograve',\n  'Oslash',\n  'Otilde',\n  'Ouml',\n  'QUOT',\n  'REG',\n  'THORN',\n  'Uacute',\n  'Ucirc',\n  'Ugrave',\n  'Uuml',\n  'Yacute',\n  'aacute',\n  'acirc',\n  'acute',\n  'aelig',\n  'agrave',\n  'amp',\n  'aring',\n  'atilde',\n  'auml',\n  'brvbar',\n  'ccedil',\n  'cedil',\n  'cent',\n  'copy',\n  'curren',\n  'deg',\n  'divide',\n  'eacute',\n  'ecirc',\n  'egrave',\n  'eth',\n  'euml',\n  'frac12',\n  'frac14',\n  'frac34',\n  'gt',\n  'iacute',\n  'icirc',\n  'iexcl',\n  'igrave',\n  'iquest',\n  'iuml',\n  'laquo',\n  'lt',\n  'macr',\n  'micro',\n  'middot',\n  'nbsp',\n  'not',\n  'ntilde',\n  'oacute',\n  'ocirc',\n  'ograve',\n  'ordf',\n  'ordm',\n  'oslash',\n  'otilde',\n  'ouml',\n  'para',\n  'plusmn',\n  'pound',\n  'quot',\n  'raquo',\n  'reg',\n  'sect',\n  'shy',\n  'sup1',\n  'sup2',\n  'sup3',\n  'szlig',\n  'thorn',\n  'times',\n  'uacute',\n  'ucirc',\n  'ugrave',\n  'uml',\n  'uuml',\n  'yacute',\n  'yen',\n  'yuml'\n]\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/character-entities-legacy/index.js\n");

/***/ })

};
;