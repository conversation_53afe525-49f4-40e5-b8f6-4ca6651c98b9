"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-attr";
exports.ids = ["vendor-chunks/rehype-attr"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-attr/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/rehype-attr/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/rehype-attr/lib/utils.js\");\n\n\nconst rehypeAttrs = (options = {}) => {\n    const { properties = 'data', codeBlockParames = true } = options;\n    return (tree) => {\n        (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_0__.visit)(tree, 'element', (node, index, parent) => {\n            if (codeBlockParames && node.tagName === 'pre' && node && Array.isArray(node.children) && parent && Array.isArray(parent.children) && parent.children.length > 1) {\n                const firstChild = node.children[0];\n                if (firstChild && firstChild.tagName === 'code' && typeof index === 'number') {\n                    const child = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.prevChild)(parent.children, index);\n                    if (child) {\n                        const attr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getCommentObject)(child);\n                        if (Object.keys(attr).length > 0) {\n                            node.properties = { ...node.properties, ...{ 'data-type': 'rehyp' } };\n                            firstChild.properties = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.propertiesHandle)(firstChild.properties, attr, properties);\n                        }\n                    }\n                }\n            }\n            if (/^(em|strong|b|a|i|p|pre|kbd|blockquote|h(1|2|3|4|5|6)|code|table|img|del|ul|ol)$/.test(node.tagName) && parent && Array.isArray(parent.children) && typeof index === 'number') {\n                const child = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.nextChild)(parent.children, index, '', codeBlockParames);\n                if (child) {\n                    const attr = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.getCommentObject)(child);\n                    if (Object.keys(attr).length > 0) {\n                        node.properties = (0,_utils_js__WEBPACK_IMPORTED_MODULE_1__.propertiesHandle)(node.properties, attr, properties);\n                    }\n                }\n            }\n        });\n    };\n};\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (rehypeAttrs);\n//# sourceMappingURL=index.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-attr/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/rehype-attr/lib/utils.js":
/*!***********************************************!*\
  !*** ./node_modules/rehype-attr/lib/utils.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getCommentObject: () => (/* binding */ getCommentObject),\n/* harmony export */   getURLParameters: () => (/* binding */ getURLParameters),\n/* harmony export */   nextChild: () => (/* binding */ nextChild),\n/* harmony export */   prevChild: () => (/* binding */ prevChild),\n/* harmony export */   propertiesHandle: () => (/* binding */ propertiesHandle)\n/* harmony export */ });\nconst getURLParameters = (url = '') => (url.match(/([^?=&]+)(=([^&]*))/g) || []).reduce((a, v) => ((a[v.slice(0, v.indexOf('='))] = v.slice(v.indexOf('=') + 1)), a), {});\nconst prevChild = (data = [], index) => {\n    let i = index;\n    while (i > -1) {\n        i--;\n        if (!data[i])\n            return;\n        if ((data[i] && data[i].value && data[i].value.replace(/(\\n|\\s)/g, '') !== '') || data[i].type !== 'text') {\n            if (!/^rehype:/.test(data[i].value) || data[i].type !== 'comment')\n                return;\n            return data[i];\n        }\n    }\n    return;\n};\nconst nextChild = (data = [], index, tagName, codeBlockParames) => {\n    let i = index;\n    while (i < data.length) {\n        i++;\n        if (tagName) {\n            const element = data[i];\n            if (element && element.value && element.value.replace(/(\\n|\\s)/g, '') !== '' || data[i] && data[i].type === 'element') {\n                return element.tagName === tagName ? element : undefined;\n            }\n        }\n        else {\n            const element = data[i];\n            if (!element || element.type === 'element')\n                return;\n            if (element.type === 'text' && element.value.replace(/(\\n|\\s)/g, '') !== '')\n                return;\n            if (element.type && /^(comment|raw)$/ig.test(element.type)) {\n                if (element.value && !/^rehype:/.test(element.value.replace(/^(\\s+)?<!--(.*?)-->/, '$2') || '')) {\n                    return;\n                }\n                ;\n                if (codeBlockParames) {\n                    const nextNode = nextChild(data, i, 'pre', codeBlockParames);\n                    if (nextNode)\n                        return;\n                    element.value = (element.value || '').replace(/^(\\n|\\s)+/, '');\n                    return element;\n                }\n                else {\n                    element.value = (element.value || '').replace(/^(\\n|\\s)+/, '');\n                    return element;\n                }\n            }\n        }\n    }\n    return;\n};\n/**\n * 获取代码注视的位置\n * @param data 数据\n * @param index 当前数据所在的位置\n * @returns 返回 当前参数数据 Object，`{}`\n */\nconst getCommentObject = ({ value = '' }) => {\n    const param = getURLParameters(value.replace(/^<!--(.*?)-->/, '$1').replace(/^rehype:/, ''));\n    Object.keys(param).forEach((keyName) => {\n        if (param[keyName] === 'true') {\n            param[keyName] = true;\n        }\n        if (param[keyName] === 'false') {\n            param[keyName] = false;\n        }\n        if (typeof param[keyName] === 'string' && !/^0/.test(param[keyName]) && !isNaN(+param[keyName])) {\n            param[keyName] = +param[keyName];\n        }\n    });\n    return param;\n};\nconst propertiesHandle = (defaultAttrs, attrs, type) => {\n    if (type === 'string') {\n        return { ...defaultAttrs, 'data-config': JSON.stringify({ ...attrs, rehyp: true }) };\n    }\n    else if (type === 'attr') {\n        return { ...defaultAttrs, ...attrs };\n    }\n    return { ...defaultAttrs, 'data-config': { ...attrs, rehyp: true } };\n};\n//# sourceMappingURL=utils.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvcmVoeXBlLWF0dHIvbGliL3V0aWxzLmpzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7O0FBQU8sd0tBQXdLO0FBQ3hLO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsZ0NBQWdDO0FBQ2hDO0FBQ08sNEJBQTRCLFlBQVk7QUFDL0M7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ087QUFDUDtBQUNBLGlCQUFpQixpREFBaUQsdUJBQXVCO0FBQ3pGO0FBQ0E7QUFDQSxpQkFBaUI7QUFDakI7QUFDQSxhQUFhLGtDQUFrQztBQUMvQztBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFudGhvXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXG15bm90ZVxccmVuZGVyZXJcXG5vZGVfbW9kdWxlc1xccmVoeXBlLWF0dHJcXGxpYlxcdXRpbHMuanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGNvbnN0IGdldFVSTFBhcmFtZXRlcnMgPSAodXJsID0gJycpID0+ICh1cmwubWF0Y2goLyhbXj89Jl0rKSg9KFteJl0qKSkvZykgfHwgW10pLnJlZHVjZSgoYSwgdikgPT4gKChhW3Yuc2xpY2UoMCwgdi5pbmRleE9mKCc9JykpXSA9IHYuc2xpY2Uodi5pbmRleE9mKCc9JykgKyAxKSksIGEpLCB7fSk7XG5leHBvcnQgY29uc3QgcHJldkNoaWxkID0gKGRhdGEgPSBbXSwgaW5kZXgpID0+IHtcbiAgICBsZXQgaSA9IGluZGV4O1xuICAgIHdoaWxlIChpID4gLTEpIHtcbiAgICAgICAgaS0tO1xuICAgICAgICBpZiAoIWRhdGFbaV0pXG4gICAgICAgICAgICByZXR1cm47XG4gICAgICAgIGlmICgoZGF0YVtpXSAmJiBkYXRhW2ldLnZhbHVlICYmIGRhdGFbaV0udmFsdWUucmVwbGFjZSgvKFxcbnxcXHMpL2csICcnKSAhPT0gJycpIHx8IGRhdGFbaV0udHlwZSAhPT0gJ3RleHQnKSB7XG4gICAgICAgICAgICBpZiAoIS9ecmVoeXBlOi8udGVzdChkYXRhW2ldLnZhbHVlKSB8fCBkYXRhW2ldLnR5cGUgIT09ICdjb21tZW50JylcbiAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICByZXR1cm4gZGF0YVtpXTtcbiAgICAgICAgfVxuICAgIH1cbiAgICByZXR1cm47XG59O1xuZXhwb3J0IGNvbnN0IG5leHRDaGlsZCA9IChkYXRhID0gW10sIGluZGV4LCB0YWdOYW1lLCBjb2RlQmxvY2tQYXJhbWVzKSA9PiB7XG4gICAgbGV0IGkgPSBpbmRleDtcbiAgICB3aGlsZSAoaSA8IGRhdGEubGVuZ3RoKSB7XG4gICAgICAgIGkrKztcbiAgICAgICAgaWYgKHRhZ05hbWUpIHtcbiAgICAgICAgICAgIGNvbnN0IGVsZW1lbnQgPSBkYXRhW2ldO1xuICAgICAgICAgICAgaWYgKGVsZW1lbnQgJiYgZWxlbWVudC52YWx1ZSAmJiBlbGVtZW50LnZhbHVlLnJlcGxhY2UoLyhcXG58XFxzKS9nLCAnJykgIT09ICcnIHx8IGRhdGFbaV0gJiYgZGF0YVtpXS50eXBlID09PSAnZWxlbWVudCcpIHtcbiAgICAgICAgICAgICAgICByZXR1cm4gZWxlbWVudC50YWdOYW1lID09PSB0YWdOYW1lID8gZWxlbWVudCA6IHVuZGVmaW5lZDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIGNvbnN0IGVsZW1lbnQgPSBkYXRhW2ldO1xuICAgICAgICAgICAgaWYgKCFlbGVtZW50IHx8IGVsZW1lbnQudHlwZSA9PT0gJ2VsZW1lbnQnKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGlmIChlbGVtZW50LnR5cGUgPT09ICd0ZXh0JyAmJiBlbGVtZW50LnZhbHVlLnJlcGxhY2UoLyhcXG58XFxzKS9nLCAnJykgIT09ICcnKVxuICAgICAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgICAgIGlmIChlbGVtZW50LnR5cGUgJiYgL14oY29tbWVudHxyYXcpJC9pZy50ZXN0KGVsZW1lbnQudHlwZSkpIHtcbiAgICAgICAgICAgICAgICBpZiAoZWxlbWVudC52YWx1ZSAmJiAhL15yZWh5cGU6Ly50ZXN0KGVsZW1lbnQudmFsdWUucmVwbGFjZSgvXihcXHMrKT88IS0tKC4qPyktLT4vLCAnJDInKSB8fCAnJykpIHtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuO1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICA7XG4gICAgICAgICAgICAgICAgaWYgKGNvZGVCbG9ja1BhcmFtZXMpIHtcbiAgICAgICAgICAgICAgICAgICAgY29uc3QgbmV4dE5vZGUgPSBuZXh0Q2hpbGQoZGF0YSwgaSwgJ3ByZScsIGNvZGVCbG9ja1BhcmFtZXMpO1xuICAgICAgICAgICAgICAgICAgICBpZiAobmV4dE5vZGUpXG4gICAgICAgICAgICAgICAgICAgICAgICByZXR1cm47XG4gICAgICAgICAgICAgICAgICAgIGVsZW1lbnQudmFsdWUgPSAoZWxlbWVudC52YWx1ZSB8fCAnJykucmVwbGFjZSgvXihcXG58XFxzKSsvLCAnJyk7XG4gICAgICAgICAgICAgICAgICAgIHJldHVybiBlbGVtZW50O1xuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZWxlbWVudC52YWx1ZSA9IChlbGVtZW50LnZhbHVlIHx8ICcnKS5yZXBsYWNlKC9eKFxcbnxcXHMpKy8sICcnKTtcbiAgICAgICAgICAgICAgICAgICAgcmV0dXJuIGVsZW1lbnQ7XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfVxuICAgIHJldHVybjtcbn07XG4vKipcbiAqIOiOt+WPluS7o+eggeazqOinhueahOS9jee9rlxuICogQHBhcmFtIGRhdGEg5pWw5o2uXG4gKiBAcGFyYW0gaW5kZXgg5b2T5YmN5pWw5o2u5omA5Zyo55qE5L2N572uXG4gKiBAcmV0dXJucyDov5Tlm54g5b2T5YmN5Y+C5pWw5pWw5o2uIE9iamVjdO+8jGB7fWBcbiAqL1xuZXhwb3J0IGNvbnN0IGdldENvbW1lbnRPYmplY3QgPSAoeyB2YWx1ZSA9ICcnIH0pID0+IHtcbiAgICBjb25zdCBwYXJhbSA9IGdldFVSTFBhcmFtZXRlcnModmFsdWUucmVwbGFjZSgvXjwhLS0oLio/KS0tPi8sICckMScpLnJlcGxhY2UoL15yZWh5cGU6LywgJycpKTtcbiAgICBPYmplY3Qua2V5cyhwYXJhbSkuZm9yRWFjaCgoa2V5TmFtZSkgPT4ge1xuICAgICAgICBpZiAocGFyYW1ba2V5TmFtZV0gPT09ICd0cnVlJykge1xuICAgICAgICAgICAgcGFyYW1ba2V5TmFtZV0gPSB0cnVlO1xuICAgICAgICB9XG4gICAgICAgIGlmIChwYXJhbVtrZXlOYW1lXSA9PT0gJ2ZhbHNlJykge1xuICAgICAgICAgICAgcGFyYW1ba2V5TmFtZV0gPSBmYWxzZTtcbiAgICAgICAgfVxuICAgICAgICBpZiAodHlwZW9mIHBhcmFtW2tleU5hbWVdID09PSAnc3RyaW5nJyAmJiAhL14wLy50ZXN0KHBhcmFtW2tleU5hbWVdKSAmJiAhaXNOYU4oK3BhcmFtW2tleU5hbWVdKSkge1xuICAgICAgICAgICAgcGFyYW1ba2V5TmFtZV0gPSArcGFyYW1ba2V5TmFtZV07XG4gICAgICAgIH1cbiAgICB9KTtcbiAgICByZXR1cm4gcGFyYW07XG59O1xuZXhwb3J0IGNvbnN0IHByb3BlcnRpZXNIYW5kbGUgPSAoZGVmYXVsdEF0dHJzLCBhdHRycywgdHlwZSkgPT4ge1xuICAgIGlmICh0eXBlID09PSAnc3RyaW5nJykge1xuICAgICAgICByZXR1cm4geyAuLi5kZWZhdWx0QXR0cnMsICdkYXRhLWNvbmZpZyc6IEpTT04uc3RyaW5naWZ5KHsgLi4uYXR0cnMsIHJlaHlwOiB0cnVlIH0pIH07XG4gICAgfVxuICAgIGVsc2UgaWYgKHR5cGUgPT09ICdhdHRyJykge1xuICAgICAgICByZXR1cm4geyAuLi5kZWZhdWx0QXR0cnMsIC4uLmF0dHJzIH07XG4gICAgfVxuICAgIHJldHVybiB7IC4uLmRlZmF1bHRBdHRycywgJ2RhdGEtY29uZmlnJzogeyAuLi5hdHRycywgcmVoeXA6IHRydWUgfSB9O1xufTtcbi8vIyBzb3VyY2VNYXBwaW5nVVJMPXV0aWxzLmpzLm1hcCJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-attr/lib/utils.js\n");

/***/ })

};
;