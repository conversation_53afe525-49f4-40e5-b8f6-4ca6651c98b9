"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/css-selector-parser";
exports.ids = ["vendor-chunks/css-selector-parser"];
exports.modules = {

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js":
/*!**********************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/ast.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ast: () => (/* binding */ ast)\n/* harmony export */ });\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nfunction astMethods(type) {\n    return function (generatorName, checkerName) {\n        var _a;\n        return (_a = {},\n            _a[generatorName] = function (props) { return (__assign({ type: type }, props)); },\n            _a[checkerName] = function (entity) {\n                return typeof entity === 'object' && entity !== null && entity.type === type;\n            },\n            _a);\n    };\n}\n/**\n * AST structure generators and matchers.\n * For instance, `ast.selector({rules: [...]})` creates AstSelector and `ast.isSelector(...)` checks if\n * AstSelector was specified.\n *\n * @example\n *\n * // Represents CSS selector: ns|div#user-34.user.user-active[role=\"button\"]:lang(en)::before > *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'div', namespace: ast.namespaceName({name: 'ns'})}),\n *                 ast.id({name: 'user-34'}),\n *                 ast.className({name: 'user'}),\n *                 ast.className({name: 'user-active'}),\n *                 ast.attribute({\n *                     name: 'role',\n *                     operator: '=',\n *                     value: ast.string({value: 'button'})\n *                 }),\n *                 ast.pseudoClass({\n *                     name: 'lang',\n *                     argument: ast.string({value: 'en'})\n *                 }),\n *                 ast.pseudoElement({name: 'before'})\n *             ],\n *             nestedRule: ast.rule({combinator: '>', items: [ast.wildcardTag()]})\n *         })\n *     ]\n * });\n * console.log(ast.isSelector(selector)); // prints true\n * console.log(ast.isRule(selector)); // prints false\n */\nvar ast = __assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign(__assign({}, astMethods('Selector')('selector', 'isSelector')), astMethods('Rule')('rule', 'isRule')), astMethods('TagName')('tagName', 'isTagName')), astMethods('Id')('id', 'isId')), astMethods('ClassName')('className', 'isClassName')), astMethods('WildcardTag')('wildcardTag', 'isWildcardTag')), astMethods('NamespaceName')('namespaceName', 'isNamespaceName')), astMethods('WildcardNamespace')('wildcardNamespace', 'isWildcardNamespace')), astMethods('NoNamespace')('noNamespace', 'isNoNamespace')), astMethods('Attribute')('attribute', 'isAttribute')), astMethods('PseudoClass')('pseudoClass', 'isPseudoClass')), astMethods('PseudoElement')('pseudoElement', 'isPseudoElement')), astMethods('String')('string', 'isString')), astMethods('Formula')('formula', 'isFormula')), astMethods('FormulaOfSelector')('formulaOfSelector', 'isFormulaOfSelector')), astMethods('Substitution')('substitution', 'isSubstitution'));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/index.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ast: () => (/* reexport safe */ _ast_js__WEBPACK_IMPORTED_MODULE_2__.ast),\n/* harmony export */   createParser: () => (/* reexport safe */ _parser_js__WEBPACK_IMPORTED_MODULE_0__.createParser),\n/* harmony export */   render: () => (/* reexport safe */ _render_js__WEBPACK_IMPORTED_MODULE_1__.render)\n/* harmony export */ });\n/* harmony import */ var _parser_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./parser.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js\");\n/* harmony import */ var _render_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./render.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js\");\n/* harmony import */ var _ast_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ast.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/ast.js\");\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L21qcy9pbmRleC5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFBMkM7QUFDTjtBQUNOIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFudGhvXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXG15bm90ZVxccmVuZGVyZXJcXG5vZGVfbW9kdWxlc1xcY3NzLXNlbGVjdG9yLXBhcnNlclxcZGlzdFxcbWpzXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgeyBjcmVhdGVQYXJzZXIgfSBmcm9tICcuL3BhcnNlci5qcyc7XG5leHBvcnQgeyByZW5kZXIgfSBmcm9tICcuL3JlbmRlci5qcyc7XG5leHBvcnQgeyBhc3QgfSBmcm9tICcuL2FzdC5qcyc7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js":
/*!**************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/indexes.js ***!
  \**************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createMulticharIndex: () => (/* binding */ createMulticharIndex),\n/* harmony export */   createRegularIndex: () => (/* binding */ createRegularIndex),\n/* harmony export */   emptyMulticharIndex: () => (/* binding */ emptyMulticharIndex),\n/* harmony export */   emptyRegularIndex: () => (/* binding */ emptyRegularIndex)\n/* harmony export */ });\nvar emptyMulticharIndex = {};\nvar emptyRegularIndex = {};\nfunction extendIndex(item, index) {\n    var currentIndex = index;\n    for (var pos = 0; pos < item.length; pos++) {\n        var isLast = pos === item.length - 1;\n        var char = item.charAt(pos);\n        var charIndex = currentIndex[char] || (currentIndex[char] = { chars: {} });\n        if (isLast) {\n            charIndex.self = item;\n        }\n        currentIndex = charIndex.chars;\n    }\n}\nfunction createMulticharIndex(items) {\n    if (items.length === 0) {\n        return emptyMulticharIndex;\n    }\n    var index = {};\n    for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n        var item = items_1[_i];\n        extendIndex(item, index);\n    }\n    return index;\n}\nfunction createRegularIndex(items) {\n    if (items.length === 0) {\n        return emptyRegularIndex;\n    }\n    var result = {};\n    for (var _i = 0, items_2 = items; _i < items_2.length; _i++) {\n        var item = items_2[_i];\n        result[item] = true;\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/parser.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createParser: () => (/* binding */ createParser)\n/* harmony export */ });\n/* harmony import */ var _indexes_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./indexes.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/indexes.js\");\n/* harmony import */ var _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./pseudo-signatures.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js\");\n/* harmony import */ var _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./syntax-definitions.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js\");\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\");\n\n\n\n\nvar errorPrefix = \"css-selector-parser parse error: \";\n/**\n * Creates a parse function to be used later to parse CSS selectors.\n */\nfunction createParser(options) {\n    if (options === void 0) { options = {}; }\n    var _a = options.syntax, syntax = _a === void 0 ? 'latest' : _a, substitutes = options.substitutes, _b = options.strict, strict = _b === void 0 ? true : _b, modules = options.modules;\n    var syntaxDefinition = typeof syntax === 'object' ? syntax : _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssSyntaxDefinitions[syntax];\n    if (syntaxDefinition.baseSyntax) {\n        syntaxDefinition = (0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.extendSyntaxDefinition)(_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssSyntaxDefinitions[syntaxDefinition.baseSyntax], syntaxDefinition);\n    }\n    // Apply modules from syntax definition\n    if (syntaxDefinition.modules && syntaxDefinition.modules.length > 0) {\n        for (var _i = 0, _c = syntaxDefinition.modules; _i < _c.length; _i++) {\n            var module_1 = _c[_i];\n            var moduleSyntax = _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssModules[module_1];\n            if (moduleSyntax) {\n                syntaxDefinition = (0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.extendSyntaxDefinition)(moduleSyntax, syntaxDefinition);\n            }\n        }\n    }\n    // Apply additional modules if specified from options\n    if (modules && modules.length > 0) {\n        for (var _d = 0, modules_1 = modules; _d < modules_1.length; _d++) {\n            var module_2 = modules_1[_d];\n            var moduleSyntax = _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.cssModules[module_2];\n            if (moduleSyntax) {\n                syntaxDefinition = (0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.extendSyntaxDefinition)(moduleSyntax, syntaxDefinition);\n            }\n        }\n    }\n    var _e = syntaxDefinition.tag\n        ? [true, Boolean((0,_syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.getXmlOptions)(syntaxDefinition.tag).wildcard)]\n        : [false, false], tagNameEnabled = _e[0], tagNameWildcardEnabled = _e[1];\n    var idEnabled = Boolean(syntaxDefinition.ids);\n    var classNamesEnabled = Boolean(syntaxDefinition.classNames);\n    var namespaceEnabled = Boolean(syntaxDefinition.namespace);\n    var namespaceWildcardEnabled = syntaxDefinition.namespace &&\n        (syntaxDefinition.namespace === true || syntaxDefinition.namespace.wildcard === true);\n    if (namespaceEnabled && !tagNameEnabled) {\n        throw new Error(\"\".concat(errorPrefix, \"Namespaces cannot be enabled while tags are disabled.\"));\n    }\n    var substitutesEnabled = Boolean(substitutes);\n    var combinatorsIndex = syntaxDefinition.combinators\n        ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createMulticharIndex)(syntaxDefinition.combinators)\n        : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex;\n    var _f = syntaxDefinition.attributes\n        ? [\n            true,\n            syntaxDefinition.attributes.operators\n                ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createMulticharIndex)(syntaxDefinition.attributes.operators)\n                : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex,\n            syntaxDefinition.attributes.caseSensitivityModifiers\n                ? (0,_indexes_js__WEBPACK_IMPORTED_MODULE_0__.createRegularIndex)(syntaxDefinition.attributes.caseSensitivityModifiers)\n                : _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyRegularIndex,\n            syntaxDefinition.attributes.unknownCaseSensitivityModifiers === 'accept'\n        ]\n        : [false, _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyMulticharIndex, _indexes_js__WEBPACK_IMPORTED_MODULE_0__.emptyRegularIndex, false], attributesEnabled = _f[0], attributesOperatorsIndex = _f[1], attributesCaseSensitivityModifiers = _f[2], attributesAcceptUnknownCaseSensitivityModifiers = _f[3];\n    var attributesCaseSensitivityModifiersEnabled = attributesAcceptUnknownCaseSensitivityModifiers || Object.keys(attributesCaseSensitivityModifiers).length > 0;\n    var _g = syntaxDefinition.pseudoClasses\n        ? [\n            true,\n            syntaxDefinition.pseudoClasses.definitions\n                ? (0,_pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.calculatePseudoSignatures)(syntaxDefinition.pseudoClasses.definitions)\n                : _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures,\n            syntaxDefinition.pseudoClasses.unknown === 'accept'\n        ]\n        : [false, _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures, false], pseudoClassesEnabled = _g[0], pseudoClassesDefinitions = _g[1], pseudoClassesAcceptUnknown = _g[2];\n    var _h = syntaxDefinition.pseudoElements\n        ? [\n            true,\n            syntaxDefinition.pseudoElements.notation === 'singleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            !syntaxDefinition.pseudoElements.notation ||\n                syntaxDefinition.pseudoElements.notation === 'doubleColon' ||\n                syntaxDefinition.pseudoElements.notation === 'both',\n            syntaxDefinition.pseudoElements.definitions\n                ? (0,_pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.calculatePseudoSignatures)(Array.isArray(syntaxDefinition.pseudoElements.definitions)\n                    ? { NoArgument: syntaxDefinition.pseudoElements.definitions }\n                    : syntaxDefinition.pseudoElements.definitions)\n                : _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures,\n            syntaxDefinition.pseudoElements.unknown === 'accept'\n        ]\n        : [false, false, false, _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.emptyPseudoSignatures, false], pseudoElementsEnabled = _h[0], pseudoElementsSingleColonNotationEnabled = _h[1], pseudoElementsDoubleColonNotationEnabled = _h[2], pseudoElementsDefinitions = _h[3], pseudoElementsAcceptUnknown = _h[4];\n    var str = '';\n    var l = str.length;\n    var pos = 0;\n    var chr = '';\n    var is = function (comparison) { return chr === comparison; };\n    var isTagStart = function () { return is('*') || (0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr); };\n    var rewind = function (newPos) {\n        pos = newPos;\n        chr = str.charAt(pos);\n    };\n    var next = function () {\n        pos++;\n        chr = str.charAt(pos);\n    };\n    var readAndNext = function () {\n        var current = chr;\n        pos++;\n        chr = str.charAt(pos);\n        return current;\n    };\n    /** @throws ParserError */\n    function fail(errorMessage) {\n        var position = Math.min(l - 1, pos);\n        var error = new Error(\"\".concat(errorPrefix).concat(errorMessage, \" Pos: \").concat(position, \".\"));\n        error.position = position;\n        error.name = 'ParserError';\n        throw error;\n    }\n    function assert(condition, errorMessage) {\n        if (!condition) {\n            return fail(errorMessage);\n        }\n    }\n    var assertNonEof = function () {\n        assert(pos < l, 'Unexpected end of input.');\n    };\n    var isEof = function () { return pos >= l; };\n    var pass = function (character) {\n        assert(pos < l, \"Expected \\\"\".concat(character, \"\\\" but end of input reached.\"));\n        assert(chr === character, \"Expected \\\"\".concat(character, \"\\\" but \\\"\").concat(chr, \"\\\" found.\"));\n        pos++;\n        chr = str.charAt(pos);\n    };\n    function matchMulticharIndex(index) {\n        var match = matchMulticharIndexPos(index, pos);\n        if (match) {\n            pos += match.length;\n            chr = str.charAt(pos);\n            return match;\n        }\n    }\n    function matchMulticharIndexPos(index, subPos) {\n        var char = str.charAt(subPos);\n        var charIndex = index[char];\n        if (charIndex) {\n            var subMatch = matchMulticharIndexPos(charIndex.chars, subPos + 1);\n            if (subMatch) {\n                return subMatch;\n            }\n            if (charIndex.self) {\n                return charIndex.self;\n            }\n        }\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#hex-digit-diagram\n     */\n    function parseHex() {\n        var hex = readAndNext();\n        var count = 1;\n        while ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr) && count < _utils_js__WEBPACK_IMPORTED_MODULE_3__.maxHexLength) {\n            hex += readAndNext();\n            count++;\n        }\n        skipSingleWhitespace();\n        return String.fromCharCode(parseInt(hex, 16));\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#string-token-diagram\n     */\n    function parseString(quote) {\n        var result = '';\n        pass(quote);\n        while (pos < l) {\n            if (is(quote)) {\n                next();\n                return result;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (is(quote)) {\n                    result += quote;\n                    next();\n                }\n                else if (chr === '\\n' || chr === '\\f') {\n                    next();\n                }\n                else if (chr === '\\r') {\n                    next();\n                    if (is('\\n')) {\n                        next();\n                    }\n                }\n                else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += chr;\n                    next();\n                }\n            }\n            else {\n                result += chr;\n                next();\n            }\n        }\n        return result;\n    }\n    /**\n     * @see https://www.w3.org/TR/css-syntax/#ident-token-diagram\n     */\n    function parseIdentifier() {\n        if (!(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            return null;\n        }\n        var result = '';\n        while (is('-')) {\n            result += chr;\n            next();\n        }\n        if (result === '-' && !(0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdent)(chr) && !is('\\\\')) {\n            fail('Identifiers cannot consist of a single hyphen.');\n        }\n        if (strict && result.length >= 2) {\n            // Checking this only for strict mode since browsers work fine with these identifiers.\n            fail('Identifiers cannot start with two hyphens with strict mode on.');\n        }\n        if (_utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]) {\n            fail('Identifiers cannot start with hyphens followed by digits.');\n        }\n        while (pos < l) {\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdent)(chr)) {\n                result += readAndNext();\n            }\n            else if (is('\\\\')) {\n                next();\n                assertNonEof();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                break;\n            }\n        }\n        return result;\n    }\n    function parsePseudoClassString() {\n        var result = '';\n        while (pos < l) {\n            if (is(')')) {\n                break;\n            }\n            else if (is('\\\\')) {\n                next();\n                if (isEof() && !strict) {\n                    return (result + '\\\\').trim();\n                }\n                assertNonEof();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                    result += parseHex();\n                }\n                else {\n                    result += readAndNext();\n                }\n            }\n            else {\n                result += readAndNext();\n            }\n        }\n        return result.trim();\n    }\n    function skipSingleWhitespace() {\n        if (chr === ' ' || chr === '\\t' || chr === '\\f' || chr === '\\n') {\n            next();\n            return;\n        }\n        if (chr === '\\r') {\n            next();\n        }\n        if (chr === '\\n') {\n            next();\n        }\n    }\n    function skipWhitespace() {\n        while (_utils_js__WEBPACK_IMPORTED_MODULE_3__.whitespaceChars[chr]) {\n            next();\n        }\n    }\n    function parseSelector(relative) {\n        if (relative === void 0) { relative = false; }\n        skipWhitespace();\n        var rules = [parseRule(relative)];\n        while (is(',')) {\n            next();\n            skipWhitespace();\n            rules.push(parseRule(relative));\n        }\n        return {\n            type: 'Selector',\n            rules: rules\n        };\n    }\n    function parseAttribute() {\n        pass('[');\n        skipWhitespace();\n        var attr;\n        if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var name_1 = parseIdentifier();\n            assert(name_1, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_1,\n                namespace: { type: 'NoNamespace' }\n            };\n        }\n        else if (is('*')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            next();\n            pass('|');\n            var name_2 = parseIdentifier();\n            assert(name_2, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: name_2,\n                namespace: { type: 'WildcardNamespace' }\n            };\n        }\n        else {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected attribute name.');\n            attr = {\n                type: 'Attribute',\n                name: identifier\n            };\n            if (is('|')) {\n                var savedPos = pos;\n                next();\n                if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n                    assert(namespaceEnabled, 'Namespaces are not enabled.');\n                    var name_3 = parseIdentifier();\n                    assert(name_3, 'Expected attribute name.');\n                    attr = {\n                        type: 'Attribute',\n                        name: name_3,\n                        namespace: { type: 'NamespaceName', name: identifier }\n                    };\n                }\n                else {\n                    rewind(savedPos);\n                }\n            }\n        }\n        assert(attr.name, 'Expected attribute name.');\n        skipWhitespace();\n        if (isEof() && !strict) {\n            return attr;\n        }\n        if (is(']')) {\n            next();\n        }\n        else {\n            attr.operator = matchMulticharIndex(attributesOperatorsIndex);\n            assert(attr.operator, 'Expected a valid attribute selector operator.');\n            skipWhitespace();\n            assertNonEof();\n            if (_utils_js__WEBPACK_IMPORTED_MODULE_3__.quoteChars[chr]) {\n                attr.value = {\n                    type: 'String',\n                    value: parseString(chr)\n                };\n            }\n            else if (substitutesEnabled && is('$')) {\n                next();\n                var name_4 = parseIdentifier();\n                assert(name_4, 'Expected substitute name.');\n                attr.value = {\n                    type: 'Substitution',\n                    name: name_4\n                };\n            }\n            else {\n                var value = parseIdentifier();\n                assert(value, 'Expected attribute value.');\n                attr.value = {\n                    type: 'String',\n                    value: value\n                };\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return attr;\n            }\n            if (!is(']')) {\n                var caseSensitivityModifier = parseIdentifier();\n                assert(caseSensitivityModifier, 'Expected end of attribute selector.');\n                attr.caseSensitivityModifier = caseSensitivityModifier;\n                assert(attributesCaseSensitivityModifiersEnabled, 'Attribute case sensitivity modifiers are not enabled.');\n                assert(attributesAcceptUnknownCaseSensitivityModifiers ||\n                    attributesCaseSensitivityModifiers[attr.caseSensitivityModifier], 'Unknown attribute case sensitivity modifier.');\n                skipWhitespace();\n                if (isEof() && !strict) {\n                    return attr;\n                }\n            }\n            pass(']');\n        }\n        return attr;\n    }\n    function parseNumber() {\n        var result = '';\n        while (_utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]) {\n            result += readAndNext();\n        }\n        assert(result !== '', 'Formula parse error.');\n        return parseInt(result);\n    }\n    var isNumberStart = function () { return is('-') || is('+') || _utils_js__WEBPACK_IMPORTED_MODULE_3__.digitsChars[chr]; };\n    function parseFormula() {\n        if (is('e') || is('o')) {\n            var ident = parseIdentifier();\n            if (ident === 'even') {\n                skipWhitespace();\n                return [2, 0];\n            }\n            if (ident === 'odd') {\n                skipWhitespace();\n                return [2, 1];\n            }\n        }\n        var firstNumber = null;\n        var firstNumberMultiplier = 1;\n        if (is('-')) {\n            next();\n            firstNumberMultiplier = -1;\n        }\n        if (isNumberStart()) {\n            if (is('+')) {\n                next();\n            }\n            firstNumber = parseNumber();\n            if (!is('\\\\') && !is('n')) {\n                return [0, firstNumber * firstNumberMultiplier];\n            }\n        }\n        if (firstNumber === null) {\n            firstNumber = 1;\n        }\n        firstNumber *= firstNumberMultiplier;\n        var identifier;\n        if (is('\\\\')) {\n            next();\n            if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isHex)(chr)) {\n                identifier = parseHex();\n            }\n            else {\n                identifier = readAndNext();\n            }\n        }\n        else {\n            identifier = readAndNext();\n        }\n        assert(identifier === 'n', 'Formula parse error: expected \"n\".');\n        skipWhitespace();\n        if (is('+') || is('-')) {\n            var sign = is('+') ? 1 : -1;\n            next();\n            skipWhitespace();\n            return [firstNumber, sign * parseNumber()];\n        }\n        else {\n            return [firstNumber, 0];\n        }\n    }\n    function parsePseudoArgument(pseudoName, type, signature) {\n        var argument;\n        if (is('(')) {\n            next();\n            skipWhitespace();\n            if (substitutesEnabled && is('$')) {\n                next();\n                var name_5 = parseIdentifier();\n                assert(name_5, 'Expected substitute name.');\n                argument = {\n                    type: 'Substitution',\n                    name: name_5\n                };\n            }\n            else if (signature.type === 'String') {\n                argument = {\n                    type: 'String',\n                    value: parsePseudoClassString()\n                };\n                assert(argument.value, \"Expected \".concat(type, \" argument value.\"));\n            }\n            else if (signature.type === 'Selector') {\n                argument = parseSelector(true);\n            }\n            else if (signature.type === 'Formula') {\n                var _a = parseFormula(), a = _a[0], b = _a[1];\n                argument = {\n                    type: 'Formula',\n                    a: a,\n                    b: b\n                };\n                if (signature.ofSelector) {\n                    skipWhitespace();\n                    if (is('o') || is('\\\\')) {\n                        var ident = parseIdentifier();\n                        assert(ident === 'of', 'Formula of selector parse error.');\n                        skipWhitespace();\n                        argument = {\n                            type: 'FormulaOfSelector',\n                            a: a,\n                            b: b,\n                            selector: parseRule()\n                        };\n                    }\n                }\n            }\n            else {\n                return fail(\"Invalid \".concat(type, \" signature.\"));\n            }\n            skipWhitespace();\n            if (isEof() && !strict) {\n                return argument;\n            }\n            pass(')');\n        }\n        else {\n            assert(signature.optional, \"Argument is required for \".concat(type, \" \\\"\").concat(pseudoName, \"\\\".\"));\n        }\n        return argument;\n    }\n    function parseTagName() {\n        if (is('*')) {\n            assert(tagNameWildcardEnabled, 'Wildcard tag name is not enabled.');\n            next();\n            return { type: 'WildcardTag' };\n        }\n        else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            assert(tagNameEnabled, 'Tag names are not enabled.');\n            var name_6 = parseIdentifier();\n            assert(name_6, 'Expected tag name.');\n            return {\n                type: 'TagName',\n                name: name_6\n            };\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseTagNameWithNamespace() {\n        if (is('*')) {\n            var savedPos = pos;\n            next();\n            if (!is('|')) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return parseTagName();\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            assert(namespaceWildcardEnabled, 'Wildcard namespace is not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'WildcardNamespace' };\n            return tagName;\n        }\n        else if (is('|')) {\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            next();\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NoNamespace' };\n            return tagName;\n        }\n        else if ((0,_utils_js__WEBPACK_IMPORTED_MODULE_3__.isIdentStart)(chr)) {\n            var identifier = parseIdentifier();\n            assert(identifier, 'Expected tag name.');\n            if (!is('|')) {\n                assert(tagNameEnabled, 'Tag names are not enabled.');\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            var savedPos = pos;\n            next();\n            if (!isTagStart()) {\n                rewind(savedPos);\n                return {\n                    type: 'TagName',\n                    name: identifier\n                };\n            }\n            assert(namespaceEnabled, 'Namespaces are not enabled.');\n            var tagName = parseTagName();\n            tagName.namespace = { type: 'NamespaceName', name: identifier };\n            return tagName;\n        }\n        else {\n            return fail('Expected tag name.');\n        }\n    }\n    function parseRule(relative) {\n        var _a, _b;\n        if (relative === void 0) { relative = false; }\n        var rule = { type: 'Rule', items: [] };\n        if (relative) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            if (combinator) {\n                rule.combinator = combinator;\n                skipWhitespace();\n            }\n        }\n        while (pos < l) {\n            if (isTagStart()) {\n                assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                rule.items.push(parseTagNameWithNamespace());\n            }\n            else if (is('|')) {\n                var savedPos = pos;\n                next();\n                if (isTagStart()) {\n                    assert(rule.items.length === 0, 'Unexpected tag/namespace start.');\n                    rewind(savedPos);\n                    rule.items.push(parseTagNameWithNamespace());\n                }\n                else {\n                    rewind(savedPos);\n                    break;\n                }\n            }\n            else if (is('.')) {\n                assert(classNamesEnabled, 'Class names are not enabled.');\n                next();\n                var className = parseIdentifier();\n                assert(className, 'Expected class name.');\n                rule.items.push({ type: 'ClassName', name: className });\n            }\n            else if (is('#')) {\n                assert(idEnabled, 'IDs are not enabled.');\n                next();\n                var idName = parseIdentifier();\n                assert(idName, 'Expected ID name.');\n                rule.items.push({ type: 'Id', name: idName });\n            }\n            else if (is('[')) {\n                assert(attributesEnabled, 'Attributes are not enabled.');\n                rule.items.push(parseAttribute());\n            }\n            else if (is(':')) {\n                var isDoubleColon = false;\n                var isPseudoElement = false;\n                next();\n                if (is(':')) {\n                    assert(pseudoElementsEnabled, 'Pseudo elements are not enabled.');\n                    assert(pseudoElementsDoubleColonNotationEnabled, 'Pseudo elements double colon notation is not enabled.');\n                    isDoubleColon = true;\n                    next();\n                }\n                var pseudoName = parseIdentifier();\n                assert(isDoubleColon || pseudoName, 'Expected pseudo-class name.');\n                assert(!isDoubleColon || pseudoName, 'Expected pseudo-element name.');\n                assert(pseudoName, 'Expected pseudo-class name.');\n                if (!isDoubleColon ||\n                    pseudoElementsAcceptUnknown ||\n                    Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName)) {\n                    // All good\n                }\n                else {\n                    // Generate a helpful error message with location information\n                    var locations = _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.pseudoLocationIndex.pseudoElements[pseudoName];\n                    var errorMessage = \"Unknown pseudo-element \\\"\".concat(pseudoName, \"\\\"\");\n                    if (locations && locations.length > 0) {\n                        errorMessage += \". It is defined in: \".concat(locations.join(', '));\n                    }\n                    fail(errorMessage + '.');\n                }\n                isPseudoElement =\n                    pseudoElementsEnabled &&\n                        (isDoubleColon ||\n                            (!isDoubleColon &&\n                                pseudoElementsSingleColonNotationEnabled &&\n                                Object.prototype.hasOwnProperty.call(pseudoElementsDefinitions, pseudoName)));\n                if (isPseudoElement) {\n                    var signature = (_a = pseudoElementsDefinitions[pseudoName]) !== null && _a !== void 0 ? _a : (pseudoElementsAcceptUnknown && _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.defaultPseudoSignature);\n                    var pseudoElement = {\n                        type: 'PseudoElement',\n                        name: pseudoName\n                    };\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-element', signature);\n                    if (argument) {\n                        assert(argument.type !== 'Formula' && argument.type !== 'FormulaOfSelector', 'Pseudo-elements cannot have formula argument.');\n                        pseudoElement.argument = argument;\n                    }\n                    rule.items.push(pseudoElement);\n                }\n                else {\n                    assert(pseudoClassesEnabled, 'Pseudo-classes are not enabled.');\n                    var signature = (_b = pseudoClassesDefinitions[pseudoName]) !== null && _b !== void 0 ? _b : (pseudoClassesAcceptUnknown && _pseudo_signatures_js__WEBPACK_IMPORTED_MODULE_1__.defaultPseudoSignature);\n                    if (signature) {\n                        // All good\n                    }\n                    else {\n                        // Generate a helpful error message with location information\n                        var locations = _syntax_definitions_js__WEBPACK_IMPORTED_MODULE_2__.pseudoLocationIndex.pseudoClasses[pseudoName];\n                        var errorMessage = \"Unknown pseudo-class: \\\"\".concat(pseudoName, \"\\\"\");\n                        if (locations && locations.length > 0) {\n                            errorMessage += \". It is defined in: \".concat(locations.join(', '));\n                        }\n                        fail(errorMessage + '.');\n                    }\n                    var argument = parsePseudoArgument(pseudoName, 'pseudo-class', signature);\n                    var pseudoClass = {\n                        type: 'PseudoClass',\n                        name: pseudoName\n                    };\n                    if (argument) {\n                        pseudoClass.argument = argument;\n                    }\n                    rule.items.push(pseudoClass);\n                }\n            }\n            else {\n                break;\n            }\n        }\n        if (rule.items.length === 0) {\n            if (isEof()) {\n                return fail('Expected rule but end of input reached.');\n            }\n            else {\n                return fail(\"Expected rule but \\\"\".concat(chr, \"\\\" found.\"));\n            }\n        }\n        skipWhitespace();\n        if (!isEof() && !is(',') && !is(')')) {\n            var combinator = matchMulticharIndex(combinatorsIndex);\n            skipWhitespace();\n            rule.nestedRule = parseRule();\n            rule.nestedRule.combinator = combinator;\n        }\n        return rule;\n    }\n    return function (input) {\n        // noinspection SuspiciousTypeOfGuard\n        if (typeof input !== 'string') {\n            throw new Error(\"\".concat(errorPrefix, \"Expected string input.\"));\n        }\n        str = input;\n        l = str.length;\n        pos = 0;\n        chr = str.charAt(0);\n        return parseSelector();\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/parser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js":
/*!************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculatePseudoSignatures: () => (/* binding */ calculatePseudoSignatures),\n/* harmony export */   defaultPseudoSignature: () => (/* binding */ defaultPseudoSignature),\n/* harmony export */   emptyPseudoSignatures: () => (/* binding */ emptyPseudoSignatures),\n/* harmony export */   inverseCategories: () => (/* binding */ inverseCategories)\n/* harmony export */ });\nvar emptyPseudoSignatures = {};\nvar defaultPseudoSignature = {\n    type: 'String',\n    optional: true\n};\nfunction calculatePseudoSignature(types) {\n    var result = {\n        type: 'NoArgument',\n        optional: false\n    };\n    function setResultType(type) {\n        if (result.type && result.type !== type && result.type !== 'NoArgument') {\n            throw new Error(\"Conflicting pseudo-class argument type: \\\"\".concat(result.type, \"\\\" vs \\\"\").concat(type, \"\\\".\"));\n        }\n        result.type = type;\n    }\n    for (var _i = 0, types_1 = types; _i < types_1.length; _i++) {\n        var type = types_1[_i];\n        if (type === 'NoArgument') {\n            result.optional = true;\n        }\n        if (type === 'Formula') {\n            setResultType('Formula');\n        }\n        if (type === 'FormulaOfSelector') {\n            setResultType('Formula');\n            result.ofSelector = true;\n        }\n        if (type === 'String') {\n            setResultType('String');\n        }\n        if (type === 'Selector') {\n            setResultType('Selector');\n        }\n    }\n    return result;\n}\nfunction inverseCategories(obj) {\n    var result = {};\n    for (var _i = 0, _a = Object.keys(obj); _i < _a.length; _i++) {\n        var category = _a[_i];\n        var items = obj[category];\n        if (items) {\n            for (var _b = 0, _c = items; _b < _c.length; _b++) {\n                var item = _c[_b];\n                (result[item] || (result[item] = [])).push(category);\n            }\n        }\n    }\n    return result;\n}\nfunction calculatePseudoSignatures(definitions) {\n    var pseudoClassesToArgumentTypes = inverseCategories(definitions);\n    var result = {};\n    for (var _i = 0, _a = Object.keys(pseudoClassesToArgumentTypes); _i < _a.length; _i++) {\n        var pseudoClass = _a[_i];\n        var argumentTypes = pseudoClassesToArgumentTypes[pseudoClass];\n        if (argumentTypes) {\n            result[pseudoClass] = calculatePseudoSignature(argumentTypes);\n        }\n    }\n    return result;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/pseudo-signatures.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js":
/*!*************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/render.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   render: () => (/* binding */ render)\n/* harmony export */ });\n/* harmony import */ var _utils_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./utils.js */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\");\n\nvar errorPrefix = \"css-selector-parser render error: \";\nfunction renderNamespace(namespace) {\n    if (namespace.type === 'WildcardNamespace') {\n        return '*|';\n    }\n    else if (namespace.type === 'NamespaceName') {\n        return \"\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(namespace.name), \"|\");\n    }\n    else if (namespace.type === 'NoNamespace') {\n        return '|';\n    }\n    throw new Error(\"\".concat(errorPrefix, \"Unknown namespace type: \").concat(namespace.type, \".\"));\n}\nfunction renderSubstitution(sub) {\n    return \"$\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(sub.name));\n}\nfunction renderFormula(a, b) {\n    if (a) {\n        var result = \"\".concat(a === 1 ? '' : a === -1 ? '-' : a, \"n\");\n        if (b) {\n            result += \"\".concat(b > 0 ? '+' : '').concat(b);\n        }\n        return result;\n    }\n    else {\n        return String(b);\n    }\n}\n/**\n * Renders CSS Selector AST back to a string.\n *\n * @example\n *\n * import {ast, render} from 'css-selector-parser';\n *\n * const selector = ast.selector({\n *     rules: [\n *         ast.rule({\n *             items: [\n *                 ast.tagName({name: 'a'}),\n *                 ast.id({name: 'user-23'}),\n *                 ast.className({name: 'user'}),\n *                 ast.pseudoClass({name: 'visited'}),\n *                 ast.pseudoElement({name: 'before'})\n *             ]\n *         })\n *     ]\n * });\n *\n * console.log(render(selector)); // a#user-23.user:visited::before\n */\nfunction render(entity) {\n    if (entity.type === 'Selector') {\n        return entity.rules.map(render).join(', ');\n    }\n    if (entity.type === 'Rule') {\n        var result = '';\n        var items = entity.items, combinator = entity.combinator, nestedRule = entity.nestedRule;\n        if (combinator) {\n            result += \"\".concat(combinator, \" \");\n        }\n        for (var _i = 0, items_1 = items; _i < items_1.length; _i++) {\n            var item = items_1[_i];\n            result += render(item);\n        }\n        if (nestedRule) {\n            result += \" \".concat(render(nestedRule));\n        }\n        return result;\n    }\n    else if (entity.type === 'TagName' || entity.type === 'WildcardTag') {\n        var result = '';\n        var namespace = entity.namespace;\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        if (entity.type === 'TagName') {\n            result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name);\n        }\n        else if (entity.type === 'WildcardTag') {\n            result += '*';\n        }\n        return result;\n    }\n    else if (entity.type === 'Id') {\n        return \"#\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'ClassName') {\n        return \".\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    else if (entity.type === 'Attribute') {\n        var name_1 = entity.name, namespace = entity.namespace, operator = entity.operator, value = entity.value, caseSensitivityModifier = entity.caseSensitivityModifier;\n        var result = '[';\n        if (namespace) {\n            result += renderNamespace(namespace);\n        }\n        result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_1);\n        if (operator && value) {\n            result += operator;\n            if (value.type === 'String') {\n                result += (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeString)(value.value);\n            }\n            else if (value.type === 'Substitution') {\n                result += renderSubstitution(value);\n            }\n            else {\n                throw new Error(\"Unknown attribute value type: \".concat(value.type, \".\"));\n            }\n            if (caseSensitivityModifier) {\n                result += \" \".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(caseSensitivityModifier));\n            }\n        }\n        result += ']';\n        return result;\n    }\n    else if (entity.type === 'PseudoClass') {\n        var name_2 = entity.name, argument = entity.argument;\n        var result = \":\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_2));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'PseudoElement') {\n        var name_3 = entity.name, argument = entity.argument;\n        var result = \"::\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(name_3));\n        if (argument) {\n            result += \"(\".concat(argument.type === 'String' ? (0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(argument.value) : render(argument), \")\");\n        }\n        return result;\n    }\n    else if (entity.type === 'String') {\n        throw new Error(\"\".concat(errorPrefix, \"String cannot be rendered outside of context.\"));\n    }\n    else if (entity.type === 'Formula') {\n        return renderFormula(entity.a, entity.b);\n    }\n    else if (entity.type === 'FormulaOfSelector') {\n        return renderFormula(entity.a, entity.b) + ' of ' + render(entity.selector);\n    }\n    else if (entity.type === 'Substitution') {\n        return \"$\".concat((0,_utils_js__WEBPACK_IMPORTED_MODULE_0__.escapeIdentifier)(entity.name));\n    }\n    throw new Error(\"Unknown type specified to render method: \".concat(entity.type, \".\"));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/render.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js":
/*!*************************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   buildPseudoLocationIndex: () => (/* binding */ buildPseudoLocationIndex),\n/* harmony export */   cssModules: () => (/* binding */ cssModules),\n/* harmony export */   cssSyntaxDefinitions: () => (/* binding */ cssSyntaxDefinitions),\n/* harmony export */   extendSyntaxDefinition: () => (/* binding */ extendSyntaxDefinition),\n/* harmony export */   getXmlOptions: () => (/* binding */ getXmlOptions),\n/* harmony export */   pseudoLocationIndex: () => (/* binding */ pseudoLocationIndex)\n/* harmony export */ });\nvar __assign = (undefined && undefined.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nvar emptyXmlOptions = {};\nvar defaultXmlOptions = { wildcard: true };\nfunction getXmlOptions(param) {\n    if (param) {\n        if (typeof param === 'boolean') {\n            return defaultXmlOptions;\n        }\n        else {\n            return param;\n        }\n    }\n    else {\n        return emptyXmlOptions;\n    }\n}\nfunction withMigration(migration, merge) {\n    return function (base, extension) { return merge(migration(base), migration(extension)); };\n}\nfunction withNoNegative(merge) {\n    return function (base, extension) {\n        var result = merge(base, extension);\n        if (!result) {\n            throw new Error(\"Syntax definition cannot be null or undefined.\");\n        }\n        return result;\n    };\n}\nfunction withPositive(positive, merge) {\n    return function (base, extension) {\n        if (extension === true) {\n            return positive;\n        }\n        return merge(base === true ? positive : base, extension);\n    };\n}\nfunction mergeSection(values) {\n    return function (base, extension) {\n        if (!extension || !base) {\n            return extension;\n        }\n        if (typeof extension !== 'object' || extension === null) {\n            throw new Error(\"Unexpected syntax definition extension type: \".concat(extension, \".\"));\n        }\n        var result = __assign({}, base);\n        for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n            var _b = _a[_i], key = _b[0], value = _b[1];\n            if (key === 'latest') {\n                continue;\n            }\n            var mergeSchema = values[key];\n            result[key] = mergeSchema(base[key], value);\n        }\n        return result;\n    };\n}\nfunction replaceValueIfSpecified(base, extension) {\n    if (extension !== undefined) {\n        return extension;\n    }\n    return base;\n}\nfunction concatArray(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    return base.concat(extension);\n}\nfunction mergeDefinitions(base, extension) {\n    if (!extension) {\n        return base;\n    }\n    if (!base) {\n        return extension;\n    }\n    var result = __assign({}, base);\n    for (var _i = 0, _a = Object.entries(extension); _i < _a.length; _i++) {\n        var _b = _a[_i], key = _b[0], value = _b[1];\n        if (!value) {\n            delete result[key];\n            continue;\n        }\n        var baseValue = base[key];\n        if (!baseValue) {\n            result[key] = value;\n            continue;\n        }\n        result[key] = baseValue.concat(value);\n    }\n    return result;\n}\nvar extendSyntaxDefinition = withNoNegative(mergeSection({\n    baseSyntax: replaceValueIfSpecified,\n    modules: concatArray,\n    tag: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    ids: replaceValueIfSpecified,\n    classNames: replaceValueIfSpecified,\n    namespace: withPositive(defaultXmlOptions, mergeSection({\n        wildcard: replaceValueIfSpecified\n    })),\n    combinators: concatArray,\n    attributes: mergeSection({\n        operators: concatArray,\n        caseSensitivityModifiers: concatArray,\n        unknownCaseSensitivityModifiers: replaceValueIfSpecified\n    }),\n    pseudoClasses: mergeSection({\n        unknown: replaceValueIfSpecified,\n        definitions: mergeDefinitions\n    }),\n    pseudoElements: mergeSection({\n        unknown: replaceValueIfSpecified,\n        notation: replaceValueIfSpecified,\n        definitions: withMigration(function (definitions) { return (Array.isArray(definitions) ? { NoArgument: definitions } : definitions); }, mergeDefinitions)\n    })\n}));\nvar css1SyntaxDefinition = {\n    tag: {},\n    ids: true,\n    classNames: true,\n    combinators: [],\n    pseudoElements: {\n        unknown: 'reject',\n        notation: 'singleColon',\n        definitions: ['first-letter', 'first-line']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['link', 'visited', 'active']\n        }\n    }\n};\nvar css2SyntaxDefinition = extendSyntaxDefinition(css1SyntaxDefinition, {\n    tag: { wildcard: true },\n    combinators: ['>', '+'],\n    attributes: {\n        unknownCaseSensitivityModifiers: 'reject',\n        operators: ['=', '~=', '|=']\n    },\n    pseudoElements: {\n        definitions: ['before', 'after']\n    },\n    pseudoClasses: {\n        unknown: 'reject',\n        definitions: {\n            NoArgument: ['hover', 'focus', 'first-child'],\n            String: ['lang']\n        }\n    }\n});\nvar selectors3SyntaxDefinition = extendSyntaxDefinition(css2SyntaxDefinition, {\n    namespace: {\n        wildcard: true\n    },\n    combinators: ['~'],\n    attributes: {\n        operators: ['^=', '$=', '*=']\n    },\n    pseudoElements: {\n        notation: 'both'\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'root',\n                'last-child',\n                'first-of-type',\n                'last-of-type',\n                'only-child',\n                'only-of-type',\n                'empty',\n                'target',\n                'enabled',\n                'disabled',\n                'checked',\n                'indeterminate'\n            ],\n            Formula: ['nth-child', 'nth-last-child', 'nth-of-type', 'nth-last-of-type'],\n            Selector: ['not']\n        }\n    }\n});\nvar selectors4SyntaxDefinition = extendSyntaxDefinition(selectors3SyntaxDefinition, {\n    combinators: ['||'],\n    attributes: {\n        caseSensitivityModifiers: ['i', 'I', 's', 'S']\n    },\n    pseudoClasses: {\n        definitions: {\n            NoArgument: [\n                'any-link',\n                'local-link',\n                'target-within',\n                'scope',\n                'current',\n                'past',\n                'future',\n                'focus-within',\n                'focus-visible',\n                'read-write',\n                'read-only',\n                'placeholder-shown',\n                'default',\n                'valid',\n                'invalid',\n                'in-range',\n                'out-of-range',\n                'required',\n                'optional',\n                'blank',\n                'user-invalid',\n                'playing',\n                'paused',\n                'autofill',\n                'modal',\n                'fullscreen',\n                'picture-in-picture',\n                'defined',\n                'loading',\n                'popover-open'\n            ],\n            Formula: ['nth-col', 'nth-last-col'],\n            String: ['dir'],\n            FormulaOfSelector: ['nth-child', 'nth-last-child'],\n            Selector: ['current', 'is', 'where', 'has', 'state']\n        }\n    },\n    pseudoElements: {\n        definitions: {\n            NoArgument: ['marker']\n        }\n    }\n});\n/**\n * CSS Modules with their syntax definitions.\n * These can be used to extend the parser with specific CSS modules.\n *\n * @example\n * // Using the css-position-3 module\n * createParser({ modules: ['css-position-3'] })\n */\nvar cssModules = {\n    'css-position-1': {\n        latest: false,\n        pseudoClasses: {\n            definitions: {\n                NoArgument: ['static', 'relative', 'absolute']\n            }\n        }\n    },\n    'css-position-2': {\n        latest: false,\n        pseudoClasses: {\n            definitions: {\n                NoArgument: ['static', 'relative', 'absolute', 'fixed']\n            }\n        }\n    },\n    'css-position-3': {\n        latest: false,\n        pseudoClasses: {\n            definitions: {\n                NoArgument: ['sticky', 'fixed', 'absolute', 'relative', 'static']\n            }\n        }\n    },\n    'css-position-4': {\n        latest: true,\n        pseudoClasses: {\n            definitions: {\n                NoArgument: ['sticky', 'fixed', 'absolute', 'relative', 'static', 'initial']\n            }\n        }\n    },\n    'css-scoping-1': {\n        latest: true,\n        pseudoClasses: {\n            definitions: {\n                NoArgument: ['host', 'host-context'],\n                Selector: ['host', 'host-context']\n            }\n        },\n        pseudoElements: {\n            definitions: {\n                Selector: ['slotted']\n            }\n        }\n    },\n    'css-pseudo-4': {\n        latest: true,\n        pseudoElements: {\n            definitions: {\n                NoArgument: [\n                    'marker',\n                    'selection',\n                    'target-text',\n                    'search-text',\n                    'spelling-error',\n                    'grammar-error',\n                    'backdrop',\n                    'file-selector-button',\n                    'prefix',\n                    'postfix',\n                    'placeholder',\n                    'details-content'\n                ],\n                String: ['highlight']\n            }\n        }\n    },\n    'css-shadow-parts-1': {\n        latest: true,\n        pseudoElements: {\n            definitions: {\n                String: ['part']\n            }\n        }\n    }\n};\nvar latestSyntaxDefinition = __assign(__assign({}, selectors4SyntaxDefinition), { modules: Object.entries(cssModules)\n        .filter(function (_a) {\n        var latest = _a[1].latest;\n        return latest;\n    })\n        .map(function (_a) {\n        var name = _a[0];\n        return name;\n    }) });\nvar progressiveSyntaxDefinition = extendSyntaxDefinition(latestSyntaxDefinition, {\n    pseudoElements: {\n        unknown: 'accept'\n    },\n    pseudoClasses: {\n        unknown: 'accept'\n    },\n    attributes: {\n        unknownCaseSensitivityModifiers: 'accept'\n    }\n});\nvar cssSyntaxDefinitions = {\n    css1: css1SyntaxDefinition,\n    css2: css2SyntaxDefinition,\n    css3: selectors3SyntaxDefinition,\n    'selectors-3': selectors3SyntaxDefinition,\n    'selectors-4': selectors4SyntaxDefinition,\n    latest: latestSyntaxDefinition,\n    progressive: progressiveSyntaxDefinition\n};\n/**\n * Builds an index of where each pseudo-class and pseudo-element is defined\n * (in which CSS Level or CSS Module)\n */\nfunction buildPseudoLocationIndex() {\n    var index = {\n        pseudoClasses: {},\n        pseudoElements: {}\n    };\n    // Add CSS Levels (excluding 'latest' and 'progressive')\n    var cssLevels = ['css1', 'css2', 'css3', 'selectors-3', 'selectors-4'];\n    for (var _i = 0, cssLevels_1 = cssLevels; _i < cssLevels_1.length; _i++) {\n        var level = cssLevels_1[_i];\n        var syntax = cssSyntaxDefinitions[level];\n        // Process pseudo-classes\n        if (syntax.pseudoClasses && typeof syntax.pseudoClasses === 'object') {\n            var definitions = syntax.pseudoClasses.definitions;\n            if (definitions) {\n                for (var _a = 0, _b = Object.entries(definitions); _a < _b.length; _a++) {\n                    var _c = _b[_a], names = _c[1];\n                    for (var _d = 0, names_1 = names; _d < names_1.length; _d++) {\n                        var name_1 = names_1[_d];\n                        if (!index.pseudoClasses[name_1]) {\n                            index.pseudoClasses[name_1] = [];\n                        }\n                        if (!index.pseudoClasses[name_1].includes(level)) {\n                            index.pseudoClasses[name_1].push(level);\n                        }\n                    }\n                }\n            }\n        }\n        // Process pseudo-elements\n        if (syntax.pseudoElements && typeof syntax.pseudoElements === 'object') {\n            var definitions = syntax.pseudoElements.definitions;\n            if (definitions) {\n                if (Array.isArray(definitions)) {\n                    for (var _e = 0, definitions_1 = definitions; _e < definitions_1.length; _e++) {\n                        var name_2 = definitions_1[_e];\n                        if (!index.pseudoElements[name_2]) {\n                            index.pseudoElements[name_2] = [];\n                        }\n                        if (!index.pseudoElements[name_2].includes(level)) {\n                            index.pseudoElements[name_2].push(level);\n                        }\n                    }\n                }\n                else {\n                    for (var _f = 0, _g = Object.values(definitions); _f < _g.length; _f++) {\n                        var names = _g[_f];\n                        for (var _h = 0, names_2 = names; _h < names_2.length; _h++) {\n                            var name_3 = names_2[_h];\n                            if (!index.pseudoElements[name_3]) {\n                                index.pseudoElements[name_3] = [];\n                            }\n                            if (!index.pseudoElements[name_3].includes(level)) {\n                                index.pseudoElements[name_3].push(level);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n    // Add CSS Modules\n    for (var _j = 0, _k = Object.entries(cssModules); _j < _k.length; _j++) {\n        var _l = _k[_j], moduleName = _l[0], moduleSyntax = _l[1];\n        // Process pseudo-classes\n        if (moduleSyntax.pseudoClasses && typeof moduleSyntax.pseudoClasses === 'object') {\n            var definitions = moduleSyntax.pseudoClasses.definitions;\n            if (definitions) {\n                for (var _m = 0, _o = Object.values(definitions); _m < _o.length; _m++) {\n                    var names = _o[_m];\n                    for (var _p = 0, names_3 = names; _p < names_3.length; _p++) {\n                        var name_4 = names_3[_p];\n                        if (!index.pseudoClasses[name_4]) {\n                            index.pseudoClasses[name_4] = [];\n                        }\n                        if (!index.pseudoClasses[name_4].includes(moduleName)) {\n                            index.pseudoClasses[name_4].push(moduleName);\n                        }\n                    }\n                }\n            }\n        }\n        // Process pseudo-elements\n        if (moduleSyntax.pseudoElements && typeof moduleSyntax.pseudoElements === 'object') {\n            var definitions = moduleSyntax.pseudoElements.definitions;\n            if (definitions) {\n                if (Array.isArray(definitions)) {\n                    for (var _q = 0, definitions_2 = definitions; _q < definitions_2.length; _q++) {\n                        var name_5 = definitions_2[_q];\n                        if (!index.pseudoElements[name_5]) {\n                            index.pseudoElements[name_5] = [];\n                        }\n                        if (!index.pseudoElements[name_5].includes(moduleName)) {\n                            index.pseudoElements[name_5].push(moduleName);\n                        }\n                    }\n                }\n                else {\n                    for (var _r = 0, _s = Object.values(definitions); _r < _s.length; _r++) {\n                        var names = _s[_r];\n                        for (var _t = 0, names_4 = names; _t < names_4.length; _t++) {\n                            var name_6 = names_4[_t];\n                            if (!index.pseudoElements[name_6]) {\n                                index.pseudoElements[name_6] = [];\n                            }\n                            if (!index.pseudoElements[name_6].includes(moduleName)) {\n                                index.pseudoElements[name_6].push(moduleName);\n                            }\n                        }\n                    }\n                }\n            }\n        }\n    }\n    return index;\n}\n// Pre-build the index for faster lookup\nvar pseudoLocationIndex = buildPseudoLocationIndex();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvY3NzLXNlbGVjdG9yLXBhcnNlci9kaXN0L21qcy9zeW50YXgtZGVmaW5pdGlvbnMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7O0FBQUEsZ0JBQWdCLFNBQUksSUFBSSxTQUFJO0FBQzVCO0FBQ0EsaURBQWlELE9BQU87QUFDeEQ7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsMEJBQTBCO0FBQ25CO0FBQ1A7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSx3Q0FBd0M7QUFDeEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGdDQUFnQztBQUNoQyx5REFBeUQsZ0JBQWdCO0FBQ3pFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSw0QkFBNEI7QUFDNUIscURBQXFELGdCQUFnQjtBQUNyRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSw0REFBNEQsdUNBQXVDLDBCQUEwQixpQkFBaUI7QUFDOUksS0FBSztBQUNMLENBQUM7QUFDRDtBQUNBLFdBQVc7QUFDWDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsV0FBVyxnQkFBZ0I7QUFDM0I7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrQkFBa0IsNkJBQTZCO0FBQy9DO0FBQ087QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFNBQVM7QUFDVDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxpREFBaUQsaUNBQWlDO0FBQ2xGO0FBQ0E7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxLQUFLLEdBQUc7QUFDUjtBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0w7QUFDQTtBQUNBLEtBQUs7QUFDTDtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ007QUFDUDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQO0FBQ0EseUJBQXlCO0FBQ3pCO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsOENBQThDLHlCQUF5QjtBQUN2RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxtRUFBbUUsZ0JBQWdCO0FBQ25GO0FBQ0Esc0RBQXNELHFCQUFxQjtBQUMzRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGtFQUFrRSwyQkFBMkI7QUFDN0Y7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxzRUFBc0UsZ0JBQWdCO0FBQ3RGO0FBQ0EsMERBQTBELHFCQUFxQjtBQUMvRTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0RBQXNELGdCQUFnQjtBQUN0RTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esa0VBQWtFLGdCQUFnQjtBQUNsRjtBQUNBLHNEQUFzRCxxQkFBcUI7QUFDM0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxrRUFBa0UsMkJBQTJCO0FBQzdGO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0Esc0VBQXNFLGdCQUFnQjtBQUN0RjtBQUNBLDBEQUEwRCxxQkFBcUI7QUFDL0U7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTyIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbnRob1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxteW5vdGVcXHJlbmRlcmVyXFxub2RlX21vZHVsZXNcXGNzcy1zZWxlY3Rvci1wYXJzZXJcXGRpc3RcXG1qc1xcc3ludGF4LWRlZmluaXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbInZhciBfX2Fzc2lnbiA9ICh0aGlzICYmIHRoaXMuX19hc3NpZ24pIHx8IGZ1bmN0aW9uICgpIHtcbiAgICBfX2Fzc2lnbiA9IE9iamVjdC5hc3NpZ24gfHwgZnVuY3Rpb24odCkge1xuICAgICAgICBmb3IgKHZhciBzLCBpID0gMSwgbiA9IGFyZ3VtZW50cy5sZW5ndGg7IGkgPCBuOyBpKyspIHtcbiAgICAgICAgICAgIHMgPSBhcmd1bWVudHNbaV07XG4gICAgICAgICAgICBmb3IgKHZhciBwIGluIHMpIGlmIChPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwocywgcCkpXG4gICAgICAgICAgICAgICAgdFtwXSA9IHNbcF07XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHQ7XG4gICAgfTtcbiAgICByZXR1cm4gX19hc3NpZ24uYXBwbHkodGhpcywgYXJndW1lbnRzKTtcbn07XG52YXIgZW1wdHlYbWxPcHRpb25zID0ge307XG52YXIgZGVmYXVsdFhtbE9wdGlvbnMgPSB7IHdpbGRjYXJkOiB0cnVlIH07XG5leHBvcnQgZnVuY3Rpb24gZ2V0WG1sT3B0aW9ucyhwYXJhbSkge1xuICAgIGlmIChwYXJhbSkge1xuICAgICAgICBpZiAodHlwZW9mIHBhcmFtID09PSAnYm9vbGVhbicpIHtcbiAgICAgICAgICAgIHJldHVybiBkZWZhdWx0WG1sT3B0aW9ucztcbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHJldHVybiBwYXJhbTtcbiAgICAgICAgfVxuICAgIH1cbiAgICBlbHNlIHtcbiAgICAgICAgcmV0dXJuIGVtcHR5WG1sT3B0aW9ucztcbiAgICB9XG59XG5mdW5jdGlvbiB3aXRoTWlncmF0aW9uKG1pZ3JhdGlvbiwgbWVyZ2UpIHtcbiAgICByZXR1cm4gZnVuY3Rpb24gKGJhc2UsIGV4dGVuc2lvbikgeyByZXR1cm4gbWVyZ2UobWlncmF0aW9uKGJhc2UpLCBtaWdyYXRpb24oZXh0ZW5zaW9uKSk7IH07XG59XG5mdW5jdGlvbiB3aXRoTm9OZWdhdGl2ZShtZXJnZSkge1xuICAgIHJldHVybiBmdW5jdGlvbiAoYmFzZSwgZXh0ZW5zaW9uKSB7XG4gICAgICAgIHZhciByZXN1bHQgPSBtZXJnZShiYXNlLCBleHRlbnNpb24pO1xuICAgICAgICBpZiAoIXJlc3VsdCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiU3ludGF4IGRlZmluaXRpb24gY2Fubm90IGJlIG51bGwgb3IgdW5kZWZpbmVkLlwiKTtcbiAgICAgICAgfVxuICAgICAgICByZXR1cm4gcmVzdWx0O1xuICAgIH07XG59XG5mdW5jdGlvbiB3aXRoUG9zaXRpdmUocG9zaXRpdmUsIG1lcmdlKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIChiYXNlLCBleHRlbnNpb24pIHtcbiAgICAgICAgaWYgKGV4dGVuc2lvbiA9PT0gdHJ1ZSkge1xuICAgICAgICAgICAgcmV0dXJuIHBvc2l0aXZlO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiBtZXJnZShiYXNlID09PSB0cnVlID8gcG9zaXRpdmUgOiBiYXNlLCBleHRlbnNpb24pO1xuICAgIH07XG59XG5mdW5jdGlvbiBtZXJnZVNlY3Rpb24odmFsdWVzKSB7XG4gICAgcmV0dXJuIGZ1bmN0aW9uIChiYXNlLCBleHRlbnNpb24pIHtcbiAgICAgICAgaWYgKCFleHRlbnNpb24gfHwgIWJhc2UpIHtcbiAgICAgICAgICAgIHJldHVybiBleHRlbnNpb247XG4gICAgICAgIH1cbiAgICAgICAgaWYgKHR5cGVvZiBleHRlbnNpb24gIT09ICdvYmplY3QnIHx8IGV4dGVuc2lvbiA9PT0gbnVsbCkge1xuICAgICAgICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVW5leHBlY3RlZCBzeW50YXggZGVmaW5pdGlvbiBleHRlbnNpb24gdHlwZTogXCIuY29uY2F0KGV4dGVuc2lvbiwgXCIuXCIpKTtcbiAgICAgICAgfVxuICAgICAgICB2YXIgcmVzdWx0ID0gX19hc3NpZ24oe30sIGJhc2UpO1xuICAgICAgICBmb3IgKHZhciBfaSA9IDAsIF9hID0gT2JqZWN0LmVudHJpZXMoZXh0ZW5zaW9uKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgICAgIHZhciBfYiA9IF9hW19pXSwga2V5ID0gX2JbMF0sIHZhbHVlID0gX2JbMV07XG4gICAgICAgICAgICBpZiAoa2V5ID09PSAnbGF0ZXN0Jykge1xuICAgICAgICAgICAgICAgIGNvbnRpbnVlO1xuICAgICAgICAgICAgfVxuICAgICAgICAgICAgdmFyIG1lcmdlU2NoZW1hID0gdmFsdWVzW2tleV07XG4gICAgICAgICAgICByZXN1bHRba2V5XSA9IG1lcmdlU2NoZW1hKGJhc2Vba2V5XSwgdmFsdWUpO1xuICAgICAgICB9XG4gICAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfTtcbn1cbmZ1bmN0aW9uIHJlcGxhY2VWYWx1ZUlmU3BlY2lmaWVkKGJhc2UsIGV4dGVuc2lvbikge1xuICAgIGlmIChleHRlbnNpb24gIT09IHVuZGVmaW5lZCkge1xuICAgICAgICByZXR1cm4gZXh0ZW5zaW9uO1xuICAgIH1cbiAgICByZXR1cm4gYmFzZTtcbn1cbmZ1bmN0aW9uIGNvbmNhdEFycmF5KGJhc2UsIGV4dGVuc2lvbikge1xuICAgIGlmICghZXh0ZW5zaW9uKSB7XG4gICAgICAgIHJldHVybiBiYXNlO1xuICAgIH1cbiAgICBpZiAoIWJhc2UpIHtcbiAgICAgICAgcmV0dXJuIGV4dGVuc2lvbjtcbiAgICB9XG4gICAgcmV0dXJuIGJhc2UuY29uY2F0KGV4dGVuc2lvbik7XG59XG5mdW5jdGlvbiBtZXJnZURlZmluaXRpb25zKGJhc2UsIGV4dGVuc2lvbikge1xuICAgIGlmICghZXh0ZW5zaW9uKSB7XG4gICAgICAgIHJldHVybiBiYXNlO1xuICAgIH1cbiAgICBpZiAoIWJhc2UpIHtcbiAgICAgICAgcmV0dXJuIGV4dGVuc2lvbjtcbiAgICB9XG4gICAgdmFyIHJlc3VsdCA9IF9fYXNzaWduKHt9LCBiYXNlKTtcbiAgICBmb3IgKHZhciBfaSA9IDAsIF9hID0gT2JqZWN0LmVudHJpZXMoZXh0ZW5zaW9uKTsgX2kgPCBfYS5sZW5ndGg7IF9pKyspIHtcbiAgICAgICAgdmFyIF9iID0gX2FbX2ldLCBrZXkgPSBfYlswXSwgdmFsdWUgPSBfYlsxXTtcbiAgICAgICAgaWYgKCF2YWx1ZSkge1xuICAgICAgICAgICAgZGVsZXRlIHJlc3VsdFtrZXldO1xuICAgICAgICAgICAgY29udGludWU7XG4gICAgICAgIH1cbiAgICAgICAgdmFyIGJhc2VWYWx1ZSA9IGJhc2Vba2V5XTtcbiAgICAgICAgaWYgKCFiYXNlVmFsdWUpIHtcbiAgICAgICAgICAgIHJlc3VsdFtrZXldID0gdmFsdWU7XG4gICAgICAgICAgICBjb250aW51ZTtcbiAgICAgICAgfVxuICAgICAgICByZXN1bHRba2V5XSA9IGJhc2VWYWx1ZS5jb25jYXQodmFsdWUpO1xuICAgIH1cbiAgICByZXR1cm4gcmVzdWx0O1xufVxuZXhwb3J0IHZhciBleHRlbmRTeW50YXhEZWZpbml0aW9uID0gd2l0aE5vTmVnYXRpdmUobWVyZ2VTZWN0aW9uKHtcbiAgICBiYXNlU3ludGF4OiByZXBsYWNlVmFsdWVJZlNwZWNpZmllZCxcbiAgICBtb2R1bGVzOiBjb25jYXRBcnJheSxcbiAgICB0YWc6IHdpdGhQb3NpdGl2ZShkZWZhdWx0WG1sT3B0aW9ucywgbWVyZ2VTZWN0aW9uKHtcbiAgICAgICAgd2lsZGNhcmQ6IHJlcGxhY2VWYWx1ZUlmU3BlY2lmaWVkXG4gICAgfSkpLFxuICAgIGlkczogcmVwbGFjZVZhbHVlSWZTcGVjaWZpZWQsXG4gICAgY2xhc3NOYW1lczogcmVwbGFjZVZhbHVlSWZTcGVjaWZpZWQsXG4gICAgbmFtZXNwYWNlOiB3aXRoUG9zaXRpdmUoZGVmYXVsdFhtbE9wdGlvbnMsIG1lcmdlU2VjdGlvbih7XG4gICAgICAgIHdpbGRjYXJkOiByZXBsYWNlVmFsdWVJZlNwZWNpZmllZFxuICAgIH0pKSxcbiAgICBjb21iaW5hdG9yczogY29uY2F0QXJyYXksXG4gICAgYXR0cmlidXRlczogbWVyZ2VTZWN0aW9uKHtcbiAgICAgICAgb3BlcmF0b3JzOiBjb25jYXRBcnJheSxcbiAgICAgICAgY2FzZVNlbnNpdGl2aXR5TW9kaWZpZXJzOiBjb25jYXRBcnJheSxcbiAgICAgICAgdW5rbm93bkNhc2VTZW5zaXRpdml0eU1vZGlmaWVyczogcmVwbGFjZVZhbHVlSWZTcGVjaWZpZWRcbiAgICB9KSxcbiAgICBwc2V1ZG9DbGFzc2VzOiBtZXJnZVNlY3Rpb24oe1xuICAgICAgICB1bmtub3duOiByZXBsYWNlVmFsdWVJZlNwZWNpZmllZCxcbiAgICAgICAgZGVmaW5pdGlvbnM6IG1lcmdlRGVmaW5pdGlvbnNcbiAgICB9KSxcbiAgICBwc2V1ZG9FbGVtZW50czogbWVyZ2VTZWN0aW9uKHtcbiAgICAgICAgdW5rbm93bjogcmVwbGFjZVZhbHVlSWZTcGVjaWZpZWQsXG4gICAgICAgIG5vdGF0aW9uOiByZXBsYWNlVmFsdWVJZlNwZWNpZmllZCxcbiAgICAgICAgZGVmaW5pdGlvbnM6IHdpdGhNaWdyYXRpb24oZnVuY3Rpb24gKGRlZmluaXRpb25zKSB7IHJldHVybiAoQXJyYXkuaXNBcnJheShkZWZpbml0aW9ucykgPyB7IE5vQXJndW1lbnQ6IGRlZmluaXRpb25zIH0gOiBkZWZpbml0aW9ucyk7IH0sIG1lcmdlRGVmaW5pdGlvbnMpXG4gICAgfSlcbn0pKTtcbnZhciBjc3MxU3ludGF4RGVmaW5pdGlvbiA9IHtcbiAgICB0YWc6IHt9LFxuICAgIGlkczogdHJ1ZSxcbiAgICBjbGFzc05hbWVzOiB0cnVlLFxuICAgIGNvbWJpbmF0b3JzOiBbXSxcbiAgICBwc2V1ZG9FbGVtZW50czoge1xuICAgICAgICB1bmtub3duOiAncmVqZWN0JyxcbiAgICAgICAgbm90YXRpb246ICdzaW5nbGVDb2xvbicsXG4gICAgICAgIGRlZmluaXRpb25zOiBbJ2ZpcnN0LWxldHRlcicsICdmaXJzdC1saW5lJ11cbiAgICB9LFxuICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgdW5rbm93bjogJ3JlamVjdCcsXG4gICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICBOb0FyZ3VtZW50OiBbJ2xpbmsnLCAndmlzaXRlZCcsICdhY3RpdmUnXVxuICAgICAgICB9XG4gICAgfVxufTtcbnZhciBjc3MyU3ludGF4RGVmaW5pdGlvbiA9IGV4dGVuZFN5bnRheERlZmluaXRpb24oY3NzMVN5bnRheERlZmluaXRpb24sIHtcbiAgICB0YWc6IHsgd2lsZGNhcmQ6IHRydWUgfSxcbiAgICBjb21iaW5hdG9yczogWyc+JywgJysnXSxcbiAgICBhdHRyaWJ1dGVzOiB7XG4gICAgICAgIHVua25vd25DYXNlU2Vuc2l0aXZpdHlNb2RpZmllcnM6ICdyZWplY3QnLFxuICAgICAgICBvcGVyYXRvcnM6IFsnPScsICd+PScsICd8PSddXG4gICAgfSxcbiAgICBwc2V1ZG9FbGVtZW50czoge1xuICAgICAgICBkZWZpbml0aW9uczogWydiZWZvcmUnLCAnYWZ0ZXInXVxuICAgIH0sXG4gICAgcHNldWRvQ2xhc3Nlczoge1xuICAgICAgICB1bmtub3duOiAncmVqZWN0JyxcbiAgICAgICAgZGVmaW5pdGlvbnM6IHtcbiAgICAgICAgICAgIE5vQXJndW1lbnQ6IFsnaG92ZXInLCAnZm9jdXMnLCAnZmlyc3QtY2hpbGQnXSxcbiAgICAgICAgICAgIFN0cmluZzogWydsYW5nJ11cbiAgICAgICAgfVxuICAgIH1cbn0pO1xudmFyIHNlbGVjdG9yczNTeW50YXhEZWZpbml0aW9uID0gZXh0ZW5kU3ludGF4RGVmaW5pdGlvbihjc3MyU3ludGF4RGVmaW5pdGlvbiwge1xuICAgIG5hbWVzcGFjZToge1xuICAgICAgICB3aWxkY2FyZDogdHJ1ZVxuICAgIH0sXG4gICAgY29tYmluYXRvcnM6IFsnfiddLFxuICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgb3BlcmF0b3JzOiBbJ149JywgJyQ9JywgJyo9J11cbiAgICB9LFxuICAgIHBzZXVkb0VsZW1lbnRzOiB7XG4gICAgICAgIG5vdGF0aW9uOiAnYm90aCdcbiAgICB9LFxuICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgZGVmaW5pdGlvbnM6IHtcbiAgICAgICAgICAgIE5vQXJndW1lbnQ6IFtcbiAgICAgICAgICAgICAgICAncm9vdCcsXG4gICAgICAgICAgICAgICAgJ2xhc3QtY2hpbGQnLFxuICAgICAgICAgICAgICAgICdmaXJzdC1vZi10eXBlJyxcbiAgICAgICAgICAgICAgICAnbGFzdC1vZi10eXBlJyxcbiAgICAgICAgICAgICAgICAnb25seS1jaGlsZCcsXG4gICAgICAgICAgICAgICAgJ29ubHktb2YtdHlwZScsXG4gICAgICAgICAgICAgICAgJ2VtcHR5JyxcbiAgICAgICAgICAgICAgICAndGFyZ2V0JyxcbiAgICAgICAgICAgICAgICAnZW5hYmxlZCcsXG4gICAgICAgICAgICAgICAgJ2Rpc2FibGVkJyxcbiAgICAgICAgICAgICAgICAnY2hlY2tlZCcsXG4gICAgICAgICAgICAgICAgJ2luZGV0ZXJtaW5hdGUnXG4gICAgICAgICAgICBdLFxuICAgICAgICAgICAgRm9ybXVsYTogWydudGgtY2hpbGQnLCAnbnRoLWxhc3QtY2hpbGQnLCAnbnRoLW9mLXR5cGUnLCAnbnRoLWxhc3Qtb2YtdHlwZSddLFxuICAgICAgICAgICAgU2VsZWN0b3I6IFsnbm90J11cbiAgICAgICAgfVxuICAgIH1cbn0pO1xudmFyIHNlbGVjdG9yczRTeW50YXhEZWZpbml0aW9uID0gZXh0ZW5kU3ludGF4RGVmaW5pdGlvbihzZWxlY3RvcnMzU3ludGF4RGVmaW5pdGlvbiwge1xuICAgIGNvbWJpbmF0b3JzOiBbJ3x8J10sXG4gICAgYXR0cmlidXRlczoge1xuICAgICAgICBjYXNlU2Vuc2l0aXZpdHlNb2RpZmllcnM6IFsnaScsICdJJywgJ3MnLCAnUyddXG4gICAgfSxcbiAgICBwc2V1ZG9DbGFzc2VzOiB7XG4gICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICBOb0FyZ3VtZW50OiBbXG4gICAgICAgICAgICAgICAgJ2FueS1saW5rJyxcbiAgICAgICAgICAgICAgICAnbG9jYWwtbGluaycsXG4gICAgICAgICAgICAgICAgJ3RhcmdldC13aXRoaW4nLFxuICAgICAgICAgICAgICAgICdzY29wZScsXG4gICAgICAgICAgICAgICAgJ2N1cnJlbnQnLFxuICAgICAgICAgICAgICAgICdwYXN0JyxcbiAgICAgICAgICAgICAgICAnZnV0dXJlJyxcbiAgICAgICAgICAgICAgICAnZm9jdXMtd2l0aGluJyxcbiAgICAgICAgICAgICAgICAnZm9jdXMtdmlzaWJsZScsXG4gICAgICAgICAgICAgICAgJ3JlYWQtd3JpdGUnLFxuICAgICAgICAgICAgICAgICdyZWFkLW9ubHknLFxuICAgICAgICAgICAgICAgICdwbGFjZWhvbGRlci1zaG93bicsXG4gICAgICAgICAgICAgICAgJ2RlZmF1bHQnLFxuICAgICAgICAgICAgICAgICd2YWxpZCcsXG4gICAgICAgICAgICAgICAgJ2ludmFsaWQnLFxuICAgICAgICAgICAgICAgICdpbi1yYW5nZScsXG4gICAgICAgICAgICAgICAgJ291dC1vZi1yYW5nZScsXG4gICAgICAgICAgICAgICAgJ3JlcXVpcmVkJyxcbiAgICAgICAgICAgICAgICAnb3B0aW9uYWwnLFxuICAgICAgICAgICAgICAgICdibGFuaycsXG4gICAgICAgICAgICAgICAgJ3VzZXItaW52YWxpZCcsXG4gICAgICAgICAgICAgICAgJ3BsYXlpbmcnLFxuICAgICAgICAgICAgICAgICdwYXVzZWQnLFxuICAgICAgICAgICAgICAgICdhdXRvZmlsbCcsXG4gICAgICAgICAgICAgICAgJ21vZGFsJyxcbiAgICAgICAgICAgICAgICAnZnVsbHNjcmVlbicsXG4gICAgICAgICAgICAgICAgJ3BpY3R1cmUtaW4tcGljdHVyZScsXG4gICAgICAgICAgICAgICAgJ2RlZmluZWQnLFxuICAgICAgICAgICAgICAgICdsb2FkaW5nJyxcbiAgICAgICAgICAgICAgICAncG9wb3Zlci1vcGVuJ1xuICAgICAgICAgICAgXSxcbiAgICAgICAgICAgIEZvcm11bGE6IFsnbnRoLWNvbCcsICdudGgtbGFzdC1jb2wnXSxcbiAgICAgICAgICAgIFN0cmluZzogWydkaXInXSxcbiAgICAgICAgICAgIEZvcm11bGFPZlNlbGVjdG9yOiBbJ250aC1jaGlsZCcsICdudGgtbGFzdC1jaGlsZCddLFxuICAgICAgICAgICAgU2VsZWN0b3I6IFsnY3VycmVudCcsICdpcycsICd3aGVyZScsICdoYXMnLCAnc3RhdGUnXVxuICAgICAgICB9XG4gICAgfSxcbiAgICBwc2V1ZG9FbGVtZW50czoge1xuICAgICAgICBkZWZpbml0aW9uczoge1xuICAgICAgICAgICAgTm9Bcmd1bWVudDogWydtYXJrZXInXVxuICAgICAgICB9XG4gICAgfVxufSk7XG4vKipcbiAqIENTUyBNb2R1bGVzIHdpdGggdGhlaXIgc3ludGF4IGRlZmluaXRpb25zLlxuICogVGhlc2UgY2FuIGJlIHVzZWQgdG8gZXh0ZW5kIHRoZSBwYXJzZXIgd2l0aCBzcGVjaWZpYyBDU1MgbW9kdWxlcy5cbiAqXG4gKiBAZXhhbXBsZVxuICogLy8gVXNpbmcgdGhlIGNzcy1wb3NpdGlvbi0zIG1vZHVsZVxuICogY3JlYXRlUGFyc2VyKHsgbW9kdWxlczogWydjc3MtcG9zaXRpb24tMyddIH0pXG4gKi9cbmV4cG9ydCB2YXIgY3NzTW9kdWxlcyA9IHtcbiAgICAnY3NzLXBvc2l0aW9uLTEnOiB7XG4gICAgICAgIGxhdGVzdDogZmFsc2UsXG4gICAgICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICAgICAgTm9Bcmd1bWVudDogWydzdGF0aWMnLCAncmVsYXRpdmUnLCAnYWJzb2x1dGUnXVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSxcbiAgICAnY3NzLXBvc2l0aW9uLTInOiB7XG4gICAgICAgIGxhdGVzdDogZmFsc2UsXG4gICAgICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICAgICAgTm9Bcmd1bWVudDogWydzdGF0aWMnLCAncmVsYXRpdmUnLCAnYWJzb2x1dGUnLCAnZml4ZWQnXVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSxcbiAgICAnY3NzLXBvc2l0aW9uLTMnOiB7XG4gICAgICAgIGxhdGVzdDogZmFsc2UsXG4gICAgICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICAgICAgTm9Bcmd1bWVudDogWydzdGlja3knLCAnZml4ZWQnLCAnYWJzb2x1dGUnLCAncmVsYXRpdmUnLCAnc3RhdGljJ11cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0sXG4gICAgJ2Nzcy1wb3NpdGlvbi00Jzoge1xuICAgICAgICBsYXRlc3Q6IHRydWUsXG4gICAgICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgICAgIGRlZmluaXRpb25zOiB7XG4gICAgICAgICAgICAgICAgTm9Bcmd1bWVudDogWydzdGlja3knLCAnZml4ZWQnLCAnYWJzb2x1dGUnLCAncmVsYXRpdmUnLCAnc3RhdGljJywgJ2luaXRpYWwnXVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSxcbiAgICAnY3NzLXNjb3BpbmctMSc6IHtcbiAgICAgICAgbGF0ZXN0OiB0cnVlLFxuICAgICAgICBwc2V1ZG9DbGFzc2VzOiB7XG4gICAgICAgICAgICBkZWZpbml0aW9uczoge1xuICAgICAgICAgICAgICAgIE5vQXJndW1lbnQ6IFsnaG9zdCcsICdob3N0LWNvbnRleHQnXSxcbiAgICAgICAgICAgICAgICBTZWxlY3RvcjogWydob3N0JywgJ2hvc3QtY29udGV4dCddXG4gICAgICAgICAgICB9XG4gICAgICAgIH0sXG4gICAgICAgIHBzZXVkb0VsZW1lbnRzOiB7XG4gICAgICAgICAgICBkZWZpbml0aW9uczoge1xuICAgICAgICAgICAgICAgIFNlbGVjdG9yOiBbJ3Nsb3R0ZWQnXVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgfSxcbiAgICAnY3NzLXBzZXVkby00Jzoge1xuICAgICAgICBsYXRlc3Q6IHRydWUsXG4gICAgICAgIHBzZXVkb0VsZW1lbnRzOiB7XG4gICAgICAgICAgICBkZWZpbml0aW9uczoge1xuICAgICAgICAgICAgICAgIE5vQXJndW1lbnQ6IFtcbiAgICAgICAgICAgICAgICAgICAgJ21hcmtlcicsXG4gICAgICAgICAgICAgICAgICAgICdzZWxlY3Rpb24nLFxuICAgICAgICAgICAgICAgICAgICAndGFyZ2V0LXRleHQnLFxuICAgICAgICAgICAgICAgICAgICAnc2VhcmNoLXRleHQnLFxuICAgICAgICAgICAgICAgICAgICAnc3BlbGxpbmctZXJyb3InLFxuICAgICAgICAgICAgICAgICAgICAnZ3JhbW1hci1lcnJvcicsXG4gICAgICAgICAgICAgICAgICAgICdiYWNrZHJvcCcsXG4gICAgICAgICAgICAgICAgICAgICdmaWxlLXNlbGVjdG9yLWJ1dHRvbicsXG4gICAgICAgICAgICAgICAgICAgICdwcmVmaXgnLFxuICAgICAgICAgICAgICAgICAgICAncG9zdGZpeCcsXG4gICAgICAgICAgICAgICAgICAgICdwbGFjZWhvbGRlcicsXG4gICAgICAgICAgICAgICAgICAgICdkZXRhaWxzLWNvbnRlbnQnXG4gICAgICAgICAgICAgICAgXSxcbiAgICAgICAgICAgICAgICBTdHJpbmc6IFsnaGlnaGxpZ2h0J11cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgIH0sXG4gICAgJ2Nzcy1zaGFkb3ctcGFydHMtMSc6IHtcbiAgICAgICAgbGF0ZXN0OiB0cnVlLFxuICAgICAgICBwc2V1ZG9FbGVtZW50czoge1xuICAgICAgICAgICAgZGVmaW5pdGlvbnM6IHtcbiAgICAgICAgICAgICAgICBTdHJpbmc6IFsncGFydCddXG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG59O1xudmFyIGxhdGVzdFN5bnRheERlZmluaXRpb24gPSBfX2Fzc2lnbihfX2Fzc2lnbih7fSwgc2VsZWN0b3JzNFN5bnRheERlZmluaXRpb24pLCB7IG1vZHVsZXM6IE9iamVjdC5lbnRyaWVzKGNzc01vZHVsZXMpXG4gICAgICAgIC5maWx0ZXIoZnVuY3Rpb24gKF9hKSB7XG4gICAgICAgIHZhciBsYXRlc3QgPSBfYVsxXS5sYXRlc3Q7XG4gICAgICAgIHJldHVybiBsYXRlc3Q7XG4gICAgfSlcbiAgICAgICAgLm1hcChmdW5jdGlvbiAoX2EpIHtcbiAgICAgICAgdmFyIG5hbWUgPSBfYVswXTtcbiAgICAgICAgcmV0dXJuIG5hbWU7XG4gICAgfSkgfSk7XG52YXIgcHJvZ3Jlc3NpdmVTeW50YXhEZWZpbml0aW9uID0gZXh0ZW5kU3ludGF4RGVmaW5pdGlvbihsYXRlc3RTeW50YXhEZWZpbml0aW9uLCB7XG4gICAgcHNldWRvRWxlbWVudHM6IHtcbiAgICAgICAgdW5rbm93bjogJ2FjY2VwdCdcbiAgICB9LFxuICAgIHBzZXVkb0NsYXNzZXM6IHtcbiAgICAgICAgdW5rbm93bjogJ2FjY2VwdCdcbiAgICB9LFxuICAgIGF0dHJpYnV0ZXM6IHtcbiAgICAgICAgdW5rbm93bkNhc2VTZW5zaXRpdml0eU1vZGlmaWVyczogJ2FjY2VwdCdcbiAgICB9XG59KTtcbmV4cG9ydCB2YXIgY3NzU3ludGF4RGVmaW5pdGlvbnMgPSB7XG4gICAgY3NzMTogY3NzMVN5bnRheERlZmluaXRpb24sXG4gICAgY3NzMjogY3NzMlN5bnRheERlZmluaXRpb24sXG4gICAgY3NzMzogc2VsZWN0b3JzM1N5bnRheERlZmluaXRpb24sXG4gICAgJ3NlbGVjdG9ycy0zJzogc2VsZWN0b3JzM1N5bnRheERlZmluaXRpb24sXG4gICAgJ3NlbGVjdG9ycy00Jzogc2VsZWN0b3JzNFN5bnRheERlZmluaXRpb24sXG4gICAgbGF0ZXN0OiBsYXRlc3RTeW50YXhEZWZpbml0aW9uLFxuICAgIHByb2dyZXNzaXZlOiBwcm9ncmVzc2l2ZVN5bnRheERlZmluaXRpb25cbn07XG4vKipcbiAqIEJ1aWxkcyBhbiBpbmRleCBvZiB3aGVyZSBlYWNoIHBzZXVkby1jbGFzcyBhbmQgcHNldWRvLWVsZW1lbnQgaXMgZGVmaW5lZFxuICogKGluIHdoaWNoIENTUyBMZXZlbCBvciBDU1MgTW9kdWxlKVxuICovXG5leHBvcnQgZnVuY3Rpb24gYnVpbGRQc2V1ZG9Mb2NhdGlvbkluZGV4KCkge1xuICAgIHZhciBpbmRleCA9IHtcbiAgICAgICAgcHNldWRvQ2xhc3Nlczoge30sXG4gICAgICAgIHBzZXVkb0VsZW1lbnRzOiB7fVxuICAgIH07XG4gICAgLy8gQWRkIENTUyBMZXZlbHMgKGV4Y2x1ZGluZyAnbGF0ZXN0JyBhbmQgJ3Byb2dyZXNzaXZlJylcbiAgICB2YXIgY3NzTGV2ZWxzID0gWydjc3MxJywgJ2NzczInLCAnY3NzMycsICdzZWxlY3RvcnMtMycsICdzZWxlY3RvcnMtNCddO1xuICAgIGZvciAodmFyIF9pID0gMCwgY3NzTGV2ZWxzXzEgPSBjc3NMZXZlbHM7IF9pIDwgY3NzTGV2ZWxzXzEubGVuZ3RoOyBfaSsrKSB7XG4gICAgICAgIHZhciBsZXZlbCA9IGNzc0xldmVsc18xW19pXTtcbiAgICAgICAgdmFyIHN5bnRheCA9IGNzc1N5bnRheERlZmluaXRpb25zW2xldmVsXTtcbiAgICAgICAgLy8gUHJvY2VzcyBwc2V1ZG8tY2xhc3Nlc1xuICAgICAgICBpZiAoc3ludGF4LnBzZXVkb0NsYXNzZXMgJiYgdHlwZW9mIHN5bnRheC5wc2V1ZG9DbGFzc2VzID09PSAnb2JqZWN0Jykge1xuICAgICAgICAgICAgdmFyIGRlZmluaXRpb25zID0gc3ludGF4LnBzZXVkb0NsYXNzZXMuZGVmaW5pdGlvbnM7XG4gICAgICAgICAgICBpZiAoZGVmaW5pdGlvbnMpIHtcbiAgICAgICAgICAgICAgICBmb3IgKHZhciBfYSA9IDAsIF9iID0gT2JqZWN0LmVudHJpZXMoZGVmaW5pdGlvbnMpOyBfYSA8IF9iLmxlbmd0aDsgX2ErKykge1xuICAgICAgICAgICAgICAgICAgICB2YXIgX2MgPSBfYltfYV0sIG5hbWVzID0gX2NbMV07XG4gICAgICAgICAgICAgICAgICAgIGZvciAodmFyIF9kID0gMCwgbmFtZXNfMSA9IG5hbWVzOyBfZCA8IG5hbWVzXzEubGVuZ3RoOyBfZCsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgbmFtZV8xID0gbmFtZXNfMVtfZF07XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWluZGV4LnBzZXVkb0NsYXNzZXNbbmFtZV8xXSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0NsYXNzZXNbbmFtZV8xXSA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9DbGFzc2VzW25hbWVfMV0uaW5jbHVkZXMobGV2ZWwpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgucHNldWRvQ2xhc3Nlc1tuYW1lXzFdLnB1c2gobGV2ZWwpO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgfVxuICAgICAgICB9XG4gICAgICAgIC8vIFByb2Nlc3MgcHNldWRvLWVsZW1lbnRzXG4gICAgICAgIGlmIChzeW50YXgucHNldWRvRWxlbWVudHMgJiYgdHlwZW9mIHN5bnRheC5wc2V1ZG9FbGVtZW50cyA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgICAgIHZhciBkZWZpbml0aW9ucyA9IHN5bnRheC5wc2V1ZG9FbGVtZW50cy5kZWZpbml0aW9ucztcbiAgICAgICAgICAgIGlmIChkZWZpbml0aW9ucykge1xuICAgICAgICAgICAgICAgIGlmIChBcnJheS5pc0FycmF5KGRlZmluaXRpb25zKSkge1xuICAgICAgICAgICAgICAgICAgICBmb3IgKHZhciBfZSA9IDAsIGRlZmluaXRpb25zXzEgPSBkZWZpbml0aW9uczsgX2UgPCBkZWZpbml0aW9uc18xLmxlbmd0aDsgX2UrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG5hbWVfMiA9IGRlZmluaXRpb25zXzFbX2VdO1xuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9FbGVtZW50c1tuYW1lXzJdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV8yXSA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9FbGVtZW50c1tuYW1lXzJdLmluY2x1ZGVzKGxldmVsKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0VsZW1lbnRzW25hbWVfMl0ucHVzaChsZXZlbCk7XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgZWxzZSB7XG4gICAgICAgICAgICAgICAgICAgIGZvciAodmFyIF9mID0gMCwgX2cgPSBPYmplY3QudmFsdWVzKGRlZmluaXRpb25zKTsgX2YgPCBfZy5sZW5ndGg7IF9mKyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBuYW1lcyA9IF9nW19mXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGZvciAodmFyIF9oID0gMCwgbmFtZXNfMiA9IG5hbWVzOyBfaCA8IG5hbWVzXzIubGVuZ3RoOyBfaCsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG5hbWVfMyA9IG5hbWVzXzJbX2hdO1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV8zXSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleC5wc2V1ZG9FbGVtZW50c1tuYW1lXzNdID0gW107XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV8zXS5pbmNsdWRlcyhsZXZlbCkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV8zXS5wdXNoKGxldmVsKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgLy8gQWRkIENTUyBNb2R1bGVzXG4gICAgZm9yICh2YXIgX2ogPSAwLCBfayA9IE9iamVjdC5lbnRyaWVzKGNzc01vZHVsZXMpOyBfaiA8IF9rLmxlbmd0aDsgX2orKykge1xuICAgICAgICB2YXIgX2wgPSBfa1tfal0sIG1vZHVsZU5hbWUgPSBfbFswXSwgbW9kdWxlU3ludGF4ID0gX2xbMV07XG4gICAgICAgIC8vIFByb2Nlc3MgcHNldWRvLWNsYXNzZXNcbiAgICAgICAgaWYgKG1vZHVsZVN5bnRheC5wc2V1ZG9DbGFzc2VzICYmIHR5cGVvZiBtb2R1bGVTeW50YXgucHNldWRvQ2xhc3NlcyA9PT0gJ29iamVjdCcpIHtcbiAgICAgICAgICAgIHZhciBkZWZpbml0aW9ucyA9IG1vZHVsZVN5bnRheC5wc2V1ZG9DbGFzc2VzLmRlZmluaXRpb25zO1xuICAgICAgICAgICAgaWYgKGRlZmluaXRpb25zKSB7XG4gICAgICAgICAgICAgICAgZm9yICh2YXIgX20gPSAwLCBfbyA9IE9iamVjdC52YWx1ZXMoZGVmaW5pdGlvbnMpOyBfbSA8IF9vLmxlbmd0aDsgX20rKykge1xuICAgICAgICAgICAgICAgICAgICB2YXIgbmFtZXMgPSBfb1tfbV07XG4gICAgICAgICAgICAgICAgICAgIGZvciAodmFyIF9wID0gMCwgbmFtZXNfMyA9IG5hbWVzOyBfcCA8IG5hbWVzXzMubGVuZ3RoOyBfcCsrKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICB2YXIgbmFtZV80ID0gbmFtZXNfM1tfcF07XG4gICAgICAgICAgICAgICAgICAgICAgICBpZiAoIWluZGV4LnBzZXVkb0NsYXNzZXNbbmFtZV80XSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0NsYXNzZXNbbmFtZV80XSA9IFtdO1xuICAgICAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9DbGFzc2VzW25hbWVfNF0uaW5jbHVkZXMobW9kdWxlTmFtZSkpIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICBpbmRleC5wc2V1ZG9DbGFzc2VzW25hbWVfNF0ucHVzaChtb2R1bGVOYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICAvLyBQcm9jZXNzIHBzZXVkby1lbGVtZW50c1xuICAgICAgICBpZiAobW9kdWxlU3ludGF4LnBzZXVkb0VsZW1lbnRzICYmIHR5cGVvZiBtb2R1bGVTeW50YXgucHNldWRvRWxlbWVudHMgPT09ICdvYmplY3QnKSB7XG4gICAgICAgICAgICB2YXIgZGVmaW5pdGlvbnMgPSBtb2R1bGVTeW50YXgucHNldWRvRWxlbWVudHMuZGVmaW5pdGlvbnM7XG4gICAgICAgICAgICBpZiAoZGVmaW5pdGlvbnMpIHtcbiAgICAgICAgICAgICAgICBpZiAoQXJyYXkuaXNBcnJheShkZWZpbml0aW9ucykpIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yICh2YXIgX3EgPSAwLCBkZWZpbml0aW9uc18yID0gZGVmaW5pdGlvbnM7IF9xIDwgZGVmaW5pdGlvbnNfMi5sZW5ndGg7IF9xKyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgIHZhciBuYW1lXzUgPSBkZWZpbml0aW9uc18yW19xXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV81XSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0VsZW1lbnRzW25hbWVfNV0gPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgICAgIGlmICghaW5kZXgucHNldWRvRWxlbWVudHNbbmFtZV81XS5pbmNsdWRlcyhtb2R1bGVOYW1lKSkge1xuICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0VsZW1lbnRzW25hbWVfNV0ucHVzaChtb2R1bGVOYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICAgICAgfVxuICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgICAgICAgICAgZm9yICh2YXIgX3IgPSAwLCBfcyA9IE9iamVjdC52YWx1ZXMoZGVmaW5pdGlvbnMpOyBfciA8IF9zLmxlbmd0aDsgX3IrKykge1xuICAgICAgICAgICAgICAgICAgICAgICAgdmFyIG5hbWVzID0gX3NbX3JdO1xuICAgICAgICAgICAgICAgICAgICAgICAgZm9yICh2YXIgX3QgPSAwLCBuYW1lc180ID0gbmFtZXM7IF90IDwgbmFtZXNfNC5sZW5ndGg7IF90KyspIHtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB2YXIgbmFtZV82ID0gbmFtZXNfNFtfdF07XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9FbGVtZW50c1tuYW1lXzZdKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0VsZW1lbnRzW25hbWVfNl0gPSBbXTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgaWYgKCFpbmRleC5wc2V1ZG9FbGVtZW50c1tuYW1lXzZdLmluY2x1ZGVzKG1vZHVsZU5hbWUpKSB7XG4gICAgICAgICAgICAgICAgICAgICAgICAgICAgICAgIGluZGV4LnBzZXVkb0VsZW1lbnRzW25hbWVfNl0ucHVzaChtb2R1bGVOYW1lKTtcbiAgICAgICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICAgICAgICAgIH1cbiAgICAgICAgICAgICAgICB9XG4gICAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICB9XG4gICAgcmV0dXJuIGluZGV4O1xufVxuLy8gUHJlLWJ1aWxkIHRoZSBpbmRleCBmb3IgZmFzdGVyIGxvb2t1cFxuZXhwb3J0IHZhciBwc2V1ZG9Mb2NhdGlvbkluZGV4ID0gYnVpbGRQc2V1ZG9Mb2NhdGlvbkluZGV4KCk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/syntax-definitions.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js":
/*!************************************************************!*\
  !*** ./node_modules/css-selector-parser/dist/mjs/utils.js ***!
  \************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   digitsChars: () => (/* binding */ digitsChars),\n/* harmony export */   escapeIdentifier: () => (/* binding */ escapeIdentifier),\n/* harmony export */   escapeString: () => (/* binding */ escapeString),\n/* harmony export */   identEscapeChars: () => (/* binding */ identEscapeChars),\n/* harmony export */   isHex: () => (/* binding */ isHex),\n/* harmony export */   isIdent: () => (/* binding */ isIdent),\n/* harmony export */   isIdentStart: () => (/* binding */ isIdentStart),\n/* harmony export */   maxHexLength: () => (/* binding */ maxHexLength),\n/* harmony export */   quoteChars: () => (/* binding */ quoteChars),\n/* harmony export */   stringRenderEscapeChars: () => (/* binding */ stringRenderEscapeChars),\n/* harmony export */   whitespaceChars: () => (/* binding */ whitespaceChars)\n/* harmony export */ });\nfunction isIdentStart(c) {\n    return (c >= 'a' && c <= 'z') || (c >= 'A' && c <= 'Z') || c === '-' || c === '_' || c === '\\\\' || c >= '\\u00a0';\n}\nfunction isIdent(c) {\n    return ((c >= 'a' && c <= 'z') ||\n        (c >= 'A' && c <= 'Z') ||\n        (c >= '0' && c <= '9') ||\n        c === '-' ||\n        c === '_' ||\n        c >= '\\u00a0');\n}\nfunction isHex(c) {\n    return (c >= 'a' && c <= 'f') || (c >= 'A' && c <= 'F') || (c >= '0' && c <= '9');\n}\nvar identEscapeChars = {\n    '!': true,\n    '\"': true,\n    '#': true,\n    $: true,\n    '%': true,\n    '&': true,\n    \"'\": true,\n    '(': true,\n    ')': true,\n    '*': true,\n    '+': true,\n    ',': true,\n    '.': true,\n    '/': true,\n    ';': true,\n    '<': true,\n    '=': true,\n    '>': true,\n    '?': true,\n    '@': true,\n    '[': true,\n    '\\\\': true,\n    ']': true,\n    '^': true,\n    '`': true,\n    '{': true,\n    '|': true,\n    '}': true,\n    '~': true\n};\nvar stringRenderEscapeChars = {\n    '\\n': true,\n    '\\r': true,\n    '\\t': true,\n    '\\f': true,\n    '\\v': true\n};\nvar whitespaceChars = {\n    ' ': true,\n    '\\t': true,\n    '\\n': true,\n    '\\r': true,\n    '\\f': true\n};\nvar quoteChars = {\n    '\"': true,\n    \"'\": true\n};\nvar digitsChars = {\n    0: true,\n    1: true,\n    2: true,\n    3: true,\n    4: true,\n    5: true,\n    6: true,\n    7: true,\n    8: true,\n    9: true\n};\nvar maxHexLength = 6;\nfunction escapeIdentifier(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (identEscapeChars[chr] || (chr === '-' && i === 1 && s.charAt(0) === '-')) {\n            result += '\\\\' + chr;\n        }\n        else {\n            if (chr === '-' ||\n                chr === '_' ||\n                (chr >= 'A' && chr <= 'Z') ||\n                (chr >= 'a' && chr <= 'z') ||\n                (chr >= '0' && chr <= '9' && i !== 0 && !(i === 1 && s.charAt(0) === '-'))) {\n                result += chr;\n            }\n            else {\n                var charCode = chr.charCodeAt(0);\n                if ((charCode & 0xf800) === 0xd800) {\n                    var extraCharCode = s.charCodeAt(i++);\n                    if ((charCode & 0xfc00) !== 0xd800 || (extraCharCode & 0xfc00) !== 0xdc00) {\n                        throw Error('UCS-2(decode): illegal sequence');\n                    }\n                    charCode = ((charCode & 0x3ff) << 10) + (extraCharCode & 0x3ff) + 0x10000;\n                }\n                result += '\\\\' + charCode.toString(16) + ' ';\n            }\n        }\n        i++;\n    }\n    return result.trim();\n}\nfunction escapeString(s) {\n    var len = s.length;\n    var result = '';\n    var i = 0;\n    while (i < len) {\n        var chr = s.charAt(i);\n        if (chr === '\"') {\n            chr = '\\\\\"';\n        }\n        else if (chr === '\\\\') {\n            chr = '\\\\\\\\';\n        }\n        else if (stringRenderEscapeChars[chr]) {\n            chr = '\\\\' + chr.charCodeAt(0).toString(16) + (i === len - 1 ? '' : ' ');\n        }\n        result += chr;\n        i++;\n    }\n    return \"\\\"\".concat(result, \"\\\"\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/css-selector-parser/dist/mjs/utils.js\n");

/***/ })

};
;