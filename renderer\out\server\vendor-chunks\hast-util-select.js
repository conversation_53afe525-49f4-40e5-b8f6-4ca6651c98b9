"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/hast-util-select";
exports.ids = ["vendor-chunks/hast-util-select"];
exports.modules = {

/***/ "(ssr)/./node_modules/hast-util-select/lib/attribute.js":
/*!********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/attribute.js ***!
  \********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   attribute: () => (/* binding */ attribute)\n/* harmony export */ });\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/lib/find.js\");\n/* harmony import */ var space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! space-separated-tokens */ \"(ssr)/./node_modules/space-separated-tokens/index.js\");\n/**\n * @import {AstAttribute} from 'css-selector-parser'\n * @import {Element, Properties} from 'hast'\n * @import {Info, Schema} from 'property-information'\n */\n\n\n\n\n\n\n/**\n * @param {AstAttribute} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {Schema} schema\n *   Schema of element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction attribute(query, element, schema) {\n  const info = (0,property_information__WEBPACK_IMPORTED_MODULE_0__.find)(schema, query.name)\n  const propertyValue = element.properties[info.property]\n  let value = normalizeValue(propertyValue, info)\n\n  // Exists.\n  if (!query.value) {\n    return value !== undefined\n  }\n\n  (0,devlop__WEBPACK_IMPORTED_MODULE_1__.ok)(query.value.type === 'String', 'expected plain string')\n  let key = query.value.value\n\n  // Case-sensitivity.\n  if (query.caseSensitivityModifier === 'i') {\n    key = key.toLowerCase()\n\n    if (value) {\n      value = value.toLowerCase()\n    }\n  }\n\n  if (value !== undefined) {\n    switch (query.operator) {\n      // Exact.\n      case '=': {\n        return key === value\n      }\n\n      // Ends.\n      case '$=': {\n        return key === value.slice(-key.length)\n      }\n\n      // Contains.\n      case '*=': {\n        return value.includes(key)\n      }\n\n      // Begins.\n      case '^=': {\n        return key === value.slice(0, key.length)\n      }\n\n      // Exact or prefix.\n      case '|=': {\n        return (\n          key === value ||\n          (key === value.slice(0, key.length) &&\n            value.charAt(key.length) === '-')\n        )\n      }\n\n      // Space-separated list.\n      case '~=': {\n        return (\n          // For all other values (including comma-separated lists), return whether this\n          // is an exact match.\n          key === value ||\n          // If this is a space-separated list, and the query is contained in it, return\n          // true.\n          space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.parse(value).includes(key)\n        )\n      }\n      // Other values are not yet supported by CSS.\n      // No default\n    }\n  }\n\n  return false\n}\n\n/**\n *\n * @param {Properties[keyof Properties]} value\n * @param {Info} info\n * @returns {string | undefined}\n */\nfunction normalizeValue(value, info) {\n  if (value === null || value === undefined) {\n    // Empty.\n  } else if (typeof value === 'boolean') {\n    if (value) {\n      return info.attribute\n    }\n  } else if (Array.isArray(value)) {\n    if (value.length > 0) {\n      return (info.commaSeparated ? comma_separated_tokens__WEBPACK_IMPORTED_MODULE_3__.stringify : space_separated_tokens__WEBPACK_IMPORTED_MODULE_2__.stringify)(value)\n    }\n  } else {\n    return String(value)\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/attribute.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/class-name.js":
/*!*********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/class-name.js ***!
  \*********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   className: () => (/* binding */ className)\n/* harmony export */ });\n/**\n * @import {AstClassName} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/** @type {Array<never>} */\nconst emptyClassNames = []\n\n/**\n * Check whether an element has all class names.\n *\n * @param {AstClassName} query\n *   AST rule (with `classNames`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction className(query, element) {\n  // Assume array.\n  const value = /** @type {Readonly<Array<string>>} */ (\n    element.properties.className || emptyClassNames\n  )\n\n  return value.includes(query.name)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvY2xhc3MtbmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLGNBQWM7QUFDMUIsWUFBWSxTQUFTO0FBQ3JCOztBQUVBLFdBQVcsY0FBYztBQUN6Qjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLGNBQWM7QUFDekI7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSwyQkFBMkIseUJBQXlCO0FBQ3BEO0FBQ0E7O0FBRUE7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbnRob1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxteW5vdGVcXHJlbmRlcmVyXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC1zZWxlY3RcXGxpYlxcY2xhc3MtbmFtZS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEBpbXBvcnQge0FzdENsYXNzTmFtZX0gZnJvbSAnY3NzLXNlbGVjdG9yLXBhcnNlcidcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKi9cblxuLyoqIEB0eXBlIHtBcnJheTxuZXZlcj59ICovXG5jb25zdCBlbXB0eUNsYXNzTmFtZXMgPSBbXVxuXG4vKipcbiAqIENoZWNrIHdoZXRoZXIgYW4gZWxlbWVudCBoYXMgYWxsIGNsYXNzIG5hbWVzLlxuICpcbiAqIEBwYXJhbSB7QXN0Q2xhc3NOYW1lfSBxdWVyeVxuICogICBBU1QgcnVsZSAod2l0aCBgY2xhc3NOYW1lc2ApLlxuICogQHBhcmFtIHtFbGVtZW50fSBlbGVtZW50XG4gKiAgIEVsZW1lbnQuXG4gKiBAcmV0dXJucyB7Ym9vbGVhbn1cbiAqICAgV2hldGhlciBgZWxlbWVudGAgbWF0Y2hlcyBgcXVlcnlgLlxuICovXG5leHBvcnQgZnVuY3Rpb24gY2xhc3NOYW1lKHF1ZXJ5LCBlbGVtZW50KSB7XG4gIC8vIEFzc3VtZSBhcnJheS5cbiAgY29uc3QgdmFsdWUgPSAvKiogQHR5cGUge1JlYWRvbmx5PEFycmF5PHN0cmluZz4+fSAqLyAoXG4gICAgZWxlbWVudC5wcm9wZXJ0aWVzLmNsYXNzTmFtZSB8fCBlbXB0eUNsYXNzTmFtZXNcbiAgKVxuXG4gIHJldHVybiB2YWx1ZS5pbmNsdWRlcyhxdWVyeS5uYW1lKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/class-name.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/enter-state.js":
/*!**********************************************************!*\
  !*** ./node_modules/hast-util-select/lib/enter-state.js ***!
  \**********************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   enterState: () => (/* binding */ enterState)\n/* harmony export */ });\n/* harmony import */ var direction__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! direction */ \"(ssr)/./node_modules/direction/index.js\");\n/* harmony import */ var hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! hast-util-to-string */ \"(ssr)/./node_modules/hast-util-to-string/lib/index.js\");\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit-parents/lib/index.js\");\n/**\n * @import {Visitor} from 'unist-util-visit'\n * @import {ElementContent, Nodes} from 'hast'\n * @import {Direction, State} from './index.js'\n */\n\n\n\n\n\n\n/**\n * Enter a node.\n *\n * The caller is responsible for calling the return value `exit`.\n *\n * @param {State} state\n *   Current state.\n *\n *   Will be mutated: `exit` undos the changes.\n * @param {Nodes} node\n *   Node to enter.\n * @returns {() => undefined}\n *   Call to exit.\n */\n// eslint-disable-next-line complexity\nfunction enterState(state, node) {\n  const schema = state.schema\n  const language = state.language\n  const currentDirection = state.direction\n  const editableOrEditingHost = state.editableOrEditingHost\n  /** @type {Direction | undefined} */\n  let directionInferred\n\n  if (node.type === 'element') {\n    const lang = node.properties.xmlLang || node.properties.lang\n    const type = node.properties.type || 'text'\n    const direction = directionProperty(node)\n\n    if (lang !== null && lang !== undefined) {\n      state.language = String(lang)\n    }\n\n    if (schema && schema.space === 'html') {\n      if (node.properties.contentEditable === 'true') {\n        state.editableOrEditingHost = true\n      }\n\n      if (node.tagName === 'svg') {\n        state.schema = property_information__WEBPACK_IMPORTED_MODULE_0__.svg\n      }\n\n      // See: <https://html.spec.whatwg.org/#the-directionality>.\n      // Explicit `[dir=rtl]`.\n      if (direction === 'rtl') {\n        directionInferred = direction\n      } else if (\n        // Explicit `[dir=ltr]`.\n        direction === 'ltr' ||\n        // HTML with an invalid or no `[dir]`.\n        (direction !== 'auto' && node.tagName === 'html') ||\n        // `input[type=tel]` with an invalid or no `[dir]`.\n        (direction !== 'auto' && node.tagName === 'input' && type === 'tel')\n      ) {\n        directionInferred = 'ltr'\n        // `[dir=auto]` or `bdi` with an invalid or no `[dir]`.\n      } else if (direction === 'auto' || node.tagName === 'bdi') {\n        if (node.tagName === 'textarea') {\n          // Check contents of `<textarea>`.\n          directionInferred = directionBidi((0,hast_util_to_string__WEBPACK_IMPORTED_MODULE_1__.toString)(node))\n        } else if (\n          node.tagName === 'input' &&\n          (type === 'email' ||\n            type === 'search' ||\n            type === 'tel' ||\n            type === 'text')\n        ) {\n          // Check value of `<input>`.\n          directionInferred = node.properties.value\n            ? directionBidi(String(node.properties.value))\n            : 'ltr'\n        } else {\n          // Check text nodes in `node`.\n          (0,unist_util_visit__WEBPACK_IMPORTED_MODULE_2__.visit)(node, inferDirectionality)\n        }\n      }\n\n      if (directionInferred) {\n        state.direction = directionInferred\n      }\n    }\n    // Turn off editing mode in non-HTML spaces.\n    else if (state.editableOrEditingHost) {\n      state.editableOrEditingHost = false\n    }\n  }\n\n  return reset\n\n  /**\n   * @returns {undefined}\n   *   Nothing.\n   */\n  function reset() {\n    state.schema = schema\n    state.language = language\n    state.direction = currentDirection\n    state.editableOrEditingHost = editableOrEditingHost\n  }\n\n  /** @type {Visitor<ElementContent>} */\n  function inferDirectionality(child) {\n    if (child.type === 'text') {\n      directionInferred = directionBidi(child.value)\n      return directionInferred ? unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.EXIT : undefined\n    }\n\n    if (\n      child !== node &&\n      child.type === 'element' &&\n      (child.tagName === 'bdi' ||\n        child.tagName === 'script' ||\n        child.tagName === 'style' ||\n        child.tagName === 'textare' ||\n        directionProperty(child))\n    ) {\n      return unist_util_visit__WEBPACK_IMPORTED_MODULE_3__.SKIP\n    }\n  }\n}\n\n/**\n * See `wooorm/direction`.\n *\n * @param {string} value\n *   Value to check.\n * @returns {Exclude<Direction, 'auto'> | undefined}\n *   Directionality.\n */\nfunction directionBidi(value) {\n  const result = (0,direction__WEBPACK_IMPORTED_MODULE_4__.direction)(value)\n  return result === 'neutral' ? undefined : result\n}\n\n/**\n * @param {ElementContent} node\n *   Node to check.\n * @returns {Direction | undefined}\n *   Directionality.\n */\nfunction directionProperty(node) {\n  const value =\n    node.type === 'element' && typeof node.properties.dir === 'string'\n      ? node.properties.dir.toLowerCase()\n      : undefined\n\n  return value === 'auto' || value === 'ltr' || value === 'rtl'\n    ? value\n    : undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/enter-state.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/id.js":
/*!*************************************************!*\
  !*** ./node_modules/hast-util-select/lib/id.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   id: () => (/* binding */ id)\n/* harmony export */ });\n/**\n * @import {AstId} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/**\n * Check whether an element has an ID.\n *\n * @param {AstId} query\n *   AST rule (with `ids`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction id(query, element) {\n  return element.properties.id === query.name\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvaWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0EsWUFBWSxPQUFPO0FBQ25CLFlBQVksU0FBUztBQUNyQjs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxXQUFXLE9BQU87QUFDbEI7QUFDQSxXQUFXLFNBQVM7QUFDcEI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxhbnRob1xcRG9jdW1lbnRzXFxhdWdtZW50LXByb2plY3RzXFxteW5vdGVcXHJlbmRlcmVyXFxub2RlX21vZHVsZXNcXGhhc3QtdXRpbC1zZWxlY3RcXGxpYlxcaWQuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLyoqXG4gKiBAaW1wb3J0IHtBc3RJZH0gZnJvbSAnY3NzLXNlbGVjdG9yLXBhcnNlcidcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKi9cblxuLyoqXG4gKiBDaGVjayB3aGV0aGVyIGFuIGVsZW1lbnQgaGFzIGFuIElELlxuICpcbiAqIEBwYXJhbSB7QXN0SWR9IHF1ZXJ5XG4gKiAgIEFTVCBydWxlICh3aXRoIGBpZHNgKS5cbiAqIEBwYXJhbSB7RWxlbWVudH0gZWxlbWVudFxuICogICBFbGVtZW50LlxuICogQHJldHVybnMge2Jvb2xlYW59XG4gKiAgIFdoZXRoZXIgYGVsZW1lbnRgIG1hdGNoZXMgYHF1ZXJ5YC5cbiAqL1xuZXhwb3J0IGZ1bmN0aW9uIGlkKHF1ZXJ5LCBlbGVtZW50KSB7XG4gIHJldHVybiBlbGVtZW50LnByb3BlcnRpZXMuaWQgPT09IHF1ZXJ5Lm5hbWVcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/id.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/index.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/index.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   matches: () => (/* binding */ matches),\n/* harmony export */   select: () => (/* binding */ select),\n/* harmony export */   selectAll: () => (/* binding */ selectAll)\n/* harmony export */ });\n/* harmony import */ var property_information__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! property-information */ \"(ssr)/./node_modules/property-information/index.js\");\n/* harmony import */ var _parse_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./parse.js */ \"(ssr)/./node_modules/hast-util-select/lib/parse.js\");\n/* harmony import */ var _walk_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./walk.js */ \"(ssr)/./node_modules/hast-util-select/lib/walk.js\");\n/**\n * @import {AstSelector} from 'css-selector-parser'\n * @import {Element, Nodes, RootContent} from 'hast'\n * @import {Schema} from 'property-information'\n */\n\n/**\n * @typedef {'html' | 'svg'} Space\n *   Name of namespace.\n *\n * @typedef {'auto' | 'ltr' | 'rtl'} Direction\n *   Direction.\n *\n * @typedef State\n *   Current state.\n * @property {Direction} direction\n *   Current direction.\n * @property {boolean} editableOrEditingHost\n *   Whether we’re in `contentEditable`.\n * @property {number | undefined} elementCount\n *   Track siblings: there are `n` siblings.\n * @property {number | undefined} elementIndex\n *   Track siblings: this current element has `n` elements before it.\n * @property {boolean} found\n *   Whether we found at least one match.\n * @property {string | undefined} language\n *   Current language.\n * @property {boolean} one\n *   Whether we can stop looking after we found one element.\n * @property {Array<Element>} results\n *   Matches.\n * @property {AstSelector} rootQuery\n *   Original root selectors.\n * @property {Schema} schema\n *   Current schema.\n * @property {Array<RootContent>} scopeElements\n *   Elements in scope.\n * @property {boolean} shallow\n *   Whether we only allow selectors without nesting.\n * @property {number | undefined} typeCount\n *   Track siblings: there are `n` siblings with this element’s tag name.\n * @property {number | undefined} typeIndex\n *   Track siblings: this current element has `n` elements with its tag name\n *   before it.\n */\n\n\n\n\n\n/**\n * Check that the given `node` matches `selector`.\n *\n * This only checks the element itself, not the surrounding tree.\n * Thus, nesting in selectors is not supported (`p b`, `p > b`), neither are\n * selectors like `:first-child`, etc.\n * This only checks that the given element matches the selector.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [node]\n *   Node that might match `selector`, should be an element (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {boolean}\n *   Whether `node` matches `selector`.\n */\nfunction matches(selector, node, space) {\n  const state = createState(selector, node, space)\n  state.one = true\n  state.shallow = true\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, node || undefined)\n  return state.results.length > 0\n}\n\n/**\n * Select the first element that matches `selector` in the given `tree`.\n * Searches the tree in *preorder*.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {Element | undefined}\n *   First element in `tree` that matches `selector` or `undefined` if nothing\n *   is found; this could be `tree` itself.\n */\nfunction select(selector, tree, space) {\n  const state = createState(selector, tree, space)\n  state.one = true\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, tree || undefined)\n  return state.results[0]\n}\n\n/**\n * Select all elements that match `selector` in the given `tree`.\n * Searches the tree in *preorder*.\n *\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {Array<Element>}\n *   Elements in `tree` that match `selector`.\n *   This could include `tree` itself.\n */\nfunction selectAll(selector, tree, space) {\n  const state = createState(selector, tree, space)\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_0__.walk)(state, tree || undefined)\n  return state.results\n}\n\n/**\n * @param {string} selector\n *   CSS selector, such as (`h1`, `a, b`).\n * @param {Nodes | null | undefined} [tree]\n *   Tree to search (optional).\n * @param {Space | null | undefined} [space='html']\n *   Name of namespace (default: `'html'`).\n * @returns {State} State\n *   State.\n */\nfunction createState(selector, tree, space) {\n  return {\n    direction: 'ltr',\n    editableOrEditingHost: false,\n    elementCount: undefined,\n    elementIndex: undefined,\n    found: false,\n    language: undefined,\n    one: false,\n    // State of the query.\n    results: [],\n    rootQuery: (0,_parse_js__WEBPACK_IMPORTED_MODULE_1__.parse)(selector),\n    schema: space === 'svg' ? property_information__WEBPACK_IMPORTED_MODULE_2__.svg : property_information__WEBPACK_IMPORTED_MODULE_2__.html,\n    scopeElements: tree ? (tree.type === 'root' ? tree.children : [tree]) : [],\n    shallow: false,\n    typeIndex: undefined,\n    typeCount: undefined\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/name.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/name.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   name: () => (/* binding */ name)\n/* harmony export */ });\n/**\n * @import {AstTagName} from 'css-selector-parser'\n * @import {Element} from 'hast'\n */\n\n/**\n * Check whether an element has a tag name.\n *\n * @param {AstTagName} query\n *   AST rule (with `tag`).\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction name(query, element) {\n  return query.name === element.tagName\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvbmFtZS5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7QUFDQSxZQUFZLFlBQVk7QUFDeEIsWUFBWSxTQUFTO0FBQ3JCOztBQUVBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsWUFBWTtBQUN2QjtBQUNBLFdBQVcsU0FBUztBQUNwQjtBQUNBLGFBQWE7QUFDYjtBQUNBO0FBQ087QUFDUDtBQUNBIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGFudGhvXFxEb2N1bWVudHNcXGF1Z21lbnQtcHJvamVjdHNcXG15bm90ZVxccmVuZGVyZXJcXG5vZGVfbW9kdWxlc1xcaGFzdC11dGlsLXNlbGVjdFxcbGliXFxuYW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QXN0VGFnTmFtZX0gZnJvbSAnY3NzLXNlbGVjdG9yLXBhcnNlcidcbiAqIEBpbXBvcnQge0VsZW1lbnR9IGZyb20gJ2hhc3QnXG4gKi9cblxuLyoqXG4gKiBDaGVjayB3aGV0aGVyIGFuIGVsZW1lbnQgaGFzIGEgdGFnIG5hbWUuXG4gKlxuICogQHBhcmFtIHtBc3RUYWdOYW1lfSBxdWVyeVxuICogICBBU1QgcnVsZSAod2l0aCBgdGFnYCkuXG4gKiBAcGFyYW0ge0VsZW1lbnR9IGVsZW1lbnRcbiAqICAgRWxlbWVudC5cbiAqIEByZXR1cm5zIHtib29sZWFufVxuICogICBXaGV0aGVyIGBlbGVtZW50YCBtYXRjaGVzIGBxdWVyeWAuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBuYW1lKHF1ZXJ5LCBlbGVtZW50KSB7XG4gIHJldHVybiBxdWVyeS5uYW1lID09PSBlbGVtZW50LnRhZ05hbWVcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/name.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/parse.js":
/*!****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/parse.js ***!
  \****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parse: () => (/* binding */ parse)\n/* harmony export */ });\n/* harmony import */ var css_selector_parser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! css-selector-parser */ \"(ssr)/./node_modules/css-selector-parser/dist/mjs/index.js\");\n/**\n * @import {AstSelector} from 'css-selector-parser'\n */\n\n\n\nconst cssSelectorParse = (0,css_selector_parser__WEBPACK_IMPORTED_MODULE_0__.createParser)({syntax: 'selectors-4'})\n\n/**\n * @param {string} selector\n *   Selector to parse.\n * @returns {AstSelector}\n *   Parsed selector.\n */\nfunction parse(selector) {\n  if (typeof selector !== 'string') {\n    throw new TypeError('Expected `string` as selector, not `' + selector + '`')\n  }\n\n  return cssSelectorParse(selector)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvaGFzdC11dGlsLXNlbGVjdC9saWIvcGFyc2UuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBLFlBQVksYUFBYTtBQUN6Qjs7QUFFZ0Q7O0FBRWhELHlCQUF5QixpRUFBWSxFQUFFLHNCQUFzQjs7QUFFN0Q7QUFDQSxXQUFXLFFBQVE7QUFDbkI7QUFDQSxhQUFhO0FBQ2I7QUFDQTtBQUNPO0FBQ1A7QUFDQTtBQUNBOztBQUVBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW50aG9cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcbXlub3RlXFxyZW5kZXJlclxcbm9kZV9tb2R1bGVzXFxoYXN0LXV0aWwtc2VsZWN0XFxsaWJcXHBhcnNlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qKlxuICogQGltcG9ydCB7QXN0U2VsZWN0b3J9IGZyb20gJ2Nzcy1zZWxlY3Rvci1wYXJzZXInXG4gKi9cblxuaW1wb3J0IHtjcmVhdGVQYXJzZXJ9IGZyb20gJ2Nzcy1zZWxlY3Rvci1wYXJzZXInXG5cbmNvbnN0IGNzc1NlbGVjdG9yUGFyc2UgPSBjcmVhdGVQYXJzZXIoe3N5bnRheDogJ3NlbGVjdG9ycy00J30pXG5cbi8qKlxuICogQHBhcmFtIHtzdHJpbmd9IHNlbGVjdG9yXG4gKiAgIFNlbGVjdG9yIHRvIHBhcnNlLlxuICogQHJldHVybnMge0FzdFNlbGVjdG9yfVxuICogICBQYXJzZWQgc2VsZWN0b3IuXG4gKi9cbmV4cG9ydCBmdW5jdGlvbiBwYXJzZShzZWxlY3Rvcikge1xuICBpZiAodHlwZW9mIHNlbGVjdG9yICE9PSAnc3RyaW5nJykge1xuICAgIHRocm93IG5ldyBUeXBlRXJyb3IoJ0V4cGVjdGVkIGBzdHJpbmdgIGFzIHNlbGVjdG9yLCBub3QgYCcgKyBzZWxlY3RvciArICdgJylcbiAgfVxuXG4gIHJldHVybiBjc3NTZWxlY3RvclBhcnNlKHNlbGVjdG9yKVxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/parse.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/pseudo.js":
/*!*****************************************************!*\
  !*** ./node_modules/hast-util-select/lib/pseudo.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   pseudo: () => (/* binding */ pseudo)\n/* harmony export */ });\n/* harmony import */ var bcp_47_match__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! bcp-47-match */ \"(ssr)/./node_modules/bcp-47-match/index.js\");\n/* harmony import */ var comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! comma-separated-tokens */ \"(ssr)/./node_modules/comma-separated-tokens/index.js\");\n/* harmony import */ var devlop__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! devlop */ \"(ssr)/./node_modules/devlop/lib/development.js\");\n/* harmony import */ var hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-has-property */ \"(ssr)/./node_modules/hast-util-has-property/lib/index.js\");\n/* harmony import */ var hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-whitespace */ \"(ssr)/./node_modules/hast-util-whitespace/lib/index.js\");\n/* harmony import */ var nth_check__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! nth-check */ \"(ssr)/./node_modules/nth-check/lib/esm/index.js\");\n/* harmony import */ var zwitch__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! zwitch */ \"(ssr)/./node_modules/zwitch/index.js\");\n/* harmony import */ var _walk_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./walk.js */ \"(ssr)/./node_modules/hast-util-select/lib/walk.js\");\n/**\n * @import {AstPseudoClass} from 'css-selector-parser'\n * @import {default as NthCheck} from 'nth-check'\n * @import {ElementContent, Element, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n\n\n\n\n\n\n\n\n\n/** @type {NthCheck} */\n// @ts-expect-error: types are broken.\nconst nthCheck = nth_check__WEBPACK_IMPORTED_MODULE_0__[\"default\"][\"default\"] || nth_check__WEBPACK_IMPORTED_MODULE_0__[\"default\"]\n\n/** @type {(rule: AstPseudoClass, element: Element, index: number | undefined, parent: Parents | undefined, state: State) => boolean} */\nconst pseudo = (0,zwitch__WEBPACK_IMPORTED_MODULE_1__.zwitch)('name', {\n  handlers: {\n    'any-link': anyLink,\n    blank,\n    checked,\n    dir,\n    disabled,\n    empty,\n    enabled,\n    'first-child': firstChild,\n    'first-of-type': firstOfType,\n    has,\n    is,\n    lang,\n    'last-child': lastChild,\n    'last-of-type': lastOfType,\n    not,\n    'nth-child': nthChild,\n    'nth-last-child': nthLastChild,\n    'nth-last-of-type': nthLastOfType,\n    'nth-of-type': nthOfType,\n    'only-child': onlyChild,\n    'only-of-type': onlyOfType,\n    optional,\n    'read-only': readOnly,\n    'read-write': readWrite,\n    required,\n    root,\n    scope\n  },\n  invalid: invalidPseudo,\n  unknown: unknownPseudo\n})\n\n/**\n * Check whether an element matches an `:any-link` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction anyLink(_, element) {\n  return (\n    (element.tagName === 'a' ||\n      element.tagName === 'area' ||\n      element.tagName === 'link') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'href')\n  )\n}\n\n/**\n * @param {State} state\n *   State.\n * @param {AstPseudoClass} query\n *   Query.\n */\nfunction assertDeep(state, query) {\n  if (state.shallow) {\n    throw new Error('Cannot use `:' + query.name + '` without parent')\n  }\n}\n\n/**\n * Check whether an element matches a `:blank` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction blank(_, element) {\n  return !someChildren(element, check)\n\n  /**\n   * @param {ElementContent} child\n   * @returns {boolean}\n   */\n  function check(child) {\n    return (\n      child.type === 'element' || (child.type === 'text' && !(0,hast_util_whitespace__WEBPACK_IMPORTED_MODULE_3__.whitespace)(child))\n    )\n  }\n}\n\n/**\n * Check whether an element matches a `:checked` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction checked(_, element) {\n  if (element.tagName === 'input' || element.tagName === 'menuitem') {\n    return Boolean(\n      (element.properties.type === 'checkbox' ||\n        element.properties.type === 'radio') &&\n        (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'checked')\n    )\n  }\n\n  if (element.tagName === 'option') {\n    return (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'selected')\n  }\n\n  return false\n}\n\n/**\n * Check whether an element matches a `:dir()` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\n// eslint-disable-next-line unicorn/prevent-abbreviations\nfunction dir(query, _1, _2, _3, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'String', 'expected plain text')\n  return state.direction === query.argument.value\n}\n\n/**\n * Check whether an element matches a `:disabled` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction disabled(_, element) {\n  return (\n    (element.tagName === 'button' ||\n      element.tagName === 'input' ||\n      element.tagName === 'select' ||\n      element.tagName === 'textarea' ||\n      element.tagName === 'optgroup' ||\n      element.tagName === 'option' ||\n      element.tagName === 'menuitem' ||\n      element.tagName === 'fieldset') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'disabled')\n  )\n}\n\n/**\n * Check whether an element matches an `:empty` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction empty(_, element) {\n  return !someChildren(element, check)\n\n  /**\n   * @param {ElementContent} child\n   * @returns {boolean}\n   */\n  function check(child) {\n    return child.type === 'element' || child.type === 'text'\n  }\n}\n\n/**\n * Check whether an element matches an `:enabled` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction enabled(query, element) {\n  return !disabled(query, element)\n}\n\n/**\n * Check whether an element matches a `:first-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction firstChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.elementIndex === 0\n}\n\n/**\n * Check whether an element matches a `:first-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction firstOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.typeIndex === 0\n}\n\n/**\n * @param {AstPseudoClass} query\n *   Query.\n * @returns {(value: number) => boolean}\n *   N.\n */\nfunction getCachedNthCheck(query) {\n  /** @type {(value: number) => boolean} */\n  // @ts-expect-error: cache.\n  let cachedFunction = query._cachedFn\n\n  if (!cachedFunction) {\n    const value = query.argument\n    ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(value, 'expected `argument`')\n\n    if (value.type !== 'Formula') {\n      throw new Error(\n        'Expected `nth` formula, such as `even` or `2n+1` (`of` is not yet supported)'\n      )\n    }\n\n    cachedFunction = nthCheck(value.a + 'n+' + value.b)\n    // @ts-expect-error: cache.\n    query._cachedFn = cachedFunction\n  }\n\n  return cachedFunction\n}\n\n/**\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction has(query, element, _1, _2, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'Selector', 'expected selector')\n\n  /** @type {State} */\n  const childState = {\n    ...state,\n    // Not found yet.\n    found: false,\n    // One result is enough.\n    one: true,\n    results: [],\n    rootQuery: query.argument,\n    scopeElements: [element],\n    // Do walk deep.\n    shallow: false\n  }\n\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_5__.walk)(childState, {type: 'root', children: element.children})\n\n  return childState.results.length > 0\n}\n\n// Shouldn’t be called, parser gives correct data.\n/* c8 ignore next 3 */\nfunction invalidPseudo() {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.unreachable)('Invalid pseudo-selector')\n}\n\n/**\n * Check whether an element `:is` further selectors.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction is(query, element, _1, _2, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'Selector', 'expected selector')\n\n  /** @type {State} */\n  const childState = {\n    ...state,\n    // Not found yet.\n    found: false,\n    // One result is enough.\n    one: true,\n    results: [],\n    rootQuery: query.argument,\n    scopeElements: [element],\n    // Do walk deep.\n    shallow: false\n  }\n\n  ;(0,_walk_js__WEBPACK_IMPORTED_MODULE_5__.walk)(childState, element)\n\n  return childState.results[0] === element\n}\n\n/**\n * Check whether an element matches a `:lang()` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lang(query, _1, _2, _3, state) {\n  (0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument, 'expected `argument`')\n  ;(0,devlop__WEBPACK_IMPORTED_MODULE_4__.ok)(query.argument.type === 'String', 'expected string')\n\n  return (\n    state.language !== '' &&\n    state.language !== undefined &&\n    (0,bcp_47_match__WEBPACK_IMPORTED_MODULE_6__.extendedFilter)(state.language, (0,comma_separated_tokens__WEBPACK_IMPORTED_MODULE_7__.parse)(query.argument.value)).length > 0\n  )\n}\n\n/**\n * Check whether an element matches a `:last-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lastChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return Boolean(\n    state.elementCount && state.elementIndex === state.elementCount - 1\n  )\n}\n\n/**\n * Check whether an element matches a `:last-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction lastOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return (\n    typeof state.typeIndex === 'number' &&\n    typeof state.typeCount === 'number' &&\n    state.typeIndex === state.typeCount - 1\n  )\n}\n\n/**\n * Check whether an element does `:not` match further selectors.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction not(query, element, index, parent, state) {\n  return !is(query, element, index, parent, state)\n}\n\n/**\n * Check whether an element matches an `:nth-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthChild(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return (\n    typeof state.elementIndex === 'number' && cachedFunction(state.elementIndex)\n  )\n}\n\n/**\n * Check whether an element matches an `:nth-last-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthLastChild(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return Boolean(\n    typeof state.elementCount === 'number' &&\n      typeof state.elementIndex === 'number' &&\n      cachedFunction(state.elementCount - state.elementIndex - 1)\n  )\n}\n\n/**\n * Check whether an element matches a `:nth-last-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthLastOfType(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return (\n    typeof state.typeCount === 'number' &&\n    typeof state.typeIndex === 'number' &&\n    cachedFunction(state.typeCount - 1 - state.typeIndex)\n  )\n}\n\n/**\n * Check whether an element matches an `:nth-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction nthOfType(query, _1, _2, _3, state) {\n  const cachedFunction = getCachedNthCheck(query)\n  assertDeep(state, query)\n  return typeof state.typeIndex === 'number' && cachedFunction(state.typeIndex)\n}\n\n/**\n * Check whether an element matches an `:only-child` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction onlyChild(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.elementCount === 1\n}\n\n/**\n * Check whether an element matches an `:only-of-type` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} _1\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction onlyOfType(query, _1, _2, _3, state) {\n  assertDeep(state, query)\n  return state.typeCount === 1\n}\n\n/**\n * Check whether an element matches an `:optional` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction optional(query, element) {\n  return !required(query, element)\n}\n\n/**\n * Check whether an element matches a `:read-only` pseudo.\n *\n * @param {AstPseudoClass} query\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction readOnly(query, element, index, parent, state) {\n  return !readWrite(query, element, index, parent, state)\n}\n\n/**\n * Check whether an element matches a `:read-write` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _1\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _2\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction readWrite(_, element, _1, _2, state) {\n  return element.tagName === 'input' || element.tagName === 'textarea'\n    ? !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'readOnly') && !(0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'disabled')\n    : Boolean(state.editableOrEditingHost)\n}\n\n/**\n * Check whether an element matches a `:required` pseudo.\n *\n * @param {AstPseudoClass} _\n *   Query.\n * @param {Element} element\n *   Element.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction required(_, element) {\n  return (\n    (element.tagName === 'input' ||\n      element.tagName === 'textarea' ||\n      element.tagName === 'select') &&\n    (0,hast_util_has_property__WEBPACK_IMPORTED_MODULE_2__.hasProperty)(element, 'required')\n  )\n}\n\n/**\n * Check whether an element matches a `:root` pseudo.\n *\n * @param {AstPseudoClass} _1\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction root(_1, element, _2, parent, state) {\n  return Boolean(\n    (!parent || parent.type === 'root') &&\n      state.schema &&\n      (state.schema.space === 'html' || state.schema.space === 'svg') &&\n      (element.tagName === 'html' || element.tagName === 'svg')\n  )\n}\n\n/**\n * Check whether an element matches a `:scope` pseudo.\n *\n * @param {AstPseudoClass} _1\n *   Query.\n * @param {Element} element\n *   Element.\n * @param {number | undefined} _2\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} _3\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction scope(_1, element, _2, _3, state) {\n  return state.scopeElements.includes(element)\n}\n\n/**\n * Check children.\n *\n * @param {Element} element\n *   Element.\n * @param {(child: ElementContent) => boolean} check\n *   Check.\n * @returns {boolean}\n *   Whether a child of `element` matches `check`.\n */\nfunction someChildren(element, check) {\n  const children = element.children\n  let index = -1\n\n  while (++index < children.length) {\n    if (check(children[index])) return true\n  }\n\n  return false\n}\n\n/**\n * @param {unknown} query_\n *   Query-like value.\n * @returns {never}\n *   Nothing.\n * @throws\n *   Exception.\n */\nfunction unknownPseudo(query_) {\n  // Runtime JS guarantees it has a `name`.\n  const query = /** @type {AstPseudoClass} */ (query_)\n  throw new Error('Unknown pseudo-selector `' + query.name + '`')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/pseudo.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/test.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/test.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   test: () => (/* binding */ test)\n/* harmony export */ });\n/* harmony import */ var _attribute_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./attribute.js */ \"(ssr)/./node_modules/hast-util-select/lib/attribute.js\");\n/* harmony import */ var _class_name_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./class-name.js */ \"(ssr)/./node_modules/hast-util-select/lib/class-name.js\");\n/* harmony import */ var _id_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./id.js */ \"(ssr)/./node_modules/hast-util-select/lib/id.js\");\n/* harmony import */ var _name_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./name.js */ \"(ssr)/./node_modules/hast-util-select/lib/name.js\");\n/* harmony import */ var _pseudo_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./pseudo.js */ \"(ssr)/./node_modules/hast-util-select/lib/pseudo.js\");\n/**\n * @import {AstRule} from 'css-selector-parser'\n * @import {Element, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n\n\n\n\n\n\n/**\n * Test a rule.\n *\n * @param {AstRule} query\n *   AST rule (with `pseudoClasses`).\n * @param {Element} element\n *   Element.\n * @param {number | undefined} index\n *   Index of `element` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `element`.\n * @param {State} state\n *   State.\n * @returns {boolean}\n *   Whether `element` matches `query`.\n */\nfunction test(query, element, index, parent, state) {\n  for (const item of query.items) {\n    // eslint-disable-next-line unicorn/prefer-switch\n    if (item.type === 'Attribute') {\n      if (!(0,_attribute_js__WEBPACK_IMPORTED_MODULE_0__.attribute)(item, element, state.schema)) return false\n    } else if (item.type === 'Id') {\n      if (!(0,_id_js__WEBPACK_IMPORTED_MODULE_1__.id)(item, element)) return false\n    } else if (item.type === 'ClassName') {\n      if (!(0,_class_name_js__WEBPACK_IMPORTED_MODULE_2__.className)(item, element)) return false\n    } else if (item.type === 'PseudoClass') {\n      if (!(0,_pseudo_js__WEBPACK_IMPORTED_MODULE_3__.pseudo)(item, element, index, parent, state)) return false\n    } else if (item.type === 'PseudoElement') {\n      throw new Error('Invalid selector: `::' + item.name + '`')\n    } else if (item.type === 'TagName') {\n      if (!(0,_name_js__WEBPACK_IMPORTED_MODULE_4__.name)(item, element)) return false\n    } else {\n      // Otherwise `item.type` is `WildcardTag`, which matches.\n    }\n  }\n\n  return true\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/test.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/hast-util-select/lib/walk.js":
/*!***************************************************!*\
  !*** ./node_modules/hast-util-select/lib/walk.js ***!
  \***************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   walk: () => (/* binding */ walk)\n/* harmony export */ });\n/* harmony import */ var _enter_state_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./enter-state.js */ \"(ssr)/./node_modules/hast-util-select/lib/enter-state.js\");\n/* harmony import */ var _test_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./test.js */ \"(ssr)/./node_modules/hast-util-select/lib/test.js\");\n/**\n * @import {AstRule} from 'css-selector-parser'\n * @import {Element, Nodes, Parents} from 'hast'\n * @import {State} from './index.js'\n */\n\n/**\n * @typedef Counts\n *   Info on elements in a parent.\n * @property {number} count\n *   Number of elements.\n * @property {Map<string, number>} types\n *   Number of elements by tag name.\n *\n * @typedef Nest\n *   Rule sets by nesting.\n * @property {Array<AstRule> | undefined} adjacentSibling\n *   `a + b`\n * @property {Array<AstRule> | undefined} descendant\n *   `a b`\n * @property {Array<AstRule> | undefined} directChild\n *   `a > b`\n * @property {Array<AstRule> | undefined} generalSibling\n *   `a ~ b`\n */\n\n\n\n\n/** @type {Array<never>} */\nconst empty = []\n\n/**\n * Walk a tree.\n *\n * @param {State} state\n *   State.\n * @param {Nodes | undefined} tree\n *   Tree.\n */\nfunction walk(state, tree) {\n  if (tree) {\n    one(state, [], tree, undefined, undefined, tree)\n  }\n}\n\n/**\n * Add a rule to a nesting map.\n *\n * @param {Nest} nest\n *   Nesting.\n * @param {keyof Nest} field\n *   Field.\n * @param {AstRule} rule\n *   Rule.\n */\nfunction add(nest, field, rule) {\n  const list = nest[field]\n  if (list) {\n    list.push(rule)\n  } else {\n    nest[field] = [rule]\n  }\n}\n\n/**\n * Check in a parent.\n *\n * @param {State} state\n *   State.\n * @param {Nest} nest\n *   Nesting.\n * @param {Parents} node\n *   Parent.\n * @param {Nodes} tree\n *   Tree.\n * @returns {undefined}\n *   Nothing.\n */\nfunction all(state, nest, node, tree) {\n  const fromParent = combine(nest.descendant, nest.directChild)\n  /** @type {Array<AstRule> | undefined} */\n  let fromSibling\n  let index = -1\n  /**\n   * Total counts.\n   * @type {Counts}\n   */\n  const total = {count: 0, types: new Map()}\n  /**\n   * Counts of previous siblings.\n   * @type {Counts}\n   */\n  const before = {count: 0, types: new Map()}\n\n  while (++index < node.children.length) {\n    count(total, node.children[index])\n  }\n\n  index = -1\n\n  while (++index < node.children.length) {\n    const child = node.children[index]\n    // Uppercase to prevent prototype polution, injecting `constructor` or so.\n    // Normalize because HTML is insensitive.\n    const name =\n      child.type === 'element' ? child.tagName.toUpperCase() : undefined\n    // Before counting further elements:\n    state.elementIndex = before.count\n    state.typeIndex = name ? before.types.get(name) || 0 : 0\n    // After counting all elements.\n    state.elementCount = total.count\n    state.typeCount = name ? total.types.get(name) : 0\n\n    // Only apply if this is a parent, this should be an element, but we check\n    // for parents so that we delve into custom nodes too.\n    if ('children' in child) {\n      const forSibling = combine(fromParent, fromSibling)\n      const nest = one(\n        state,\n        forSibling,\n        node.children[index],\n        index,\n        node,\n        tree\n      )\n      fromSibling = combine(nest.generalSibling, nest.adjacentSibling)\n    }\n\n    // We found one thing, and one is enough.\n    if (state.one && state.found) {\n      break\n    }\n\n    count(before, node.children[index])\n  }\n}\n\n/**\n * Apply selectors to an element.\n *\n * @param {State} state\n *   Current state.\n * @param {Array<AstRule>} rules\n *   Rules to apply.\n * @param {Element} node\n *   Element to apply rules to.\n * @param {number | undefined} index\n *   Index of `node` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @returns {Nest}\n *   Further rules.\n */\nfunction applySelectors(state, rules, node, index, parent) {\n  /** @type {Nest} */\n  const nestResult = {\n    adjacentSibling: undefined,\n    descendant: undefined,\n    directChild: undefined,\n    generalSibling: undefined\n  }\n  let selectorIndex = -1\n\n  while (++selectorIndex < rules.length) {\n    const rule = rules[selectorIndex]\n\n    // We found one thing, and one is enough.\n    if (state.one && state.found) {\n      break\n    }\n\n    // When shallow, we don’t allow nested rules.\n    // Idea: we could allow a stack of parents?\n    // Might get quite complex though.\n    if (state.shallow && rule.nestedRule) {\n      throw new Error('Expected selector without nesting')\n    }\n\n    // If this rule matches:\n    if ((0,_test_js__WEBPACK_IMPORTED_MODULE_0__.test)(rule, node, index, parent, state)) {\n      const nest = rule.nestedRule\n\n      // Are there more?\n      if (nest) {\n        /** @type {keyof Nest} */\n        const label =\n          nest.combinator === '+'\n            ? 'adjacentSibling'\n            : nest.combinator === '~'\n              ? 'generalSibling'\n              : nest.combinator === '>'\n                ? 'directChild'\n                : 'descendant'\n        add(nestResult, label, nest)\n      } else {\n        // We have a match!\n        state.found = true\n\n        if (!state.results.includes(node)) {\n          state.results.push(node)\n        }\n      }\n    }\n\n    // Descendant.\n    if (rule.combinator === undefined) {\n      add(nestResult, 'descendant', rule)\n    }\n    // Adjacent.\n    else if (rule.combinator === '~') {\n      add(nestResult, 'generalSibling', rule)\n    }\n    // Drop direct child (`>`), adjacent sibling (`+`).\n  }\n\n  return nestResult\n}\n\n/**\n * Combine two lists, if needed.\n *\n * This is optimized to create as few lists as possible.\n *\n * @param {Array<AstRule> | undefined} left\n *   Rules.\n * @param {Array<AstRule> | undefined} right\n *   Rules.\n * @returns {Array<AstRule>}\n *   Rules.\n */\nfunction combine(left, right) {\n  return left && right && left.length > 0 && right.length > 0\n    ? [...left, ...right]\n    : left && left.length > 0\n      ? left\n      : right && right.length > 0\n        ? right\n        : empty\n}\n\n/**\n * Count a node.\n *\n * @param {Counts} counts\n *   Counts.\n * @param {Nodes} node\n *   Node (we’re looking for elements).\n * @returns {undefined}\n *   Nothing.\n */\nfunction count(counts, node) {\n  if (node.type === 'element') {\n    // Uppercase to prevent prototype polution, injecting `constructor` or so.\n    // Normalize because HTML is insensitive.\n    const name = node.tagName.toUpperCase()\n    const count = (counts.types.get(name) || 0) + 1\n    counts.count++\n    counts.types.set(name, count)\n  }\n}\n\n/**\n * Check a node.\n *\n * @param {State} state\n *   State.\n * @param {Array<AstRule>} currentRules\n *   Rules.\n * @param {Nodes} node\n *   Node.\n * @param {number | undefined} index\n *   Index of `node` in `parent`.\n * @param {Parents | undefined} parent\n *   Parent of `node`.\n * @param {Nodes} tree\n *   Tree.\n * @returns {Nest}\n *   Nesting.\n */\nfunction one(state, currentRules, node, index, parent, tree) {\n  /** @type {Nest} */\n  let nestResult = {\n    adjacentSibling: undefined,\n    descendant: undefined,\n    directChild: undefined,\n    generalSibling: undefined\n  }\n\n  const exit = (0,_enter_state_js__WEBPACK_IMPORTED_MODULE_1__.enterState)(state, node)\n\n  if (node.type === 'element') {\n    let rootRules = state.rootQuery.rules\n\n    // Remove direct child rules if this is the root.\n    // This only happens for a `:has()` rule, which can be like\n    // `a:has(> b)`.\n    if (parent && parent !== tree) {\n      rootRules = state.rootQuery.rules.filter(\n        (d) =>\n          d.combinator === undefined ||\n          (d.combinator === '>' && parent === tree)\n      )\n    }\n\n    nestResult = applySelectors(\n      state,\n      // Try the root rules for this element too.\n      combine(currentRules, rootRules),\n      node,\n      index,\n      parent\n    )\n  }\n\n  // If this is a parent, and we want to delve into them, and we haven’t found\n  // our single result yet.\n  if ('children' in node && !state.shallow && !(state.one && state.found)) {\n    all(state, nestResult, node, tree)\n  }\n\n  exit()\n\n  return nestResult\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/hast-util-select/lib/walk.js\n");

/***/ })

};
;