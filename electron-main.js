const { app, BrowserWindow, ipcMain, shell, dialog } = require('electron');

// 檢查 Electron 是否正確導入
if (!app) {
  console.error('Electron app module not found. Please ensure Electron is properly installed.');
  process.exit(1);
}
const path = require('path');
const fs = require('fs').promises;
const sqlite3 = require('sqlite3').verbose();

let db;
let mainWindow;

async function createWindow() {
  const { default: isDev } = await import('electron-is-dev');

  mainWindow = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      preload: path.join(__dirname, 'electron-preload.js'),
      nodeIntegration: false,
      contextIsolation: true,
    },
  });

  if (isDev) {
    mainWindow.loadURL('http://localhost:3000');
    mainWindow.webContents.openDevTools();
  } else {
    mainWindow.loadFile(path.join(__dirname, 'renderer/out/index.html'));
  }

  mainWindow.webContents.on('new-window', (event, url) => {
    event.preventDefault();
    const popupWindow = new BrowserWindow({
      width: 800,
      height: 600,
      parent: mainWindow,
      webPreferences: {
        nodeIntegration: false,
        contextIsolation: true,
      }
    });
    popupWindow.loadURL(url);
  });
}

app.whenReady().then(async () => {
  // 初始化 SQLite 資料庫
  const dbPath = path.join(app.getPath('userData'), 'notes.sqlite');
  db = new sqlite3.Database(dbPath, (err) => {
    if (err) {
      console.error('資料庫連接失敗:', err.message);
    } else {
      console.log('已連接到 SQLite 資料庫');
      db.serialize(() => {
        db.run(`CREATE TABLE IF NOT EXISTS notes (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT NOT NULL,
          content TEXT,
          createdAt TEXT DEFAULT CURRENT_TIMESTAMP,
          updatedAt TEXT DEFAULT CURRENT_TIMESTAMP
        )`);

        // 檢查 'tags' 欄位是否存在
        db.all("PRAGMA table_info(notes)", (err, columns) => {
          if (err) {
            console.error("無法讀取資料表資訊:", err);
            return;
          }
          
          const hasTagsColumn = columns.some(column => column.name === 'tags');
          
          if (!hasTagsColumn) {
            console.log("正在新增 'tags' 欄位到 'notes' 資料表...");
            db.run("ALTER TABLE notes ADD COLUMN tags TEXT", (alterErr) => {
              if (alterErr) {
                console.error("新增 'tags' 欄位失敗:", alterErr);
              } else {
                console.log("'tags' 欄位已成功新增");
              }
            });
          }
        });
      });
    }
  });

  await createWindow();

  // IPC 處理器
  ipcMain.handle('get-notes', async () => {
    return new Promise((resolve, reject) => {
      db.all('SELECT * FROM notes ORDER BY updatedAt DESC', (err, rows) => {
        if (err) {
          reject(err);
        } else {
          const notes = rows.map(note => ({
            ...note,
            tags: note.tags ? JSON.parse(note.tags) : [],
          }));
          resolve(notes);
        }
      });
    });
  });

  ipcMain.handle('get-note', async (event, id) => {
    return new Promise((resolve, reject) => {
      db.get('SELECT * FROM notes WHERE id = ?', [id], (err, row) => {
        if (err) {
          reject(err);
        } else {
          if (row) {
            row.tags = row.tags ? JSON.parse(row.tags) : [];
          }
          resolve(row || null);
        }
      });
    });
  });

  ipcMain.handle('create-note', async (event, { title, content, tags }) => {
    return new Promise((resolve, reject) => {
      const tagsString = JSON.stringify(tags);
      db.run('INSERT INTO notes (title, content, tags) VALUES (?, ?, ?)', [title, content, tagsString], function(err) {
        if (err) {
          reject(err);
        } else {
          db.get('SELECT * FROM notes WHERE id = ?', [this.lastID], (err, row) => {
            if (err) {
              reject(err);
            } else {
              if (row) {
                row.tags = row.tags ? JSON.parse(row.tags) : [];
              }
              resolve(row);
            }
          });
        }
      });
    });
  });

  ipcMain.handle('update-note', async (event, { id, title, content, tags }) => {
    return new Promise((resolve, reject) => {
      const tagsString = JSON.stringify(tags);
      db.run('UPDATE notes SET title = ?, content = ?, tags = ?, updatedAt = CURRENT_TIMESTAMP WHERE id = ?', [title, content, tagsString, id], function(err) {
        if (err) {
          reject(err);
        } else {
          db.get('SELECT * FROM notes WHERE id = ?', [id], (err, row) => {
            if (err) {
              reject(err);
            } else {
              if (row) {
                row.tags = row.tags ? JSON.parse(row.tags) : [];
              }
              resolve(row);
            }
          });
        }
      });
    });
  });

  ipcMain.handle('delete-note', async (event, id) => {
    return new Promise((resolve, reject) => {
      db.run('DELETE FROM notes WHERE id = ?', [id], function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ success: true, message: '筆記已刪除' });
        }
      });
    });
  });

 // Export data handler
 ipcMain.handle('export-data', async () => {
   try {
     const result = await dialog.showSaveDialog(mainWindow, {
       title: '匯出筆記資料',
       defaultPath: `mynote-backup-${new Date().toISOString().split('T')[0]}.json`,
       filters: [
         { name: 'JSON Files', extensions: ['json'] }
       ]
     });

     if (result.canceled) {
       return { success: false, message: '用戶取消了匯出操作' };
     }

     // Get all notes from database
     const notes = await new Promise((resolve, reject) => {
       db.all('SELECT * FROM notes ORDER BY updatedAt DESC', (err, rows) => {
         if (err) {
           reject(err);
         } else {
           const notes = rows.map(note => ({
             ...note,
             tags: note.tags ? JSON.parse(note.tags) : [],
           }));
           resolve(notes);
         }
       });
     });

     // Get settings from renderer process
     const settings = await mainWindow.webContents.executeJavaScript(`
       ({
         gemini_api_key: localStorage.getItem("gemini_api_key") || "",
         openrouter_api_key: localStorage.getItem("openrouter_api_key") || "",
         ai_provider: localStorage.getItem("ai_provider") || "gemini",
         gemini_model: localStorage.getItem("gemini_model") || "gemini-1.5-pro-latest",
         openrouter_model: localStorage.getItem("openrouter_model") || "mistralai/mistral-7b-instruct",
         locale: localStorage.getItem("locale") || "zh"
       })
     `);

     const exportData = {
       version: '1.0',
       exportDate: new Date().toISOString(),
       notes: notes,
       settings: settings
     };

     await fs.writeFile(result.filePath, JSON.stringify(exportData, null, 2), 'utf8');

     return {
       success: true,
       message: `資料已成功匯出到 ${result.filePath}`,
       filePath: result.filePath
     };
   } catch (error) {
     console.error('Export error:', error);
     return {
       success: false,
       message: `匯出失敗: ${error.message}`
     };
   }
 });

 // Import data handler
 ipcMain.handle('import-data', async () => {
   try {
     const result = await dialog.showOpenDialog(mainWindow, {
       title: '匯入筆記資料',
       filters: [
         { name: 'JSON Files', extensions: ['json'] }
       ],
       properties: ['openFile']
     });

     if (result.canceled) {
       return { success: false, message: '用戶取消了匯入操作' };
     }

     const filePath = result.filePaths[0];
     const fileContent = await fs.readFile(filePath, 'utf8');
     const importData = JSON.parse(fileContent);

     // Validate import data structure
     if (!importData.notes || !Array.isArray(importData.notes)) {
       throw new Error('無效的匯入檔案格式');
     }

     // Clear existing notes
     await new Promise((resolve, reject) => {
       db.run('DELETE FROM notes', (err) => {
         if (err) {
           reject(err);
         } else {
           resolve();
         }
       });
     });

     // Import notes
     for (const note of importData.notes) {
       await new Promise((resolve, reject) => {
         const tagsString = JSON.stringify(note.tags || []);
         db.run(
           'INSERT INTO notes (title, content, tags, createdAt, updatedAt) VALUES (?, ?, ?, ?, ?)',
           [note.title, note.content, tagsString, note.createdAt, note.updatedAt],
           function(err) {
             if (err) {
               reject(err);
             } else {
               resolve();
             }
           }
         );
       });
     }

     return {
       success: true,
       message: `資料匯入成功！已還原 ${importData.notes.length} 筆筆記和相關應用程式設定。應用程式將會重新啟動以套用變更。`,
       importedCount: importData.notes.length,
       settings: importData.settings || {}
     };
   } catch (error) {
     console.error('Import error:', error);
     return {
       success: false,
       message: `匯入失敗: ${error.message}`
     };
   }
 });

  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
   }
 });
 app.on('window-all-closed', () => {
   if (process.platform !== 'darwin') {
     app.quit();
   }
   if (db) {
     db.close();
   }
 });
});