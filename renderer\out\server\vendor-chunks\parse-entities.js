"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/parse-entities";
exports.ids = ["vendor-chunks/parse-entities"];
exports.modules = {

/***/ "(ssr)/./node_modules/parse-entities/lib/index.js":
/*!**************************************************!*\
  !*** ./node_modules/parse-entities/lib/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   parseEntities: () => (/* binding */ parseEntities)\n/* harmony export */ });\n/* harmony import */ var character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! character-entities-legacy */ \"(ssr)/./node_modules/character-entities-legacy/index.js\");\n/* harmony import */ var character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! character-reference-invalid */ \"(ssr)/./node_modules/character-reference-invalid/index.js\");\n/* harmony import */ var is_decimal__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! is-decimal */ \"(ssr)/./node_modules/is-decimal/index.js\");\n/* harmony import */ var is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! is-hexadecimal */ \"(ssr)/./node_modules/is-hexadecimal/index.js\");\n/* harmony import */ var is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! is-alphanumerical */ \"(ssr)/./node_modules/is-alphanumerical/index.js\");\n/* harmony import */ var decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! decode-named-character-reference */ \"(ssr)/./node_modules/decode-named-character-reference/index.js\");\n/**\n * @import {Point} from 'unist'\n * @import {Options} from '../index.js'\n */\n\n\n\n\n\n\n\n\n// Warning messages.\nconst messages = [\n  '',\n  /* 1: Non terminated (named) */\n  'Named character references must be terminated by a semicolon',\n  /* 2: Non terminated (numeric) */\n  'Numeric character references must be terminated by a semicolon',\n  /* 3: Empty (named) */\n  'Named character references cannot be empty',\n  /* 4: Empty (numeric) */\n  'Numeric character references cannot be empty',\n  /* 5: Unknown (named) */\n  'Named character references must be known',\n  /* 6: Disallowed (numeric) */\n  'Numeric character references cannot be disallowed',\n  /* 7: Prohibited (numeric) */\n  'Numeric character references cannot be outside the permissible Unicode range'\n]\n\n/**\n * Parse HTML character references.\n *\n * @param {string} value\n * @param {Readonly<Options> | null | undefined} [options]\n */\nfunction parseEntities(value, options) {\n  const settings = options || {}\n  const additional =\n    typeof settings.additional === 'string'\n      ? settings.additional.charCodeAt(0)\n      : settings.additional\n  /** @type {Array<string>} */\n  const result = []\n  let index = 0\n  let lines = -1\n  let queue = ''\n  /** @type {Point | undefined} */\n  let point\n  /** @type {Array<number>|undefined} */\n  let indent\n\n  if (settings.position) {\n    if ('start' in settings.position || 'indent' in settings.position) {\n      // @ts-expect-error: points don’t have indent.\n      indent = settings.position.indent\n      // @ts-expect-error: points don’t have indent.\n      point = settings.position.start\n    } else {\n      point = settings.position\n    }\n  }\n\n  let line = (point ? point.line : 0) || 1\n  let column = (point ? point.column : 0) || 1\n\n  // Cache the current point.\n  let previous = now()\n  /** @type {number|undefined} */\n  let character\n\n  // Ensure the algorithm walks over the first character (inclusive).\n  index--\n\n  while (++index <= value.length) {\n    // If the previous character was a newline.\n    if (character === 10 /* `\\n` */) {\n      column = (indent ? indent[lines] : 0) || 1\n    }\n\n    character = value.charCodeAt(index)\n\n    if (character === 38 /* `&` */) {\n      const following = value.charCodeAt(index + 1)\n\n      // The behavior depends on the identity of the next character.\n      if (\n        following === 9 /* `\\t` */ ||\n        following === 10 /* `\\n` */ ||\n        following === 12 /* `\\f` */ ||\n        following === 32 /* ` ` */ ||\n        following === 38 /* `&` */ ||\n        following === 60 /* `<` */ ||\n        Number.isNaN(following) ||\n        (additional && following === additional)\n      ) {\n        // Not a character reference.\n        // No characters are consumed, and nothing is returned.\n        // This is not an error, either.\n        queue += String.fromCharCode(character)\n        column++\n        continue\n      }\n\n      const start = index + 1\n      let begin = start\n      let end = start\n      /** @type {string} */\n      let type\n\n      if (following === 35 /* `#` */) {\n        // Numerical reference.\n        end = ++begin\n\n        // The behavior further depends on the next character.\n        const following = value.charCodeAt(end)\n\n        if (following === 88 /* `X` */ || following === 120 /* `x` */) {\n          // ASCII hexadecimal digits.\n          type = 'hexadecimal'\n          end = ++begin\n        } else {\n          // ASCII decimal digits.\n          type = 'decimal'\n        }\n      } else {\n        // Named reference.\n        type = 'named'\n      }\n\n      let characterReferenceCharacters = ''\n      let characterReference = ''\n      let characters = ''\n      // Each type of character reference accepts different characters.\n      // This test is used to detect whether a reference has ended (as the semicolon\n      // is not strictly needed).\n      const test =\n        type === 'named'\n          ? is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical\n          : type === 'decimal'\n            ? is_decimal__WEBPACK_IMPORTED_MODULE_1__.isDecimal\n            : is_hexadecimal__WEBPACK_IMPORTED_MODULE_2__.isHexadecimal\n\n      end--\n\n      while (++end <= value.length) {\n        const following = value.charCodeAt(end)\n\n        if (!test(following)) {\n          break\n        }\n\n        characters += String.fromCharCode(following)\n\n        // Check if we can match a legacy named reference.\n        // If so, we cache that as the last viable named reference.\n        // This ensures we do not need to walk backwards later.\n        if (type === 'named' && character_entities_legacy__WEBPACK_IMPORTED_MODULE_3__.characterEntitiesLegacy.includes(characters)) {\n          characterReferenceCharacters = characters\n          // @ts-expect-error: always able to decode.\n          characterReference = (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters)\n        }\n      }\n\n      let terminated = value.charCodeAt(end) === 59 /* `;` */\n\n      if (terminated) {\n        end++\n\n        const namedReference =\n          type === 'named' ? (0,decode_named_character_reference__WEBPACK_IMPORTED_MODULE_4__.decodeNamedCharacterReference)(characters) : false\n\n        if (namedReference) {\n          characterReferenceCharacters = characters\n          characterReference = namedReference\n        }\n      }\n\n      let diff = 1 + end - start\n      let reference = ''\n\n      if (!terminated && settings.nonTerminated === false) {\n        // Empty.\n      } else if (!characters) {\n        // An empty (possible) reference is valid, unless it’s numeric (thus an\n        // ampersand followed by an octothorp).\n        if (type !== 'named') {\n          warning(4 /* Empty (numeric) */, diff)\n        }\n      } else if (type === 'named') {\n        // An ampersand followed by anything unknown, and not terminated, is\n        // invalid.\n        if (terminated && !characterReference) {\n          warning(5 /* Unknown (named) */, 1)\n        } else {\n          // If there’s something after an named reference which is not known,\n          // cap the reference.\n          if (characterReferenceCharacters !== characters) {\n            end = begin + characterReferenceCharacters.length\n            diff = 1 + end - begin\n            terminated = false\n          }\n\n          // If the reference is not terminated, warn.\n          if (!terminated) {\n            const reason = characterReferenceCharacters\n              ? 1 /* Non terminated (named) */\n              : 3 /* Empty (named) */\n\n            if (settings.attribute) {\n              const following = value.charCodeAt(end)\n\n              if (following === 61 /* `=` */) {\n                warning(reason, diff)\n                characterReference = ''\n              } else if ((0,is_alphanumerical__WEBPACK_IMPORTED_MODULE_0__.isAlphanumerical)(following)) {\n                characterReference = ''\n              } else {\n                warning(reason, diff)\n              }\n            } else {\n              warning(reason, diff)\n            }\n          }\n        }\n\n        reference = characterReference\n      } else {\n        if (!terminated) {\n          // All nonterminated numeric references are not rendered, and emit a\n          // warning.\n          warning(2 /* Non terminated (numeric) */, diff)\n        }\n\n        // When terminated and numerical, parse as either hexadecimal or\n        // decimal.\n        let referenceCode = Number.parseInt(\n          characters,\n          type === 'hexadecimal' ? 16 : 10\n        )\n\n        // Emit a warning when the parsed number is prohibited, and replace with\n        // replacement character.\n        if (prohibited(referenceCode)) {\n          warning(7 /* Prohibited (numeric) */, diff)\n          reference = String.fromCharCode(65533 /* `�` */)\n        } else if (referenceCode in character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid) {\n          // Emit a warning when the parsed number is disallowed, and replace by\n          // an alternative.\n          warning(6 /* Disallowed (numeric) */, diff)\n          reference = character_reference_invalid__WEBPACK_IMPORTED_MODULE_5__.characterReferenceInvalid[referenceCode]\n        } else {\n          // Parse the number.\n          let output = ''\n\n          // Emit a warning when the parsed number should not be used.\n          if (disallowed(referenceCode)) {\n            warning(6 /* Disallowed (numeric) */, diff)\n          }\n\n          // Serialize the number.\n          if (referenceCode > 0xffff) {\n            referenceCode -= 0x10000\n            output += String.fromCharCode(\n              (referenceCode >>> (10 & 0x3ff)) | 0xd800\n            )\n            referenceCode = 0xdc00 | (referenceCode & 0x3ff)\n          }\n\n          reference = output + String.fromCharCode(referenceCode)\n        }\n      }\n\n      // Found it!\n      // First eat the queued characters as normal text, then eat a reference.\n      if (reference) {\n        flush()\n\n        previous = now()\n        index = end - 1\n        column += end - start + 1\n        result.push(reference)\n        const next = now()\n        next.offset++\n\n        if (settings.reference) {\n          settings.reference.call(\n            settings.referenceContext || undefined,\n            reference,\n            {start: previous, end: next},\n            value.slice(start - 1, end)\n          )\n        }\n\n        previous = next\n      } else {\n        // If we could not find a reference, queue the checked characters (as\n        // normal characters), and move the pointer to their end.\n        // This is possible because we can be certain neither newlines nor\n        // ampersands are included.\n        characters = value.slice(start - 1, end)\n        queue += characters\n        column += characters.length\n        index = end - 1\n      }\n    } else {\n      // Handle anything other than an ampersand, including newlines and EOF.\n      if (character === 10 /* `\\n` */) {\n        line++\n        lines++\n        column = 0\n      }\n\n      if (Number.isNaN(character)) {\n        flush()\n      } else {\n        queue += String.fromCharCode(character)\n        column++\n      }\n    }\n  }\n\n  // Return the reduced nodes.\n  return result.join('')\n\n  // Get current position.\n  function now() {\n    return {\n      line,\n      column,\n      offset: index + ((point ? point.offset : 0) || 0)\n    }\n  }\n\n  /**\n   * Handle the warning.\n   *\n   * @param {1|2|3|4|5|6|7} code\n   * @param {number} offset\n   */\n  function warning(code, offset) {\n    /** @type {ReturnType<now>} */\n    let position\n\n    if (settings.warning) {\n      position = now()\n      position.column += offset\n      position.offset += offset\n\n      settings.warning.call(\n        settings.warningContext || undefined,\n        messages[code],\n        position,\n        code\n      )\n    }\n  }\n\n  /**\n   * Flush `queue` (normal text).\n   * Macro invoked before each reference and at the end of `value`.\n   * Does nothing when `queue` is empty.\n   */\n  function flush() {\n    if (queue) {\n      result.push(queue)\n\n      if (settings.text) {\n        settings.text.call(settings.textContext || undefined, queue, {\n          start: previous,\n          end: now()\n        })\n      }\n\n      queue = ''\n    }\n  }\n}\n\n/**\n * Check if `character` is outside the permissible unicode range.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction prohibited(code) {\n  return (code >= 0xd800 && code <= 0xdfff) || code > 0x10ffff\n}\n\n/**\n * Check if `character` is disallowed.\n *\n * @param {number} code\n * @returns {boolean}\n */\nfunction disallowed(code) {\n  return (\n    (code >= 0x0001 && code <= 0x0008) ||\n    code === 0x000b ||\n    (code >= 0x000d && code <= 0x001f) ||\n    (code >= 0x007f && code <= 0x009f) ||\n    (code >= 0xfdd0 && code <= 0xfdef) ||\n    (code & 0xffff) === 0xffff ||\n    (code & 0xffff) === 0xfffe\n  )\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/parse-entities/lib/index.js\n");

/***/ })

};
;