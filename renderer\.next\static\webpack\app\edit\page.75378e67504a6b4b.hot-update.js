"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 272,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 260,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，提供簡單的 textarea 編輯器\n    if (!CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"簡易編輯器 (Cherry Markdown 載入失敗)\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 291,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                    value: value,\n                    onChange: (e)=>onChange && onChange(e.target.value),\n                    style: {\n                        flex: 1,\n                        border: \"none\",\n                        outline: \"none\",\n                        padding: \"16px\",\n                        resize: \"none\",\n                        backgroundColor: \"transparent\",\n                        color: \"hsl(var(--foreground))\",\n                        fontFamily: \"monospace\",\n                        fontSize: \"14px\",\n                        lineHeight: \"1.5\"\n                    },\n                    placeholder: \"在此輸入 Markdown 內容...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 294,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 280,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 316,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});