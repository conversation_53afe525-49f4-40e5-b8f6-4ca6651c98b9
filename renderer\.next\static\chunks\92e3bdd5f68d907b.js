(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,3083,(e,r,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"warnOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},96496,e=>{"use strict";e.s(["Card",()=>d,"CardContent",()=>o,"CardDescription",()=>n,"CardFooter",()=>i,"CardHeader",()=>l,"CardTitle",()=>s]);var r=e.i(67857),t=e.i(24302),a=e.i(92072);let d=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",d),...l})});d.displayName="Card";let l=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",d),...l})});l.displayName="CardHeader";let s=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",d),...l})});s.displayName="CardTitle";let n=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",d),...l})});n.displayName="CardDescription";let o=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",d),...l})});o.displayName="CardContent";let i=t.forwardRef((e,t)=>{let{className:d,...l}=e;return(0,r.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",d),...l})});i.displayName="CardFooter"}]);