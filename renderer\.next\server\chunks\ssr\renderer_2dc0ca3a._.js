module.exports=[1905,a=>{"use strict";a.s(["I18nProvider",()=>h,"useI18n",()=>i]);var b=a.i(28386),c=a.i(54436),d=a.i(29369),e=a.i(20104);let f={en:d.default,zh:e.default},g=(0,c.createContext)(void 0);function h({children:a}){let[d,e]=(0,c.useState)("zh");(0,c.useEffect)(()=>{let a=localStorage.getItem("locale");a&&f[a]&&e(a)},[]);let h=(0,c.useCallback)(a=>{let b=a.split("."),c=f[d];for(let d of b)if(void 0===(c=c?.[d])){let c=f.en;for(let a of b)c=c?.[a];return c||a}return c||a},[d]);return(0,b.jsx)(g.Provider,{value:{locale:d,setLocale:a=>{e(a),localStorage.setItem("locale",a)},t:h},children:a})}function i(){let a=(0,c.useContext)(g);if(void 0===a)throw Error("useI18n must be used within an I18nProvider");return a}},3332,a=>{"use strict";a.s(["toast",()=>i,"useToast",()=>j]);var b=a.i(54436);let c=0,d=new Map,e=a=>{if(d.has(a))return;let b=setTimeout(()=>{d.delete(a),h({type:"REMOVE_TOAST",toastId:a})},1e6);d.set(a,b)},f=[],g={toasts:[]};function h(a){g=((a,b)=>{switch(b.type){case"ADD_TOAST":return{...a,toasts:[b.toast,...a.toasts].slice(0,1)};case"UPDATE_TOAST":return{...a,toasts:a.toasts.map(a=>a.id===b.toast.id?{...a,...b.toast}:a)};case"DISMISS_TOAST":{let{toastId:c}=b;return c?e(c):a.toasts.forEach(a=>{e(a.id)}),{...a,toasts:a.toasts.map(a=>a.id===c||void 0===c?{...a,open:!1}:a)}}case"REMOVE_TOAST":if(void 0===b.toastId)return{...a,toasts:[]};return{...a,toasts:a.toasts.filter(a=>a.id!==b.toastId)}}})(g,a),f.forEach(a=>{a(g)})}function i({...a}){let b=(c=(c+1)%Number.MAX_SAFE_INTEGER).toString(),d=()=>h({type:"DISMISS_TOAST",toastId:b});return h({type:"ADD_TOAST",toast:{...a,id:b,open:!0,onOpenChange:a=>{a||d()}}}),{id:b,dismiss:d,update:a=>h({type:"UPDATE_TOAST",toast:{...a,id:b}})}}function j(){let[a,c]=b.useState(g);return b.useEffect(()=>(f.push(c),()=>{let a=f.indexOf(c);a>-1&&f.splice(a,1)}),[a]),{...a,toast:i,dismiss:a=>h({type:"DISMISS_TOAST",toastId:a})}}},64161,a=>{"use strict";function b(a,c,{checkForDefaultPrevented:d=!0}={}){return function(b){if(a?.(b),!1===d||!b.defaultPrevented)return c?.(b)}}a.s(["composeEventHandlers",()=>b])},42527,a=>{"use strict";a.s(["composeRefs",()=>d,"useComposedRefs",()=>e]);var b=a.i(54436);function c(a,b){if("function"==typeof a)return a(b);null!=a&&(a.current=b)}function d(...a){return b=>{let d=!1,e=a.map(a=>{let e=c(a,b);return d||"function"!=typeof e||(d=!0),e});if(d)return()=>{for(let b=0;b<e.length;b++){let d=e[b];"function"==typeof d?d():c(a[b],null)}}}}function e(...a){return b.useCallback(d(...a),a)}},33616,a=>{"use strict";a.s(["createContext",()=>d,"createContextScope",()=>e]);var b=a.i(54436),c=a.i(28386);function d(a,d){let e=b.createContext(d),f=a=>{let{children:d,...f}=a,g=b.useMemo(()=>f,Object.values(f));return(0,c.jsx)(e.Provider,{value:g,children:d})};return f.displayName=a+"Provider",[f,function(c){let f=b.useContext(e);if(f)return f;if(void 0!==d)return d;throw Error(`\`${c}\` must be used within \`${a}\``)}]}function e(a,d=[]){let f=[],g=()=>{let c=f.map(a=>b.createContext(a));return function(d){let e=d?.[a]||c;return b.useMemo(()=>({[`__scope${a}`]:{...d,[a]:e}}),[d,e])}};return g.scopeName=a,[function(d,e){let g=b.createContext(e),h=f.length;f=[...f,e];let i=d=>{let{scope:e,children:f,...i}=d,j=e?.[a]?.[h]||g,k=b.useMemo(()=>i,Object.values(i));return(0,c.jsx)(j.Provider,{value:k,children:f})};return i.displayName=d+"Provider",[i,function(c,f){let i=f?.[a]?.[h]||g,j=b.useContext(i);if(j)return j;if(void 0!==e)return e;throw Error(`\`${c}\` must be used within \`${d}\``)}]},function(...a){let c=a[0];if(1===a.length)return c;let d=()=>{let d=a.map(a=>({useScope:a(),scopeName:a.scopeName}));return function(a){let e=d.reduce((b,{useScope:c,scopeName:d})=>{let e=c(a)[`__scope${d}`];return{...b,...e}},{});return b.useMemo(()=>({[`__scope${c.scopeName}`]:e}),[e])}};return d.scopeName=c.scopeName,d}(g,...d)]}},57495,a=>{"use strict";a.s(["Slot",()=>f,"createSlot",()=>e,"createSlottable",()=>h]);var b=a.i(54436),c=a.i(42527),d=a.i(28386);function e(a){let e=function(a){let d=b.forwardRef((a,d)=>{let{children:e,...f}=a;if(b.isValidElement(e)){var g;let a,h,i=(g=e,(h=(a=Object.getOwnPropertyDescriptor(g.props,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.ref:(h=(a=Object.getOwnPropertyDescriptor(g,"ref")?.get)&&"isReactWarning"in a&&a.isReactWarning)?g.props.ref:g.props.ref||g.ref),j=function(a,b){let c={...b};for(let d in b){let e=a[d],f=b[d];/^on[A-Z]/.test(d)?e&&f?c[d]=(...a)=>{let b=f(...a);return e(...a),b}:e&&(c[d]=e):"style"===d?c[d]={...e,...f}:"className"===d&&(c[d]=[e,f].filter(Boolean).join(" "))}return{...a,...c}}(f,e.props);return e.type!==b.Fragment&&(j.ref=d?(0,c.composeRefs)(d,i):i),b.cloneElement(e,j)}return b.Children.count(e)>1?b.Children.only(null):null});return d.displayName=`${a}.SlotClone`,d}(a),f=b.forwardRef((a,c)=>{let{children:f,...g}=a,h=b.Children.toArray(f),j=h.find(i);if(j){let a=j.props.children,f=h.map(c=>c!==j?c:b.Children.count(a)>1?b.Children.only(null):b.isValidElement(a)?a.props.children:null);return(0,d.jsx)(e,{...g,ref:c,children:b.isValidElement(a)?b.cloneElement(a,void 0,f):null})}return(0,d.jsx)(e,{...g,ref:c,children:f})});return f.displayName=`${a}.Slot`,f}var f=e("Slot"),g=Symbol("radix.slottable");function h(a){let b=({children:a})=>(0,d.jsx)(d.Fragment,{children:a});return b.displayName=`${a}.Slottable`,b.__radixId=g,b}function i(a){return b.isValidElement(a)&&"function"==typeof a.type&&"__radixId"in a.type&&a.type.__radixId===g}},94120,a=>{"use strict";a.s(["createCollection",()=>g]);var b=a.i(54436),c=a.i(33616),d=a.i(42527),e=a.i(57495),f=a.i(28386);function g(a){let g=a+"CollectionProvider",[h,i]=(0,c.createContextScope)(g),[j,k]=h(g,{collectionRef:{current:null},itemMap:new Map}),l=a=>{let{scope:c,children:d}=a,e=b.default.useRef(null),g=b.default.useRef(new Map).current;return(0,f.jsx)(j,{scope:c,itemMap:g,collectionRef:e,children:d})};l.displayName=g;let m=a+"CollectionSlot",n=(0,e.createSlot)(m),o=b.default.forwardRef((a,b)=>{let{scope:c,children:e}=a,g=k(m,c),h=(0,d.useComposedRefs)(b,g.collectionRef);return(0,f.jsx)(n,{ref:h,children:e})});o.displayName=m;let p=a+"CollectionItemSlot",q="data-radix-collection-item",r=(0,e.createSlot)(p),s=b.default.forwardRef((a,c)=>{let{scope:e,children:g,...h}=a,i=b.default.useRef(null),j=(0,d.useComposedRefs)(c,i),l=k(p,e);return b.default.useEffect(()=>(l.itemMap.set(i,{ref:i,...h}),()=>void l.itemMap.delete(i))),(0,f.jsx)(r,{...{[q]:""},ref:j,children:g})});return s.displayName=p,[{Provider:l,Slot:o,ItemSlot:s},function(c){let d=k(a+"CollectionConsumer",c);return b.default.useCallback(()=>{let a=d.collectionRef.current;if(!a)return[];let b=Array.from(a.querySelectorAll(`[${q}]`));return Array.from(d.itemMap.values()).sort((a,c)=>b.indexOf(a.ref.current)-b.indexOf(c.ref.current))},[d.collectionRef,d.itemMap])},i]}var h=new WeakMap;function i(a,b){if("at"in Array.prototype)return Array.prototype.at.call(a,b);let c=function(a,b){let c=a.length,d=j(b),e=d>=0?d:c+d;return e<0||e>=c?-1:e}(a,b);return -1===c?void 0:a[c]}function j(a){return a!=a||0===a?0:Math.trunc(a)}(class a extends Map{#a;constructor(a){super(a),this.#a=[...super.keys()],h.set(this,!0)}set(a,b){return h.get(this)&&(this.has(a)?this.#a[this.#a.indexOf(a)]=a:this.#a.push(a)),super.set(a,b),this}insert(a,b,c){let d,e=this.has(b),f=this.#a.length,g=j(a),h=g>=0?g:f+g,i=h<0||h>=f?-1:h;if(i===this.size||e&&i===this.size-1||-1===i)return this.set(b,c),this;let k=this.size+ +!e;g<0&&h++;let l=[...this.#a],m=!1;for(let a=h;a<k;a++)if(h===a){let f=l[a];l[a]===b&&(f=l[a+1]),e&&this.delete(b),d=this.get(f),this.set(b,c)}else{m||l[a-1]!==b||(m=!0);let c=l[m?a:a-1],e=d;d=this.get(c),this.delete(c),this.set(c,e)}return this}with(b,c,d){let e=new a(this);return e.insert(b,c,d),e}before(a){let b=this.#a.indexOf(a)-1;if(!(b<0))return this.entryAt(b)}setBefore(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d,b,c)}after(a){let b=this.#a.indexOf(a);if(-1!==(b=-1===b||b===this.size-1?-1:b+1))return this.entryAt(b)}setAfter(a,b,c){let d=this.#a.indexOf(a);return -1===d?this:this.insert(d+1,b,c)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return this.#a=[],super.clear()}delete(a){let b=super.delete(a);return b&&this.#a.splice(this.#a.indexOf(a),1),b}deleteAt(a){let b=this.keyAt(a);return void 0!==b&&this.delete(b)}at(a){let b=i(this.#a,a);if(void 0!==b)return this.get(b)}entryAt(a){let b=i(this.#a,a);if(void 0!==b)return[b,this.get(b)]}indexOf(a){return this.#a.indexOf(a)}keyAt(a){return i(this.#a,a)}from(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.at(d)}keyFrom(a,b){let c=this.indexOf(a);if(-1===c)return;let d=c+b;return d<0&&(d=0),d>=this.size&&(d=this.size-1),this.keyAt(d)}find(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return d;c++}}findIndex(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return c;c++}return -1}filter(b,c){let d=[],e=0;for(let a of this)Reflect.apply(b,c,[a,e,this])&&d.push(a),e++;return new a(d)}map(b,c){let d=[],e=0;for(let a of this)d.push([a[0],Reflect.apply(b,c,[a,e,this])]),e++;return new a(d)}reduce(...a){let[b,c]=a,d=0,e=c??this.at(0);for(let c of this)e=0===d&&1===a.length?c:Reflect.apply(b,this,[e,c,d,this]),d++;return e}reduceRight(...a){let[b,c]=a,d=c??this.at(-1);for(let c=this.size-1;c>=0;c--){let e=this.at(c);d=c===this.size-1&&1===a.length?e:Reflect.apply(b,this,[d,e,c,this])}return d}toSorted(b){return new a([...this.entries()].sort(b))}toReversed(){let b=new a;for(let a=this.size-1;a>=0;a--){let c=this.keyAt(a),d=this.get(c);b.set(c,d)}return b}toSpliced(...b){let c=[...this.entries()];return c.splice(...b),new a(c)}slice(b,c){let d=new a,e=this.size-1;if(void 0===b)return d;b<0&&(b+=this.size),void 0!==c&&c>0&&(e=c-1);for(let a=b;a<=e;a++){let b=this.keyAt(a),c=this.get(b);d.set(b,c)}return d}every(a,b){let c=0;for(let d of this){if(!Reflect.apply(a,b,[d,c,this]))return!1;c++}return!0}some(a,b){let c=0;for(let d of this){if(Reflect.apply(a,b,[d,c,this]))return!0;c++}return!1}})},40686,a=>{"use strict";a.s(["Primitive",()=>f,"dispatchDiscreteCustomEvent",()=>g]);var b=a.i(54436),c=a.i(27444),d=a.i(57495),e=a.i(28386),f=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((a,c)=>{let f=(0,d.createSlot)(`Primitive.${c}`),g=b.forwardRef((a,b)=>{let{asChild:d,...g}=a;return(0,e.jsx)(d?f:c,{...g,ref:b})});return g.displayName=`Primitive.${c}`,{...a,[c]:g}},{});function g(a,b){a&&c.flushSync(()=>a.dispatchEvent(b))}},98445,a=>{"use strict";a.s(["useCallbackRef",()=>c]);var b=a.i(54436);function c(a){let c=b.useRef(a);return b.useEffect(()=>{c.current=a}),b.useMemo(()=>(...a)=>c.current?.(...a),[])}},28432,a=>{"use strict";a.s(["Branch",()=>p,"DismissableLayer",()=>k,"Root",()=>o],28432);var b,c=a.i(54436),d=a.i(64161),e=a.i(40686),f=a.i(42527),g=a.i(98445),h=a.i(28386),i="dismissableLayer.update",j=c.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),k=c.forwardRef((a,k)=>{let{disableOutsidePointerEvents:l=!1,onEscapeKeyDown:o,onPointerDownOutside:p,onFocusOutside:q,onInteractOutside:r,onDismiss:s,...t}=a,u=c.useContext(j),[v,w]=c.useState(null),x=v?.ownerDocument??globalThis?.document,[,y]=c.useState({}),z=(0,f.useComposedRefs)(k,a=>w(a)),A=Array.from(u.layers),[B]=[...u.layersWithOutsidePointerEventsDisabled].slice(-1),C=A.indexOf(B),D=v?A.indexOf(v):-1,E=u.layersWithOutsidePointerEventsDisabled.size>0,F=D>=C,G=function(a,b=globalThis?.document){let d=(0,g.useCallbackRef)(a),e=c.useRef(!1),f=c.useRef(()=>{});return c.useEffect(()=>{let a=a=>{if(a.target&&!e.current){let c=function(){n("dismissableLayer.pointerDownOutside",d,e,{discrete:!0})},e={originalEvent:a};"touch"===a.pointerType?(b.removeEventListener("click",f.current),f.current=c,b.addEventListener("click",f.current,{once:!0})):c()}else b.removeEventListener("click",f.current);e.current=!1},c=window.setTimeout(()=>{b.addEventListener("pointerdown",a)},0);return()=>{window.clearTimeout(c),b.removeEventListener("pointerdown",a),b.removeEventListener("click",f.current)}},[b,d]),{onPointerDownCapture:()=>e.current=!0}}(a=>{let b=a.target,c=[...u.branches].some(a=>a.contains(b));F&&!c&&(p?.(a),r?.(a),a.defaultPrevented||s?.())},x),H=function(a,b=globalThis?.document){let d=(0,g.useCallbackRef)(a),e=c.useRef(!1);return c.useEffect(()=>{let a=a=>{a.target&&!e.current&&n("dismissableLayer.focusOutside",d,{originalEvent:a},{discrete:!1})};return b.addEventListener("focusin",a),()=>b.removeEventListener("focusin",a)},[b,d]),{onFocusCapture:()=>e.current=!0,onBlurCapture:()=>e.current=!1}}(a=>{let b=a.target;![...u.branches].some(a=>a.contains(b))&&(q?.(a),r?.(a),a.defaultPrevented||s?.())},x);return!function(a,b=globalThis?.document){let d=(0,g.useCallbackRef)(a);c.useEffect(()=>{let a=a=>{"Escape"===a.key&&d(a)};return b.addEventListener("keydown",a,{capture:!0}),()=>b.removeEventListener("keydown",a,{capture:!0})},[d,b])}(a=>{D===u.layers.size-1&&(o?.(a),!a.defaultPrevented&&s&&(a.preventDefault(),s()))},x),c.useEffect(()=>{if(v)return l&&(0===u.layersWithOutsidePointerEventsDisabled.size&&(b=x.body.style.pointerEvents,x.body.style.pointerEvents="none"),u.layersWithOutsidePointerEventsDisabled.add(v)),u.layers.add(v),m(),()=>{l&&1===u.layersWithOutsidePointerEventsDisabled.size&&(x.body.style.pointerEvents=b)}},[v,x,l,u]),c.useEffect(()=>()=>{v&&(u.layers.delete(v),u.layersWithOutsidePointerEventsDisabled.delete(v),m())},[v,u]),c.useEffect(()=>{let a=()=>y({});return document.addEventListener(i,a),()=>document.removeEventListener(i,a)},[]),(0,h.jsx)(e.Primitive.div,{...t,ref:z,style:{pointerEvents:E?F?"auto":"none":void 0,...a.style},onFocusCapture:(0,d.composeEventHandlers)(a.onFocusCapture,H.onFocusCapture),onBlurCapture:(0,d.composeEventHandlers)(a.onBlurCapture,H.onBlurCapture),onPointerDownCapture:(0,d.composeEventHandlers)(a.onPointerDownCapture,G.onPointerDownCapture)})});k.displayName="DismissableLayer";var l=c.forwardRef((a,b)=>{let d=c.useContext(j),g=c.useRef(null),i=(0,f.useComposedRefs)(b,g);return c.useEffect(()=>{let a=g.current;if(a)return d.branches.add(a),()=>{d.branches.delete(a)}},[d.branches]),(0,h.jsx)(e.Primitive.div,{...a,ref:i})});function m(){let a=new CustomEvent(i);document.dispatchEvent(a)}function n(a,b,c,{discrete:d}){let f=c.originalEvent.target,g=new CustomEvent(a,{bubbles:!1,cancelable:!0,detail:c});b&&f.addEventListener(a,b,{once:!0}),d?(0,e.dispatchDiscreteCustomEvent)(f,g):f.dispatchEvent(g)}l.displayName="DismissableLayerBranch";var o=k,p=l},64249,a=>{"use strict";a.s(["useLayoutEffect",()=>c]);var b=a.i(54436),c=globalThis?.document?b.useLayoutEffect:()=>{}},62705,a=>{"use strict";a.s(["Portal",()=>g]);var b=a.i(54436),c=a.i(27444),d=a.i(40686),e=a.i(64249),f=a.i(28386),g=b.forwardRef((a,g)=>{let{container:h,...i}=a,[j,k]=b.useState(!1);(0,e.useLayoutEffect)(()=>k(!0),[]);let l=h||j&&globalThis?.document?.body;return l?c.default.createPortal((0,f.jsx)(d.Primitive.div,{...i,ref:g}),l):null});g.displayName="Portal"},15209,a=>{"use strict";a.s(["Presence",()=>e]);var b=a.i(54436),c=a.i(42527),d=a.i(64249),e=a=>{let{present:e,children:g}=a,h=function(a){var c,e;let[g,h]=b.useState(),i=b.useRef(null),j=b.useRef(a),k=b.useRef("none"),[l,m]=(c=a?"mounted":"unmounted",e={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},b.useReducer((a,b)=>e[a][b]??a,c));return b.useEffect(()=>{let a=f(i.current);k.current="mounted"===l?a:"none"},[l]),(0,d.useLayoutEffect)(()=>{let b=i.current,c=j.current;if(c!==a){let d=k.current,e=f(b);a?m("MOUNT"):"none"===e||b?.display==="none"?m("UNMOUNT"):c&&d!==e?m("ANIMATION_OUT"):m("UNMOUNT"),j.current=a}},[a,m]),(0,d.useLayoutEffect)(()=>{if(g){let a,b=g.ownerDocument.defaultView??window,c=c=>{let d=f(i.current).includes(CSS.escape(c.animationName));if(c.target===g&&d&&(m("ANIMATION_END"),!j.current)){let c=g.style.animationFillMode;g.style.animationFillMode="forwards",a=b.setTimeout(()=>{"forwards"===g.style.animationFillMode&&(g.style.animationFillMode=c)})}},d=a=>{a.target===g&&(k.current=f(i.current))};return g.addEventListener("animationstart",d),g.addEventListener("animationcancel",c),g.addEventListener("animationend",c),()=>{b.clearTimeout(a),g.removeEventListener("animationstart",d),g.removeEventListener("animationcancel",c),g.removeEventListener("animationend",c)}}m("ANIMATION_END")},[g,m]),{isPresent:["mounted","unmountSuspended"].includes(l),ref:b.useCallback(a=>{i.current=a?getComputedStyle(a):null,h(a)},[])}}(e),i="function"==typeof g?g({present:h.isPresent}):b.Children.only(g),j=(0,c.useComposedRefs)(h.ref,function(a){let b=Object.getOwnPropertyDescriptor(a.props,"ref")?.get,c=b&&"isReactWarning"in b&&b.isReactWarning;return c?a.ref:(c=(b=Object.getOwnPropertyDescriptor(a,"ref")?.get)&&"isReactWarning"in b&&b.isReactWarning)?a.props.ref:a.props.ref||a.ref}(i));return"function"==typeof g||h.isPresent?b.cloneElement(i,{ref:j}):null};function f(a){return a?.animationName||"none"}e.displayName="Presence"},9851,a=>{"use strict";a.s(["useControllableState",()=>e],9851);var b=a.i(54436),c=a.i(64249);b[" useEffectEvent ".trim().toString()],b[" useInsertionEffect ".trim().toString()];var d=b[" useInsertionEffect ".trim().toString()]||c.useLayoutEffect;function e({prop:a,defaultProp:c,onChange:e=()=>{},caller:f}){let[g,h,i]=function({defaultProp:a,onChange:c}){let[e,f]=b.useState(a),g=b.useRef(e),h=b.useRef(c);return d(()=>{h.current=c},[c]),b.useEffect(()=>{g.current!==e&&(h.current?.(e),g.current=e)},[e,g]),[e,f,h]}({defaultProp:c,onChange:e}),j=void 0!==a,k=j?a:g;{let c=b.useRef(void 0!==a);b.useEffect(()=>{let a=c.current;if(a!==j){let b=j?"controlled":"uncontrolled";console.warn(`${f} is changing from ${a?"controlled":"uncontrolled"} to ${b}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}c.current=j},[j,f])}return[k,b.useCallback(b=>{if(j){let c="function"==typeof b?b(a):b;c!==a&&i.current?.(c)}else h(b)},[j,a,h,i])]}Symbol("RADIX:SYNC_STATE")},80761,57703,a=>{"use strict";a.s(["Action",()=>aa,"Close",()=>ab,"Description",()=>_,"Provider",()=>X,"Root",()=>Z,"Title",()=>$,"Viewport",()=>Y],80761);var b=a.i(54436),c=a.i(27444),d=a.i(64161),e=a.i(42527),f=a.i(94120),g=a.i(33616),h=a.i(28432),i=a.i(62705),j=a.i(15209),k=a.i(40686),l=a.i(98445),m=a.i(9851),n=a.i(64249);a.s(["VISUALLY_HIDDEN_STYLES",()=>p,"VisuallyHidden",()=>q],57703);var o=a.i(28386),p=Object.freeze({position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal"}),q=b.forwardRef((a,b)=>(0,o.jsx)(k.Primitive.span,{...a,ref:b,style:{...p,...a.style}}));q.displayName="VisuallyHidden";var r="ToastProvider",[s,t,u]=(0,f.createCollection)("Toast"),[v,w]=(0,g.createContextScope)("Toast",[u]),[x,y]=v(r),z=a=>{let{__scopeToast:c,label:d="Notification",duration:e=5e3,swipeDirection:f="right",swipeThreshold:g=50,children:h}=a,[i,j]=b.useState(null),[k,l]=b.useState(0),m=b.useRef(!1),n=b.useRef(!1);return d.trim()||console.error(`Invalid prop \`label\` supplied to \`${r}\`. Expected non-empty \`string\`.`),(0,o.jsx)(s.Provider,{scope:c,children:(0,o.jsx)(x,{scope:c,label:d,duration:e,swipeDirection:f,swipeThreshold:g,toastCount:k,viewport:i,onViewportChange:j,onToastAdd:b.useCallback(()=>l(a=>a+1),[]),onToastRemove:b.useCallback(()=>l(a=>a-1),[]),isFocusedToastEscapeKeyDownRef:m,isClosePausedRef:n,children:h})})};z.displayName=r;var A="ToastViewport",B=["F8"],C="toast.viewportPause",D="toast.viewportResume",E=b.forwardRef((a,c)=>{let{__scopeToast:d,hotkey:f=B,label:g="Notifications ({hotkey})",...i}=a,j=y(A,d),l=t(d),m=b.useRef(null),n=b.useRef(null),p=b.useRef(null),q=b.useRef(null),r=(0,e.useComposedRefs)(c,q,j.onViewportChange),u=f.join("+").replace(/Key/g,"").replace(/Digit/g,""),v=j.toastCount>0;b.useEffect(()=>{let a=a=>{0!==f.length&&f.every(b=>a[b]||a.code===b)&&q.current?.focus()};return document.addEventListener("keydown",a),()=>document.removeEventListener("keydown",a)},[f]),b.useEffect(()=>{let a=m.current,b=q.current;if(v&&a&&b){let c=()=>{if(!j.isClosePausedRef.current){let a=new CustomEvent(C);b.dispatchEvent(a),j.isClosePausedRef.current=!0}},d=()=>{if(j.isClosePausedRef.current){let a=new CustomEvent(D);b.dispatchEvent(a),j.isClosePausedRef.current=!1}},e=b=>{a.contains(b.relatedTarget)||d()},f=()=>{a.contains(document.activeElement)||d()};return a.addEventListener("focusin",c),a.addEventListener("focusout",e),a.addEventListener("pointermove",c),a.addEventListener("pointerleave",f),window.addEventListener("blur",c),window.addEventListener("focus",d),()=>{a.removeEventListener("focusin",c),a.removeEventListener("focusout",e),a.removeEventListener("pointermove",c),a.removeEventListener("pointerleave",f),window.removeEventListener("blur",c),window.removeEventListener("focus",d)}}},[v,j.isClosePausedRef]);let w=b.useCallback(({tabbingDirection:a})=>{let b=l().map(b=>{let c=b.ref.current,d=[c,...function(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}(c)];return"forwards"===a?d:d.reverse()});return("forwards"===a?b.reverse():b).flat()},[l]);return b.useEffect(()=>{let a=q.current;if(a){let b=b=>{let c=b.altKey||b.ctrlKey||b.metaKey;if("Tab"===b.key&&!c){let c=document.activeElement,d=b.shiftKey;if(b.target===a&&d)return void n.current?.focus();let e=w({tabbingDirection:d?"backwards":"forwards"}),f=e.findIndex(a=>a===c);W(e.slice(f+1))?b.preventDefault():d?n.current?.focus():p.current?.focus()}};return a.addEventListener("keydown",b),()=>a.removeEventListener("keydown",b)}},[l,w]),(0,o.jsxs)(h.Branch,{ref:m,role:"region","aria-label":g.replace("{hotkey}",u),tabIndex:-1,style:{pointerEvents:v?void 0:"none"},children:[v&&(0,o.jsx)(G,{ref:n,onFocusFromOutsideViewport:()=>{W(w({tabbingDirection:"forwards"}))}}),(0,o.jsx)(s.Slot,{scope:d,children:(0,o.jsx)(k.Primitive.ol,{tabIndex:-1,...i,ref:r})}),v&&(0,o.jsx)(G,{ref:p,onFocusFromOutsideViewport:()=>{W(w({tabbingDirection:"backwards"}))}})]})});E.displayName=A;var F="ToastFocusProxy",G=b.forwardRef((a,b)=>{let{__scopeToast:c,onFocusFromOutsideViewport:d,...e}=a,f=y(F,c);return(0,o.jsx)(q,{tabIndex:0,...e,ref:b,style:{position:"fixed"},onFocus:a=>{let b=a.relatedTarget;f.viewport?.contains(b)||d()}})});G.displayName=F;var H="Toast",I=b.forwardRef((a,b)=>{let{forceMount:c,open:e,defaultOpen:f,onOpenChange:g,...h}=a,[i,k]=(0,m.useControllableState)({prop:e,defaultProp:f??!0,onChange:g,caller:H});return(0,o.jsx)(j.Presence,{present:c||i,children:(0,o.jsx)(L,{open:i,...h,ref:b,onClose:()=>k(!1),onPause:(0,l.useCallbackRef)(a.onPause),onResume:(0,l.useCallbackRef)(a.onResume),onSwipeStart:(0,d.composeEventHandlers)(a.onSwipeStart,a=>{a.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,d.composeEventHandlers)(a.onSwipeMove,a=>{let{x:b,y:c}=a.detail.delta;a.currentTarget.setAttribute("data-swipe","move"),a.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${b}px`),a.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${c}px`)}),onSwipeCancel:(0,d.composeEventHandlers)(a.onSwipeCancel,a=>{a.currentTarget.setAttribute("data-swipe","cancel"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),a.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,d.composeEventHandlers)(a.onSwipeEnd,a=>{let{x:b,y:c}=a.detail.delta;a.currentTarget.setAttribute("data-swipe","end"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),a.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),a.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${b}px`),a.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${c}px`),k(!1)})})})});I.displayName=H;var[J,K]=v(H,{onClose(){}}),L=b.forwardRef((a,f)=>{let{__scopeToast:g,type:i="foreground",duration:j,open:m,onClose:n,onEscapeKeyDown:p,onPause:q,onResume:r,onSwipeStart:t,onSwipeMove:u,onSwipeCancel:v,onSwipeEnd:w,...x}=a,z=y(H,g),[A,B]=b.useState(null),E=(0,e.useComposedRefs)(f,a=>B(a)),F=b.useRef(null),G=b.useRef(null),I=j||z.duration,K=b.useRef(0),L=b.useRef(I),N=b.useRef(0),{onToastAdd:O,onToastRemove:P}=z,Q=(0,l.useCallbackRef)(()=>{A?.contains(document.activeElement)&&z.viewport?.focus(),n()}),R=b.useCallback(a=>{a&&a!==1/0&&(window.clearTimeout(N.current),K.current=new Date().getTime(),N.current=window.setTimeout(Q,a))},[Q]);b.useEffect(()=>{let a=z.viewport;if(a){let b=()=>{R(L.current),r?.()},c=()=>{let a=new Date().getTime()-K.current;L.current=L.current-a,window.clearTimeout(N.current),q?.()};return a.addEventListener(C,c),a.addEventListener(D,b),()=>{a.removeEventListener(C,c),a.removeEventListener(D,b)}}},[z.viewport,I,q,r,R]),b.useEffect(()=>{m&&!z.isClosePausedRef.current&&R(I)},[m,I,z.isClosePausedRef,R]),b.useEffect(()=>(O(),()=>P()),[O,P]);let S=b.useMemo(()=>A?function a(b){let c=[];return Array.from(b.childNodes).forEach(b=>{var d;if(b.nodeType===b.TEXT_NODE&&b.textContent&&c.push(b.textContent),(d=b).nodeType===d.ELEMENT_NODE){let d=b.ariaHidden||b.hidden||"none"===b.style.display,e=""===b.dataset.radixToastAnnounceExclude;if(!d)if(e){let a=b.dataset.radixToastAnnounceAlt;a&&c.push(a)}else c.push(...a(b))}}),c}(A):null,[A]);return z.viewport?(0,o.jsxs)(o.Fragment,{children:[S&&(0,o.jsx)(M,{__scopeToast:g,role:"status","aria-live":"foreground"===i?"assertive":"polite",children:S}),(0,o.jsx)(J,{scope:g,onClose:Q,children:c.createPortal((0,o.jsx)(s.ItemSlot,{scope:g,children:(0,o.jsx)(h.Root,{asChild:!0,onEscapeKeyDown:(0,d.composeEventHandlers)(p,()=>{z.isFocusedToastEscapeKeyDownRef.current||Q(),z.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,o.jsx)(k.Primitive.li,{tabIndex:0,"data-state":m?"open":"closed","data-swipe-direction":z.swipeDirection,...x,ref:E,style:{userSelect:"none",touchAction:"none",...a.style},onKeyDown:(0,d.composeEventHandlers)(a.onKeyDown,a=>{"Escape"===a.key&&(p?.(a.nativeEvent),a.nativeEvent.defaultPrevented||(z.isFocusedToastEscapeKeyDownRef.current=!0,Q()))}),onPointerDown:(0,d.composeEventHandlers)(a.onPointerDown,a=>{0===a.button&&(F.current={x:a.clientX,y:a.clientY})}),onPointerMove:(0,d.composeEventHandlers)(a.onPointerMove,a=>{if(!F.current)return;let b=a.clientX-F.current.x,c=a.clientY-F.current.y,d=!!G.current,e=["left","right"].includes(z.swipeDirection),f=["left","up"].includes(z.swipeDirection)?Math.min:Math.max,g=e?f(0,b):0,h=e?0:f(0,c),i="touch"===a.pointerType?10:2,j={x:g,y:h},k={originalEvent:a,delta:j};d?(G.current=j,U("toast.swipeMove",u,k,{discrete:!1})):V(j,z.swipeDirection,i)?(G.current=j,U("toast.swipeStart",t,k,{discrete:!1}),a.target.setPointerCapture(a.pointerId)):(Math.abs(b)>i||Math.abs(c)>i)&&(F.current=null)}),onPointerUp:(0,d.composeEventHandlers)(a.onPointerUp,a=>{let b=G.current,c=a.target;if(c.hasPointerCapture(a.pointerId)&&c.releasePointerCapture(a.pointerId),G.current=null,F.current=null,b){let c=a.currentTarget,d={originalEvent:a,delta:b};V(b,z.swipeDirection,z.swipeThreshold)?U("toast.swipeEnd",w,d,{discrete:!0}):U("toast.swipeCancel",v,d,{discrete:!0}),c.addEventListener("click",a=>a.preventDefault(),{once:!0})}})})})}),z.viewport)})]}):null}),M=a=>{let{__scopeToast:c,children:d,...e}=a,f=y(H,c),[g,h]=b.useState(!1),[j,k]=b.useState(!1);return function(a=()=>{}){let b=(0,l.useCallbackRef)(a);(0,n.useLayoutEffect)(()=>{let a=0,c=0;return a=window.requestAnimationFrame(()=>c=window.requestAnimationFrame(b)),()=>{window.cancelAnimationFrame(a),window.cancelAnimationFrame(c)}},[b])}(()=>h(!0)),b.useEffect(()=>{let a=window.setTimeout(()=>k(!0),1e3);return()=>window.clearTimeout(a)},[]),j?null:(0,o.jsx)(i.Portal,{asChild:!0,children:(0,o.jsx)(q,{...e,children:g&&(0,o.jsxs)(o.Fragment,{children:[f.label," ",d]})})})},N=b.forwardRef((a,b)=>{let{__scopeToast:c,...d}=a;return(0,o.jsx)(k.Primitive.div,{...d,ref:b})});N.displayName="ToastTitle";var O=b.forwardRef((a,b)=>{let{__scopeToast:c,...d}=a;return(0,o.jsx)(k.Primitive.div,{...d,ref:b})});O.displayName="ToastDescription";var P="ToastAction",Q=b.forwardRef((a,b)=>{let{altText:c,...d}=a;return c.trim()?(0,o.jsx)(T,{altText:c,asChild:!0,children:(0,o.jsx)(S,{...d,ref:b})}):(console.error(`Invalid prop \`altText\` supplied to \`${P}\`. Expected non-empty \`string\`.`),null)});Q.displayName=P;var R="ToastClose",S=b.forwardRef((a,b)=>{let{__scopeToast:c,...e}=a,f=K(R,c);return(0,o.jsx)(T,{asChild:!0,children:(0,o.jsx)(k.Primitive.button,{type:"button",...e,ref:b,onClick:(0,d.composeEventHandlers)(a.onClick,f.onClose)})})});S.displayName=R;var T=b.forwardRef((a,b)=>{let{__scopeToast:c,altText:d,...e}=a;return(0,o.jsx)(k.Primitive.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":d||void 0,...e,ref:b})});function U(a,b,c,{discrete:d}){let e=c.originalEvent.currentTarget,f=new CustomEvent(a,{bubbles:!0,cancelable:!0,detail:c});b&&e.addEventListener(a,b,{once:!0}),d?(0,k.dispatchDiscreteCustomEvent)(e,f):e.dispatchEvent(f)}var V=(a,b,c=0)=>{let d=Math.abs(a.x),e=Math.abs(a.y),f=d>e;return"left"===b||"right"===b?f&&d>c:!f&&e>c};function W(a){let b=document.activeElement;return a.some(a=>a===b||(a.focus(),document.activeElement!==b))}var X=z,Y=E,Z=I,$=N,_=O,aa=Q,ab=S},30718,48110,a=>{"use strict";function b(){for(var a,b,c=0,d="",e=arguments.length;c<e;c++)(a=arguments[c])&&(b=function a(b){var c,d,e="";if("string"==typeof b||"number"==typeof b)e+=b;else if("object"==typeof b)if(Array.isArray(b)){var f=b.length;for(c=0;c<f;c++)b[c]&&(d=a(b[c]))&&(e&&(e+=" "),e+=d)}else for(d in b)b[d]&&(e&&(e+=" "),e+=d);return e}(a))&&(d&&(d+=" "),d+=b);return d}a.s(["cva",()=>e],30718),a.s(["clsx",()=>b],48110);let c=a=>"boolean"==typeof a?`${a}`:0===a?"0":a,d=b,e=(a,b)=>e=>{var f;if((null==b?void 0:b.variants)==null)return d(a,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:g,defaultVariants:h}=b,i=Object.keys(g).map(a=>{let b=null==e?void 0:e[a],d=null==h?void 0:h[a];if(null===b)return null;let f=c(b)||c(d);return g[a][f]}),j=e&&Object.entries(e).reduce((a,b)=>{let[c,d]=b;return void 0===d||(a[c]=d),a},{});return d(a,i,null==b||null==(f=b.compoundVariants)?void 0:f.reduce((a,b)=>{let{class:c,className:d,...e}=b;return Object.entries(e).every(a=>{let[b,c]=a;return Array.isArray(c)?c.includes({...h,...j}[b]):({...h,...j})[b]===c})?[...a,c,d]:a},[]),null==e?void 0:e.class,null==e?void 0:e.className)}},88700,93007,a=>{"use strict";a.s(["X",()=>h],88700),a.s(["default",()=>g],93007);var b=a.i(54436);let c=a=>{let b=a.replace(/^([A-Z])|[\s-_]+(\w)/g,(a,b,c)=>c?c.toUpperCase():b.toLowerCase());return b.charAt(0).toUpperCase()+b.slice(1)},d=(...a)=>a.filter((a,b,c)=>!!a&&""!==a.trim()&&c.indexOf(a)===b).join(" ").trim();var e={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let f=(0,b.forwardRef)(({color:a="currentColor",size:c=24,strokeWidth:f=2,absoluteStrokeWidth:g,className:h="",children:i,iconNode:j,...k},l)=>(0,b.createElement)("svg",{ref:l,...e,width:c,height:c,stroke:a,strokeWidth:g?24*Number(f)/Number(c):f,className:d("lucide",h),...!i&&!(a=>{for(let b in a)if(b.startsWith("aria-")||"role"===b||"title"===b)return!0})(k)&&{"aria-hidden":"true"},...k},[...j.map(([a,c])=>(0,b.createElement)(a,c)),...Array.isArray(i)?i:[i]])),g=(a,e)=>{let g=(0,b.forwardRef)(({className:g,...h},i)=>(0,b.createElement)(f,{ref:i,iconNode:e,className:d(`lucide-${c(a).replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${a}`,g),...h}));return g.displayName=c(a),g},h=g("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},90334,a=>{"use strict";a.s(["cn",()=>aa],90334);var b=a.i(48110);let c=(a,b)=>{if(0===a.length)return b.classGroupId;let d=a[0],e=b.nextPart.get(d),f=e?c(a.slice(1),e):void 0;if(f)return f;if(0===b.validators.length)return;let g=a.join("-");return b.validators.find(({validator:a})=>a(g))?.classGroupId},d=/^\[(.+)\]$/,e=(a,b,c,d)=>{a.forEach(a=>{if("string"==typeof a){(""===a?b:f(b,a)).classGroupId=c;return}if("function"==typeof a)return g(a)?void e(a(d),b,c,d):void b.validators.push({validator:a,classGroupId:c});Object.entries(a).forEach(([a,g])=>{e(g,f(b,a),c,d)})})},f=(a,b)=>{let c=a;return b.split("-").forEach(a=>{c.nextPart.has(a)||c.nextPart.set(a,{nextPart:new Map,validators:[]}),c=c.nextPart.get(a)}),c},g=a=>a.isThemeGetter,h=/\s+/;function i(){let a,b,c=0,d="";for(;c<arguments.length;)(a=arguments[c++])&&(b=j(a))&&(d&&(d+=" "),d+=b);return d}let j=a=>{let b;if("string"==typeof a)return a;let c="";for(let d=0;d<a.length;d++)a[d]&&(b=j(a[d]))&&(c&&(c+=" "),c+=b);return c},k=a=>{let b=b=>b[a]||[];return b.isThemeGetter=!0,b},l=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,m=/^\((?:(\w[\w-]*):)?(.+)\)$/i,n=/^\d+\/\d+$/,o=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,p=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,q=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,r=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,s=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,t=a=>n.test(a),u=a=>!!a&&!Number.isNaN(Number(a)),v=a=>!!a&&Number.isInteger(Number(a)),w=a=>a.endsWith("%")&&u(a.slice(0,-1)),x=a=>o.test(a),y=()=>!0,z=a=>p.test(a)&&!q.test(a),A=()=>!1,B=a=>r.test(a),C=a=>s.test(a),D=a=>!F(a)&&!L(a),E=a=>S(a,W,A),F=a=>l.test(a),G=a=>S(a,X,z),H=a=>S(a,Y,u),I=a=>S(a,U,A),J=a=>S(a,V,C),K=a=>S(a,$,B),L=a=>m.test(a),M=a=>T(a,X),N=a=>T(a,Z),O=a=>T(a,U),P=a=>T(a,W),Q=a=>T(a,V),R=a=>T(a,$,!0),S=(a,b,c)=>{let d=l.exec(a);return!!d&&(d[1]?b(d[1]):c(d[2]))},T=(a,b,c=!1)=>{let d=m.exec(a);return!!d&&(d[1]?b(d[1]):c)},U=a=>"position"===a||"percentage"===a,V=a=>"image"===a||"url"===a,W=a=>"length"===a||"size"===a||"bg-size"===a,X=a=>"length"===a,Y=a=>"number"===a,Z=a=>"family-name"===a,$=a=>"shadow"===a;Symbol.toStringTag;let _=function(a,...b){let f,g,j,k=function(h){let i;return g=(f={cache:(a=>{if(a<1)return{get:()=>void 0,set:()=>{}};let b=0,c=new Map,d=new Map,e=(e,f)=>{c.set(e,f),++b>a&&(b=0,d=c,c=new Map)};return{get(a){let b=c.get(a);return void 0!==b?b:void 0!==(b=d.get(a))?(e(a,b),b):void 0},set(a,b){c.has(a)?c.set(a,b):e(a,b)}}})((i=b.reduce((a,b)=>b(a),a())).cacheSize),parseClassName:(a=>{let{prefix:b,experimentalParseClassName:c}=a,d=a=>{let b,c,d=[],e=0,f=0,g=0;for(let c=0;c<a.length;c++){let h=a[c];if(0===e&&0===f){if(":"===h){d.push(a.slice(g,c)),g=c+1;continue}if("/"===h){b=c;continue}}"["===h?e++:"]"===h?e--:"("===h?f++:")"===h&&f--}let h=0===d.length?a:a.substring(g),i=(c=h).endsWith("!")?c.substring(0,c.length-1):c.startsWith("!")?c.substring(1):c;return{modifiers:d,hasImportantModifier:i!==h,baseClassName:i,maybePostfixModifierPosition:b&&b>g?b-g:void 0}};if(b){let a=b+":",c=d;d=b=>b.startsWith(a)?c(b.substring(a.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:b,maybePostfixModifierPosition:void 0}}if(c){let a=d;d=b=>c({className:b,parseClassName:a})}return d})(i),sortModifiers:(a=>{let b=Object.fromEntries(a.orderSensitiveModifiers.map(a=>[a,!0]));return a=>{if(a.length<=1)return a;let c=[],d=[];return a.forEach(a=>{"["===a[0]||b[a]?(c.push(...d.sort(),a),d=[]):d.push(a)}),c.push(...d.sort()),c}})(i),...(a=>{let b=(a=>{let{theme:b,classGroups:c}=a,d={nextPart:new Map,validators:[]};for(let a in c)e(c[a],d,a,b);return d})(a),{conflictingClassGroups:f,conflictingClassGroupModifiers:g}=a;return{getClassGroupId:a=>{let e=a.split("-");return""===e[0]&&1!==e.length&&e.shift(),c(e,b)||(a=>{if(d.test(a)){let b=d.exec(a)[1],c=b?.substring(0,b.indexOf(":"));if(c)return"arbitrary.."+c}})(a)},getConflictingClassGroupIds:(a,b)=>{let c=f[a]||[];return b&&g[a]?[...c,...g[a]]:c}}})(i)}).cache.get,j=f.cache.set,k=l,l(h)};function l(a){let b=g(a);if(b)return b;let c=((a,b)=>{let{parseClassName:c,getClassGroupId:d,getConflictingClassGroupIds:e,sortModifiers:f}=b,g=[],i=a.trim().split(h),j="";for(let a=i.length-1;a>=0;a-=1){let b=i[a],{isExternal:h,modifiers:k,hasImportantModifier:l,baseClassName:m,maybePostfixModifierPosition:n}=c(b);if(h){j=b+(j.length>0?" "+j:j);continue}let o=!!n,p=d(o?m.substring(0,n):m);if(!p){if(!o||!(p=d(m))){j=b+(j.length>0?" "+j:j);continue}o=!1}let q=f(k).join(":"),r=l?q+"!":q,s=r+p;if(g.includes(s))continue;g.push(s);let t=e(p,o);for(let a=0;a<t.length;++a){let b=t[a];g.push(r+b)}j=b+(j.length>0?" "+j:j)}return j})(a,f);return j(a,c),c}return function(){return k(i.apply(null,arguments))}}(()=>{let a=k("color"),b=k("font"),c=k("text"),d=k("font-weight"),e=k("tracking"),f=k("leading"),g=k("breakpoint"),h=k("container"),i=k("spacing"),j=k("radius"),l=k("shadow"),m=k("inset-shadow"),n=k("text-shadow"),o=k("drop-shadow"),p=k("blur"),q=k("perspective"),r=k("aspect"),s=k("ease"),z=k("animate"),A=()=>["auto","avoid","all","avoid-page","page","left","right","column"],B=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"],C=()=>[...B(),L,F],S=()=>["auto","hidden","clip","visible","scroll"],T=()=>["auto","contain","none"],U=()=>[L,F,i],V=()=>[t,"full","auto",...U()],W=()=>[v,"none","subgrid",L,F],X=()=>["auto",{span:["full",v,L,F]},v,L,F],Y=()=>[v,"auto",L,F],Z=()=>["auto","min","max","fr",L,F],$=()=>["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"],_=()=>["start","end","center","stretch","center-safe","end-safe"],aa=()=>["auto",...U()],ab=()=>[t,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...U()],ac=()=>[a,L,F],ad=()=>[...B(),O,I,{position:[L,F]}],ae=()=>["no-repeat",{repeat:["","x","y","space","round"]}],af=()=>["auto","cover","contain",P,E,{size:[L,F]}],ag=()=>[w,M,G],ah=()=>["","none","full",j,L,F],ai=()=>["",u,M,G],aj=()=>["solid","dashed","dotted","double"],ak=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],al=()=>[u,w,O,I],am=()=>["","none",p,L,F],an=()=>["none",u,L,F],ao=()=>["none",u,L,F],ap=()=>[u,L,F],aq=()=>[t,"full",...U()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[x],breakpoint:[x],color:[y],container:[x],"drop-shadow":[x],ease:["in","out","in-out"],font:[D],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[x],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[x],shadow:[x],spacing:["px",u],text:[x],"text-shadow":[x],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",t,F,L,r]}],container:["container"],columns:[{columns:[u,F,L,h]}],"break-after":[{"break-after":A()}],"break-before":[{"break-before":A()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:C()}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:T()}],"overscroll-x":[{"overscroll-x":T()}],"overscroll-y":[{"overscroll-y":T()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:V()}],"inset-x":[{"inset-x":V()}],"inset-y":[{"inset-y":V()}],start:[{start:V()}],end:[{end:V()}],top:[{top:V()}],right:[{right:V()}],bottom:[{bottom:V()}],left:[{left:V()}],visibility:["visible","invisible","collapse"],z:[{z:[v,"auto",L,F]}],basis:[{basis:[t,"full","auto",h,...U()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[u,t,"auto","initial","none",F]}],grow:[{grow:["",u,L,F]}],shrink:[{shrink:["",u,L,F]}],order:[{order:[v,"first","last","none",L,F]}],"grid-cols":[{"grid-cols":W()}],"col-start-end":[{col:X()}],"col-start":[{"col-start":Y()}],"col-end":[{"col-end":Y()}],"grid-rows":[{"grid-rows":W()}],"row-start-end":[{row:X()}],"row-start":[{"row-start":Y()}],"row-end":[{"row-end":Y()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":Z()}],"auto-rows":[{"auto-rows":Z()}],gap:[{gap:U()}],"gap-x":[{"gap-x":U()}],"gap-y":[{"gap-y":U()}],"justify-content":[{justify:[...$(),"normal"]}],"justify-items":[{"justify-items":[..._(),"normal"]}],"justify-self":[{"justify-self":["auto",..._()]}],"align-content":[{content:["normal",...$()]}],"align-items":[{items:[..._(),{baseline:["","last"]}]}],"align-self":[{self:["auto",..._(),{baseline:["","last"]}]}],"place-content":[{"place-content":$()}],"place-items":[{"place-items":[..._(),"baseline"]}],"place-self":[{"place-self":["auto",..._()]}],p:[{p:U()}],px:[{px:U()}],py:[{py:U()}],ps:[{ps:U()}],pe:[{pe:U()}],pt:[{pt:U()}],pr:[{pr:U()}],pb:[{pb:U()}],pl:[{pl:U()}],m:[{m:aa()}],mx:[{mx:aa()}],my:[{my:aa()}],ms:[{ms:aa()}],me:[{me:aa()}],mt:[{mt:aa()}],mr:[{mr:aa()}],mb:[{mb:aa()}],ml:[{ml:aa()}],"space-x":[{"space-x":U()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":U()}],"space-y-reverse":["space-y-reverse"],size:[{size:ab()}],w:[{w:[h,"screen",...ab()]}],"min-w":[{"min-w":[h,"screen","none",...ab()]}],"max-w":[{"max-w":[h,"screen","none","prose",{screen:[g]},...ab()]}],h:[{h:["screen","lh",...ab()]}],"min-h":[{"min-h":["screen","lh","none",...ab()]}],"max-h":[{"max-h":["screen","lh",...ab()]}],"font-size":[{text:["base",c,M,G]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[d,L,H]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",w,F]}],"font-family":[{font:[N,F,b]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[e,L,F]}],"line-clamp":[{"line-clamp":[u,"none",L,H]}],leading:[{leading:[f,...U()]}],"list-image":[{"list-image":["none",L,F]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",L,F]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:ac()}],"text-color":[{text:ac()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...aj(),"wavy"]}],"text-decoration-thickness":[{decoration:[u,"from-font","auto",L,G]}],"text-decoration-color":[{decoration:ac()}],"underline-offset":[{"underline-offset":[u,"auto",L,F]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:U()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L,F]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L,F]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:ad()}],"bg-repeat":[{bg:ae()}],"bg-size":[{bg:af()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},v,L,F],radial:["",L,F],conic:[v,L,F]},Q,J]}],"bg-color":[{bg:ac()}],"gradient-from-pos":[{from:ag()}],"gradient-via-pos":[{via:ag()}],"gradient-to-pos":[{to:ag()}],"gradient-from":[{from:ac()}],"gradient-via":[{via:ac()}],"gradient-to":[{to:ac()}],rounded:[{rounded:ah()}],"rounded-s":[{"rounded-s":ah()}],"rounded-e":[{"rounded-e":ah()}],"rounded-t":[{"rounded-t":ah()}],"rounded-r":[{"rounded-r":ah()}],"rounded-b":[{"rounded-b":ah()}],"rounded-l":[{"rounded-l":ah()}],"rounded-ss":[{"rounded-ss":ah()}],"rounded-se":[{"rounded-se":ah()}],"rounded-ee":[{"rounded-ee":ah()}],"rounded-es":[{"rounded-es":ah()}],"rounded-tl":[{"rounded-tl":ah()}],"rounded-tr":[{"rounded-tr":ah()}],"rounded-br":[{"rounded-br":ah()}],"rounded-bl":[{"rounded-bl":ah()}],"border-w":[{border:ai()}],"border-w-x":[{"border-x":ai()}],"border-w-y":[{"border-y":ai()}],"border-w-s":[{"border-s":ai()}],"border-w-e":[{"border-e":ai()}],"border-w-t":[{"border-t":ai()}],"border-w-r":[{"border-r":ai()}],"border-w-b":[{"border-b":ai()}],"border-w-l":[{"border-l":ai()}],"divide-x":[{"divide-x":ai()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":ai()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:[...aj(),"hidden","none"]}],"divide-style":[{divide:[...aj(),"hidden","none"]}],"border-color":[{border:ac()}],"border-color-x":[{"border-x":ac()}],"border-color-y":[{"border-y":ac()}],"border-color-s":[{"border-s":ac()}],"border-color-e":[{"border-e":ac()}],"border-color-t":[{"border-t":ac()}],"border-color-r":[{"border-r":ac()}],"border-color-b":[{"border-b":ac()}],"border-color-l":[{"border-l":ac()}],"divide-color":[{divide:ac()}],"outline-style":[{outline:[...aj(),"none","hidden"]}],"outline-offset":[{"outline-offset":[u,L,F]}],"outline-w":[{outline:["",u,M,G]}],"outline-color":[{outline:ac()}],shadow:[{shadow:["","none",l,R,K]}],"shadow-color":[{shadow:ac()}],"inset-shadow":[{"inset-shadow":["none",m,R,K]}],"inset-shadow-color":[{"inset-shadow":ac()}],"ring-w":[{ring:ai()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:ac()}],"ring-offset-w":[{"ring-offset":[u,G]}],"ring-offset-color":[{"ring-offset":ac()}],"inset-ring-w":[{"inset-ring":ai()}],"inset-ring-color":[{"inset-ring":ac()}],"text-shadow":[{"text-shadow":["none",n,R,K]}],"text-shadow-color":[{"text-shadow":ac()}],opacity:[{opacity:[u,L,F]}],"mix-blend":[{"mix-blend":[...ak(),"plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":ak()}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[u]}],"mask-image-linear-from-pos":[{"mask-linear-from":al()}],"mask-image-linear-to-pos":[{"mask-linear-to":al()}],"mask-image-linear-from-color":[{"mask-linear-from":ac()}],"mask-image-linear-to-color":[{"mask-linear-to":ac()}],"mask-image-t-from-pos":[{"mask-t-from":al()}],"mask-image-t-to-pos":[{"mask-t-to":al()}],"mask-image-t-from-color":[{"mask-t-from":ac()}],"mask-image-t-to-color":[{"mask-t-to":ac()}],"mask-image-r-from-pos":[{"mask-r-from":al()}],"mask-image-r-to-pos":[{"mask-r-to":al()}],"mask-image-r-from-color":[{"mask-r-from":ac()}],"mask-image-r-to-color":[{"mask-r-to":ac()}],"mask-image-b-from-pos":[{"mask-b-from":al()}],"mask-image-b-to-pos":[{"mask-b-to":al()}],"mask-image-b-from-color":[{"mask-b-from":ac()}],"mask-image-b-to-color":[{"mask-b-to":ac()}],"mask-image-l-from-pos":[{"mask-l-from":al()}],"mask-image-l-to-pos":[{"mask-l-to":al()}],"mask-image-l-from-color":[{"mask-l-from":ac()}],"mask-image-l-to-color":[{"mask-l-to":ac()}],"mask-image-x-from-pos":[{"mask-x-from":al()}],"mask-image-x-to-pos":[{"mask-x-to":al()}],"mask-image-x-from-color":[{"mask-x-from":ac()}],"mask-image-x-to-color":[{"mask-x-to":ac()}],"mask-image-y-from-pos":[{"mask-y-from":al()}],"mask-image-y-to-pos":[{"mask-y-to":al()}],"mask-image-y-from-color":[{"mask-y-from":ac()}],"mask-image-y-to-color":[{"mask-y-to":ac()}],"mask-image-radial":[{"mask-radial":[L,F]}],"mask-image-radial-from-pos":[{"mask-radial-from":al()}],"mask-image-radial-to-pos":[{"mask-radial-to":al()}],"mask-image-radial-from-color":[{"mask-radial-from":ac()}],"mask-image-radial-to-color":[{"mask-radial-to":ac()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":B()}],"mask-image-conic-pos":[{"mask-conic":[u]}],"mask-image-conic-from-pos":[{"mask-conic-from":al()}],"mask-image-conic-to-pos":[{"mask-conic-to":al()}],"mask-image-conic-from-color":[{"mask-conic-from":ac()}],"mask-image-conic-to-color":[{"mask-conic-to":ac()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:ad()}],"mask-repeat":[{mask:ae()}],"mask-size":[{mask:af()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",L,F]}],filter:[{filter:["","none",L,F]}],blur:[{blur:am()}],brightness:[{brightness:[u,L,F]}],contrast:[{contrast:[u,L,F]}],"drop-shadow":[{"drop-shadow":["","none",o,R,K]}],"drop-shadow-color":[{"drop-shadow":ac()}],grayscale:[{grayscale:["",u,L,F]}],"hue-rotate":[{"hue-rotate":[u,L,F]}],invert:[{invert:["",u,L,F]}],saturate:[{saturate:[u,L,F]}],sepia:[{sepia:["",u,L,F]}],"backdrop-filter":[{"backdrop-filter":["","none",L,F]}],"backdrop-blur":[{"backdrop-blur":am()}],"backdrop-brightness":[{"backdrop-brightness":[u,L,F]}],"backdrop-contrast":[{"backdrop-contrast":[u,L,F]}],"backdrop-grayscale":[{"backdrop-grayscale":["",u,L,F]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[u,L,F]}],"backdrop-invert":[{"backdrop-invert":["",u,L,F]}],"backdrop-opacity":[{"backdrop-opacity":[u,L,F]}],"backdrop-saturate":[{"backdrop-saturate":[u,L,F]}],"backdrop-sepia":[{"backdrop-sepia":["",u,L,F]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":U()}],"border-spacing-x":[{"border-spacing-x":U()}],"border-spacing-y":[{"border-spacing-y":U()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",L,F]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[u,"initial",L,F]}],ease:[{ease:["linear","initial",s,L,F]}],delay:[{delay:[u,L,F]}],animate:[{animate:["none",z,L,F]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[q,L,F]}],"perspective-origin":[{"perspective-origin":C()}],rotate:[{rotate:an()}],"rotate-x":[{"rotate-x":an()}],"rotate-y":[{"rotate-y":an()}],"rotate-z":[{"rotate-z":an()}],scale:[{scale:ao()}],"scale-x":[{"scale-x":ao()}],"scale-y":[{"scale-y":ao()}],"scale-z":[{"scale-z":ao()}],"scale-3d":["scale-3d"],skew:[{skew:ap()}],"skew-x":[{"skew-x":ap()}],"skew-y":[{"skew-y":ap()}],transform:[{transform:[L,F,"","none","gpu","cpu"]}],"transform-origin":[{origin:C()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:aq()}],"translate-x":[{"translate-x":aq()}],"translate-y":[{"translate-y":aq()}],"translate-z":[{"translate-z":aq()}],"translate-none":["translate-none"],accent:[{accent:ac()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:ac()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L,F]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":U()}],"scroll-mx":[{"scroll-mx":U()}],"scroll-my":[{"scroll-my":U()}],"scroll-ms":[{"scroll-ms":U()}],"scroll-me":[{"scroll-me":U()}],"scroll-mt":[{"scroll-mt":U()}],"scroll-mr":[{"scroll-mr":U()}],"scroll-mb":[{"scroll-mb":U()}],"scroll-ml":[{"scroll-ml":U()}],"scroll-p":[{"scroll-p":U()}],"scroll-px":[{"scroll-px":U()}],"scroll-py":[{"scroll-py":U()}],"scroll-ps":[{"scroll-ps":U()}],"scroll-pe":[{"scroll-pe":U()}],"scroll-pt":[{"scroll-pt":U()}],"scroll-pr":[{"scroll-pr":U()}],"scroll-pb":[{"scroll-pb":U()}],"scroll-pl":[{"scroll-pl":U()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L,F]}],fill:[{fill:["none",...ac()]}],"stroke-w":[{stroke:[u,M,G,H]}],stroke:[{stroke:["none",...ac()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}});function aa(...a){return _((0,b.clsx)(a))}},41930,a=>{"use strict";a.s(["Toaster",()=>p],41930);var b=a.i(28386),c=a.i(3332),d=a.i(54436),e=a.i(80761),f=a.i(30718),g=a.i(88700),h=a.i(90334);let i=e.Provider,j=d.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e.Viewport,{ref:d,className:(0,h.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",a),...c}));j.displayName=e.Viewport.displayName;let k=(0,f.cva)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),l=d.forwardRef(({className:a,variant:c,...d},f)=>(0,b.jsx)(e.Root,{ref:f,className:(0,h.cn)(k({variant:c}),a),...d}));l.displayName=e.Root.displayName,d.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e.Action,{ref:d,className:(0,h.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground",a),...c})).displayName=e.Action.displayName;let m=d.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e.Close,{ref:d,className:(0,h.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50",a),"toast-close":"",...c,children:(0,b.jsx)(g.X,{className:"w-4 h-4"})}));m.displayName=e.Close.displayName;let n=d.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e.Title,{ref:d,className:(0,h.cn)("text-sm font-semibold",a),...c}));n.displayName=e.Title.displayName;let o=d.forwardRef(({className:a,...c},d)=>(0,b.jsx)(e.Description,{ref:d,className:(0,h.cn)("text-sm opacity-90",a),...c}));function p(){let{toasts:a}=(0,c.useToast)();return(0,b.jsxs)(i,{children:[a.map(function({id:a,title:c,description:d,action:e,...f}){return(0,b.jsxs)(l,{...f,children:[(0,b.jsxs)("div",{className:"grid gap-1",children:[c&&(0,b.jsx)(n,{children:c}),d&&(0,b.jsx)(o,{children:d})]}),e,(0,b.jsx)(m,{})]},a)}),(0,b.jsx)(j,{})]})}o.displayName=e.Description.displayName}];

//# sourceMappingURL=renderer_2dc0ca3a._.js.map