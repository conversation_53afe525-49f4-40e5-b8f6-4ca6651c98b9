"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                        console.log(\"Cherry module loaded:\", CherryModule);\n                        const CherryMarkdown = CherryModule.default || CherryModule;\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息\n    if (!isClient || !CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 259,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 247,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 265,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});