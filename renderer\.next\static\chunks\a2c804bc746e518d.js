(globalThis.TURBOPACK||(globalThis.TURBOPACK=[])).push(["object"==typeof document?document.currentScript:void 0,3083,(e,t,r)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"warnOnce",{enumerable:!0,get:function(){return a}});let a=e=>{}},96496,e=>{"use strict";e.s(["Card",()=>n,"CardContent",()=>s,"CardDescription",()=>i,"CardFooter",()=>d,"CardHeader",()=>l,"CardTitle",()=>o]);var t=e.i(67857),r=e.i(24302),a=e.i(92072);let n=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("rounded-lg border border-border bg-card text-card-foreground shadow-sm",n),...l})});n.displayName="Card";let l=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",n),...l})});l.displayName="CardHeader";let o=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",n),...l})});o.displayName="CardTitle";let i=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",n),...l})});i.displayName="CardDescription";let s=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("p-6 pt-0",n),...l})});s.displayName="CardContent";let d=r.forwardRef((e,r)=>{let{className:n,...l}=e;return(0,t.jsx)("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",n),...l})});d.displayName="CardFooter"},99782,e=>{"use strict";e.s(["ThemeToggle",()=>i],99782);var t=e.i(67857),r=e.i(93137);let a=(0,r.default)("moon",[["path",{d:"M20.985 12.486a9 9 0 1 1-9.473-9.472c.405-.022.617.46.402.803a6 6 0 0 0 8.268 8.268c.344-.215.825-.004.803.401",key:"kfwtm"}]]),n=(0,r.default)("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]);var l=e.i(73222),o=e.i(28157);function i(){let{theme:e,setTheme:r}=(0,l.useTheme)();return(0,t.jsxs)(o.Button,{variant:"outline",size:"icon",onClick:()=>{r("dark"===e?"light":"dark")},children:[(0,t.jsx)(n,{className:"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0"}),(0,t.jsx)(a,{className:"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100"}),(0,t.jsx)("span",{className:"sr-only",children:"Toggle theme"})]})}},57925,41764,e=>{"use strict";e.s(["Search",()=>t],57925);let t=(0,e.i(93137).default)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]]);e.s(["Dialog",()=>i,"DialogContent",()=>c,"DialogFooter",()=>f,"DialogHeader",()=>u,"DialogTitle",()=>p],41764);var r=e.i(67857),a=e.i(24302),n=e.i(48424),l=e.i(17388),o=e.i(92072);let i=n.Root;n.Trigger;let s=n.Portal;n.Close;let d=a.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.Overlay,{ref:t,className:(0,o.cn)("fixed inset-0 z-50 bg-black/80 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...l})});d.displayName=n.Overlay.displayName;let c=a.forwardRef((e,t)=>{let{className:a,children:i,...c}=e;return(0,r.jsxs)(s,{children:[(0,r.jsx)(d,{}),(0,r.jsxs)(n.Content,{ref:t,className:(0,o.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c,children:[i,(0,r.jsxs)(n.Close,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,r.jsx)(l.X,{className:"w-4 h-4"}),(0,r.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})});c.displayName=n.Content.displayName;let u=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col space-y-1.5 text-center sm:text-left",t),...a})};u.displayName="DialogHeader";let f=e=>{let{className:t,...a}=e;return(0,r.jsx)("div",{className:(0,o.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",t),...a})};f.displayName="DialogFooter";let p=a.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.Title,{ref:t,className:(0,o.cn)("text-lg font-semibold leading-none tracking-tight",a),...l})});p.displayName=n.Title.displayName,a.forwardRef((e,t)=>{let{className:a,...l}=e;return(0,r.jsx)(n.Description,{ref:t,className:(0,o.cn)("text-sm text-muted-foreground",a),...l})}).displayName=n.Description.displayName},74895,e=>{"use strict";e.s(["default",()=>eT],74895);var t=e.i(67857),r=e.i(24302),a=e.i(79431),n=e.i(28157),l=e.i(96496),o=e.i(91457),i=e.i(61620),s=e.i(30438),d=e.i(99782),c=/[\\\/_+.#"@\[\(\{&]/,u=/[\\\/_+.#"@\[\(\{&]/g,f=/[\s-]/,p=/[\s-]/g;function m(e){return e.toLowerCase().replace(p," ")}var v=e.i(48424),h=e.i(32054),g=e.i(36641),x=e.i(51906),b='[cmdk-group=""]',y='[cmdk-group-items=""]',w='[cmdk-item=""]',j="".concat(w,':not([aria-disabled="true"])'),C="cmdk-item-select",N="data-value",k=(e,t,r)=>(function(e,t,r){return function e(t,r,a,n,l,o,i){if(o===r.length)return l===t.length?1:.99;var s="".concat(l,",").concat(o);if(void 0!==i[s])return i[s];for(var d,m,v,h,g=n.charAt(o),x=a.indexOf(g,l),b=0;x>=0;)(d=e(t,r,a,n,x+1,o+1,i))>b&&(x===l?d*=1:c.test(t.charAt(x-1))?(d*=.8,(v=t.slice(l,x-1).match(u))&&l>0&&(d*=Math.pow(.999,v.length))):f.test(t.charAt(x-1))?(d*=.9,(h=t.slice(l,x-1).match(p))&&l>0&&(d*=Math.pow(.999,h.length))):(d*=.17,l>0&&(d*=Math.pow(.999,x-l))),t.charAt(x)!==r.charAt(o)&&(d*=.9999)),(d<.1&&a.charAt(x-1)===n.charAt(o+1)||n.charAt(o+1)===n.charAt(o)&&a.charAt(x-1)!==n.charAt(o))&&.1*(m=e(t,r,a,n,x+1,o+2,i))>d&&(d=.1*m),d>b&&(b=d),x=a.indexOf(g,x+1);return i[s]=b,b}(e=r&&r.length>0?"".concat(e+" "+r.join(" ")):e,t,m(e),m(t),0,0,{})})(e,t,r),E=r.createContext(void 0),R=()=>r.useContext(E),S=r.createContext(void 0),P=()=>r.useContext(S),I=r.createContext(void 0),A=r.forwardRef((e,t)=>{let a=K(()=>{var t,r;return{search:"",value:null!=(r=null!=(t=e.value)?t:e.defaultValue)?r:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),n=K(()=>new Set),l=K(()=>new Map),o=K(()=>new Map),i=K(()=>new Set),s=L(e),{label:d,children:c,value:u,onValueChange:f,filter:p,shouldFilter:m,loop:v,disablePointerSelection:x=!1,vimBindings:R=!0,...P}=e,I=(0,g.useId)(),A=(0,g.useId)(),D=(0,g.useId)(),O=r.useRef(null),M=V();H(()=>{if(void 0!==u){let e=u.trim();a.current.value=e,_.emit()}},[u]),H(()=>{M(6,W)},[]);let _=r.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>a.current,setState:(e,t,r)=>{var n,l,o,i;if(!Object.is(a.current[e],t)){if(a.current[e]=t,"search"===e)q(),z(),M(1,B);else if("value"===e){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let e=document.getElementById(D);e?e.focus():null==(n=document.getElementById(I))||n.focus()}if(M(7,()=>{var e;a.current.selectedItemId=null==(e=X())?void 0:e.id,_.emit()}),r||M(5,W),(null==(l=s.current)?void 0:l.value)!==void 0){null==(i=(o=s.current).onValueChange)||i.call(o,null!=t?t:"");return}}_.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),F=r.useMemo(()=>({value:(e,t,r)=>{var n;t!==(null==(n=o.current.get(e))?void 0:n.value)&&(o.current.set(e,{value:t,keywords:r}),a.current.filtered.items.set(e,T(t,r)),M(2,()=>{z(),_.emit()}))},item:(e,t)=>(n.current.add(e),t&&(l.current.has(t)?l.current.get(t).add(e):l.current.set(t,new Set([e]))),M(3,()=>{q(),z(),a.current.value||B(),_.emit()}),()=>{o.current.delete(e),n.current.delete(e),a.current.filtered.items.delete(e);let t=X();M(4,()=>{q(),(null==t?void 0:t.getAttribute("id"))===e&&B(),_.emit()})}),group:e=>(l.current.has(e)||l.current.set(e,new Set),()=>{o.current.delete(e),l.current.delete(e)}),filter:()=>s.current.shouldFilter,label:d||e["aria-label"],getDisablePointerSelection:()=>s.current.disablePointerSelection,listId:I,inputId:D,labelId:A,listInnerRef:O}),[]);function T(e,t){var r,n;let l=null!=(n=null==(r=s.current)?void 0:r.filter)?n:k;return e?l(e,a.current.search,t):0}function z(){if(!a.current.search||!1===s.current.shouldFilter)return;let e=a.current.filtered.items,t=[];a.current.filtered.groups.forEach(r=>{let a=l.current.get(r),n=0;a.forEach(t=>{n=Math.max(e.get(t),n)}),t.push([r,n])});let r=O.current;Z().sort((t,r)=>{var a,n;let l=t.getAttribute("id"),o=r.getAttribute("id");return(null!=(a=e.get(o))?a:0)-(null!=(n=e.get(l))?n:0)}).forEach(e=>{let t=e.closest(y);t?t.appendChild(e.parentElement===t?e:e.closest("".concat(y," > *"))):r.appendChild(e.parentElement===r?e:e.closest("".concat(y," > *")))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let r=null==(t=O.current)?void 0:t.querySelector("".concat(b,"[").concat(N,'="').concat(encodeURIComponent(e[0]),'"]'));null==r||r.parentElement.appendChild(r)})}function B(){let e=Z().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(N);_.setState("value",t||void 0)}function q(){var e,t,r,i;if(!a.current.search||!1===s.current.shouldFilter){a.current.filtered.count=n.current.size;return}a.current.filtered.groups=new Set;let d=0;for(let l of n.current){let n=T(null!=(t=null==(e=o.current.get(l))?void 0:e.value)?t:"",null!=(i=null==(r=o.current.get(l))?void 0:r.keywords)?i:[]);a.current.filtered.items.set(l,n),n>0&&d++}for(let[e,t]of l.current)for(let r of t)if(a.current.filtered.items.get(r)>0){a.current.filtered.groups.add(e);break}a.current.filtered.count=d}function W(){var e,t,r;let a=X();a&&((null==(e=a.parentElement)?void 0:e.firstChild)===a&&(null==(r=null==(t=a.closest(b))?void 0:t.querySelector('[cmdk-group-heading=""]'))||r.scrollIntoView({block:"nearest"})),a.scrollIntoView({block:"nearest"}))}function X(){var e;return null==(e=O.current)?void 0:e.querySelector("".concat(w,'[aria-selected="true"]'))}function Z(){var e;return Array.from((null==(e=O.current)?void 0:e.querySelectorAll(j))||[])}function J(e){let t=Z()[e];t&&_.setState("value",t.getAttribute(N))}function Q(e){var t;let r=X(),a=Z(),n=a.findIndex(e=>e===r),l=a[n+e];null!=(t=s.current)&&t.loop&&(l=n+e<0?a[a.length-1]:n+e===a.length?a[0]:a[n+e]),l&&_.setState("value",l.getAttribute(N))}function Y(e){let t=X(),r=null==t?void 0:t.closest(b),a;for(;r&&!a;)a=null==(r=e>0?function(e,t){let r=e.nextElementSibling;for(;r;){if(r.matches(t))return r;r=r.nextElementSibling}}(r,b):function(e,t){let r=e.previousElementSibling;for(;r;){if(r.matches(t))return r;r=r.previousElementSibling}}(r,b))?void 0:r.querySelector(j);a?_.setState("value",a.getAttribute(N)):Q(e)}let $=()=>J(Z().length-1),ee=e=>{e.preventDefault(),e.metaKey?$():e.altKey?Y(1):Q(1)},et=e=>{e.preventDefault(),e.metaKey?J(0):e.altKey?Y(-1):Q(-1)};return r.createElement(h.Primitive.div,{ref:t,tabIndex:-1,...P,"cmdk-root":"",onKeyDown:e=>{var t;null==(t=P.onKeyDown)||t.call(P,e);let r=e.nativeEvent.isComposing||229===e.keyCode;if(!(e.defaultPrevented||r))switch(e.key){case"n":case"j":R&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":R&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),J(0);break;case"End":e.preventDefault(),$();break;case"Enter":{e.preventDefault();let t=X();if(t){let e=new Event(C);t.dispatchEvent(e)}}}}},r.createElement("label",{"cmdk-label":"",htmlFor:F.inputId,id:F.labelId,style:G},d),U(e,e=>r.createElement(S.Provider,{value:_},r.createElement(E.Provider,{value:F},e))))}),D=r.forwardRef((e,t)=>{var a,n;let l=(0,g.useId)(),o=r.useRef(null),i=r.useContext(I),s=R(),d=L(e),c=null!=(n=null==(a=d.current)?void 0:a.forceMount)?n:null==i?void 0:i.forceMount;H(()=>{if(!c)return s.item(l,null==i?void 0:i.id)},[c]);let u=q(l,o,[e.value,e.children,o],e.keywords),f=P(),p=B(e=>e.value&&e.value===u.current),m=B(e=>!!c||!1===s.filter()||!e.search||e.filtered.items.get(l)>0);function v(){var e,t;b(),null==(t=(e=d.current).onSelect)||t.call(e,u.current)}function b(){f.setState("value",u.current,!0)}if(r.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(C,v),()=>t.removeEventListener(C,v)},[m,e.onSelect,e.disabled]),!m)return null;let{disabled:y,value:w,onSelect:j,forceMount:N,keywords:k,...E}=e;return r.createElement(h.Primitive.div,{ref:(0,x.composeRefs)(o,t),...E,id:l,"cmdk-item":"",role:"option","aria-disabled":!!y,"aria-selected":!!p,"data-disabled":!!y,"data-selected":!!p,onPointerMove:y||s.getDisablePointerSelection()?void 0:b,onClick:y?void 0:v},e.children)}),O=r.forwardRef((e,t)=>{let{heading:a,children:n,forceMount:l,...o}=e,i=(0,g.useId)(),s=r.useRef(null),d=r.useRef(null),c=(0,g.useId)(),u=R(),f=B(e=>!!l||!1===u.filter()||!e.search||e.filtered.groups.has(i));H(()=>u.group(i),[]),q(i,s,[e.value,e.heading,d]);let p=r.useMemo(()=>({id:i,forceMount:l}),[l]);return r.createElement(h.Primitive.div,{ref:(0,x.composeRefs)(s,t),...o,"cmdk-group":"",role:"presentation",hidden:!f||void 0},a&&r.createElement("div",{ref:d,"cmdk-group-heading":"","aria-hidden":!0,id:c},a),U(e,e=>r.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":a?c:void 0},r.createElement(I.Provider,{value:p},e))))}),M=r.forwardRef((e,t)=>{let{alwaysRender:a,...n}=e,l=r.useRef(null),o=B(e=>!e.search);return a||o?r.createElement(h.Primitive.div,{ref:(0,x.composeRefs)(l,t),...n,"cmdk-separator":"",role:"separator"}):null}),_=r.forwardRef((e,t)=>{let{onValueChange:a,...n}=e,l=null!=e.value,o=P(),i=B(e=>e.search),s=B(e=>e.selectedItemId),d=R();return r.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),r.createElement(h.Primitive.input,{ref:t,...n,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":d.listId,"aria-labelledby":d.labelId,"aria-activedescendant":s,id:d.inputId,type:"text",value:l?e.value:i,onChange:e=>{l||o.setState("search",e.target.value),null==a||a(e.target.value)}})}),F=r.forwardRef((e,t)=>{let{children:a,label:n="Suggestions",...l}=e,o=r.useRef(null),i=r.useRef(null),s=B(e=>e.selectedItemId),d=R();return r.useEffect(()=>{if(i.current&&o.current){let e=i.current,t=o.current,r,a=new ResizeObserver(()=>{r=requestAnimationFrame(()=>{let r=e.offsetHeight;t.style.setProperty("--cmdk-list-height",r.toFixed(1)+"px")})});return a.observe(e),()=>{cancelAnimationFrame(r),a.unobserve(e)}}},[]),r.createElement(h.Primitive.div,{ref:(0,x.composeRefs)(o,t),...l,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":s,"aria-label":n,id:d.listId},U(e,e=>r.createElement("div",{ref:(0,x.composeRefs)(i,d.listInnerRef),"cmdk-list-sizer":""},e)))}),T=r.forwardRef((e,t)=>{let{open:a,onOpenChange:n,overlayClassName:l,contentClassName:o,container:i,...s}=e;return r.createElement(v.Root,{open:a,onOpenChange:n},r.createElement(v.Portal,{container:i},r.createElement(v.Overlay,{"cmdk-overlay":"",className:l}),r.createElement(v.Content,{"aria-label":e.label,"cmdk-dialog":"",className:o},r.createElement(A,{ref:t,...s}))))}),z=Object.assign(A,{List:F,Item:D,Input:_,Group:O,Separator:M,Dialog:T,Empty:r.forwardRef((e,t)=>B(e=>0===e.filtered.count)?r.createElement(h.Primitive.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:r.forwardRef((e,t)=>{let{progress:a,children:n,label:l="Loading...",...o}=e;return r.createElement(h.Primitive.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":a,"aria-valuemin":0,"aria-valuemax":100,"aria-label":l},U(e,e=>r.createElement("div",{"aria-hidden":!0},e)))})});function L(e){let t=r.useRef(e);return H(()=>{t.current=e}),t}var H="undefined"==typeof window?r.useEffect:r.useLayoutEffect;function K(e){let t=r.useRef();return void 0===t.current&&(t.current=e()),t}function B(e){let t=P(),a=()=>e(t.snapshot());return r.useSyncExternalStore(t.subscribe,a,a)}function q(e,t,a){let n=arguments.length>3&&void 0!==arguments[3]?arguments[3]:[],l=r.useRef(),o=R();return H(()=>{var r;let i=(()=>{var e;for(let t of a){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():l.current}})(),s=n.map(e=>e.trim());o.value(e,i,s),null==(r=t.current)||r.setAttribute(N,i),l.current=i}),l}var V=()=>{let[e,t]=r.useState(),a=K(()=>new Map);return H(()=>{a.current.forEach(e=>e()),a.current=new Map},[e]),(e,r)=>{a.current.set(e,r),t({})}};function U(e,t){let a,{asChild:n,children:l}=e;return n&&r.isValidElement(l)?r.cloneElement("function"==typeof(a=l.type)?a(l.props):"render"in a?a.render(l.props):l,{ref:l.ref},t(l.props.children)):t(l)}var G={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},W=e.i(57925),X=e.i(92072);e.i(41764);let Z=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(z,{ref:r,className:(0,X.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",a),...n})});Z.displayName=z.displayName;let J=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsxs)("div",{className:"flex items-center px-3 border-b border-border","cmdk-input-wrapper":"",children:[(0,t.jsx)(W.Search,{className:"w-4 h-4 mr-2 opacity-50 shrink-0"}),(0,t.jsx)(z.Input,{ref:r,className:(0,X.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",a),...n})]})});J.displayName=z.Input.displayName;let Q=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(z.List,{ref:r,className:(0,X.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",a),...n})});Q.displayName=z.List.displayName;let Y=r.forwardRef((e,r)=>(0,t.jsx)(z.Empty,{ref:r,className:"py-6 text-sm text-center",...e}));Y.displayName=z.Empty.displayName;let $=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(z.Group,{ref:r,className:(0,X.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",a),...n})});$.displayName=z.Group.displayName,r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(z.Separator,{ref:r,className:(0,X.cn)("-mx-1 h-px bg-border",a),...n})}).displayName=z.Separator.displayName;let ee=r.forwardRef((e,r)=>{let{className:a,...n}=e;return(0,t.jsx)(z.Item,{ref:r,className:(0,X.cn)("relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a),...n})});ee.displayName=z.Item.displayName;var et=e.i(61540),er=e.i(76825),ea=e.i(80435),en=e.i(40971),el=e.i(89920),eo=e.i(36838),ei=e.i(87804),es=e.i(22859),ed=e.i(81692),ec=e.i(89199),eu=e.i(15119),ef=e.i(67084),ep="Popover",[em,ev]=(0,er.createContextScope)(ep,[eo.createPopperScope]),eh=(0,eo.createPopperScope)(),[eg,ex]=em(ep),eb=e=>{let{__scopePopover:a,children:n,open:l,defaultOpen:o,onOpenChange:i,modal:s=!1}=e,d=eh(a),c=r.useRef(null),[u,f]=r.useState(!1),[p,m]=(0,ec.useControllableState)({prop:l,defaultProp:null!=o&&o,onChange:i,caller:ep});return(0,t.jsx)(eo.Root,{...d,children:(0,t.jsx)(eg,{scope:a,contentId:(0,g.useId)(),triggerRef:c,open:p,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:u,onCustomAnchorAdd:r.useCallback(()=>f(!0),[]),onCustomAnchorRemove:r.useCallback(()=>f(!1),[]),modal:s,children:n})})};eb.displayName=ep;var ey="PopoverAnchor";r.forwardRef((e,a)=>{let{__scopePopover:n,...l}=e,o=ex(ey,n),i=eh(n),{onCustomAnchorAdd:s,onCustomAnchorRemove:d}=o;return r.useEffect(()=>(s(),()=>d()),[s,d]),(0,t.jsx)(eo.Anchor,{...i,...l,ref:a})}).displayName=ey;var ew="PopoverTrigger",ej=r.forwardRef((e,r)=>{let{__scopePopover:a,...n}=e,l=ex(ew,a),o=eh(a),i=(0,x.useComposedRefs)(r,l.triggerRef),s=(0,t.jsx)(h.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":l.open,"aria-controls":l.contentId,"data-state":eM(l.open),...n,ref:i,onClick:(0,et.composeEventHandlers)(e.onClick,l.onOpenToggle)});return l.hasCustomAnchor?s:(0,t.jsx)(eo.Anchor,{asChild:!0,...o,children:s})});ej.displayName=ew;var eC="PopoverPortal",[eN,ek]=em(eC,{forceMount:void 0}),eE=e=>{let{__scopePopover:r,forceMount:a,children:n,container:l}=e,o=ex(eC,r);return(0,t.jsx)(eN,{scope:r,forceMount:a,children:(0,t.jsx)(es.Presence,{present:a||o.open,children:(0,t.jsx)(ei.Portal,{asChild:!0,container:l,children:n})})})};eE.displayName=eC;var eR="PopoverContent",eS=r.forwardRef((e,r)=>{let a=ek(eR,e.__scopePopover),{forceMount:n=a.forceMount,...l}=e,o=ex(eR,e.__scopePopover);return(0,t.jsx)(es.Presence,{present:n||o.open,children:o.modal?(0,t.jsx)(eI,{...l,ref:r}):(0,t.jsx)(eA,{...l,ref:r})})});eS.displayName=eR;var eP=(0,ed.createSlot)("PopoverContent.RemoveScroll"),eI=r.forwardRef((e,a)=>{let n=ex(eR,e.__scopePopover),l=r.useRef(null),o=(0,x.useComposedRefs)(a,l),i=r.useRef(!1);return r.useEffect(()=>{let e=l.current;if(e)return(0,eu.hideOthers)(e)},[]),(0,t.jsx)(ef.RemoveScroll,{as:eP,allowPinchZoom:!0,children:(0,t.jsx)(eD,{...e,ref:o,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,et.composeEventHandlers)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),i.current||null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,et.composeEventHandlers)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,r=0===t.button&&!0===t.ctrlKey;i.current=2===t.button||r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,et.composeEventHandlers)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),eA=r.forwardRef((e,a)=>{let n=ex(eR,e.__scopePopover),l=r.useRef(!1),o=r.useRef(!1);return(0,t.jsx)(eD,{...e,ref:a,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(l.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),l.current=!1,o.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(l.current=!0,"pointerdown"===t.detail.originalEvent.type&&(o.current=!0));let i=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(i))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),eD=r.forwardRef((e,r)=>{let{__scopePopover:a,trapFocus:n,onOpenAutoFocus:l,onCloseAutoFocus:o,disableOutsidePointerEvents:i,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onInteractOutside:u,...f}=e,p=ex(eR,a),m=eh(a);return(0,en.useFocusGuards)(),(0,t.jsx)(el.FocusScope,{asChild:!0,loop:!0,trapped:n,onMountAutoFocus:l,onUnmountAutoFocus:o,children:(0,t.jsx)(ea.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:u,onEscapeKeyDown:s,onPointerDownOutside:d,onFocusOutside:c,onDismiss:()=>p.onOpenChange(!1),children:(0,t.jsx)(eo.Content,{"data-state":eM(p.open),role:"dialog",id:p.contentId,...m,...f,ref:r,style:{...f.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),eO="PopoverClose";function eM(e){return e?"open":"closed"}r.forwardRef((e,r)=>{let{__scopePopover:a,...n}=e,l=ex(eO,a);return(0,t.jsx)(h.Primitive.button,{type:"button",...n,ref:r,onClick:(0,et.composeEventHandlers)(e.onClick,()=>l.onOpenChange(!1))})}).displayName=eO,r.forwardRef((e,r)=>{let{__scopePopover:a,...n}=e,l=eh(a);return(0,t.jsx)(eo.Arrow,{...l,...n,ref:r})}).displayName="PopoverArrow";let e_=r.forwardRef((e,r)=>{let{className:a,align:n="center",sideOffset:l=4,...o}=e;return(0,t.jsx)(eE,{children:(0,t.jsx)(eS,{ref:r,align:n,sideOffset:l,className:(0,X.cn)("z-50 w-72 rounded-md border border-border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",a),...o})})});e_.displayName=eS.displayName;var eF=e.i(47244);function eT(){let{t:e}=(0,eF.useI18n)(),[c,u]=(0,r.useState)([]),[f,p]=(0,r.useState)(!0),[m,v]=(0,r.useState)(""),[h,g]=(0,r.useState)([]),{toast:x}=(0,i.useToast)(),b=(0,r.useCallback)(async()=>{var e;if("function"!=typeof(null==(e=window.electron)?void 0:e.getNotes)){p(!1),x({title:"錯誤",description:"Electron API 不可用",variant:"destructive"});return}try{let e=(await window.electron.getNotes()).map(e=>({id:e.id,title:e.title,content:e.content,tags:e.tags}));u(e)}catch(e){x({title:"載入失敗",description:e instanceof Error?e.message:"無法載入筆記列表",variant:"destructive"})}finally{p(!1)}},[x]);(0,r.useEffect)(()=>{b()},[b]);let y=(0,r.useMemo)(()=>c.filter(e=>{let t=e.title.toLowerCase().includes(m.toLowerCase())||e.content.toLowerCase().includes(m.toLowerCase()),r=0===h.length||h.every(t=>{var r;return null==(r=e.tags)?void 0:r.includes(t)});return t&&r}),[c,m,h]),w=(0,r.useMemo)(()=>{let e=new Set;return c.forEach(t=>{var r;null==(r=t.tags)||r.forEach(t=>e.add(t))}),Array.from(e)},[c]);return f?(0,t.jsx)("div",{className:"flex items-center justify-center h-screen",children:e("home.loading")}):(0,t.jsxs)("div",{className:"container p-8 mx-auto",children:[(0,t.jsxs)("header",{className:"flex items-center justify-between mb-8",children:[(0,t.jsx)("h1",{className:"text-3xl font-bold",children:e("home.title")}),(0,t.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,t.jsx)(a.default,{href:"/edit",children:(0,t.jsx)(n.Button,{variant:"secondary",children:e("home.new_note")})}),(0,t.jsx)(a.default,{href:"/settings",children:(0,t.jsx)(n.Button,{variant:"outline",children:e("home.ai_settings")})}),(0,t.jsx)(d.ThemeToggle,{})]})]}),(0,t.jsxs)("main",{children:[(0,t.jsxs)("div",{className:"flex flex-col gap-4 mb-8 sm:flex-row",children:[(0,t.jsx)(o.Input,{placeholder:e("home.search_notes"),value:m,onChange:e=>v(e.target.value),className:"flex-1"}),(0,t.jsxs)(eb,{children:[(0,t.jsx)(ej,{asChild:!0,children:(0,t.jsxs)(n.Button,{variant:"outline",children:[e("home.tags"),h.length>0&&" (".concat(h.length,")")]})}),(0,t.jsx)(e_,{className:"w-[200px] p-0",children:(0,t.jsxs)(Z,{children:[(0,t.jsx)("div",{className:"flex flex-wrap gap-1 p-2",children:h.map(e=>(0,t.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 text-xs rounded-full bg-secondary text-secondary-foreground",children:[e,(0,t.jsx)("button",{onClick:()=>g(t=>t.filter(t=>t!==e)),className:"text-xs font-bold",children:"x"})]},e))}),(0,t.jsx)(J,{placeholder:e("home.search_tags")}),(0,t.jsxs)(Q,{children:[(0,t.jsx)(Y,{children:e("home.no_tags_found")}),(0,t.jsx)($,{children:w.map(e=>(0,t.jsx)(ee,{onSelect:()=>{g(t=>t.includes(e)?t.filter(t=>t!==e):[...t,e])},children:e},e))})]})]})})]})]}),(0,t.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4",children:y.map(r=>{var n;return(0,t.jsx)(a.default,{href:"/edit?id=".concat(r.id),children:(0,t.jsxs)(l.Card,{className:"flex flex-col h-full transition-colors transition-shadow cursor-pointer hover:bg-accent hover:shadow-lg",children:[(0,t.jsx)(l.CardHeader,{children:(0,t.jsx)(l.CardTitle,{className:"truncate",children:r.title||e("home.no_title")})}),(0,t.jsx)(l.CardContent,{className:"flex-1",children:(0,t.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-3",children:r.content||e("home.no_content")})}),(0,t.jsx)(l.CardFooter,{children:(0,t.jsx)("div",{className:"flex flex-wrap gap-2",children:null==(n=r.tags)?void 0:n.map(e=>(0,t.jsx)("div",{className:"px-2 py-1 text-xs rounded-full bg-secondary text-secondary-foreground",children:e},e))})})]})},r.id)})})]}),(0,t.jsx)(s.Toaster,{})]})}}]);