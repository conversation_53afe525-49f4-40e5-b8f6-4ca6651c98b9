"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-slug";
exports.ids = ["vendor-chunks/rehype-slug"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-slug/lib/index.js":
/*!***********************************************!*\
  !*** ./node_modules/rehype-slug/lib/index.js ***!
  \***********************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeSlug)\n/* harmony export */ });\n/* harmony import */ var github_slugger__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! github-slugger */ \"(ssr)/./node_modules/github-slugger/index.js\");\n/* harmony import */ var hast_util_heading_rank__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! hast-util-heading-rank */ \"(ssr)/./node_modules/hast-util-heading-rank/lib/index.js\");\n/* harmony import */ var hast_util_to_string__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! hast-util-to-string */ \"(ssr)/./node_modules/hast-util-to-string/lib/index.js\");\n/* harmony import */ var unist_util_visit__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! unist-util-visit */ \"(ssr)/./node_modules/unist-util-visit/lib/index.js\");\n/**\n * @typedef {import('hast').Root} Root\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {string} [prefix='']\n *   Prefix to add in front of `id`s (default: `''`).\n */\n\n\n\n\n\n\n/** @type {Options} */\nconst emptyOptions = {}\nconst slugs = new github_slugger__WEBPACK_IMPORTED_MODULE_0__[\"default\"]()\n\n/**\n * Add `id`s to headings.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns\n *   Transform.\n */\nfunction rehypeSlug(options) {\n  const settings = options || emptyOptions\n  const prefix = settings.prefix || ''\n\n  /**\n   * @param {Root} tree\n   *   Tree.\n   * @returns {undefined}\n   *   Nothing.\n   */\n  return function (tree) {\n    slugs.reset()\n\n    ;(0,unist_util_visit__WEBPACK_IMPORTED_MODULE_1__.visit)(tree, 'element', function (node) {\n      if ((0,hast_util_heading_rank__WEBPACK_IMPORTED_MODULE_2__.headingRank)(node) && !node.properties.id) {\n        node.properties.id = prefix + slugs.slug((0,hast_util_to_string__WEBPACK_IMPORTED_MODULE_3__.toString)(node))\n      }\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-slug/lib/index.js\n");

/***/ })

};
;