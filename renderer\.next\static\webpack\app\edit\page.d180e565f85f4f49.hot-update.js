"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* harmony import */ var next_dynamic__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dynamic */ \"(app-pages-browser)/./node_modules/next/dist/api/app-dynamic.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n// Fallback to @uiw/react-md-editor if Cherry fails\nconst MDEditor = (0,next_dynamic__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(()=>__webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_uiw_react-md-editor_esm_index_js\").then(__webpack_require__.bind(__webpack_require__, /*! @uiw/react-md-editor */ \"(app-pages-browser)/./node_modules/@uiw/react-md-editor/esm/index.js\")).then((mod)=>mod.default), {\n    loadableGenerated: {\n        modules: [\n            \"components\\\\CherryMarkdownEditor.tsx -> \" + \"@uiw/react-md-editor\"\n        ]\n    },\n    ssr: false\n});\n_c = MDEditor;\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c1 = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [cherryLoadFailed, setCherryLoadFailed] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 設置超時，如果 Cherry 載入時間過長則使用備用編輯器\n            const timeout = setTimeout({\n                \"CherryMarkdownEditor.useEffect.timeout\": ()=>{\n                    console.log(\"Cherry loading timeout, using fallback editor\");\n                    setCherryLoadFailed(true);\n                }\n            }[\"CherryMarkdownEditor.useEffect.timeout\"], 5000); // 5秒超時\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        console.log(\"Starting to load Cherry Markdown...\");\n                        // Try different import methods\n                        let CherryMarkdown;\n                        try {\n                            // First try the core version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_core_js\").then(__webpack_require__.t.bind(__webpack_require__, /*! cherry-markdown/dist/cherry-markdown.core */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.core.js\", 23));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry core module loaded:\", CherryModule);\n                        } catch (coreError) {\n                            console.log(\"Core version failed, trying full version:\", coreError);\n                            // Fallback to full version\n                            const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                            CherryMarkdown = CherryModule.default || CherryModule;\n                            console.log(\"Cherry full module loaded:\", CherryModule);\n                        }\n                        console.log(\"Cherry constructor:\", CherryMarkdown);\n                        console.log(\"Cherry constructor type:\", typeof CherryMarkdown);\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            console.log(\"Setting Cherry class...\");\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                            console.log(\"Cherry class set successfully\");\n                            clearTimeout(timeout); // 成功載入，清除超時\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                            setCherryLoadFailed(true);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                        // Set failed state to use fallback editor\n                        setCherryLoadFailed(true);\n                        clearTimeout(timeout);\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    clearTimeout(timeout);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            console.log(\"Cherry initialization effect triggered\", {\n                isClient,\n                CherryClass: !!CherryClass,\n                containerRef: !!containerRef.current,\n                preview,\n                hideToolbar,\n                theme\n            });\n            if (!isClient || !CherryClass || !containerRef.current) {\n                console.log(\"Cherry initialization skipped - missing requirements\");\n                return;\n            }\n            console.log(\"Starting Cherry initialization...\");\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                console.log(\"Destroying existing Cherry instance\");\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            console.log(\"Container cleared\");\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    width: '100%',\n                    autoHeight: false,\n                    autoWidth: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default',\n                        viewportMargin: 0,\n                        scrollbarStyle: 'native'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false,\n                    lazyLoadImg: {\n                        loadingImgPath: '',\n                        maxNumPerTime: 2,\n                        noLoadImgNum: 5,\n                        autoLoadImgNum: 5,\n                        maxTryTimesPerSrc: 2,\n                        loadingImgMaxWaitTime: 5000\n                    }\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            console.log(\"Cherry afterInit callback triggered\");\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        console.log(\"Setting Cherry element styles\");\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                        cherryElement.style.width = '100%';\n                                        cherryElement.style.maxWidth = '100%';\n                                        cherryElement.style.contain = 'layout style';\n                                        cherryElement.style.display = 'flex';\n                                        cherryElement.style.flexDirection = preview === 'edit' ? 'column' : preview === 'preview' ? 'column' : 'row';\n                                        // Also fix child elements\n                                        const editor = cherryElement.querySelector('.cherry-editor');\n                                        const previewer = cherryElement.querySelector('.cherry-previewer');\n                                        if (editor) {\n                                            editor.style.position = 'relative';\n                                            editor.style.overflow = 'hidden';\n                                            editor.style.maxHeight = '100%';\n                                            editor.style.maxWidth = '100%';\n                                        }\n                                        if (previewer) {\n                                            previewer.style.position = 'relative';\n                                            previewer.style.overflow = 'hidden';\n                                            previewer.style.maxHeight = '100%';\n                                            previewer.style.maxWidth = '100%';\n                                        }\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                    // Add mutation observer to watch for any elements created outside container\n                                    const observer = new MutationObserver({\n                                        \"CherryMarkdownEditor.useEffect\": (mutations)=>{\n                                            mutations.forEach({\n                                                \"CherryMarkdownEditor.useEffect\": (mutation)=>{\n                                                    mutation.addedNodes.forEach({\n                                                        \"CherryMarkdownEditor.useEffect\": (node)=>{\n                                                            if (node.nodeType === Node.ELEMENT_NODE) {\n                                                                const element = node;\n                                                                // Check if this element is a Cherry-related element outside our container\n                                                                if (element.className && (element.className.includes('cherry') || element.className.includes('CodeMirror') || element.className.includes('cm-')) && !container.contains(element)) {\n                                                                    console.log(\"Found Cherry element outside container, moving it:\", element);\n                                                                    // Move the element into our container\n                                                                    if (cherryElement) {\n                                                                        cherryElement.appendChild(element);\n                                                                    }\n                                                                }\n                                                            }\n                                                        }\n                                                    }[\"CherryMarkdownEditor.useEffect\"]);\n                                                }\n                                            }[\"CherryMarkdownEditor.useEffect\"]);\n                                        }\n                                    }[\"CherryMarkdownEditor.useEffect\"]);\n                                    // Start observing the document body for changes\n                                    observer.observe(document.body, {\n                                        childList: true,\n                                        subtree: true\n                                    });\n                                    // Store observer for cleanup\n                                    cherryInstance._observer = observer;\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            console.log(\"Cherry config prepared:\", cherryConfig);\n            try {\n                console.log(\"Creating new Cherry instance...\");\n                cherryRef.current = new CherryClass(cherryConfig);\n                console.log(\"Cherry instance created successfully:\", cherryRef.current);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        // Disconnect mutation observer if it exists\n                        if (cherryRef.current._observer) {\n                            cherryRef.current._observer.disconnect();\n                        }\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息或簡單編輯器\n    if (!isClient) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 367,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 355,\n            columnNumber: 9\n        }, undefined);\n    }\n    // 如果 Cherry 載入失敗，使用 MDEditor 作為備用編輯器\n    if (!CherryClass) {\n        if (cherryLoadFailed) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"cherry-markdown-editor \".concat(className),\n                style: {\n                    height: \"100%\",\n                    width: \"100%\",\n                    maxHeight: \"100%\",\n                    display: \"flex\",\n                    flexDirection: \"column\",\n                    position: \"relative\",\n                    overflow: \"hidden\",\n                    contain: \"layout style\"\n                },\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(MDEditor, {\n                    value: value,\n                    onChange: (val)=>onChange && onChange(val || \"\"),\n                    preview: preview === \"preview\" ? \"preview\" : preview === \"edit\" ? \"edit\" : \"live\",\n                    hideToolbar: hideToolbar,\n                    \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\",\n                    height: \"100%\",\n                    style: {\n                        height: \"100%\",\n                        maxHeight: \"100%\",\n                        overflow: \"hidden\",\n                        position: \"relative\"\n                    }\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 389,\n                    columnNumber: 13\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 376,\n                columnNumber: 11\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                flexDirection: \"column\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        padding: \"8px\",\n                        borderBottom: \"1px solid hsl(var(--border))\",\n                        fontSize: \"12px\",\n                        color: \"hsl(var(--muted-foreground))\"\n                    },\n                    children: \"載入編輯器中...\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 419,\n                    columnNumber: 11\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    style: {\n                        flex: 1,\n                        display: \"flex\",\n                        alignItems: \"center\",\n                        justifyContent: \"center\"\n                    },\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: \"正在載入 Cherry Markdown 編輯器...\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                        lineNumber: 423,\n                        columnNumber: 13\n                    }, undefined)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                    lineNumber: 422,\n                    columnNumber: 11\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 408,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 430,\n        columnNumber: 7\n    }, undefined);\n}, \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"C/5YkdhnPi5/8L6jjMRXvofHavk=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c2 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"MDEditor\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c2, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});