module.exports=[96869,a=>{"use strict";a.s(["default",()=>aL],96869);var b=a.i(28386),c=a.i(54436),d=a.i(46563),e=a.i(11105),f=a.i(23e3),g=a.i(47346),h=a.i(3332),i=a.i(41930),j=a.i(30260),k=/[\\\/_+.#"@\[\(\{&]/,l=/[\\\/_+.#"@\[\(\{&]/g,m=/[\s-]/,n=/[\s-]/g;function o(a){return a.toLowerCase().replace(n," ")}var p=a.i(19988),q=a.i(40686),r=a.i(73490),s=a.i(42527),t='[cmdk-group=""]',u='[cmdk-group-items=""]',v='[cmdk-item=""]',w=`${v}:not([aria-disabled="true"])`,x="cmdk-item-select",y="data-value",z=(a,b,c)=>(function(a,b,c){return function a(b,c,d,e,f,g,h){if(g===c.length)return f===b.length?1:.99;var i=`${f},${g}`;if(void 0!==h[i])return h[i];for(var j,o,p,q,r=e.charAt(g),s=d.indexOf(r,f),t=0;s>=0;)(j=a(b,c,d,e,s+1,g+1,h))>t&&(s===f?j*=1:k.test(b.charAt(s-1))?(j*=.8,(p=b.slice(f,s-1).match(l))&&f>0&&(j*=Math.pow(.999,p.length))):m.test(b.charAt(s-1))?(j*=.9,(q=b.slice(f,s-1).match(n))&&f>0&&(j*=Math.pow(.999,q.length))):(j*=.17,f>0&&(j*=Math.pow(.999,s-f))),b.charAt(s)!==c.charAt(g)&&(j*=.9999)),(j<.1&&d.charAt(s-1)===e.charAt(g+1)||e.charAt(g+1)===e.charAt(g)&&d.charAt(s-1)!==e.charAt(g))&&.1*(o=a(b,c,d,e,s+1,g+2,h))>j&&(j=.1*o),j>t&&(t=j),s=d.indexOf(r,s+1);return h[i]=t,t}(a=c&&c.length>0?`${a+" "+c.join(" ")}`:a,b,o(a),o(b),0,0,{})})(a,b,c),A=c.createContext(void 0),B=()=>c.useContext(A),C=c.createContext(void 0),D=()=>c.useContext(C),E=c.createContext(void 0),F=c.forwardRef((a,b)=>{let d=P(()=>{var b,c;return{search:"",value:null!=(c=null!=(b=a.value)?b:a.defaultValue)?c:"",selectedItemId:void 0,filtered:{count:0,items:new Map,groups:new Set}}}),e=P(()=>new Set),f=P(()=>new Map),g=P(()=>new Map),h=P(()=>new Set),i=N(a),{label:j,children:k,value:l,onValueChange:m,filter:n,shouldFilter:o,loop:p,disablePointerSelection:s=!1,vimBindings:B=!0,...D}=a,E=(0,r.useId)(),F=(0,r.useId)(),G=(0,r.useId)(),H=c.useRef(null),I=S();O(()=>{if(void 0!==l){let a=l.trim();d.current.value=a,J.emit()}},[l]),O(()=>{I(6,V)},[]);let J=c.useMemo(()=>({subscribe:a=>(h.current.add(a),()=>h.current.delete(a)),snapshot:()=>d.current,setState:(a,b,c)=>{var e,f,g,h;if(!Object.is(d.current[a],b)){if(d.current[a]=b,"search"===a)R(),M(),I(1,Q);else if("value"===a){if(document.activeElement.hasAttribute("cmdk-input")||document.activeElement.hasAttribute("cmdk-root")){let a=document.getElementById(G);a?a.focus():null==(e=document.getElementById(E))||e.focus()}if(I(7,()=>{var a;d.current.selectedItemId=null==(a=W())?void 0:a.id,J.emit()}),c||I(5,V),(null==(f=i.current)?void 0:f.value)!==void 0){null==(h=(g=i.current).onValueChange)||h.call(g,null!=b?b:"");return}}J.emit()}},emit:()=>{h.current.forEach(a=>a())}}),[]),K=c.useMemo(()=>({value:(a,b,c)=>{var e;b!==(null==(e=g.current.get(a))?void 0:e.value)&&(g.current.set(a,{value:b,keywords:c}),d.current.filtered.items.set(a,L(b,c)),I(2,()=>{M(),J.emit()}))},item:(a,b)=>(e.current.add(a),b&&(f.current.has(b)?f.current.get(b).add(a):f.current.set(b,new Set([a]))),I(3,()=>{R(),M(),d.current.value||Q(),J.emit()}),()=>{g.current.delete(a),e.current.delete(a),d.current.filtered.items.delete(a);let b=W();I(4,()=>{R(),(null==b?void 0:b.getAttribute("id"))===a&&Q(),J.emit()})}),group:a=>(f.current.has(a)||f.current.set(a,new Set),()=>{g.current.delete(a),f.current.delete(a)}),filter:()=>i.current.shouldFilter,label:j||a["aria-label"],getDisablePointerSelection:()=>i.current.disablePointerSelection,listId:E,inputId:G,labelId:F,listInnerRef:H}),[]);function L(a,b){var c,e;let f=null!=(e=null==(c=i.current)?void 0:c.filter)?e:z;return a?f(a,d.current.search,b):0}function M(){if(!d.current.search||!1===i.current.shouldFilter)return;let a=d.current.filtered.items,b=[];d.current.filtered.groups.forEach(c=>{let d=f.current.get(c),e=0;d.forEach(b=>{e=Math.max(a.get(b),e)}),b.push([c,e])});let c=H.current;X().sort((b,c)=>{var d,e;let f=b.getAttribute("id"),g=c.getAttribute("id");return(null!=(d=a.get(g))?d:0)-(null!=(e=a.get(f))?e:0)}).forEach(a=>{let b=a.closest(u);b?b.appendChild(a.parentElement===b?a:a.closest(`${u} > *`)):c.appendChild(a.parentElement===c?a:a.closest(`${u} > *`))}),b.sort((a,b)=>b[1]-a[1]).forEach(a=>{var b;let c=null==(b=H.current)?void 0:b.querySelector(`${t}[${y}="${encodeURIComponent(a[0])}"]`);null==c||c.parentElement.appendChild(c)})}function Q(){let a=X().find(a=>"true"!==a.getAttribute("aria-disabled")),b=null==a?void 0:a.getAttribute(y);J.setState("value",b||void 0)}function R(){var a,b,c,h;if(!d.current.search||!1===i.current.shouldFilter){d.current.filtered.count=e.current.size;return}d.current.filtered.groups=new Set;let j=0;for(let f of e.current){let e=L(null!=(b=null==(a=g.current.get(f))?void 0:a.value)?b:"",null!=(h=null==(c=g.current.get(f))?void 0:c.keywords)?h:[]);d.current.filtered.items.set(f,e),e>0&&j++}for(let[a,b]of f.current)for(let c of b)if(d.current.filtered.items.get(c)>0){d.current.filtered.groups.add(a);break}d.current.filtered.count=j}function V(){var a,b,c;let d=W();d&&((null==(a=d.parentElement)?void 0:a.firstChild)===d&&(null==(c=null==(b=d.closest(t))?void 0:b.querySelector('[cmdk-group-heading=""]'))||c.scrollIntoView({block:"nearest"})),d.scrollIntoView({block:"nearest"}))}function W(){var a;return null==(a=H.current)?void 0:a.querySelector(`${v}[aria-selected="true"]`)}function X(){var a;return Array.from((null==(a=H.current)?void 0:a.querySelectorAll(w))||[])}function Y(a){let b=X()[a];b&&J.setState("value",b.getAttribute(y))}function Z(a){var b;let c=W(),d=X(),e=d.findIndex(a=>a===c),f=d[e+a];null!=(b=i.current)&&b.loop&&(f=e+a<0?d[d.length-1]:e+a===d.length?d[0]:d[e+a]),f&&J.setState("value",f.getAttribute(y))}function $(a){let b=W(),c=null==b?void 0:b.closest(t),d;for(;c&&!d;)d=null==(c=a>0?function(a,b){let c=a.nextElementSibling;for(;c;){if(c.matches(b))return c;c=c.nextElementSibling}}(c,t):function(a,b){let c=a.previousElementSibling;for(;c;){if(c.matches(b))return c;c=c.previousElementSibling}}(c,t))?void 0:c.querySelector(w);d?J.setState("value",d.getAttribute(y)):Z(a)}let _=()=>Y(X().length-1),aa=a=>{a.preventDefault(),a.metaKey?_():a.altKey?$(1):Z(1)},ab=a=>{a.preventDefault(),a.metaKey?Y(0):a.altKey?$(-1):Z(-1)};return c.createElement(q.Primitive.div,{ref:b,tabIndex:-1,...D,"cmdk-root":"",onKeyDown:a=>{var b;null==(b=D.onKeyDown)||b.call(D,a);let c=a.nativeEvent.isComposing||229===a.keyCode;if(!(a.defaultPrevented||c))switch(a.key){case"n":case"j":B&&a.ctrlKey&&aa(a);break;case"ArrowDown":aa(a);break;case"p":case"k":B&&a.ctrlKey&&ab(a);break;case"ArrowUp":ab(a);break;case"Home":a.preventDefault(),Y(0);break;case"End":a.preventDefault(),_();break;case"Enter":{a.preventDefault();let b=W();if(b){let a=new Event(x);b.dispatchEvent(a)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:K.inputId,id:K.labelId,style:U},j),T(a,a=>c.createElement(C.Provider,{value:J},c.createElement(A.Provider,{value:K},a))))}),G=c.forwardRef((a,b)=>{var d,e;let f=(0,r.useId)(),g=c.useRef(null),h=c.useContext(E),i=B(),j=N(a),k=null!=(e=null==(d=j.current)?void 0:d.forceMount)?e:null==h?void 0:h.forceMount;O(()=>{if(!k)return i.item(f,null==h?void 0:h.id)},[k]);let l=R(f,g,[a.value,a.children,g],a.keywords),m=D(),n=Q(a=>a.value&&a.value===l.current),o=Q(a=>!!k||!1===i.filter()||!a.search||a.filtered.items.get(f)>0);function p(){var a,b;t(),null==(b=(a=j.current).onSelect)||b.call(a,l.current)}function t(){m.setState("value",l.current,!0)}if(c.useEffect(()=>{let b=g.current;if(!(!b||a.disabled))return b.addEventListener(x,p),()=>b.removeEventListener(x,p)},[o,a.onSelect,a.disabled]),!o)return null;let{disabled:u,value:v,onSelect:w,forceMount:y,keywords:z,...A}=a;return c.createElement(q.Primitive.div,{ref:(0,s.composeRefs)(g,b),...A,id:f,"cmdk-item":"",role:"option","aria-disabled":!!u,"aria-selected":!!n,"data-disabled":!!u,"data-selected":!!n,onPointerMove:u||i.getDisablePointerSelection()?void 0:t,onClick:u?void 0:p},a.children)}),H=c.forwardRef((a,b)=>{let{heading:d,children:e,forceMount:f,...g}=a,h=(0,r.useId)(),i=c.useRef(null),j=c.useRef(null),k=(0,r.useId)(),l=B(),m=Q(a=>!!f||!1===l.filter()||!a.search||a.filtered.groups.has(h));O(()=>l.group(h),[]),R(h,i,[a.value,a.heading,j]);let n=c.useMemo(()=>({id:h,forceMount:f}),[f]);return c.createElement(q.Primitive.div,{ref:(0,s.composeRefs)(i,b),...g,"cmdk-group":"",role:"presentation",hidden:!m||void 0},d&&c.createElement("div",{ref:j,"cmdk-group-heading":"","aria-hidden":!0,id:k},d),T(a,a=>c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":d?k:void 0},c.createElement(E.Provider,{value:n},a))))}),I=c.forwardRef((a,b)=>{let{alwaysRender:d,...e}=a,f=c.useRef(null),g=Q(a=>!a.search);return d||g?c.createElement(q.Primitive.div,{ref:(0,s.composeRefs)(f,b),...e,"cmdk-separator":"",role:"separator"}):null}),J=c.forwardRef((a,b)=>{let{onValueChange:d,...e}=a,f=null!=a.value,g=D(),h=Q(a=>a.search),i=Q(a=>a.selectedItemId),j=B();return c.useEffect(()=>{null!=a.value&&g.setState("search",a.value)},[a.value]),c.createElement(q.Primitive.input,{ref:b,...e,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":j.listId,"aria-labelledby":j.labelId,"aria-activedescendant":i,id:j.inputId,type:"text",value:f?a.value:h,onChange:a=>{f||g.setState("search",a.target.value),null==d||d(a.target.value)}})}),K=c.forwardRef((a,b)=>{let{children:d,label:e="Suggestions",...f}=a,g=c.useRef(null),h=c.useRef(null),i=Q(a=>a.selectedItemId),j=B();return c.useEffect(()=>{if(h.current&&g.current){let a=h.current,b=g.current,c,d=new ResizeObserver(()=>{c=requestAnimationFrame(()=>{let c=a.offsetHeight;b.style.setProperty("--cmdk-list-height",c.toFixed(1)+"px")})});return d.observe(a),()=>{cancelAnimationFrame(c),d.unobserve(a)}}},[]),c.createElement(q.Primitive.div,{ref:(0,s.composeRefs)(g,b),...f,"cmdk-list":"",role:"listbox",tabIndex:-1,"aria-activedescendant":i,"aria-label":e,id:j.listId},T(a,a=>c.createElement("div",{ref:(0,s.composeRefs)(h,j.listInnerRef),"cmdk-list-sizer":""},a)))}),L=c.forwardRef((a,b)=>{let{open:d,onOpenChange:e,overlayClassName:f,contentClassName:g,container:h,...i}=a;return c.createElement(p.Root,{open:d,onOpenChange:e},c.createElement(p.Portal,{container:h},c.createElement(p.Overlay,{"cmdk-overlay":"",className:f}),c.createElement(p.Content,{"aria-label":a.label,"cmdk-dialog":"",className:g},c.createElement(F,{ref:b,...i}))))}),M=Object.assign(F,{List:K,Item:G,Input:J,Group:H,Separator:I,Dialog:L,Empty:c.forwardRef((a,b)=>Q(a=>0===a.filtered.count)?c.createElement(q.Primitive.div,{ref:b,...a,"cmdk-empty":"",role:"presentation"}):null),Loading:c.forwardRef((a,b)=>{let{progress:d,children:e,label:f="Loading...",...g}=a;return c.createElement(q.Primitive.div,{ref:b,...g,"cmdk-loading":"",role:"progressbar","aria-valuenow":d,"aria-valuemin":0,"aria-valuemax":100,"aria-label":f},T(a,a=>c.createElement("div",{"aria-hidden":!0},a)))})});function N(a){let b=c.useRef(a);return O(()=>{b.current=a}),b}var O=c.useEffect;function P(a){let b=c.useRef();return void 0===b.current&&(b.current=a()),b}function Q(a){let b=D(),d=()=>a(b.snapshot());return c.useSyncExternalStore(b.subscribe,d,d)}function R(a,b,d,e=[]){let f=c.useRef(),g=B();return O(()=>{var c;let h=(()=>{var a;for(let b of d){if("string"==typeof b)return b.trim();if("object"==typeof b&&"current"in b)return b.current?null==(a=b.current.textContent)?void 0:a.trim():f.current}})(),i=e.map(a=>a.trim());g.value(a,h,i),null==(c=b.current)||c.setAttribute(y,h),f.current=h}),f}var S=()=>{let[a,b]=c.useState(),d=P(()=>new Map);return O(()=>{d.current.forEach(a=>a()),d.current=new Map},[a]),(a,c)=>{d.current.set(a,c),b({})}};function T({asChild:a,children:b},d){let e;return a&&c.isValidElement(b)?c.cloneElement("function"==typeof(e=b.type)?e(b.props):"render"in e?e.render(b.props):b,{ref:b.ref},d(b.props.children)):d(b)}var U={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"},V=a.i(1564),W=a.i(90334);a.i(72846);let X=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(M,{ref:d,className:(0,W.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",a),...c}));X.displayName=M.displayName;let Y=c.forwardRef(({className:a,...c},d)=>(0,b.jsxs)("div",{className:"flex items-center px-3 border-b border-border","cmdk-input-wrapper":"",children:[(0,b.jsx)(V.Search,{className:"w-4 h-4 mr-2 opacity-50 shrink-0"}),(0,b.jsx)(M.Input,{ref:d,className:(0,W.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",a),...c})]}));Y.displayName=M.Input.displayName;let Z=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(M.List,{ref:d,className:(0,W.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",a),...c}));Z.displayName=M.List.displayName;let $=c.forwardRef((a,c)=>(0,b.jsx)(M.Empty,{ref:c,className:"py-6 text-sm text-center",...a}));$.displayName=M.Empty.displayName;let _=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(M.Group,{ref:d,className:(0,W.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",a),...c}));_.displayName=M.Group.displayName,c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(M.Separator,{ref:d,className:(0,W.cn)("-mx-1 h-px bg-border",a),...c})).displayName=M.Separator.displayName;let aa=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(M.Item,{ref:d,className:(0,W.cn)("relative flex cursor-default gap-2 select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a),...c}));aa.displayName=M.Item.displayName;var ab=a.i(64161),ac=a.i(33616),ad=a.i(28432),ae=a.i(9935),af=a.i(14053),ag=a.i(64296),ah=a.i(62705),ai=a.i(15209),aj=a.i(57495),ak=a.i(9851),al=a.i(85502),am=a.i(6022),an="Popover",[ao,ap]=(0,ac.createContextScope)(an,[ag.createPopperScope]),aq=(0,ag.createPopperScope)(),[ar,as]=ao(an),at=a=>{let{__scopePopover:d,children:e,open:f,defaultOpen:g,onOpenChange:h,modal:i=!1}=a,j=aq(d),k=c.useRef(null),[l,m]=c.useState(!1),[n,o]=(0,ak.useControllableState)({prop:f,defaultProp:g??!1,onChange:h,caller:an});return(0,b.jsx)(ag.Root,{...j,children:(0,b.jsx)(ar,{scope:d,contentId:(0,r.useId)(),triggerRef:k,open:n,onOpenChange:o,onOpenToggle:c.useCallback(()=>o(a=>!a),[o]),hasCustomAnchor:l,onCustomAnchorAdd:c.useCallback(()=>m(!0),[]),onCustomAnchorRemove:c.useCallback(()=>m(!1),[]),modal:i,children:e})})};at.displayName=an;var au="PopoverAnchor";c.forwardRef((a,d)=>{let{__scopePopover:e,...f}=a,g=as(au,e),h=aq(e),{onCustomAnchorAdd:i,onCustomAnchorRemove:j}=g;return c.useEffect(()=>(i(),()=>j()),[i,j]),(0,b.jsx)(ag.Anchor,{...h,...f,ref:d})}).displayName=au;var av="PopoverTrigger",aw=c.forwardRef((a,c)=>{let{__scopePopover:d,...e}=a,f=as(av,d),g=aq(d),h=(0,s.useComposedRefs)(c,f.triggerRef),i=(0,b.jsx)(q.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":f.open,"aria-controls":f.contentId,"data-state":aI(f.open),...e,ref:h,onClick:(0,ab.composeEventHandlers)(a.onClick,f.onOpenToggle)});return f.hasCustomAnchor?i:(0,b.jsx)(ag.Anchor,{asChild:!0,...g,children:i})});aw.displayName=av;var ax="PopoverPortal",[ay,az]=ao(ax,{forceMount:void 0}),aA=a=>{let{__scopePopover:c,forceMount:d,children:e,container:f}=a,g=as(ax,c);return(0,b.jsx)(ay,{scope:c,forceMount:d,children:(0,b.jsx)(ai.Presence,{present:d||g.open,children:(0,b.jsx)(ah.Portal,{asChild:!0,container:f,children:e})})})};aA.displayName=ax;var aB="PopoverContent",aC=c.forwardRef((a,c)=>{let d=az(aB,a.__scopePopover),{forceMount:e=d.forceMount,...f}=a,g=as(aB,a.__scopePopover);return(0,b.jsx)(ai.Presence,{present:e||g.open,children:g.modal?(0,b.jsx)(aE,{...f,ref:c}):(0,b.jsx)(aF,{...f,ref:c})})});aC.displayName=aB;var aD=(0,aj.createSlot)("PopoverContent.RemoveScroll"),aE=c.forwardRef((a,d)=>{let e=as(aB,a.__scopePopover),f=c.useRef(null),g=(0,s.useComposedRefs)(d,f),h=c.useRef(!1);return c.useEffect(()=>{let a=f.current;if(a)return(0,al.hideOthers)(a)},[]),(0,b.jsx)(am.RemoveScroll,{as:aD,allowPinchZoom:!0,children:(0,b.jsx)(aG,{...a,ref:g,trapFocus:e.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,ab.composeEventHandlers)(a.onCloseAutoFocus,a=>{a.preventDefault(),h.current||e.triggerRef.current?.focus()}),onPointerDownOutside:(0,ab.composeEventHandlers)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;h.current=2===b.button||c},{checkForDefaultPrevented:!1}),onFocusOutside:(0,ab.composeEventHandlers)(a.onFocusOutside,a=>a.preventDefault(),{checkForDefaultPrevented:!1})})})}),aF=c.forwardRef((a,d)=>{let e=as(aB,a.__scopePopover),f=c.useRef(!1),g=c.useRef(!1);return(0,b.jsx)(aG,{...a,ref:d,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(f.current||e.triggerRef.current?.focus(),b.preventDefault()),f.current=!1,g.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(f.current=!0,"pointerdown"===b.detail.originalEvent.type&&(g.current=!0));let c=b.target;e.triggerRef.current?.contains(c)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&g.current&&b.preventDefault()}})}),aG=c.forwardRef((a,c)=>{let{__scopePopover:d,trapFocus:e,onOpenAutoFocus:f,onCloseAutoFocus:g,disableOutsidePointerEvents:h,onEscapeKeyDown:i,onPointerDownOutside:j,onFocusOutside:k,onInteractOutside:l,...m}=a,n=as(aB,d),o=aq(d);return(0,ae.useFocusGuards)(),(0,b.jsx)(af.FocusScope,{asChild:!0,loop:!0,trapped:e,onMountAutoFocus:f,onUnmountAutoFocus:g,children:(0,b.jsx)(ad.DismissableLayer,{asChild:!0,disableOutsidePointerEvents:h,onInteractOutside:l,onEscapeKeyDown:i,onPointerDownOutside:j,onFocusOutside:k,onDismiss:()=>n.onOpenChange(!1),children:(0,b.jsx)(ag.Content,{"data-state":aI(n.open),role:"dialog",id:n.contentId,...o,...m,ref:c,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),aH="PopoverClose";function aI(a){return a?"open":"closed"}c.forwardRef((a,c)=>{let{__scopePopover:d,...e}=a,f=as(aH,d);return(0,b.jsx)(q.Primitive.button,{type:"button",...e,ref:c,onClick:(0,ab.composeEventHandlers)(a.onClick,()=>f.onOpenChange(!1))})}).displayName=aH,c.forwardRef((a,c)=>{let{__scopePopover:d,...e}=a,f=aq(d);return(0,b.jsx)(ag.Arrow,{...f,...e,ref:c})}).displayName="PopoverArrow";let aJ=c.forwardRef(({className:a,align:c="center",sideOffset:d=4,...e},f)=>(0,b.jsx)(aA,{children:(0,b.jsx)(aC,{ref:f,align:c,sideOffset:d,className:(0,W.cn)("z-50 w-72 rounded-md border border-border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 origin-[--radix-popover-content-transform-origin]",a),...e})}));aJ.displayName=aC.displayName;var aK=a.i(1905);function aL(){let{t:a}=(0,aK.useI18n)(),[k,l]=(0,c.useState)([]),[m,n]=(0,c.useState)(!0),[o,p]=(0,c.useState)(""),[q,r]=(0,c.useState)([]),{toast:s}=(0,h.useToast)(),t=(0,c.useCallback)(async()=>{if("function"!=typeof window.electron?.getNotes){n(!1),s({title:"錯誤",description:"Electron API 不可用",variant:"destructive"});return}try{let a=(await window.electron.getNotes()).map(a=>({id:a.id,title:a.title,content:a.content,tags:a.tags}));l(a)}catch(a){s({title:"載入失敗",description:a instanceof Error?a.message:"無法載入筆記列表",variant:"destructive"})}finally{n(!1)}},[s]);(0,c.useEffect)(()=>{t()},[t]);let u=(0,c.useMemo)(()=>k.filter(a=>{let b=a.title.toLowerCase().includes(o.toLowerCase())||a.content.toLowerCase().includes(o.toLowerCase()),c=0===q.length||q.every(b=>a.tags?.includes(b));return b&&c}),[k,o,q]),v=(0,c.useMemo)(()=>{let a=new Set;return k.forEach(b=>{b.tags?.forEach(b=>a.add(b))}),Array.from(a)},[k]);return m?(0,b.jsx)("div",{className:"flex items-center justify-center h-screen",children:a("home.loading")}):(0,b.jsxs)("div",{className:"container p-8 mx-auto",children:[(0,b.jsxs)("header",{className:"flex items-center justify-between mb-8",children:[(0,b.jsx)("h1",{className:"text-3xl font-bold",children:a("home.title")}),(0,b.jsxs)("div",{className:"flex items-center space-x-4",children:[(0,b.jsx)(d.default,{href:"/edit",children:(0,b.jsx)(e.Button,{variant:"secondary",children:a("home.new_note")})}),(0,b.jsx)(d.default,{href:"/settings",children:(0,b.jsx)(e.Button,{variant:"outline",children:a("home.ai_settings")})}),(0,b.jsx)(j.ThemeToggle,{})]})]}),(0,b.jsxs)("main",{children:[(0,b.jsxs)("div",{className:"flex flex-col gap-4 mb-8 sm:flex-row",children:[(0,b.jsx)(g.Input,{placeholder:a("home.search_notes"),value:o,onChange:a=>p(a.target.value),className:"flex-1"}),(0,b.jsxs)(at,{children:[(0,b.jsx)(aw,{asChild:!0,children:(0,b.jsxs)(e.Button,{variant:"outline",children:[a("home.tags"),q.length>0&&` (${q.length})`]})}),(0,b.jsx)(aJ,{className:"w-[200px] p-0",children:(0,b.jsxs)(X,{children:[(0,b.jsx)("div",{className:"flex flex-wrap gap-1 p-2",children:q.map(a=>(0,b.jsxs)("div",{className:"flex items-center gap-1 px-2 py-1 text-xs rounded-full bg-secondary text-secondary-foreground",children:[a,(0,b.jsx)("button",{onClick:()=>r(b=>b.filter(b=>b!==a)),className:"text-xs font-bold",children:"x"})]},a))}),(0,b.jsx)(Y,{placeholder:a("home.search_tags")}),(0,b.jsxs)(Z,{children:[(0,b.jsx)($,{children:a("home.no_tags_found")}),(0,b.jsx)(_,{children:v.map(a=>(0,b.jsx)(aa,{onSelect:()=>{r(b=>b.includes(a)?b.filter(b=>b!==a):[...b,a])},children:a},a))})]})]})})]})]}),(0,b.jsx)("div",{className:"grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4",children:u.map(c=>(0,b.jsx)(d.default,{href:`/edit?id=${c.id}`,children:(0,b.jsxs)(f.Card,{className:"flex flex-col h-full transition-colors transition-shadow cursor-pointer hover:bg-accent hover:shadow-lg",children:[(0,b.jsx)(f.CardHeader,{children:(0,b.jsx)(f.CardTitle,{className:"truncate",children:c.title||a("home.no_title")})}),(0,b.jsx)(f.CardContent,{className:"flex-1",children:(0,b.jsx)("p",{className:"text-sm text-muted-foreground line-clamp-3",children:c.content||a("home.no_content")})}),(0,b.jsx)(f.CardFooter,{children:(0,b.jsx)("div",{className:"flex flex-wrap gap-2",children:c.tags?.map(a=>(0,b.jsx)("div",{className:"px-2 py-1 text-xs rounded-full bg-secondary text-secondary-foreground",children:a},a))})})]})},c.id))})]}),(0,b.jsx)(i.Toaster,{})]})}}];

//# sourceMappingURL=renderer_src_app_page_tsx_08a3637f._.js.map