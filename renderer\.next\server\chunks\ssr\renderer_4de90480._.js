module.exports=[5670,(a,b,c)=>{"use strict";function d(a){return Array.isArray(a)?a[1]:a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"getSegmentValue",{enumerable:!0,get:function(){return d}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},23741,(a,b,c)=>{"use strict";function d(a){return"("===a[0]&&a.endsWith(")")}function e(a){return a.startsWith("@")&&"@children"!==a}function f(a,b){if(a.includes(g)){let a=JSON.stringify(b);return"{}"!==a?g+"?"+a:g}return a}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DEFAULT_SEGMENT_KEY:function(){return h},PAGE_SEGMENT_KEY:function(){return g},addSearchParamsIfPageSegment:function(){return f},isGroupSegment:function(){return d},isParallelRouteSegment:function(){return e}});let g="__PAGE__",h="__DEFAULT__"},38626,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"RedirectStatusCode",{enumerable:!0,get:function(){return d}});var d=function(a){return a[a.SeeOther=303]="SeeOther",a[a.TemporaryRedirect=307]="TemporaryRedirect",a[a.PermanentRedirect=308]="PermanentRedirect",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},34816,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{REDIRECT_ERROR_CODE:function(){return e},RedirectType:function(){return f},isRedirectError:function(){return g}});let d=a.r(38626),e="NEXT_REDIRECT";var f=function(a){return a.push="push",a.replace="replace",a}({});function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let b=a.digest.split(";"),[c,f]=b,g=b.slice(2,-2).join(";"),h=Number(b.at(-2));return c===e&&("replace"===f||"push"===f)&&"string"==typeof g&&!isNaN(h)&&h in d.RedirectStatusCode}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},28548,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getRedirectError:function(){return g},getRedirectStatusCodeFromError:function(){return l},getRedirectTypeFromError:function(){return k},getURLFromRedirectError:function(){return j},permanentRedirect:function(){return i},redirect:function(){return h}});let d=a.r(38626),e=a.r(34816),f=a.r(20635).actionAsyncStorage;function g(a,b,c){void 0===c&&(c=d.RedirectStatusCode.TemporaryRedirect);let f=Object.defineProperty(Error(e.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return f.digest=e.REDIRECT_ERROR_CODE+";"+b+";"+a+";"+c+";",f}function h(a,b){var c;throw null!=b||(b=(null==f||null==(c=f.getStore())?void 0:c.isAction)?e.RedirectType.push:e.RedirectType.replace),g(a,b,d.RedirectStatusCode.TemporaryRedirect)}function i(a,b){throw void 0===b&&(b=e.RedirectType.replace),g(a,b,d.RedirectStatusCode.PermanentRedirect)}function j(a){return(0,e.isRedirectError)(a)?a.digest.split(";").slice(2,-2).join(";"):null}function k(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return a.digest.split(";",2)[1]}function l(a){if(!(0,e.isRedirectError)(a))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(a.digest.split(";").at(-2))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},90784,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTTPAccessErrorStatus:function(){return d},HTTP_ERROR_FALLBACK_ERROR_CODE:function(){return f},getAccessFallbackErrorTypeByStatus:function(){return i},getAccessFallbackHTTPStatus:function(){return h},isHTTPAccessFallbackError:function(){return g}});let d={NOT_FOUND:404,FORBIDDEN:403,UNAUTHORIZED:401},e=new Set(Object.values(d)),f="NEXT_HTTP_ERROR_FALLBACK";function g(a){if("object"!=typeof a||null===a||!("digest"in a)||"string"!=typeof a.digest)return!1;let[b,c]=a.digest.split(";");return b===f&&e.has(Number(c))}function h(a){return Number(a.digest.split(";")[1])}function i(a){switch(a){case 401:return"unauthorized";case 403:return"forbidden";case 404:return"not-found";default:return}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},79581,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"notFound",{enumerable:!0,get:function(){return e}});let d=""+a.r(90784).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function e(){let a=Object.defineProperty(Error(d),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw a.digest=d,a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},98205,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"forbidden",{enumerable:!0,get:function(){return d}}),a.r(90784).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},78019,(a,b,c)=>{"use strict";function d(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unauthorized",{enumerable:!0,get:function(){return d}}),a.r(90784).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},25729,(a,b,c)=>{"use strict";function d(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===e}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{isHangingPromiseRejectionError:function(){return d},makeDevtoolsIOAwarePromise:function(){return j},makeHangingPromise:function(){return h}});let e="HANGING_PROMISE_REJECTION";class f extends Error{constructor(a,b){super(`During prerendering, ${b} rejects when the prerender is complete. Typically these errors are handled by React but if you move ${b} to a different context by using \`setTimeout\`, \`after\`, or similar functions you may observe this error and you should handle it in that context. This occurred at route "${a}".`),this.route=a,this.expression=b,this.digest=e}}let g=new WeakMap;function h(a,b,c){if(a.aborted)return Promise.reject(new f(b,c));{let d=new Promise((d,e)=>{let h=e.bind(null,new f(b,c)),i=g.get(a);if(i)i.push(h);else{let b=[h];g.set(a,b),a.addEventListener("abort",()=>{for(let a=0;a<b.length;a++)b[a]()},{once:!0})}});return d.catch(i),d}}function i(){}function j(a){return new Promise(b=>{setTimeout(()=>{b(a)},0)})}},63383,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isPostpone",{enumerable:!0,get:function(){return e}});let d=Symbol.for("react.postpone");function e(a){return"object"==typeof a&&null!==a&&a.$$typeof===d}},25451,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{BailoutToCSRError:function(){return e},isBailoutToCSRError:function(){return f}});let d="BAILOUT_TO_CLIENT_SIDE_RENDERING";class e extends Error{constructor(a){super("Bail out to client-side rendering: "+a),this.reason=a,this.digest=d}}function f(a){return"object"==typeof a&&null!==a&&"digest"in a&&a.digest===d}},34721,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNextRouterError",{enumerable:!0,get:function(){return f}});let d=a.r(90784),e=a.r(34816);function f(a){return(0,e.isRedirectError)(a)||(0,d.isHTTPAccessFallbackError)(a)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},88908,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DynamicServerError:function(){return e},isDynamicServerError:function(){return f}});let d="DYNAMIC_SERVER_USAGE";class e extends Error{constructor(a){super("Dynamic server usage: "+a),this.description=a,this.digest=d}}function f(a){return"object"==typeof a&&null!==a&&"digest"in a&&"string"==typeof a.digest&&a.digest===d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},99780,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{StaticGenBailoutError:function(){return e},isStaticGenBailoutError:function(){return f}});let d="NEXT_STATIC_GEN_BAILOUT";class e extends Error{constructor(...a){super(...a),this.code=d}}function f(a){return"object"==typeof a&&null!==a&&"code"in a&&a.code===d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},2343,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{METADATA_BOUNDARY_NAME:function(){return d},OUTLET_BOUNDARY_NAME:function(){return f},ROOT_LAYOUT_BOUNDARY_NAME:function(){return g},VIEWPORT_BOUNDARY_NAME:function(){return e}});let d="__next_metadata_boundary__",e="__next_viewport_boundary__",f="__next_outlet_boundary__",g="__next_root_layout_boundary__"},26291,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{atLeastOneTask:function(){return f},scheduleImmediate:function(){return e},scheduleOnNextTick:function(){return d},waitAtLeastOneReactRenderTask:function(){return g}});let d=a=>{Promise.resolve().then(()=>{process.nextTick(a)})},e=a=>{setImmediate(a)};function f(){return new Promise(a=>e(a))}function g(){return new Promise(a=>setImmediate(a))}},14156,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"InvariantError",{enumerable:!0,get:function(){return d}});class d extends Error{constructor(a,b){super("Invariant: "+(a.endsWith(".")?a:a+".")+" This is a bug in Next.js.",b),this.name="InvariantError"}}},82409,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{Postpone:function(){return A},PreludeState:function(){return V},abortAndThrowOnSynchronousRequestDataAccess:function(){return x},abortOnSynchronousPlatformIOAccess:function(){return v},accessedDynamicData:function(){return I},annotateDynamicAccess:function(){return N},consumeDynamicAccess:function(){return J},createDynamicTrackingState:function(){return o},createDynamicValidationState:function(){return p},createHangingInputAbortSignal:function(){return M},createRenderInBrowserAbortSignal:function(){return L},delayUntilRuntimeStage:function(){return Y},formatDynamicAPIAccesses:function(){return K},getFirstDynamicReason:function(){return q},isDynamicPostpone:function(){return D},isPrerenderInterruptedError:function(){return H},logDisallowedDynamicError:function(){return W},markCurrentScopeAsDynamic:function(){return r},postponeWithTracking:function(){return B},throwIfDisallowedDynamic:function(){return X},throwToInterruptStaticGeneration:function(){return s},trackAllowedDynamicAccess:function(){return U},trackDynamicDataInDynamicRender:function(){return t},trackSynchronousPlatformIOAccessInDev:function(){return w},trackSynchronousRequestDataAccessInDev:function(){return z},useDynamicRouteParams:function(){return O},warnOnSyncDynamicError:function(){return y}});let d=function(a){return a&&a.__esModule?a:{default:a}}(a.r(54436)),e=a.r(88908),f=a.r(99780),g=a.r(32319),h=a.r(56704),i=a.r(25729),j=a.r(2343),k=a.r(26291),l=a.r(25451),m=a.r(14156),n="function"==typeof d.default.unstable_postpone;function o(a){return{isDebugDynamicAccesses:a,dynamicAccesses:[],syncDynamicErrorWithStack:null}}function p(){return{hasSuspenseAboveBody:!1,hasDynamicMetadata:!1,hasDynamicViewport:!1,hasAllowedDynamic:!1,dynamicErrors:[]}}function q(a){var b;return null==(b=a.dynamicAccesses[0])?void 0:b.expression}function r(a,b,c){if(b)switch(b.type){case"cache":case"unstable-cache":case"private-cache":return}if(!a.forceDynamic&&!a.forceStatic){if(a.dynamicShouldError)throw Object.defineProperty(new f.StaticGenBailoutError(`Route ${a.route} with \`dynamic = "error"\` couldn't be rendered statically because it used \`${c}\`. See more info here: https://nextjs.org/docs/app/building-your-application/rendering/static-and-dynamic#dynamic-rendering`),"__NEXT_ERROR_CODE",{value:"E553",enumerable:!1,configurable:!0});if(b)switch(b.type){case"prerender-ppr":return B(a.route,c,b.dynamicTracking);case"prerender-legacy":b.revalidate=0;let d=Object.defineProperty(new e.DynamicServerError(`Route ${a.route} couldn't be rendered statically because it used ${c}. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E550",enumerable:!1,configurable:!0});throw a.dynamicUsageDescription=c,a.dynamicUsageStack=d.stack,d}}}function s(a,b,c){let d=Object.defineProperty(new e.DynamicServerError(`Route ${b.route} couldn't be rendered statically because it used \`${a}\`. See more info here: https://nextjs.org/docs/messages/dynamic-server-error`),"__NEXT_ERROR_CODE",{value:"E558",enumerable:!1,configurable:!0});throw c.revalidate=0,b.dynamicUsageDescription=a,b.dynamicUsageStack=d.stack,d}function t(a){switch(a.type){case"cache":case"unstable-cache":case"private-cache":return}}function u(a,b,c){let d=G(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`);c.controller.abort(d);let e=c.dynamicTracking;e&&e.dynamicAccesses.push({stack:e.isDebugDynamicAccesses?Error().stack:void 0,expression:b})}function v(a,b,c,d){let e=d.dynamicTracking;u(a,b,d),e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}function w(a){a.prerenderPhase=!1}function x(a,b,c,d){if(!1===d.controller.signal.aborted){u(a,b,d);let e=d.dynamicTracking;e&&null===e.syncDynamicErrorWithStack&&(e.syncDynamicErrorWithStack=c)}throw G(`Route ${a} needs to bail out of prerendering at this point because it used ${b}.`)}function y(a){a.syncDynamicErrorWithStack&&console.error(a.syncDynamicErrorWithStack)}let z=w;function A({reason:a,route:b}){let c=g.workUnitAsyncStorage.getStore();B(b,a,c&&"prerender-ppr"===c.type?c.dynamicTracking:null)}function B(a,b,c){(function(){if(!n)throw Object.defineProperty(Error("Invariant: React.unstable_postpone is not defined. This suggests the wrong version of React was loaded. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E224",enumerable:!1,configurable:!0})})(),c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:b}),d.default.unstable_postpone(C(a,b))}function C(a,b){return`Route ${a} needs to bail out of prerendering at this point because it used ${b}. React throws this special object to indicate where. It should not be caught by your own try/catch. Learn more: https://nextjs.org/docs/messages/ppr-caught-error`}function D(a){return"object"==typeof a&&null!==a&&"string"==typeof a.message&&E(a.message)}function E(a){return a.includes("needs to bail out of prerendering at this point because it used")&&a.includes("Learn more: https://nextjs.org/docs/messages/ppr-caught-error")}if(!1===E(C("%%%","^^^")))throw Object.defineProperty(Error("Invariant: isDynamicPostpone misidentified a postpone reason. This is a bug in Next.js"),"__NEXT_ERROR_CODE",{value:"E296",enumerable:!1,configurable:!0});let F="NEXT_PRERENDER_INTERRUPTED";function G(a){let b=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return b.digest=F,b}function H(a){return"object"==typeof a&&null!==a&&a.digest===F&&"name"in a&&"message"in a&&a instanceof Error}function I(a){return a.length>0}function J(a,b){return a.dynamicAccesses.push(...b.dynamicAccesses),a.dynamicAccesses}function K(a){return a.filter(a=>"string"==typeof a.stack&&a.stack.length>0).map(({expression:a,stack:b})=>(b=b.split("\n").slice(4).filter(a=>!(a.includes("node_modules/next/")||a.includes(" (<anonymous>)")||a.includes(" (node:"))).join("\n"),`Dynamic API Usage Debug - ${a}:
${b}`))}function L(){let a=new AbortController;return a.abort(Object.defineProperty(new l.BailoutToCSRError("Render in Browser"),"__NEXT_ERROR_CODE",{value:"E721",enumerable:!1,configurable:!0})),a.signal}function M(a){switch(a.type){case"prerender":case"prerender-runtime":let b=new AbortController;if(a.cacheSignal)a.cacheSignal.inputReady().then(()=>{b.abort()});else{let c=(0,g.getRuntimeStagePromise)(a);c?c.then(()=>(0,k.scheduleOnNextTick)(()=>b.abort())):(0,k.scheduleOnNextTick)(()=>b.abort())}return b.signal;case"prerender-client":case"prerender-ppr":case"prerender-legacy":case"request":case"cache":case"private-cache":case"unstable-cache":return}}function N(a,b){let c=b.dynamicTracking;c&&c.dynamicAccesses.push({stack:c.isDebugDynamicAccesses?Error().stack:void 0,expression:a})}function O(a){let b=h.workAsyncStorage.getStore(),c=g.workUnitAsyncStorage.getStore();if(b&&c)switch(c.type){case"prerender-client":case"prerender":{let e=c.fallbackRouteParams;e&&e.size>0&&d.default.use((0,i.makeHangingPromise)(c.renderSignal,b.route,a));break}case"prerender-ppr":{let d=c.fallbackRouteParams;if(d&&d.size>0)return B(b.route,a,c.dynamicTracking);break}case"prerender-runtime":throw Object.defineProperty(new m.InvariantError(`\`${a}\` was called during a runtime prerender. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E771",enumerable:!1,configurable:!0});case"cache":case"private-cache":throw Object.defineProperty(new m.InvariantError(`\`${a}\` was called inside a cache scope. Next.js should be preventing ${a} from being included in server components statically, but did not in this case.`),"__NEXT_ERROR_CODE",{value:"E745",enumerable:!1,configurable:!0})}}let P=/\n\s+at Suspense \(<anonymous>\)/,Q=RegExp(`\\n\\s+at Suspense \\(<anonymous>\\)(?:(?!\\n\\s+at (?:body|div|main|section|article|aside|header|footer|nav|form|p|span|h1|h2|h3|h4|h5|h6) \\(<anonymous>\\))[\\s\\S])*?\\n\\s+at ${j.ROOT_LAYOUT_BOUNDARY_NAME} \\([^\\n]*\\)`),R=RegExp(`\\n\\s+at ${j.METADATA_BOUNDARY_NAME}[\\n\\s]`),S=RegExp(`\\n\\s+at ${j.VIEWPORT_BOUNDARY_NAME}[\\n\\s]`),T=RegExp(`\\n\\s+at ${j.OUTLET_BOUNDARY_NAME}[\\n\\s]`);function U(a,b,c,d){if(!T.test(b)){if(R.test(b)){c.hasDynamicMetadata=!0;return}if(S.test(b)){c.hasDynamicViewport=!0;return}if(Q.test(b)){c.hasAllowedDynamic=!0,c.hasSuspenseAboveBody=!0;return}else if(P.test(b)){c.hasAllowedDynamic=!0;return}else{if(d.syncDynamicErrorWithStack)return void c.dynamicErrors.push(d.syncDynamicErrorWithStack);let e=function(a,b){let c=Object.defineProperty(Error(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return c.stack=c.name+": "+a+b,c}(`Route "${a.route}": A component accessed data, headers, params, searchParams, or a short-lived cache without a Suspense boundary nor a "use cache" above it. See more info: https://nextjs.org/docs/messages/next-prerender-missing-suspense`,b);return void c.dynamicErrors.push(e)}}}var V=function(a){return a[a.Full=0]="Full",a[a.Empty=1]="Empty",a[a.Errored=2]="Errored",a}({});function W(a,b){console.error(b),a.dev||(a.hasReadableErrorStacks?console.error(`To get a more detailed stack trace and pinpoint the issue, start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.`):console.error(`To get a more detailed stack trace and pinpoint the issue, try one of the following:
  - Start the app in development mode by running \`next dev\`, then open "${a.route}" in your browser to investigate the error.
  - Rerun the production build with \`next build --debug-prerender\` to generate better stack traces.`))}function X(a,b,c,d){if(0!==b){if(c.hasSuspenseAboveBody)return;if(d.syncDynamicErrorWithStack)throw W(a,d.syncDynamicErrorWithStack),new f.StaticGenBailoutError;let e=c.dynamicErrors;if(e.length>0){for(let b=0;b<e.length;b++)W(a,e[b]);throw new f.StaticGenBailoutError}if(c.hasDynamicViewport)throw console.error(`Route "${a.route}" has a \`generateViewport\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) without explicitly allowing fully dynamic rendering. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-viewport`),new f.StaticGenBailoutError;if(1===b)throw console.error(`Route "${a.route}" did not produce a static shell and Next.js was unable to determine a reason. This is a bug in Next.js.`),new f.StaticGenBailoutError}else if(!1===c.hasAllowedDynamic&&c.hasDynamicMetadata)throw console.error(`Route "${a.route}" has a \`generateMetadata\` that depends on Request data (\`cookies()\`, etc...) or uncached external data (\`fetch(...)\`, etc...) when the rest of the route does not. See more info here: https://nextjs.org/docs/messages/next-prerender-dynamic-metadata`),new f.StaticGenBailoutError}function Y(a,b){return a.runtimeStagePromise?a.runtimeStagePromise.then(()=>b):b}},760,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return function a(b){if((0,g.isNextRouterError)(b)||(0,f.isBailoutToCSRError)(b)||(0,i.isDynamicServerError)(b)||(0,h.isDynamicPostpone)(b)||(0,e.isPostpone)(b)||(0,d.isHangingPromiseRejectionError)(b))throw b;b instanceof Error&&"cause"in b&&a(b.cause)}}});let d=a.r(25729),e=a.r(63383),f=a.r(25451),g=a.r(34721),h=a.r(82409),i=a.r(88908);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},61177,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unstable_rethrow",{enumerable:!0,get:function(){return d}});let d=a.r(760).unstable_rethrow;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11707,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ReadonlyURLSearchParams:function(){return k},RedirectType:function(){return e.RedirectType},forbidden:function(){return g.forbidden},notFound:function(){return f.notFound},permanentRedirect:function(){return d.permanentRedirect},redirect:function(){return d.redirect},unauthorized:function(){return h.unauthorized},unstable_isUnrecognizedActionError:function(){return l},unstable_rethrow:function(){return i.unstable_rethrow}});let d=a.r(28548),e=a.r(34816),f=a.r(79581),g=a.r(98205),h=a.r(78019),i=a.r(61177);class j extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class k extends URLSearchParams{append(){throw new j}delete(){throw new j}set(){throw new j}sort(){throw new j}}function l(){throw Object.defineProperty(Error("`unstable_isUnrecognizedActionError` can only be used on the client."),"__NEXT_ERROR_CODE",{value:"E776",enumerable:!1,configurable:!0})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},79045,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{UnrecognizedActionError:function(){return d},unstable_isUnrecognizedActionError:function(){return e}});class d extends Error{constructor(...a){super(...a),this.name="UnrecognizedActionError"}}function e(a){return!!(a&&"object"==typeof a&&a instanceof d)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},26960,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"bailoutToClientRendering",{enumerable:!0,get:function(){return g}});let d=a.r(25451),e=a.r(56704),f=a.r(32319);function g(a){let b=e.workAsyncStorage.getStore();if(null==b?void 0:b.forceStatic)return;let c=f.workUnitAsyncStorage.getStore();if(c)switch(c.type){case"prerender":case"prerender-runtime":case"prerender-client":case"prerender-ppr":case"prerender-legacy":throw Object.defineProperty(new d.BailoutToCSRError(a),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0})}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},92018,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ReadonlyURLSearchParams:function(){return i.ReadonlyURLSearchParams},RedirectType:function(){return i.RedirectType},ServerInsertedHTMLContext:function(){return j.ServerInsertedHTMLContext},forbidden:function(){return i.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return i.permanentRedirect},redirect:function(){return i.redirect},unauthorized:function(){return i.unauthorized},unstable_isUnrecognizedActionError:function(){return k.unstable_isUnrecognizedActionError},unstable_rethrow:function(){return i.unstable_rethrow},useParams:function(){return p},usePathname:function(){return n},useRouter:function(){return o},useSearchParams:function(){return m},useSelectedLayoutSegment:function(){return r},useSelectedLayoutSegments:function(){return q},useServerInsertedHTML:function(){return j.useServerInsertedHTML}});let d=a.r(54436),e=a.r(86847),f=a.r(19383),g=a.r(5670),h=a.r(23741),i=a.r(11707),j=a.r(41534),k=a.r(79045),l=a.r(82409).useDynamicRouteParams;function m(){let b=(0,d.useContext)(f.SearchParamsContext),c=(0,d.useMemo)(()=>b?new i.ReadonlyURLSearchParams(b):null,[b]);{let{bailoutToClientRendering:b}=a.r(26960);b("useSearchParams()")}return c}function n(){return null==l||l("usePathname()"),(0,d.useContext)(f.PathnameContext)}function o(){let a=(0,d.useContext)(e.AppRouterContext);if(null===a)throw Object.defineProperty(Error("invariant expected app router to be mounted"),"__NEXT_ERROR_CODE",{value:"E238",enumerable:!1,configurable:!0});return a}function p(){return null==l||l("useParams()"),(0,d.useContext)(f.PathParamsContext)}function q(a){void 0===a&&(a="children"),null==l||l("useSelectedLayoutSegments()");let b=(0,d.useContext)(e.LayoutRouterContext);return b?function a(b,c,d,e){let f;if(void 0===d&&(d=!0),void 0===e&&(e=[]),d)f=b[1][c];else{var i;let a=b[1];f=null!=(i=a.children)?i:Object.values(a)[0]}if(!f)return e;let j=f[0],k=(0,g.getSegmentValue)(j);return!k||k.startsWith(h.PAGE_SEGMENT_KEY)?e:(e.push(k),a(f,c,!1,e))}(b.parentTree,a):null}function r(a){void 0===a&&(a="children"),null==l||l("useSelectedLayoutSegment()");let b=q(a);if(!b||0===b.length)return null;let c="children"===a?b[0]:b[b.length-1];return c===h.DEFAULT_SEGMENT_KEY?null:c}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},11105,47346,a=>{"use strict";a.s(["Button",()=>h,"buttonVariants",()=>g],11105);var b=a.i(28386),c=a.i(54436),d=a.i(57495),e=a.i(30718),f=a.i(90334);let g=(0,e.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0 border",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90 border-primary",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90 border-destructive",outline:"border-border bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80 border-secondary",ghost:"hover:bg-accent hover:text-accent-foreground border-transparent",link:"text-primary underline-offset-4 hover:underline border-transparent"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),h=c.forwardRef(({className:a,variant:c,size:e,asChild:h=!1,...i},j)=>{let k=h?d.Slot:"button";return(0,b.jsx)(k,{className:(0,f.cn)(g({variant:c,size:e,className:a})),ref:j,...i})});h.displayName="Button",a.s(["Input",()=>i],47346);let i=c.forwardRef(({className:a,type:c,...d},e)=>(0,b.jsx)("input",{type:c,className:(0,f.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm",a),ref:e,...d}));i.displayName="Input"},95155,(a,b,c)=>{"use strict";c._=function(a){return a&&a.__esModule?a:{default:a}}},9935,a=>{"use strict";a.s(["useFocusGuards",()=>d]);var b=a.i(54436),c=0;function d(){b.useEffect(()=>{let a=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",a[0]??e()),document.body.insertAdjacentElement("beforeend",a[1]??e()),c++,()=>{1===c&&document.querySelectorAll("[data-radix-focus-guard]").forEach(a=>a.remove()),c--}},[])}function e(){let a=document.createElement("span");return a.setAttribute("data-radix-focus-guard",""),a.tabIndex=0,a.style.outline="none",a.style.opacity="0",a.style.position="fixed",a.style.pointerEvents="none",a}},14053,a=>{"use strict";a.s(["FocusScope",()=>j]);var b=a.i(54436),c=a.i(42527),d=a.i(40686),e=a.i(98445),f=a.i(28386),g="focusScope.autoFocusOnMount",h="focusScope.autoFocusOnUnmount",i={bubbles:!1,cancelable:!0},j=b.forwardRef((a,j)=>{let{loop:o=!1,trapped:p=!1,onMountAutoFocus:q,onUnmountAutoFocus:r,...s}=a,[t,u]=b.useState(null),v=(0,e.useCallbackRef)(q),w=(0,e.useCallbackRef)(r),x=b.useRef(null),y=(0,c.useComposedRefs)(j,a=>u(a)),z=b.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;b.useEffect(()=>{if(p){let a=function(a){if(z.paused||!t)return;let b=a.target;t.contains(b)?x.current=b:m(x.current,{select:!0})},b=function(a){if(z.paused||!t)return;let b=a.relatedTarget;null!==b&&(t.contains(b)||m(x.current,{select:!0}))};document.addEventListener("focusin",a),document.addEventListener("focusout",b);let c=new MutationObserver(function(a){if(document.activeElement===document.body)for(let b of a)b.removedNodes.length>0&&m(t)});return t&&c.observe(t,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",a),document.removeEventListener("focusout",b),c.disconnect()}}},[p,t,z.paused]),b.useEffect(()=>{if(t){n.add(z);let a=document.activeElement;if(!t.contains(a)){let b=new CustomEvent(g,i);t.addEventListener(g,v),t.dispatchEvent(b),b.defaultPrevented||(function(a,{select:b=!1}={}){let c=document.activeElement;for(let d of a)if(m(d,{select:b}),document.activeElement!==c)return}(k(t).filter(a=>"A"!==a.tagName),{select:!0}),document.activeElement===a&&m(t))}return()=>{t.removeEventListener(g,v),setTimeout(()=>{let b=new CustomEvent(h,i);t.addEventListener(h,w),t.dispatchEvent(b),b.defaultPrevented||m(a??document.body,{select:!0}),t.removeEventListener(h,w),n.remove(z)},0)}}},[t,v,w,z]);let A=b.useCallback(a=>{if(!o&&!p||z.paused)return;let b="Tab"===a.key&&!a.altKey&&!a.ctrlKey&&!a.metaKey,c=document.activeElement;if(b&&c){let b=a.currentTarget,[d,e]=function(a){let b=k(a);return[l(b,a),l(b.reverse(),a)]}(b);d&&e?a.shiftKey||c!==e?a.shiftKey&&c===d&&(a.preventDefault(),o&&m(e,{select:!0})):(a.preventDefault(),o&&m(d,{select:!0})):c===b&&a.preventDefault()}},[o,p,z.paused]);return(0,f.jsx)(d.Primitive.div,{tabIndex:-1,...s,ref:y,onKeyDown:A})});function k(a){let b=[],c=document.createTreeWalker(a,NodeFilter.SHOW_ELEMENT,{acceptNode:a=>{let b="INPUT"===a.tagName&&"hidden"===a.type;return a.disabled||a.hidden||b?NodeFilter.FILTER_SKIP:a.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;c.nextNode();)b.push(c.currentNode);return b}function l(a,b){for(let c of a)if(!function(a,{upTo:b}){if("hidden"===getComputedStyle(a).visibility)return!0;for(;a&&(void 0===b||a!==b);){if("none"===getComputedStyle(a).display)return!0;a=a.parentElement}return!1}(c,{upTo:b}))return c}function m(a,{select:b=!1}={}){if(a&&a.focus){var c;let d=document.activeElement;a.focus({preventScroll:!0}),a!==d&&(c=a)instanceof HTMLInputElement&&"select"in c&&b&&a.select()}}j.displayName="FocusScope";var n=function(){let a=[];return{add(b){let c=a[0];b!==c&&c?.pause(),(a=o(a,b)).unshift(b)},remove(b){a=o(a,b),a[0]?.resume()}}}();function o(a,b){let c=[...a],d=c.indexOf(b);return -1!==d&&c.splice(d,1),c}},73490,a=>{"use strict";a.s(["useId",()=>f]);var b=a.i(54436),c=a.i(64249),d=b[" useId ".trim().toString()]||(()=>void 0),e=0;function f(a){let[f,g]=b.useState(d());return(0,c.useLayoutEffect)(()=>{a||g(a=>a??String(e++))},[a]),a||(f?`radix-${f}`:"")}},64296,a=>{"use strict";a.s(["Anchor",()=>aU,"Arrow",()=>aW,"Content",()=>aV,"Root",()=>aT,"createPopperScope",()=>aD],64296);var b=a.i(54436);let c=["top","right","bottom","left"],d=Math.min,e=Math.max,f=Math.round,g=Math.floor,h=a=>({x:a,y:a}),i={left:"right",right:"left",bottom:"top",top:"bottom"},j={start:"end",end:"start"};function k(a,b){return"function"==typeof a?a(b):a}function l(a){return a.split("-")[0]}function m(a){return a.split("-")[1]}function n(a){return"x"===a?"y":"x"}function o(a){return"y"===a?"height":"width"}let p=new Set(["top","bottom"]);function q(a){return p.has(l(a))?"y":"x"}function r(a){return a.replace(/start|end/g,a=>j[a])}let s=["left","right"],t=["right","left"],u=["top","bottom"],v=["bottom","top"];function w(a){return a.replace(/left|right|bottom|top/g,a=>i[a])}function x(a){return"number"!=typeof a?{top:0,right:0,bottom:0,left:0,...a}:{top:a,right:a,bottom:a,left:a}}function y(a){let{x:b,y:c,width:d,height:e}=a;return{width:d,height:e,top:c,left:b,right:b+d,bottom:c+e,x:b,y:c}}function z(a,b,c){let d,{reference:e,floating:f}=a,g=q(b),h=n(q(b)),i=o(h),j=l(b),k="y"===g,p=e.x+e.width/2-f.width/2,r=e.y+e.height/2-f.height/2,s=e[i]/2-f[i]/2;switch(j){case"top":d={x:p,y:e.y-f.height};break;case"bottom":d={x:p,y:e.y+e.height};break;case"right":d={x:e.x+e.width,y:r};break;case"left":d={x:e.x-f.width,y:r};break;default:d={x:e.x,y:e.y}}switch(m(b)){case"start":d[h]-=s*(c&&k?-1:1);break;case"end":d[h]+=s*(c&&k?-1:1)}return d}let A=async(a,b,c)=>{let{placement:d="bottom",strategy:e="absolute",middleware:f=[],platform:g}=c,h=f.filter(Boolean),i=await (null==g.isRTL?void 0:g.isRTL(b)),j=await g.getElementRects({reference:a,floating:b,strategy:e}),{x:k,y:l}=z(j,d,i),m=d,n={},o=0;for(let c=0;c<h.length;c++){let{name:f,fn:p}=h[c],{x:q,y:r,data:s,reset:t}=await p({x:k,y:l,initialPlacement:d,placement:m,strategy:e,middlewareData:n,rects:j,platform:g,elements:{reference:a,floating:b}});k=null!=q?q:k,l=null!=r?r:l,n={...n,[f]:{...n[f],...s}},t&&o<=50&&(o++,"object"==typeof t&&(t.placement&&(m=t.placement),t.rects&&(j=!0===t.rects?await g.getElementRects({reference:a,floating:b,strategy:e}):t.rects),{x:k,y:l}=z(j,m,i)),c=-1)}return{x:k,y:l,placement:m,strategy:e,middlewareData:n}};async function B(a,b){var c;void 0===b&&(b={});let{x:d,y:e,platform:f,rects:g,elements:h,strategy:i}=a,{boundary:j="clippingAncestors",rootBoundary:l="viewport",elementContext:m="floating",altBoundary:n=!1,padding:o=0}=k(b,a),p=x(o),q=h[n?"floating"===m?"reference":"floating":m],r=y(await f.getClippingRect({element:null==(c=await (null==f.isElement?void 0:f.isElement(q)))||c?q:q.contextElement||await (null==f.getDocumentElement?void 0:f.getDocumentElement(h.floating)),boundary:j,rootBoundary:l,strategy:i})),s="floating"===m?{x:d,y:e,width:g.floating.width,height:g.floating.height}:g.reference,t=await (null==f.getOffsetParent?void 0:f.getOffsetParent(h.floating)),u=await (null==f.isElement?void 0:f.isElement(t))&&await (null==f.getScale?void 0:f.getScale(t))||{x:1,y:1},v=y(f.convertOffsetParentRelativeRectToViewportRelativeRect?await f.convertOffsetParentRelativeRectToViewportRelativeRect({elements:h,rect:s,offsetParent:t,strategy:i}):s);return{top:(r.top-v.top+p.top)/u.y,bottom:(v.bottom-r.bottom+p.bottom)/u.y,left:(r.left-v.left+p.left)/u.x,right:(v.right-r.right+p.right)/u.x}}function C(a,b){return{top:a.top-b.height,right:a.right-b.width,bottom:a.bottom-b.height,left:a.left-b.width}}function D(a){return c.some(b=>a[b]>=0)}let E=new Set(["left","top"]);async function F(a,b){let{placement:c,platform:d,elements:e}=a,f=await (null==d.isRTL?void 0:d.isRTL(e.floating)),g=l(c),h=m(c),i="y"===q(c),j=E.has(g)?-1:1,n=f&&i?-1:1,o=k(b,a),{mainAxis:p,crossAxis:r,alignmentAxis:s}="number"==typeof o?{mainAxis:o,crossAxis:0,alignmentAxis:null}:{mainAxis:o.mainAxis||0,crossAxis:o.crossAxis||0,alignmentAxis:o.alignmentAxis};return h&&"number"==typeof s&&(r="end"===h?-1*s:s),i?{x:r*n,y:p*j}:{x:p*j,y:r*n}}function G(a){return function(a){return!1}(a)?(a.nodeName||"").toLowerCase():"#document"}function H(a){var b;return(null==a||null==(b=a.ownerDocument)?void 0:b.defaultView)||window}function I(a){var b;return null==(b=(function(a){return!1}(a)?a.ownerDocument:a.document)||window.document)?void 0:b.documentElement}function J(a){return!1}let K=new Set(["inline","contents"]);function L(a){let{overflow:b,overflowX:c,overflowY:d,display:e}=W(a);return/auto|scroll|overlay|hidden|clip/.test(b+d+c)&&!K.has(e)}let M=new Set(["table","td","th"]),N=[":popover-open",":modal"];function O(a){return N.some(b=>{try{return a.matches(b)}catch(a){return!1}})}let P=["transform","translate","scale","rotate","perspective"],Q=["transform","translate","scale","rotate","perspective","filter"],R=["paint","layout","strict","content"];function S(a){let b=T(),c=a;return P.some(a=>!!c[a]&&"none"!==c[a])||!!c.containerType&&"normal"!==c.containerType||!b&&!!c.backdropFilter&&"none"!==c.backdropFilter||!b&&!!c.filter&&"none"!==c.filter||Q.some(a=>(c.willChange||"").includes(a))||R.some(a=>(c.contain||"").includes(a))}function T(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let U=new Set(["html","body","#document"]);function V(a){return U.has(G(a))}function W(a){return H(a).getComputedStyle(a)}function X(a){return{scrollLeft:a.scrollX,scrollTop:a.scrollY}}function Y(a){if("html"===G(a))return a;let b=a.assignedSlot||a.parentNode||!1||I(a);return b}function Z(a,b,c){var d;void 0===b&&(b=[]),void 0===c&&(c=!0);let e=function a(b){let c=Y(b);return V(c)?b.ownerDocument?b.ownerDocument.body:b.body:a(c)}(a),f=e===(null==(d=a.ownerDocument)?void 0:d.body),g=H(e);if(f){let a=$(g);return b.concat(g,g.visualViewport||[],L(e)?e:[],a&&c?Z(a):[])}return b.concat(e,Z(e,[],c))}function $(a){return a.parent&&Object.getPrototypeOf(a.parent)?a.frameElement:null}function _(a){let b=W(a),c=parseFloat(b.width)||0,d=parseFloat(b.height)||0,e=!1,g=e?a.offsetWidth:c,h=e?a.offsetHeight:d,i=f(c)!==g||f(d)!==h;return i&&(c=g,d=h),{width:c,height:d,$:i}}function aa(a){return 0,a.contextElement}function ab(a){let b=aa(a);1;return h(1)}let ac=h(0);function ad(a){let b=H(a);return T()&&b.visualViewport?{x:b.visualViewport.offsetLeft,y:b.visualViewport.offsetTop}:ac}function ae(a,b,c,d){var e;void 0===b&&(b=!1),void 0===c&&(c=!1);let f=a.getBoundingClientRect(),g=aa(a),i=h(1);b&&(d||(i=ab(a)));let j=(void 0===(e=c)&&(e=!1),d&&(!e||d===H(g))&&e)?ad(g):h(0),k=(f.left+j.x)/i.x,l=(f.top+j.y)/i.y,m=f.width/i.x,n=f.height/i.y;if(g){let a=H(g),b=d,c=a,e=$(c);for(;e&&d&&b!==c;){let a=ab(e),b=e.getBoundingClientRect(),d=W(e),f=b.left+(e.clientLeft+parseFloat(d.paddingLeft))*a.x,g=b.top+(e.clientTop+parseFloat(d.paddingTop))*a.y;k*=a.x,l*=a.y,m*=a.x,n*=a.y,k+=f,l+=g,e=$(c=H(e))}}return y({width:m,height:n,x:k,y:l})}function af(a,b){let c=X(a).scrollLeft;return b?b.left+c:ae(I(a)).left+c}function ag(a,b){let c=a.getBoundingClientRect();return{x:c.left+b.scrollLeft-af(a,c),y:c.top+b.scrollTop}}function ah(a,b,c){let d;if("viewport"===b)d=function(a,b){let c=H(a),d=I(a),e=c.visualViewport,f=d.clientWidth,g=d.clientHeight,h=0,i=0;if(e){f=e.width,g=e.height;let a=T();(!a||a&&"fixed"===b)&&(h=e.offsetLeft,i=e.offsetTop)}let j=af(d);if(j<=0){let a=d.ownerDocument,b=a.body,c=getComputedStyle(b),e="CSS1Compat"===a.compatMode&&parseFloat(c.marginLeft)+parseFloat(c.marginRight)||0,g=Math.abs(d.clientWidth-b.clientWidth-e);g<=25&&(f-=g)}else j<=25&&(f+=j);return{width:f,height:g,x:h,y:i}}(a,c);else if("document"===b)d=function(a){let b=I(a),c=X(a),d=a.ownerDocument.body,f=e(b.scrollWidth,b.clientWidth,d.scrollWidth,d.clientWidth),g=e(b.scrollHeight,b.clientHeight,d.scrollHeight,d.clientHeight),h=-c.scrollLeft+af(a),i=-c.scrollTop;return"rtl"===W(d).direction&&(h+=e(b.clientWidth,d.clientWidth)-f),{width:f,height:g,x:h,y:i}}(I(a));else{1;{let c=ad(a);d={x:b.x-c.x,y:b.y-c.y,width:b.width,height:b.height}}}return y(d)}function ai(a){return"static"===W(a).position}function aj(a,b){1;return null}function ak(a,b){var c;let d=H(a);if(O(a))return d;1;{let b=Y(a);for(;b&&!V(b);){0;b=Y(b)}return d}}let al=async function(a){let b=this.getOffsetParent||ak,c=this.getDimensions,d=await c(a.floating);return{reference:function(a,b,c){var d;let e=(d=0,!1),f=I(b),g="fixed"===c,i=ae(a,!0,g,b),j={scrollLeft:0,scrollTop:0},k=h(0);if(e||!e&&!g)if(("body"!==G(b)||L(f))&&(j=X(b)),e){let a=ae(b,!0,g,b);k.x=a.x+b.clientLeft,k.y=a.y+b.clientTop}else f&&(k.x=af(f));g&&!e&&f&&(k.x=af(f));let l=!f||e||g?h(0):ag(f,j);return{x:i.left+j.scrollLeft-k.x-l.x,y:i.top+j.scrollTop-k.y-l.y,width:i.width,height:i.height}}(a.reference,await b(a.floating),a.strategy),floating:{x:0,y:0,width:d.width,height:d.height}}},am={convertOffsetParentRelativeRectToViewportRelativeRect:function(a){var b,c;let{elements:d,rect:e,offsetParent:f,strategy:g}=a,i="fixed"===g,j=I(f),k=!!d&&O(d.floating);if(f===j||k&&i)return e;let l={scrollLeft:0,scrollTop:0},m=h(1),n=h(0),o=(b=0,!1);(o||!o&&!i)&&(("body"!==G(f)||L(j))&&(l=X(f)),c=0,0);let p=!j||o||i?h(0):ag(j,l);return{width:e.width*m.x,height:e.height*m.y,x:e.x*m.x-l.scrollLeft*m.x+n.x+p.x,y:e.y*m.y-l.scrollTop*m.y+n.y+p.y}},getDocumentElement:I,getClippingRect:function(a){let{element:b,boundary:c,rootBoundary:f,strategy:g}=a,h=[..."clippingAncestors"===c?O(b)?[]:function(a,b){var c;let d=b.get(a);if(d)return d;let e=Z(a,[],!1).filter(a=>{var b;return b=0,!1}),f="fixed"===W(a).position,g=f?Y(a):a;return c=0,b.set(a,e),e}(b,this._c):[].concat(c),f],i=h[0],j=h.reduce((a,c)=>{let f=ah(b,c,g);return a.top=e(f.top,a.top),a.right=d(f.right,a.right),a.bottom=d(f.bottom,a.bottom),a.left=e(f.left,a.left),a},ah(b,i,g));return{width:j.right-j.left,height:j.bottom-j.top,x:j.left,y:j.top}},getOffsetParent:ak,getElementRects:al,getClientRects:function(a){return Array.from(a.getClientRects())},getDimensions:function(a){let{width:b,height:c}=_(a);return{width:b,height:c}},getScale:ab,isElement:J,isRTL:function(a){return"rtl"===W(a).direction}};function an(a,b){return a.x===b.x&&a.y===b.y&&a.width===b.width&&a.height===b.height}let ao=a=>({name:"arrow",options:a,async fn(b){let{x:c,y:f,placement:g,rects:h,platform:i,elements:j,middlewareData:l}=b,{element:p,padding:r=0}=k(a,b)||{};if(null==p)return{};let s=x(r),t={x:c,y:f},u=n(q(g)),v=o(u),w=await i.getDimensions(p),y="y"===u,z=y?"clientHeight":"clientWidth",A=h.reference[v]+h.reference[u]-t[u]-h.floating[v],B=t[u]-h.reference[u],C=await (null==i.getOffsetParent?void 0:i.getOffsetParent(p)),D=C?C[z]:0;D&&await (null==i.isElement?void 0:i.isElement(C))||(D=j.floating[z]||h.floating[v]);let E=D/2-w[v]/2-1,F=d(s[y?"top":"left"],E),G=d(s[y?"bottom":"right"],E),H=D-w[v]-G,I=D/2-w[v]/2+(A/2-B/2),J=e(F,d(I,H)),K=!l.arrow&&null!=m(g)&&I!==J&&h.reference[v]/2-(I<F?F:G)-w[v]/2<0,L=K?I<F?I-F:I-H:0;return{[u]:t[u]+L,data:{[u]:J,centerOffset:I-J-L,...K&&{alignmentOffset:L}},reset:K}}});var ap=a.i(27444),aq="undefined"!=typeof document?b.useLayoutEffect:function(){};function ar(a,b){let c,d,e;if(a===b)return!0;if(typeof a!=typeof b)return!1;if("function"==typeof a&&a.toString()===b.toString())return!0;if(a&&b&&"object"==typeof a){if(Array.isArray(a)){if((c=a.length)!==b.length)return!1;for(d=c;0!=d--;)if(!ar(a[d],b[d]))return!1;return!0}if((c=(e=Object.keys(a)).length)!==Object.keys(b).length)return!1;for(d=c;0!=d--;)if(!({}).hasOwnProperty.call(b,e[d]))return!1;for(d=c;0!=d--;){let c=e[d];if(("_owner"!==c||!a.$$typeof)&&!ar(a[c],b[c]))return!1}return!0}return a!=a&&b!=b}function as(a,b){let c=1;return Math.round(b*c)/c}function at(a){let c=b.useRef(a);return aq(()=>{c.current=a}),c}var au=a.i(40686),av=a.i(28386),aw=b.forwardRef((a,b)=>{let{children:c,width:d=10,height:e=5,...f}=a;return(0,av.jsx)(au.Primitive.svg,{...f,ref:b,width:d,height:e,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:a.asChild?c:(0,av.jsx)("polygon",{points:"0,0 30,0 15,10"})})});aw.displayName="Arrow";var ax=a.i(42527),ay=a.i(33616),az=a.i(98445),aA=a.i(64249),aB="Popper",[aC,aD]=(0,ay.createContextScope)(aB),[aE,aF]=aC(aB),aG=a=>{let{__scopePopper:c,children:d}=a,[e,f]=b.useState(null);return(0,av.jsx)(aE,{scope:c,anchor:e,onAnchorChange:f,children:d})};aG.displayName=aB;var aH="PopperAnchor",aI=b.forwardRef((a,c)=>{let{__scopePopper:d,virtualRef:e,...f}=a,g=aF(aH,d),h=b.useRef(null),i=(0,ax.useComposedRefs)(c,h),j=b.useRef(null);return b.useEffect(()=>{let a=j.current;j.current=e?.current||h.current,a!==j.current&&g.onAnchorChange(j.current)}),e?null:(0,av.jsx)(au.Primitive.div,{...f,ref:i})});aI.displayName=aH;var aJ="PopperContent",[aK,aL]=aC(aJ),aM=b.forwardRef((a,c)=>{let{__scopePopper:f,side:h="bottom",sideOffset:i=0,align:j="center",alignOffset:p=0,arrowPadding:x=0,avoidCollisions:y=!0,collisionBoundary:z=[],collisionPadding:G=0,sticky:H="partial",hideWhenDetached:J=!1,updatePositionStrategy:K="optimized",onPlaced:L,...M}=a,N=aF(aJ,f),[O,P]=b.useState(null),Q=(0,ax.useComposedRefs)(c,a=>P(a)),[R,S]=b.useState(null),T=function(a){let[c,d]=b.useState(void 0);return(0,aA.useLayoutEffect)(()=>{if(a){d({width:a.offsetWidth,height:a.offsetHeight});let b=new ResizeObserver(b=>{let c,e;if(!Array.isArray(b)||!b.length)return;let f=b[0];if("borderBoxSize"in f){let a=f.borderBoxSize,b=Array.isArray(a)?a[0]:a;c=b.inlineSize,e=b.blockSize}else c=a.offsetWidth,e=a.offsetHeight;d({width:c,height:e})});return b.observe(a,{box:"border-box"}),()=>b.unobserve(a)}d(void 0)},[a]),c}(R),U=T?.width??0,V=T?.height??0,W="number"==typeof G?G:{top:0,right:0,bottom:0,left:0,...G},X=Array.isArray(z)?z:[z],Y=X.length>0,$={padding:W,boundary:X.filter(aQ),altBoundary:Y},{refs:_,floatingStyles:ab,placement:ac,isPositioned:ad,middlewareData:af}=function(a){void 0===a&&(a={});let{placement:c="bottom",strategy:d="absolute",middleware:e=[],platform:f,elements:{reference:g,floating:h}={},transform:i=!0,whileElementsMounted:j,open:k}=a,[l,m]=b.useState({x:0,y:0,strategy:d,placement:c,middlewareData:{},isPositioned:!1}),[n,o]=b.useState(e);ar(n,e)||o(e);let[p,q]=b.useState(null),[r,s]=b.useState(null),t=b.useCallback(a=>{a!==x.current&&(x.current=a,q(a))},[]),u=b.useCallback(a=>{a!==y.current&&(y.current=a,s(a))},[]),v=g||p,w=h||r,x=b.useRef(null),y=b.useRef(null),z=b.useRef(l),B=null!=j,C=at(j),D=at(f),E=at(k),F=b.useCallback(()=>{if(!x.current||!y.current)return;let a={placement:c,strategy:d,middleware:n};D.current&&(a.platform=D.current),((a,b,c)=>{let d=new Map,e={platform:am,...c},f={...e.platform,_c:d};return A(a,b,{...e,platform:f})})(x.current,y.current,a).then(a=>{let b={...a,isPositioned:!1!==E.current};G.current&&!ar(z.current,b)&&(z.current=b,ap.flushSync(()=>{m(b)}))})},[n,c,d,D,E]);aq(()=>{!1===k&&z.current.isPositioned&&(z.current.isPositioned=!1,m(a=>({...a,isPositioned:!1})))},[k]);let G=b.useRef(!1);aq(()=>(G.current=!0,()=>{G.current=!1}),[]),aq(()=>{if(v&&(x.current=v),w&&(y.current=w),v&&w){if(C.current)return C.current(v,w,F);F()}},[v,w,F,C,B]);let H=b.useMemo(()=>({reference:x,floating:y,setReference:t,setFloating:u}),[t,u]),I=b.useMemo(()=>({reference:v,floating:w}),[v,w]),J=b.useMemo(()=>{let a={position:d,left:0,top:0};if(!I.floating)return a;let b=as(I.floating,l.x),c=as(I.floating,l.y);if(i)return{...a,transform:"translate("+b+"px, "+c+"px)",...(I.floating,false)};return{position:d,left:b,top:c}},[d,i,I.floating,l.x,l.y]);return b.useMemo(()=>({...l,update:F,refs:H,elements:I,floatingStyles:J}),[l,F,H,I,J])}({strategy:"fixed",placement:h+("center"!==j?"-"+j:""),whileElementsMounted:(...a)=>(function(a,b,c,f){let h;void 0===f&&(f={});let{ancestorScroll:i=!0,ancestorResize:j=!0,elementResize:k="function"==typeof ResizeObserver,layoutShift:l="function"==typeof IntersectionObserver,animationFrame:m=!1}=f,n=aa(a),o=i||j?[...n?Z(n):[],...Z(b)]:[];o.forEach(a=>{i&&a.addEventListener("scroll",c,{passive:!0}),j&&a.addEventListener("resize",c)});let p=n&&l?function(a,b){let c,f=null,h=I(a);function i(){var a;clearTimeout(c),null==(a=f)||a.disconnect(),f=null}return!function j(k,l){void 0===k&&(k=!1),void 0===l&&(l=1),i();let m=a.getBoundingClientRect(),{left:n,top:o,width:p,height:q}=m;if(k||b(),!p||!q)return;let r=g(o),s=g(h.clientWidth-(n+p)),t={rootMargin:-r+"px "+-s+"px "+-g(h.clientHeight-(o+q))+"px "+-g(n)+"px",threshold:e(0,d(1,l))||1},u=!0;function v(b){let d=b[0].intersectionRatio;if(d!==l){if(!u)return j();d?j(!1,d):c=setTimeout(()=>{j(!1,1e-7)},1e3)}1!==d||an(m,a.getBoundingClientRect())||j(),u=!1}try{f=new IntersectionObserver(v,{...t,root:h.ownerDocument})}catch(a){f=new IntersectionObserver(v,t)}f.observe(a)}(!0),i}(n,c):null,q=-1,r=null;k&&(r=new ResizeObserver(a=>{let[d]=a;d&&d.target===n&&r&&(r.unobserve(b),cancelAnimationFrame(q),q=requestAnimationFrame(()=>{var a;null==(a=r)||a.observe(b)})),c()}),n&&!m&&r.observe(n),r.observe(b));let s=m?ae(a):null;return m&&function b(){let d=ae(a);s&&!an(s,d)&&c(),s=d,h=requestAnimationFrame(b)}(),c(),()=>{var a;o.forEach(a=>{i&&a.removeEventListener("scroll",c),j&&a.removeEventListener("resize",c)}),null==p||p(),null==(a=r)||a.disconnect(),r=null,m&&cancelAnimationFrame(h)}})(...a,{animationFrame:"always"===K}),elements:{reference:N.anchor},middleware:[((a,b)=>({...function(a){return void 0===a&&(a=0),{name:"offset",options:a,async fn(b){var c,d;let{x:e,y:f,placement:g,middlewareData:h}=b,i=await F(b,a);return g===(null==(c=h.offset)?void 0:c.placement)&&null!=(d=h.arrow)&&d.alignmentOffset?{}:{x:e+i.x,y:f+i.y,data:{...i,placement:g}}}}}(a),options:[a,b]}))({mainAxis:i+V,alignmentAxis:p}),y&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"shift",options:a,async fn(b){let{x:c,y:f,placement:g}=b,{mainAxis:h=!0,crossAxis:i=!1,limiter:j={fn:a=>{let{x:b,y:c}=a;return{x:b,y:c}}},...m}=k(a,b),o={x:c,y:f},p=await B(b,m),r=q(l(g)),s=n(r),t=o[s],u=o[r];if(h){let a="y"===s?"top":"left",b="y"===s?"bottom":"right",c=t+p[a],f=t-p[b];t=e(c,d(t,f))}if(i){let a="y"===r?"top":"left",b="y"===r?"bottom":"right",c=u+p[a],f=u-p[b];u=e(c,d(u,f))}let v=j.fn({...b,[s]:t,[r]:u});return{...v,data:{x:v.x-c,y:v.y-f,enabled:{[s]:h,[r]:i}}}}}}(a),options:[a,b]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===H?((a,b)=>({...function(a){return void 0===a&&(a={}),{options:a,fn(b){let{x:c,y:d,placement:e,rects:f,middlewareData:g}=b,{offset:h=0,mainAxis:i=!0,crossAxis:j=!0}=k(a,b),m={x:c,y:d},o=q(e),p=n(o),r=m[p],s=m[o],t=k(h,b),u="number"==typeof t?{mainAxis:t,crossAxis:0}:{mainAxis:0,crossAxis:0,...t};if(i){let a="y"===p?"height":"width",b=f.reference[p]-f.floating[a]+u.mainAxis,c=f.reference[p]+f.reference[a]-u.mainAxis;r<b?r=b:r>c&&(r=c)}if(j){var v,w;let a="y"===p?"width":"height",b=E.has(l(e)),c=f.reference[o]-f.floating[a]+(b&&(null==(v=g.offset)?void 0:v[o])||0)+(b?0:u.crossAxis),d=f.reference[o]+f.reference[a]+(b?0:(null==(w=g.offset)?void 0:w[o])||0)-(b?u.crossAxis:0);s<c?s=c:s>d&&(s=d)}return{[p]:r,[o]:s}}}}(a),options:[a,b]}))():void 0,...$}),y&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"flip",options:a,async fn(b){var c,d,e,f,g;let{placement:h,middlewareData:i,rects:j,initialPlacement:p,platform:x,elements:y}=b,{mainAxis:z=!0,crossAxis:A=!0,fallbackPlacements:C,fallbackStrategy:D="bestFit",fallbackAxisSideDirection:E="none",flipAlignment:F=!0,...G}=k(a,b);if(null!=(c=i.arrow)&&c.alignmentOffset)return{};let H=l(h),I=q(p),J=l(p)===p,K=await (null==x.isRTL?void 0:x.isRTL(y.floating)),L=C||(J||!F?[w(p)]:function(a){let b=w(a);return[r(a),b,r(b)]}(p)),M="none"!==E;!C&&M&&L.push(...function(a,b,c,d){let e=m(a),f=function(a,b,c){switch(a){case"top":case"bottom":if(c)return b?t:s;return b?s:t;case"left":case"right":return b?u:v;default:return[]}}(l(a),"start"===c,d);return e&&(f=f.map(a=>a+"-"+e),b&&(f=f.concat(f.map(r)))),f}(p,F,E,K));let N=[p,...L],O=await B(b,G),P=[],Q=(null==(d=i.flip)?void 0:d.overflows)||[];if(z&&P.push(O[H]),A){let a=function(a,b,c){void 0===c&&(c=!1);let d=m(a),e=n(q(a)),f=o(e),g="x"===e?d===(c?"end":"start")?"right":"left":"start"===d?"bottom":"top";return b.reference[f]>b.floating[f]&&(g=w(g)),[g,w(g)]}(h,j,K);P.push(O[a[0]],O[a[1]])}if(Q=[...Q,{placement:h,overflows:P}],!P.every(a=>a<=0)){let a=((null==(e=i.flip)?void 0:e.index)||0)+1,b=N[a];if(b&&("alignment"!==A||I===q(b)||Q.every(a=>q(a.placement)!==I||a.overflows[0]>0)))return{data:{index:a,overflows:Q},reset:{placement:b}};let c=null==(f=Q.filter(a=>a.overflows[0]<=0).sort((a,b)=>a.overflows[1]-b.overflows[1])[0])?void 0:f.placement;if(!c)switch(D){case"bestFit":{let a=null==(g=Q.filter(a=>{if(M){let b=q(a.placement);return b===I||"y"===b}return!0}).map(a=>[a.placement,a.overflows.filter(a=>a>0).reduce((a,b)=>a+b,0)]).sort((a,b)=>a[1]-b[1])[0])?void 0:g[0];a&&(c=a);break}case"initialPlacement":c=p}if(h!==c)return{reset:{placement:c}}}return{}}}}(a),options:[a,b]}))({...$}),((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"size",options:a,async fn(b){var c,f;let g,h,{placement:i,rects:j,platform:n,elements:o}=b,{apply:p=()=>{},...r}=k(a,b),s=await B(b,r),t=l(i),u=m(i),v="y"===q(i),{width:w,height:x}=j.floating;"top"===t||"bottom"===t?(g=t,h=u===(await (null==n.isRTL?void 0:n.isRTL(o.floating))?"start":"end")?"left":"right"):(h=t,g="end"===u?"top":"bottom");let y=x-s.top-s.bottom,z=w-s.left-s.right,A=d(x-s[g],y),C=d(w-s[h],z),D=!b.middlewareData.shift,E=A,F=C;if(null!=(c=b.middlewareData.shift)&&c.enabled.x&&(F=z),null!=(f=b.middlewareData.shift)&&f.enabled.y&&(E=y),D&&!u){let a=e(s.left,0),b=e(s.right,0),c=e(s.top,0),d=e(s.bottom,0);v?F=w-2*(0!==a||0!==b?a+b:e(s.left,s.right)):E=x-2*(0!==c||0!==d?c+d:e(s.top,s.bottom))}await p({...b,availableWidth:F,availableHeight:E});let G=await n.getDimensions(o.floating);return w!==G.width||x!==G.height?{reset:{rects:!0}}:{}}}}(a),options:[a,b]}))({...$,apply:({elements:a,rects:b,availableWidth:c,availableHeight:d})=>{let{width:e,height:f}=b.reference,g=a.floating.style;g.setProperty("--radix-popper-available-width",`${c}px`),g.setProperty("--radix-popper-available-height",`${d}px`),g.setProperty("--radix-popper-anchor-width",`${e}px`),g.setProperty("--radix-popper-anchor-height",`${f}px`)}}),R&&((a,b)=>({...(a=>({name:"arrow",options:a,fn(b){let{element:c,padding:d}="function"==typeof a?a(b):a;return c&&({}).hasOwnProperty.call(c,"current")?null!=c.current?ao({element:c.current,padding:d}).fn(b):{}:c?ao({element:c,padding:d}).fn(b):{}}}))(a),options:[a,b]}))({element:R,padding:x}),aR({arrowWidth:U,arrowHeight:V}),J&&((a,b)=>({...function(a){return void 0===a&&(a={}),{name:"hide",options:a,async fn(b){let{rects:c}=b,{strategy:d="referenceHidden",...e}=k(a,b);switch(d){case"referenceHidden":{let a=C(await B(b,{...e,elementContext:"reference"}),c.reference);return{data:{referenceHiddenOffsets:a,referenceHidden:D(a)}}}case"escaped":{let a=C(await B(b,{...e,altBoundary:!0}),c.floating);return{data:{escapedOffsets:a,escaped:D(a)}}}default:return{}}}}}(a),options:[a,b]}))({strategy:"referenceHidden",...$})]}),[ag,ah]=aS(ac),ai=(0,az.useCallbackRef)(L);(0,aA.useLayoutEffect)(()=>{ad&&ai?.()},[ad,ai]);let aj=af.arrow?.x,ak=af.arrow?.y,al=af.arrow?.centerOffset!==0,[aw,ay]=b.useState();return(0,aA.useLayoutEffect)(()=>{O&&ay(window.getComputedStyle(O).zIndex)},[O]),(0,av.jsx)("div",{ref:_.setFloating,"data-radix-popper-content-wrapper":"",style:{...ab,transform:ad?ab.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:aw,"--radix-popper-transform-origin":[af.transformOrigin?.x,af.transformOrigin?.y].join(" "),...af.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:a.dir,children:(0,av.jsx)(aK,{scope:f,placedSide:ag,onArrowChange:S,arrowX:aj,arrowY:ak,shouldHideArrow:al,children:(0,av.jsx)(au.Primitive.div,{"data-side":ag,"data-align":ah,...M,ref:Q,style:{...M.style,animation:ad?void 0:"none"}})})})});aM.displayName=aJ;var aN="PopperArrow",aO={top:"bottom",right:"left",bottom:"top",left:"right"},aP=b.forwardRef(function(a,b){let{__scopePopper:c,...d}=a,e=aL(aN,c),f=aO[e.placedSide];return(0,av.jsx)("span",{ref:e.onArrowChange,style:{position:"absolute",left:e.arrowX,top:e.arrowY,[f]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[e.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[e.placedSide],visibility:e.shouldHideArrow?"hidden":void 0},children:(0,av.jsx)(aw,{...d,ref:b,style:{...d.style,display:"block"}})})});function aQ(a){return null!==a}aP.displayName=aN;var aR=a=>({name:"transformOrigin",options:a,fn(b){let{placement:c,rects:d,middlewareData:e}=b,f=e.arrow?.centerOffset!==0,g=f?0:a.arrowWidth,h=f?0:a.arrowHeight,[i,j]=aS(c),k={start:"0%",center:"50%",end:"100%"}[j],l=(e.arrow?.x??0)+g/2,m=(e.arrow?.y??0)+h/2,n="",o="";return"bottom"===i?(n=f?k:`${l}px`,o=`${-h}px`):"top"===i?(n=f?k:`${l}px`,o=`${d.floating.height+h}px`):"right"===i?(n=`${-h}px`,o=f?k:`${m}px`):"left"===i&&(n=`${d.floating.width+h}px`,o=f?k:`${m}px`),{data:{x:n,y:o}}}});function aS(a){let[b,c="center"]=a.split("-");return[b,c]}var aT=aG,aU=aI,aV=aM,aW=aP},85502,a=>{"use strict";a.s(["hideOthers",()=>h]);var b=new WeakMap,c=new WeakMap,d={},e=0,f=function(a){return a&&(a.host||f(a.parentNode))},g=function(a,g,h,i){var j=(Array.isArray(a)?a:[a]).map(function(a){if(g.contains(a))return a;var b=f(a);return b&&g.contains(b)?b:(console.error("aria-hidden",a,"in not contained inside",g,". Doing nothing"),null)}).filter(function(a){return!!a});d[h]||(d[h]=new WeakMap);var k=d[h],l=[],m=new Set,n=new Set(j),o=function(a){!a||m.has(a)||(m.add(a),o(a.parentNode))};j.forEach(o);var p=function(a){!a||n.has(a)||Array.prototype.forEach.call(a.children,function(a){if(m.has(a))p(a);else try{var d=a.getAttribute(i),e=null!==d&&"false"!==d,f=(b.get(a)||0)+1,g=(k.get(a)||0)+1;b.set(a,f),k.set(a,g),l.push(a),1===f&&e&&c.set(a,!0),1===g&&a.setAttribute(h,"true"),e||a.setAttribute(i,"true")}catch(b){console.error("aria-hidden: cannot operate on ",a,b)}})};return p(g),m.clear(),e++,function(){l.forEach(function(a){var d=b.get(a)-1,e=k.get(a)-1;b.set(a,d),k.set(a,e),d||(c.has(a)||a.removeAttribute(i),c.delete(a)),e||a.removeAttribute(h)}),--e||(b=new WeakMap,b=new WeakMap,c=new WeakMap,d={})}},h=function(a,b,c){void 0===c&&(c="data-aria-hidden");var d=Array.from(Array.isArray(a)?a:[a]),e=b||("undefined"==typeof document?null:(Array.isArray(a)?a[0]:a).ownerDocument.body);return e?(d.push.apply(d,Array.from(e.querySelectorAll("[aria-live], script"))),g(d,e,c,"aria-hidden")):function(){return null}}},6022,a=>{"use strict";a.s(["RemoveScroll",()=>M],6022);var b,c,d=function(){return(d=Object.assign||function(a){for(var b,c=1,d=arguments.length;c<d;c++)for(var e in b=arguments[c])Object.prototype.hasOwnProperty.call(b,e)&&(a[e]=b[e]);return a}).apply(this,arguments)};function e(a,b){var c={};for(var d in a)Object.prototype.hasOwnProperty.call(a,d)&&0>b.indexOf(d)&&(c[d]=a[d]);if(null!=a&&"function"==typeof Object.getOwnPropertySymbols)for(var e=0,d=Object.getOwnPropertySymbols(a);e<d.length;e++)0>b.indexOf(d[e])&&Object.prototype.propertyIsEnumerable.call(a,d[e])&&(c[d[e]]=a[d[e]]);return c}Object.create;Object.create;var f=("function"==typeof SuppressedError&&SuppressedError,a.i(54436)),g="right-scroll-bar-position",h="width-before-scroll-bar";function i(a,b){return"function"==typeof a?a(b):a&&(a.current=b),a}var j=f.useEffect,k=new WeakMap;function l(a){return a}var m=function(a){void 0===a&&(a={});var b,c,e,f=(void 0===b&&(b=l),c=[],e=!1,{read:function(){if(e)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return c.length?c[c.length-1]:null},useMedium:function(a){var d=b(a,e);return c.push(d),function(){c=c.filter(function(a){return a!==d})}},assignSyncMedium:function(a){for(e=!0;c.length;){var b=c;c=[],b.forEach(a)}c={push:function(b){return a(b)},filter:function(){return c}}},assignMedium:function(a){e=!0;var b=[];if(c.length){var d=c;c=[],d.forEach(a),b=c}var f=function(){var c=b;b=[],c.forEach(a)},g=function(){return Promise.resolve().then(f)};g(),c={push:function(a){b.push(a),g()},filter:function(a){return b=b.filter(a),c}}}});return f.options=d({async:!0,ssr:!1},a),f}(),n=function(){},o=f.forwardRef(function(a,b){var c,g,h,l,o=f.useRef(null),p=f.useState({onScrollCapture:n,onWheelCapture:n,onTouchMoveCapture:n}),q=p[0],r=p[1],s=a.forwardProps,t=a.children,u=a.className,v=a.removeScrollBar,w=a.enabled,x=a.shards,y=a.sideCar,z=a.noRelative,A=a.noIsolation,B=a.inert,C=a.allowPinchZoom,D=a.as,E=a.gapMode,F=e(a,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),G=(c=[o,b],g=function(a){return c.forEach(function(b){return i(b,a)})},(h=(0,f.useState)(function(){return{value:null,callback:g,facade:{get current(){return h.value},set current(value){var a=h.value;a!==value&&(h.value=value,h.callback(value,a))}}}})[0]).callback=g,l=h.facade,j(function(){var a=k.get(l);if(a){var b=new Set(a),d=new Set(c),e=l.current;b.forEach(function(a){d.has(a)||i(a,null)}),d.forEach(function(a){b.has(a)||i(a,e)})}k.set(l,c)},[c]),l),H=d(d({},F),q);return f.createElement(f.Fragment,null,w&&f.createElement(y,{sideCar:m,removeScrollBar:v,shards:x,noRelative:z,noIsolation:A,inert:B,setCallbacks:r,allowPinchZoom:!!C,lockRef:o,gapMode:E}),s?f.cloneElement(f.Children.only(t),d(d({},H),{ref:G})):f.createElement(void 0===D?"div":D,d({},H,{className:u,ref:G}),t))});o.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},o.classNames={fullWidth:h,zeroRight:g};var p=function(a){var b=a.sideCar,c=e(a,["sideCar"]);if(!b)throw Error("Sidecar: please provide `sideCar` property to import the right car");var g=b.read();if(!g)throw Error("Sidecar medium not found");return f.createElement(g,d({},c))};p.isSideCarExport=!0;var q=function(){var a=0,b=null;return{add:function(d){if(0==a&&(b=function(){if(!document)return null;var a=document.createElement("style");a.type="text/css";var b=c||("undefined"!=typeof __webpack_nonce__?__webpack_nonce__:void 0);return b&&a.setAttribute("nonce",b),a}())){var e,f;(e=b).styleSheet?e.styleSheet.cssText=d:e.appendChild(document.createTextNode(d)),f=b,(document.head||document.getElementsByTagName("head")[0]).appendChild(f)}a++},remove:function(){--a||!b||(b.parentNode&&b.parentNode.removeChild(b),b=null)}}},r=function(){var a=q();return function(b,c){f.useEffect(function(){return a.add(b),function(){a.remove()}},[b&&c])}},s=function(){var a=r();return function(b){return a(b.styles,b.dynamic),null}},t={left:0,top:0,right:0,gap:0},u=s(),v="data-scroll-locked",w=function(a,b,c,d){var e=a.left,f=a.top,i=a.right,j=a.gap;return void 0===c&&(c="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(d,";\n   padding-right: ").concat(j,"px ").concat(d,";\n  }\n  body[").concat(v,"] {\n    overflow: hidden ").concat(d,";\n    overscroll-behavior: contain;\n    ").concat([b&&"position: relative ".concat(d,";"),"margin"===c&&"\n    padding-left: ".concat(e,"px;\n    padding-top: ").concat(f,"px;\n    padding-right: ").concat(i,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(j,"px ").concat(d,";\n    "),"padding"===c&&"padding-right: ".concat(j,"px ").concat(d,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(g," {\n    right: ").concat(j,"px ").concat(d,";\n  }\n  \n  .").concat(h," {\n    margin-right: ").concat(j,"px ").concat(d,";\n  }\n  \n  .").concat(g," .").concat(g," {\n    right: 0 ").concat(d,";\n  }\n  \n  .").concat(h," .").concat(h," {\n    margin-right: 0 ").concat(d,";\n  }\n  \n  body[").concat(v,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(j,"px;\n  }\n")},x=function(){var a=parseInt(document.body.getAttribute(v)||"0",10);return isFinite(a)?a:0},y=function(){f.useEffect(function(){return document.body.setAttribute(v,(x()+1).toString()),function(){var a=x()-1;a<=0?document.body.removeAttribute(v):document.body.setAttribute(v,a.toString())}},[])},z=function(a){var b=a.noRelative,c=a.noImportant,d=a.gapMode,e=void 0===d?"margin":d;y();var g=f.useMemo(function(){return void 0===e,t},[e]);return f.createElement(u,{styles:w(g,!b,e,c?"":"!important")})},A=function(a,b){if(!(a instanceof Element))return!1;var c=window.getComputedStyle(a);return"hidden"!==c[b]&&(c.overflowY!==c.overflowX||"TEXTAREA"===a.tagName||"visible"!==c[b])},B=function(a,b){var c=b.ownerDocument,d=b;do{if("undefined"!=typeof ShadowRoot&&d instanceof ShadowRoot&&(d=d.host),C(a,d)){var e=D(a,d);if(e[1]>e[2])return!0}d=d.parentNode}while(d&&d!==c.body)return!1},C=function(a,b){return"v"===a?A(b,"overflowY"):A(b,"overflowX")},D=function(a,b){return"v"===a?[b.scrollTop,b.scrollHeight,b.clientHeight]:[b.scrollLeft,b.scrollWidth,b.clientWidth]},E=function(a,b,c,d,e){var f,g=(f=window.getComputedStyle(b).direction,"h"===a&&"rtl"===f?-1:1),h=g*d,i=c.target,j=b.contains(i),k=!1,l=h>0,m=0,n=0;do{if(!i)break;var o=D(a,i),p=o[0],q=o[1]-o[2]-g*p;(p||q)&&C(a,i)&&(m+=q,n+=p);var r=i.parentNode;i=r&&r.nodeType===Node.DOCUMENT_FRAGMENT_NODE?r.host:r}while(!j&&i!==document.body||j&&(b.contains(i)||b===i))return l&&(e&&1>Math.abs(m)||!e&&h>m)?k=!0:!l&&(e&&1>Math.abs(n)||!e&&-h>n)&&(k=!0),k},F=function(a){return"changedTouches"in a?[a.changedTouches[0].clientX,a.changedTouches[0].clientY]:[0,0]},G=function(a){return[a.deltaX,a.deltaY]},H=function(a){return a&&"current"in a?a.current:a},I=0,J=[];let K=(b=function(a){var b=f.useRef([]),c=f.useRef([0,0]),d=f.useRef(),e=f.useState(I++)[0],g=f.useState(s)[0],h=f.useRef(a);f.useEffect(function(){h.current=a},[a]),f.useEffect(function(){if(a.inert){document.body.classList.add("block-interactivity-".concat(e));var b=(function(a,b,c){if(c||2==arguments.length)for(var d,e=0,f=b.length;e<f;e++)!d&&e in b||(d||(d=Array.prototype.slice.call(b,0,e)),d[e]=b[e]);return a.concat(d||Array.prototype.slice.call(b))})([a.lockRef.current],(a.shards||[]).map(H),!0).filter(Boolean);return b.forEach(function(a){return a.classList.add("allow-interactivity-".concat(e))}),function(){document.body.classList.remove("block-interactivity-".concat(e)),b.forEach(function(a){return a.classList.remove("allow-interactivity-".concat(e))})}}},[a.inert,a.lockRef.current,a.shards]);var i=f.useCallback(function(a,b){if("touches"in a&&2===a.touches.length||"wheel"===a.type&&a.ctrlKey)return!h.current.allowPinchZoom;var e,f=F(a),g=c.current,i="deltaX"in a?a.deltaX:g[0]-f[0],j="deltaY"in a?a.deltaY:g[1]-f[1],k=a.target,l=Math.abs(i)>Math.abs(j)?"h":"v";if("touches"in a&&"h"===l&&"range"===k.type)return!1;var m=B(l,k);if(!m)return!0;if(m?e=l:(e="v"===l?"h":"v",m=B(l,k)),!m)return!1;if(!d.current&&"changedTouches"in a&&(i||j)&&(d.current=e),!e)return!0;var n=d.current||e;return E(n,b,a,"h"===n?i:j,!0)},[]),j=f.useCallback(function(a){if(J.length&&J[J.length-1]===g){var c="deltaY"in a?G(a):F(a),d=b.current.filter(function(b){var d;return b.name===a.type&&(b.target===a.target||a.target===b.shadowParent)&&(d=b.delta,d[0]===c[0]&&d[1]===c[1])})[0];if(d&&d.should){a.cancelable&&a.preventDefault();return}if(!d){var e=(h.current.shards||[]).map(H).filter(Boolean).filter(function(b){return b.contains(a.target)});(e.length>0?i(a,e[0]):!h.current.noIsolation)&&a.cancelable&&a.preventDefault()}}},[]),k=f.useCallback(function(a,c,d,e){var f={name:a,delta:c,target:d,should:e,shadowParent:function(a){for(var b=null;null!==a;)a instanceof ShadowRoot&&(b=a.host,a=a.host),a=a.parentNode;return b}(d)};b.current.push(f),setTimeout(function(){b.current=b.current.filter(function(a){return a!==f})},1)},[]),l=f.useCallback(function(a){c.current=F(a),d.current=void 0},[]),m=f.useCallback(function(b){k(b.type,G(b),b.target,i(b,a.lockRef.current))},[]),n=f.useCallback(function(b){k(b.type,F(b),b.target,i(b,a.lockRef.current))},[]);f.useEffect(function(){return J.push(g),a.setCallbacks({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:n}),document.addEventListener("wheel",j,!1),document.addEventListener("touchmove",j,!1),document.addEventListener("touchstart",l,!1),function(){J=J.filter(function(a){return a!==g}),document.removeEventListener("wheel",j,!1),document.removeEventListener("touchmove",j,!1),document.removeEventListener("touchstart",l,!1)}},[]);var o=a.removeScrollBar,p=a.inert;return f.createElement(f.Fragment,null,p?f.createElement(g,{styles:"\n  .block-interactivity-".concat(e," {pointer-events: none;}\n  .allow-interactivity-").concat(e," {pointer-events: all;}\n")}):null,o?f.createElement(z,{noRelative:a.noRelative,gapMode:a.gapMode}):null)},m.useMedium(b),p);var L=f.forwardRef(function(a,b){return f.createElement(o,d({},a,{ref:b,sideCar:K}))});L.classNames=o.classNames;let M=L},19988,a=>{"use strict";a.s(["Close",()=>ad,"Content",()=>aa,"Description",()=>ac,"Overlay",()=>_,"Portal",()=>$,"Root",()=>Y,"Title",()=>ab,"Trigger",()=>Z,"WarningProvider",()=>U,"createDialogScope",()=>t]);var b=a.i(54436),c=a.i(64161),d=a.i(42527),e=a.i(33616),f=a.i(73490),g=a.i(9851),h=a.i(28432),i=a.i(14053),j=a.i(62705),k=a.i(15209),l=a.i(40686),m=a.i(9935),n=a.i(6022),o=a.i(85502),p=a.i(57495),q=a.i(28386),r="Dialog",[s,t]=(0,e.createContextScope)(r),[u,v]=s(r),w=a=>{let{__scopeDialog:c,children:d,open:e,defaultOpen:h,onOpenChange:i,modal:j=!0}=a,k=b.useRef(null),l=b.useRef(null),[m,n]=(0,g.useControllableState)({prop:e,defaultProp:h??!1,onChange:i,caller:r});return(0,q.jsx)(u,{scope:c,triggerRef:k,contentRef:l,contentId:(0,f.useId)(),titleId:(0,f.useId)(),descriptionId:(0,f.useId)(),open:m,onOpenChange:n,onOpenToggle:b.useCallback(()=>n(a=>!a),[n]),modal:j,children:d})};w.displayName=r;var x="DialogTrigger",y=b.forwardRef((a,b)=>{let{__scopeDialog:e,...f}=a,g=v(x,e),h=(0,d.useComposedRefs)(b,g.triggerRef);return(0,q.jsx)(l.Primitive.button,{type:"button","aria-haspopup":"dialog","aria-expanded":g.open,"aria-controls":g.contentId,"data-state":S(g.open),...f,ref:h,onClick:(0,c.composeEventHandlers)(a.onClick,g.onOpenToggle)})});y.displayName=x;var z="DialogPortal",[A,B]=s(z,{forceMount:void 0}),C=a=>{let{__scopeDialog:c,forceMount:d,children:e,container:f}=a,g=v(z,c);return(0,q.jsx)(A,{scope:c,forceMount:d,children:b.Children.map(e,a=>(0,q.jsx)(k.Presence,{present:d||g.open,children:(0,q.jsx)(j.Portal,{asChild:!0,container:f,children:a})}))})};C.displayName=z;var D="DialogOverlay",E=b.forwardRef((a,b)=>{let c=B(D,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=v(D,a.__scopeDialog);return f.modal?(0,q.jsx)(k.Presence,{present:d||f.open,children:(0,q.jsx)(G,{...e,ref:b})}):null});E.displayName=D;var F=(0,p.createSlot)("DialogOverlay.RemoveScroll"),G=b.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=v(D,c);return(0,q.jsx)(n.RemoveScroll,{as:F,allowPinchZoom:!0,shards:[e.contentRef],children:(0,q.jsx)(l.Primitive.div,{"data-state":S(e.open),...d,ref:b,style:{pointerEvents:"auto",...d.style}})})}),H="DialogContent",I=b.forwardRef((a,b)=>{let c=B(H,a.__scopeDialog),{forceMount:d=c.forceMount,...e}=a,f=v(H,a.__scopeDialog);return(0,q.jsx)(k.Presence,{present:d||f.open,children:f.modal?(0,q.jsx)(J,{...e,ref:b}):(0,q.jsx)(K,{...e,ref:b})})});I.displayName=H;var J=b.forwardRef((a,e)=>{let f=v(H,a.__scopeDialog),g=b.useRef(null),h=(0,d.useComposedRefs)(e,f.contentRef,g);return b.useEffect(()=>{let a=g.current;if(a)return(0,o.hideOthers)(a)},[]),(0,q.jsx)(L,{...a,ref:h,trapFocus:f.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,c.composeEventHandlers)(a.onCloseAutoFocus,a=>{a.preventDefault(),f.triggerRef.current?.focus()}),onPointerDownOutside:(0,c.composeEventHandlers)(a.onPointerDownOutside,a=>{let b=a.detail.originalEvent,c=0===b.button&&!0===b.ctrlKey;(2===b.button||c)&&a.preventDefault()}),onFocusOutside:(0,c.composeEventHandlers)(a.onFocusOutside,a=>a.preventDefault())})}),K=b.forwardRef((a,c)=>{let d=v(H,a.__scopeDialog),e=b.useRef(!1),f=b.useRef(!1);return(0,q.jsx)(L,{...a,ref:c,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:b=>{a.onCloseAutoFocus?.(b),b.defaultPrevented||(e.current||d.triggerRef.current?.focus(),b.preventDefault()),e.current=!1,f.current=!1},onInteractOutside:b=>{a.onInteractOutside?.(b),b.defaultPrevented||(e.current=!0,"pointerdown"===b.detail.originalEvent.type&&(f.current=!0));let c=b.target;d.triggerRef.current?.contains(c)&&b.preventDefault(),"focusin"===b.detail.originalEvent.type&&f.current&&b.preventDefault()}})}),L=b.forwardRef((a,c)=>{let{__scopeDialog:e,trapFocus:f,onOpenAutoFocus:g,onCloseAutoFocus:j,...k}=a,l=v(H,e),n=b.useRef(null),o=(0,d.useComposedRefs)(c,n);return(0,m.useFocusGuards)(),(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(i.FocusScope,{asChild:!0,loop:!0,trapped:f,onMountAutoFocus:g,onUnmountAutoFocus:j,children:(0,q.jsx)(h.DismissableLayer,{role:"dialog",id:l.contentId,"aria-describedby":l.descriptionId,"aria-labelledby":l.titleId,"data-state":S(l.open),...k,ref:o,onDismiss:()=>l.onOpenChange(!1)})}),(0,q.jsxs)(q.Fragment,{children:[(0,q.jsx)(W,{titleId:l.titleId}),(0,q.jsx)(X,{contentRef:n,descriptionId:l.descriptionId})]})]})}),M="DialogTitle",N=b.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=v(M,c);return(0,q.jsx)(l.Primitive.h2,{id:e.titleId,...d,ref:b})});N.displayName=M;var O="DialogDescription",P=b.forwardRef((a,b)=>{let{__scopeDialog:c,...d}=a,e=v(O,c);return(0,q.jsx)(l.Primitive.p,{id:e.descriptionId,...d,ref:b})});P.displayName=O;var Q="DialogClose",R=b.forwardRef((a,b)=>{let{__scopeDialog:d,...e}=a,f=v(Q,d);return(0,q.jsx)(l.Primitive.button,{type:"button",...e,ref:b,onClick:(0,c.composeEventHandlers)(a.onClick,()=>f.onOpenChange(!1))})});function S(a){return a?"open":"closed"}R.displayName=Q;var T="DialogTitleWarning",[U,V]=(0,e.createContext)(T,{contentName:H,titleName:M,docsSlug:"dialog"}),W=({titleId:a})=>{let c=V(T),d=`\`${c.contentName}\` requires a \`${c.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${c.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${c.docsSlug}`;return b.useEffect(()=>{a&&(document.getElementById(a)||console.error(d))},[d,a]),null},X=({contentRef:a,descriptionId:c})=>{let d=V("DialogDescriptionWarning"),e=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${d.contentName}}.`;return b.useEffect(()=>{let b=a.current?.getAttribute("aria-describedby");c&&b&&(document.getElementById(c)||console.warn(e))},[e,a,c]),null},Y=w,Z=y,$=C,_=E,aa=I,ab=N,ac=P,ad=R},84332,(a,b,c)=>{"use strict";function d(a){if("function"!=typeof WeakMap)return null;var b=new WeakMap,c=new WeakMap;return(d=function(a){return a?c:b})(a)}c._=function(a,b){if(!b&&a&&a.__esModule)return a;if(null===a||"object"!=typeof a&&"function"!=typeof a)return{default:a};var c=d(b);if(c&&c.has(a))return c.get(a);var e={__proto__:null},f=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var g in a)if("default"!==g&&Object.prototype.hasOwnProperty.call(a,g)){var h=f?Object.getOwnPropertyDescriptor(a,g):null;h&&(h.get||h.set)?Object.defineProperty(e,g,h):e[g]=a[g]}return e.default=a,c&&c.set(a,e),e}},93823,(a,b,c)=>{"use strict";function d(a){let b={};for(let[c,d]of a.entries()){let a=b[c];void 0===a?b[c]=d:Array.isArray(a)?a.push(d):b[c]=[a,d]}return b}function e(a){return"string"==typeof a?a:("number"!=typeof a||isNaN(a))&&"boolean"!=typeof a?"":String(a)}function f(a){let b=new URLSearchParams;for(let[c,d]of Object.entries(a))if(Array.isArray(d))for(let a of d)b.append(c,e(a));else b.set(c,e(d));return b}function g(a){for(var b=arguments.length,c=Array(b>1?b-1:0),d=1;d<b;d++)c[d-1]=arguments[d];for(let b of c){for(let c of b.keys())a.delete(c);for(let[c,d]of b.entries())a.append(c,d)}return a}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{assign:function(){return g},searchParamsToUrlQuery:function(){return d},urlQueryToSearchParams:function(){return f}})},88542,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{formatUrl:function(){return f},formatWithValidation:function(){return h},urlObjectKeys:function(){return g}});let d=a.r(84332)._(a.r(93823)),e=/https?|ftp|gopher|file/;function f(a){let{auth:b,hostname:c}=a,f=a.protocol||"",g=a.pathname||"",h=a.hash||"",i=a.query||"",j=!1;b=b?encodeURIComponent(b).replace(/%3A/i,":")+"@":"",a.host?j=b+a.host:c&&(j=b+(~c.indexOf(":")?"["+c+"]":c),a.port&&(j+=":"+a.port)),i&&"object"==typeof i&&(i=String(d.urlQueryToSearchParams(i)));let k=a.search||i&&"?"+i||"";return f&&!f.endsWith(":")&&(f+=":"),a.slashes||(!f||e.test(f))&&!1!==j?(j="//"+(j||""),g&&"/"!==g[0]&&(g="/"+g)):j||(j=""),h&&"#"!==h[0]&&(h="#"+h),k&&"?"!==k[0]&&(k="?"+k),""+f+j+(g=g.replace(/[?#]/g,encodeURIComponent))+(k=k.replace("#","%23"))+h}let g=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function h(a){return f(a)}},32663,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useMergedRef",{enumerable:!0,get:function(){return e}});let d=a.r(54436);function e(a,b){let c=(0,d.useRef)(null),e=(0,d.useRef)(null);return(0,d.useCallback)(d=>{if(null===d){let a=c.current;a&&(c.current=null,a());let b=e.current;b&&(e.current=null,b())}else a&&(c.current=f(a,d)),b&&(e.current=f(b,d))},[a,b])}function f(a,b){if("function"!=typeof a)return a.current=b,()=>{a.current=null};{let c=a(b);return"function"==typeof c?c:()=>a(null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54109,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DecodeError:function(){return p},MiddlewareNotFoundError:function(){return t},MissingStaticPage:function(){return s},NormalizeError:function(){return q},PageNotFoundError:function(){return r},SP:function(){return n},ST:function(){return o},WEB_VITALS:function(){return d},execOnce:function(){return e},getDisplayName:function(){return j},getLocationOrigin:function(){return h},getURL:function(){return i},isAbsoluteUrl:function(){return g},isResSent:function(){return k},loadGetInitialProps:function(){return m},normalizeRepeatedSlashes:function(){return l},stringifyError:function(){return u}});let d=["CLS","FCP","FID","INP","LCP","TTFB"];function e(a){let b,c=!1;return function(){for(var d=arguments.length,e=Array(d),f=0;f<d;f++)e[f]=arguments[f];return c||(c=!0,b=a(...e)),b}}let f=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,g=a=>f.test(a);function h(){let{protocol:a,hostname:b,port:c}=window.location;return a+"//"+b+(c?":"+c:"")}function i(){let{href:a}=window.location,b=h();return a.substring(b.length)}function j(a){return"string"==typeof a?a:a.displayName||a.name||"Unknown"}function k(a){return a.finished||a.headersSent}function l(a){let b=a.split("?");return b[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(b[1]?"?"+b.slice(1).join("?"):"")}async function m(a,b){let c=b.res||b.ctx&&b.ctx.res;if(!a.getInitialProps)return b.ctx&&b.Component?{pageProps:await m(b.Component,b.ctx)}:{};let d=await a.getInitialProps(b);if(c&&k(c))return d;if(!d)throw Object.defineProperty(Error('"'+j(a)+'.getInitialProps()" should resolve to an object. But found "'+d+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return d}let n="undefined"!=typeof performance,o=n&&["mark","measure","getEntriesByName"].every(a=>"function"==typeof performance[a]);class p extends Error{}class q extends Error{}class r extends Error{constructor(a){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+a}}class s extends Error{constructor(a,b){super(),this.message="Failed to load static file for page: "+a+" "+b}}class t extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function u(a){return JSON.stringify({message:a.message,stack:a.stack})}},42192,(a,b,c)=>{"use strict";function d(a){let b=a.indexOf("#"),c=a.indexOf("?"),d=c>-1&&(b<0||c<b);return d||b>-1?{pathname:a.substring(0,d?c:b),query:d?a.substring(c,b>-1?b:void 0):"",hash:b>-1?a.slice(b):""}:{pathname:a,query:"",hash:""}}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"parsePath",{enumerable:!0,get:function(){return d}})},77244,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addPathPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(42192);function e(a,b){if(!a.startsWith("/")||!b)return a;let{pathname:c,query:e,hash:f}=(0,d.parsePath)(a);return""+b+c+e+f}},26474,(a,b,c)=>{"use strict";function d(a){return a.replace(/\/$/,"")||"/"}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeTrailingSlash",{enumerable:!0,get:function(){return d}})},88661,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"normalizePathTrailingSlash",{enumerable:!0,get:function(){return f}});let d=a.r(26474),e=a.r(42192),f=a=>{if(!a.startsWith("/"))return a;let{pathname:b,query:c,hash:f}=(0,e.parsePath)(a);return/\.[^/]+\/?$/.test(b)?""+(0,d.removeTrailingSlash)(b)+c+f:b.endsWith("/")?""+b+c+f:b+"/"+c+f};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},41069,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"addBasePath",{enumerable:!0,get:function(){return f}});let d=a.r(77244),e=a.r(88661);function f(a,b){return(0,e.normalizePathTrailingSlash)((0,d.addPathPrefix)(a,""))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},80108,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"warnOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},64048,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HMR_REFRESH:function(){return i},ACTION_NAVIGATE:function(){return e},ACTION_PREFETCH:function(){return h},ACTION_REFRESH:function(){return d},ACTION_RESTORE:function(){return f},ACTION_SERVER_ACTION:function(){return j},ACTION_SERVER_PATCH:function(){return g},PrefetchCacheEntryStatus:function(){return l},PrefetchKind:function(){return k}});let d="refresh",e="navigate",f="restore",g="server-patch",h="prefetch",i="hmr-refresh",j="server-action";var k=function(a){return a.AUTO="auto",a.FULL="full",a.TEMPORARY="temporary",a}({}),l=function(a){return a.fresh="fresh",a.reusable="reusable",a.expired="expired",a.stale="stale",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},28070,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ACTION_HEADER:function(){return e},FLIGHT_HEADERS:function(){return m},NEXT_ACTION_NOT_FOUND_HEADER:function(){return t},NEXT_DID_POSTPONE_HEADER:function(){return p},NEXT_HMR_REFRESH_HASH_COOKIE:function(){return j},NEXT_HMR_REFRESH_HEADER:function(){return i},NEXT_IS_PRERENDER_HEADER:function(){return s},NEXT_REWRITTEN_PATH_HEADER:function(){return q},NEXT_REWRITTEN_QUERY_HEADER:function(){return r},NEXT_ROUTER_PREFETCH_HEADER:function(){return g},NEXT_ROUTER_SEGMENT_PREFETCH_HEADER:function(){return h},NEXT_ROUTER_STALE_TIME_HEADER:function(){return o},NEXT_ROUTER_STATE_TREE_HEADER:function(){return f},NEXT_RSC_UNION_QUERY:function(){return n},NEXT_URL:function(){return k},RSC_CONTENT_TYPE_HEADER:function(){return l},RSC_HEADER:function(){return d}});let d="rsc",e="next-action",f="next-router-state-tree",g="next-router-prefetch",h="next-router-segment-prefetch",i="next-hmr-refresh",j="__next_hmr_refresh_hash__",k="next-url",l="text/x-component",m=[d,f,g,i,h],n="_rsc",o="x-nextjs-stale-time",p="x-nextjs-postponed",q="x-nextjs-rewritten-path",r="x-nextjs-rewritten-query",s="x-nextjs-prerender",t="x-nextjs-action-not-found";("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30820,(a,b,c)=>{"use strict";function d(a){return null!==a&&"object"==typeof a&&"then"in a&&"function"==typeof a.then}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isThenable",{enumerable:!0,get:function(){return d}})},39732,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{dispatchAppRouterAction:function(){return g},useActionQueue:function(){return h}});let d=a.r(84332)._(a.r(54436)),e=a.r(30820),f=null;function g(a){if(null===f)throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0});f(a)}function h(a){let[b,c]=d.default.useState(a.state);return f=b=>a.dispatch(b,c),(0,e.isThenable)(b)?(0,d.use)(b):b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},62778,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"callServer",{enumerable:!0,get:function(){return g}});let d=a.r(54436),e=a.r(64048),f=a.r(39732);async function g(a,b){return new Promise((c,g)=>{(0,d.startTransition)(()=>{(0,f.dispatchAppRouterAction)({type:e.ACTION_SERVER_ACTION,actionId:a,actionArgs:b,resolve:c,reject:g})})})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},70522,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findSourceMapURL",{enumerable:!0,get:function(){return d}});let d=void 0;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},33865,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getFlightDataPartsFromPath:function(){return e},getNextFlightSegmentPath:function(){return f},normalizeFlightData:function(){return g},prepareFlightRouterStateForRequest:function(){return h}});let d=a.r(23741);function e(a){var b;let[c,d,e,f]=a.slice(-4),g=a.slice(0,-4);return{pathToSegment:g.slice(0,-1),segmentPath:g,segment:null!=(b=g[g.length-1])?b:"",tree:c,seedData:d,head:e,isHeadPartial:f,isRootRender:4===a.length}}function f(a){return a.slice(2)}function g(a){return"string"==typeof a?a:a.map(a=>e(a))}function h(a,b){return b?encodeURIComponent(JSON.stringify(a)):encodeURIComponent(JSON.stringify(function a(b){var c,e;let[f,g,h,i,j,k]=b,l="string"==typeof(c=f)&&c.startsWith(d.PAGE_SEGMENT_KEY+"?")?d.PAGE_SEGMENT_KEY:c,m={};for(let[b,c]of Object.entries(g))m[b]=a(c);let n=[l,m,null,(e=i)&&"refresh"!==e?i:null];return void 0!==j&&(n[4]=j),void 0!==k&&(n[5]=k),n}(a)))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},34061,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{getAppBuildId:function(){return f},setAppBuildId:function(){return e}});let d="";function e(a){d=a}function f(){return d}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},38722,(a,b,c)=>{"use strict";function d(a){let b=5381;for(let c=0;c<a.length;c++)b=(b<<5)+b+a.charCodeAt(c)|0;return b>>>0}function e(a){return d(a).toString(36).slice(0,5)}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{djb2Hash:function(){return d},hexHash:function(){return e}})},38792,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"computeCacheBustingSearchParam",{enumerable:!0,get:function(){return e}});let d=a.r(38722);function e(a,b,c,e){return(void 0===a||"0"===a)&&void 0===b&&void 0===c&&void 0===e?"":(0,d.hexHash)([a||"0",b||"0",c||"0",e||"0"].join(","))}},9932,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{setCacheBustingSearchParam:function(){return f},setCacheBustingSearchParamWithHash:function(){return g}});let d=a.r(38792),e=a.r(28070),f=(a,b)=>{g(a,(0,d.computeCacheBustingSearchParam)(b[e.NEXT_ROUTER_PREFETCH_HEADER],b[e.NEXT_ROUTER_SEGMENT_PREFETCH_HEADER],b[e.NEXT_ROUTER_STATE_TREE_HEADER],b[e.NEXT_URL]))},g=(a,b)=>{let c=a.search,d=(c.startsWith("?")?c.slice(1):c).split("&").filter(a=>a&&!a.startsWith(""+e.NEXT_RSC_UNION_QUERY+"="));b.length>0?d.push(e.NEXT_RSC_UNION_QUERY+"="+b):d.push(""+e.NEXT_RSC_UNION_QUERY),a.search=d.length?"?"+d.join("&"):""};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},76664,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ROOT_SEGMENT_CACHE_KEY:function(){return f},ROOT_SEGMENT_REQUEST_KEY:function(){return e},appendSegmentCacheKeyPart:function(){return j},appendSegmentRequestKeyPart:function(){return h},convertSegmentPathToStaticExportFilename:function(){return m},createSegmentCacheKeyPart:function(){return i},createSegmentRequestKeyPart:function(){return g}});let d=a.r(23741),e="",f="";function g(a){if("string"==typeof a)return a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:"/_not-found"===a?"_not-found":l(a);let b=a[0],c=a[2];return"$"+c+"$"+l(b)}function h(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}function i(a,b){return"string"==typeof b?a:a+"$"+l(b[1])}function j(a,b,c){return a+"/"+("children"===b?c:"@"+l(b)+"/"+c)}let k=/^[a-zA-Z0-9\-_@]+$/;function l(a){return k.test(a)?a:"!"+btoa(a).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}function m(a){return"__next"+a.replace(/\//g,".")+".txt"}},16849,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{doesStaticSegmentAppearInURL:function(){return j},getCacheKeyForDynamicParam:function(){return k},getParamValueFromCacheKey:function(){return m},getRenderedPathname:function(){return h},getRenderedSearch:function(){return g},parseDynamicParamFromURLPart:function(){return i},urlToUrlWithoutFlightMarker:function(){return l}});let d=a.r(23741),e=a.r(76664),f=a.r(28070);function g(a){let b=a.headers.get(f.NEXT_REWRITTEN_QUERY_HEADER);return null!==b?""===b?"":"?"+b:l(new URL(a.url)).search}function h(a){let b=a.headers.get(f.NEXT_REWRITTEN_PATH_HEADER);return null!=b?b:l(new URL(a.url)).pathname}function i(a,b,c){switch(a){case"c":case"ci":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):[];case"oc":return c<b.length?b.slice(c).map(a=>encodeURIComponent(a)):null;case"d":case"di":if(c>=b.length)return"";return encodeURIComponent(b[c]);default:return""}}function j(a){return!(a===e.ROOT_SEGMENT_REQUEST_KEY||a.startsWith(d.PAGE_SEGMENT_KEY)||"("===a[0]&&a.endsWith(")"))&&a!==d.DEFAULT_SEGMENT_KEY&&"/_not-found"!==a}function k(a,b){return"string"==typeof a?(0,d.addSearchParamsIfPageSegment)(a,Object.fromEntries(new URLSearchParams(b))):null===a?"":a.join("/")}function l(a){let b=new URL(a);if(b.searchParams.delete(f.NEXT_RSC_UNION_QUERY),b.pathname.endsWith(".txt")){let{pathname:a}=b,c=a.endsWith("/index.txt")?10:4;b.pathname=a.slice(0,-c)}return b}function m(a,b){return"c"===b||"oc"===b?a.split("/"):a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},84907,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createFetch:function(){return q},createFromNextReadableStream:function(){return r},fetchServerResponse:function(){return p}});let d=a.r(41376),e=a.r(28070),f=a.r(62778),g=a.r(70522),h=a.r(64048),i=a.r(33865),j=a.r(34061),k=a.r(9932),l=a.r(16849),m=d.createFromReadableStream;function n(a){return{flightData:(0,l.urlToUrlWithoutFlightMarker)(new URL(a,location.origin)).toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}let o=new AbortController;async function p(a,b){let{flightRouterState:c,nextUrl:d,prefetchKind:f}=b,g={[e.RSC_HEADER]:"1",[e.NEXT_ROUTER_STATE_TREE_HEADER]:(0,i.prepareFlightRouterStateForRequest)(c,b.isHmrRefresh)};f===h.PrefetchKind.AUTO&&(g[e.NEXT_ROUTER_PREFETCH_HEADER]="1"),d&&(g[e.NEXT_URL]=d);try{var k;let b=f?f===h.PrefetchKind.TEMPORARY?"high":"low":"auto";(a=new URL(a)).pathname.endsWith("/")?a.pathname+="index.txt":a.pathname+=".txt";let c=await q(a,g,b,o.signal),d=(0,l.urlToUrlWithoutFlightMarker)(new URL(c.url)),m=c.redirected?d:void 0,p=c.headers.get("content-type")||"",s=!!(null==(k=c.headers.get("vary"))?void 0:k.includes(e.NEXT_URL)),t=!!c.headers.get(e.NEXT_DID_POSTPONE_HEADER),u=c.headers.get(e.NEXT_ROUTER_STALE_TIME_HEADER),v=null!==u?1e3*parseInt(u,10):-1,w=p.startsWith(e.RSC_CONTENT_TYPE_HEADER);if(w||(w=p.startsWith("text/plain")),!w||!c.ok||!c.body)return a.hash&&(d.hash=a.hash),n(d.toString());let x=t?function(a){let b=a.getReader();return new ReadableStream({async pull(a){for(;;){let{done:c,value:d}=await b.read();if(!c){a.enqueue(d);continue}return}}})}(c.body):c.body,y=await r(x);if((0,j.getAppBuildId)()!==y.b)return n(c.url);return{flightData:(0,i.normalizeFlightData)(y.f),canonicalUrl:m,couldBeIntercepted:s,prerendered:y.S,postponed:t,staleTime:v}}catch(b){return o.signal.aborted||console.error("Failed to fetch RSC payload for "+a+". Falling back to browser navigation.",b),{flightData:a.toString(),canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1}}}async function q(a,b,c,d){let f=new URL(a);(0,k.setCacheBustingSearchParam)(f,b);let g=await fetch(f,{credentials:"same-origin",headers:b,priority:c||void 0,signal:d}),h=g.redirected,i=new URL(g.url,f);return i.searchParams.delete(e.NEXT_RSC_UNION_QUERY),{url:i.href,redirected:h,ok:g.ok,headers:g.headers,body:g.body,status:g.status}}function r(a){return m(a,{callServer:f.callServer,findSourceMapURL:g.findSourceMapURL})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},831,(a,b,c)=>{"use strict";function d(a,b){return void 0===b&&(b=!0),a.pathname+a.search+(b?a.hash:"")}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createHrefFromUrl",{enumerable:!0,get:function(){return d}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},92822,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"createRouterCacheKey",{enumerable:!0,get:function(){return e}});let d=a.r(23741);function e(a,b){return(void 0===b&&(b=!1),Array.isArray(a))?a[0]+"|"+a[1]+"|"+a[2]:b&&a.startsWith(d.PAGE_SEGMENT_KEY)?d.PAGE_SEGMENT_KEY:a}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},2559,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheBelowFlightSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,d.createRouterCacheKey)(i),k=c.parallelRoutes.get(h);if(!k)return;let l=b.parallelRoutes.get(h);if(l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l)),g)return void l.delete(j);let m=k.get(j),n=l.get(j);n&&m&&(n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes)},l.set(j,n)),a(n,m,(0,e.getNextFlightSegmentPath)(f)))}}});let d=a.r(92822),e=a.r(33865);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},95784,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"matchSegment",{enumerable:!0,get:function(){return d}});let d=(a,b)=>"string"==typeof a?"string"==typeof b&&a===b:"string"!=typeof b&&a[0]===b[0]&&a[1]===b[1];("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},65492,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"fillLazyItemsTillLeafWithHead",{enumerable:!0,get:function(){return function a(b,c,f,g,h,i,j){if(0===Object.keys(g[1]).length){c.head=i;return}for(let k in g[1]){let l,m=g[1][k],n=m[0],o=(0,d.createRouterCacheKey)(n),p=null!==h&&void 0!==h[2][k]?h[2][k]:null;if(f){let d=f.parallelRoutes.get(k);if(d){let f,g=(null==j?void 0:j.kind)==="auto"&&j.status===e.PrefetchCacheEntryStatus.reusable,h=new Map(d),l=h.get(o);f=null!==p?{lazyData:null,rsc:p[1],prefetchRsc:null,head:null,prefetchHead:null,loading:p[3],parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),navigatedAt:b}:g&&l?{lazyData:l.lazyData,rsc:l.rsc,prefetchRsc:l.prefetchRsc,head:l.head,prefetchHead:l.prefetchHead,parallelRoutes:new Map(l.parallelRoutes),loading:l.loading}:{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map(null==l?void 0:l.parallelRoutes),loading:null,navigatedAt:b},h.set(o,f),a(b,f,l,m,p||null,i,j),c.parallelRoutes.set(k,h);continue}}if(null!==p){let a=p[1],c=p[3];l={lazyData:null,rsc:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else l={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:b};let q=c.parallelRoutes.get(k);q?q.set(o,l):c.parallelRoutes.set(k,new Map([[o,l]])),a(b,l,void 0,m,p,i,j)}}}});let d=a.r(92822),e=a.r(64048);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},5697,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"invalidateCacheByRouterState",{enumerable:!0,get:function(){return e}});let d=a.r(92822);function e(a,b,c){for(let e in c[1]){let f=c[1][e][0],g=(0,d.createRouterCacheKey)(f),h=b.parallelRoutes.get(e);if(h){let b=new Map(h);b.delete(g),a.parallelRoutes.set(e,b)}}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},45137,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{fillCacheWithNewSubTreeData:function(){return i},fillCacheWithNewSubTreeDataButOnlyLoading:function(){return j}});let d=a.r(5697),e=a.r(65492),f=a.r(92822),g=a.r(23741);function h(a,b,c,h,i,j){let{segmentPath:k,seedData:l,tree:m,head:n}=h,o=b,p=c;for(let b=0;b<k.length;b+=2){let c=k[b],h=k[b+1],q=b===k.length-2,r=(0,f.createRouterCacheKey)(h),s=p.parallelRoutes.get(c);if(!s)continue;let t=o.parallelRoutes.get(c);t&&t!==s||(t=new Map(s),o.parallelRoutes.set(c,t));let u=s.get(r),v=t.get(r);if(q){if(l&&(!v||!v.lazyData||v===u)){let b=l[0],c=l[1],f=l[3];v={lazyData:null,rsc:j||b!==g.PAGE_SEGMENT_KEY?c:null,prefetchRsc:null,head:null,prefetchHead:null,loading:f,parallelRoutes:j&&u?new Map(u.parallelRoutes):new Map,navigatedAt:a},u&&j&&(0,d.invalidateCacheByRouterState)(v,u,m),j&&(0,e.fillLazyItemsTillLeafWithHead)(a,v,u,m,l,n,i),t.set(r,v)}continue}v&&u&&(v===u&&(v={lazyData:v.lazyData,rsc:v.rsc,prefetchRsc:v.prefetchRsc,head:v.head,prefetchHead:v.prefetchHead,parallelRoutes:new Map(v.parallelRoutes),loading:v.loading},t.set(r,v)),o=v,p=u)}}function i(a,b,c,d,e){h(a,b,c,d,e,!0)}function j(a,b,c,d,e){h(a,b,c,d,e,!1)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},18609,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyFlightData",{enumerable:!0,get:function(){return f}});let d=a.r(65492),e=a.r(45137);function f(a,b,c,f,g){let{tree:h,seedData:i,head:j,isRootRender:k}=f;if(null===i)return!1;if(k){let e=i[1];c.loading=i[3],c.rsc=e,c.prefetchRsc=null,(0,d.fillLazyItemsTillLeafWithHead)(a,c,b,h,i,j,g)}else c.rsc=b.rsc,c.prefetchRsc=b.prefetchRsc,c.parallelRoutes=new Map(b.parallelRoutes),c.loading=b.loading,(0,e.fillCacheWithNewSubTreeData)(a,c,b,f,g);return!0}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},56085,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addRefreshMarkerToActiveParallelSegments:function(){return function a(b,c){let[d,e,,g]=b;for(let h in d.includes(f.PAGE_SEGMENT_KEY)&&"refresh"!==g&&(b[2]=c,b[3]="refresh"),e)a(e[h],c)}},refreshInactiveParallelSegments:function(){return g}});let d=a.r(18609),e=a.r(84907),f=a.r(23741);async function g(a){let b=new Set;await h({...a,rootTree:a.updatedTree,fetchedSegments:b})}async function h(a){let{navigatedAt:b,state:c,updatedTree:f,updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k=f,canonicalUrl:l}=a,[,m,n,o]=f,p=[];if(n&&n!==l&&"refresh"===o&&!j.has(n)){j.add(n);let a=(0,e.fetchServerResponse)(new URL(n,location.origin),{flightRouterState:[k[0],k[1],k[2],"refetch"],nextUrl:i?c.nextUrl:null}).then(a=>{let{flightData:c}=a;if("string"!=typeof c)for(let a of c)(0,d.applyFlightData)(b,g,g,a)});p.push(a)}for(let a in m){let d=h({navigatedAt:b,state:c,updatedTree:m[a],updatedCache:g,includeNextUrl:i,fetchedSegments:j,rootTree:k,canonicalUrl:l});p.push(d)}await Promise.all(p)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},14057,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"applyRouterStatePatchToTree",{enumerable:!0,get:function(){return function a(b,c,d,i){let j,[k,l,m,n,o]=c;if(1===b.length){let a=h(c,d);return(0,g.addRefreshMarkerToActiveParallelSegments)(a,i),a}let[p,q]=b;if(!(0,f.matchSegment)(p,k))return null;if(2===b.length)j=h(l[q],d);else if(null===(j=a((0,e.getNextFlightSegmentPath)(b),l[q],d,i)))return null;let r=[b[0],{...l,[q]:j},m,n];return o&&(r[4]=!0),(0,g.addRefreshMarkerToActiveParallelSegments)(r,i),r}}});let d=a.r(23741),e=a.r(33865),f=a.r(95784),g=a.r(56085);function h(a,b){let[c,e]=a,[g,i]=b;if(g===d.DEFAULT_SEGMENT_KEY&&c!==d.DEFAULT_SEGMENT_KEY)return a;if((0,f.matchSegment)(c,g)){let b={};for(let a in e)void 0!==i[a]?b[a]=h(e[a],i[a]):b[a]=e[a];for(let a in i)b[a]||(b[a]=i[a]);let d=[c,b];return a[2]&&(d[2]=a[2]),a[3]&&(d[3]=a[3]),a[4]&&(d[4]=a[4]),d}return b}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},25144,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"shouldHardNavigate",{enumerable:!0,get:function(){return function a(b,c){let[f,g]=c,[h,i]=b;return(0,e.matchSegment)(h,f)?!(b.length<=2)&&a((0,d.getNextFlightSegmentPath)(b),g[i]):!!Array.isArray(h)}}});let d=a.r(33865),e=a.r(95784);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},49215,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isNavigatingToNewRootLayout",{enumerable:!0,get:function(){return function a(b,c){let d=b[0],e=c[0];if(Array.isArray(d)&&Array.isArray(e)){if(d[0]!==e[0]||d[2]!==e[2])return!0}else if(d!==e)return!0;if(b[4])return!c[4];if(c[4])return!0;let f=Object.values(b[1])[0],g=Object.values(c[1])[0];return!f||!g||a(f,g)}}}),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},83312,(a,b,c)=>{"use strict";function d(a){return a.startsWith("/")?a:"/"+a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"ensureLeadingSlash",{enumerable:!0,get:function(){return d}})},15093,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{normalizeAppPath:function(){return f},normalizeRscURL:function(){return g}});let d=a.r(83312),e=a.r(23741);function f(a){return(0,d.ensureLeadingSlash)(a.split("/").reduce((a,b,c,d)=>!b||(0,e.isGroupSegment)(b)||"@"===b[0]||("page"===b||"route"===b)&&c===d.length-1?a:a+"/"+b,""))}function g(a){return a.replace(/\.rsc($|\?)/,"$1")}},49063,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{INTERCEPTION_ROUTE_MARKERS:function(){return e},extractInterceptionRouteInformation:function(){return g},isInterceptionRouteAppPath:function(){return f}});let d=a.r(15093),e=["(..)(..)","(.)","(..)","(...)"];function f(a){return void 0!==a.split("/").find(a=>e.find(b=>a.startsWith(b)))}function g(a){let b,c,f;for(let d of a.split("/"))if(c=e.find(a=>d.startsWith(a))){[b,f]=a.split(c,2);break}if(!b||!c||!f)throw Object.defineProperty(Error("Invalid interception route: "+a+". Must be in the format /<intercepting route>/(..|...|..)(..)/<intercepted route>"),"__NEXT_ERROR_CODE",{value:"E269",enumerable:!1,configurable:!0});switch(b=(0,d.normalizeAppPath)(b),c){case"(.)":f="/"===b?"/"+f:b+"/"+f;break;case"(..)":if("/"===b)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..) marker at the root level, use (.) instead."),"__NEXT_ERROR_CODE",{value:"E207",enumerable:!1,configurable:!0});f=b.split("/").slice(0,-1).concat(f).join("/");break;case"(...)":f="/"+f;break;case"(..)(..)":let g=b.split("/");if(g.length<=2)throw Object.defineProperty(Error("Invalid interception route: "+a+". Cannot use (..)(..) marker at the root level or one level up."),"__NEXT_ERROR_CODE",{value:"E486",enumerable:!1,configurable:!0});f=g.slice(0,-2).concat(f).join("/");break;default:throw Object.defineProperty(Error("Invariant: unexpected marker"),"__NEXT_ERROR_CODE",{value:"E112",enumerable:!1,configurable:!0})}return{interceptingRoute:b,interceptedRoute:f}}},1856,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{computeChangedPath:function(){return j},extractPathFromFlightRouterState:function(){return i},getSelectedParams:function(){return function a(b,c){for(let d of(void 0===c&&(c={}),Object.values(b[1]))){let b=d[0],f=Array.isArray(b),g=f?b[1]:b;!g||g.startsWith(e.PAGE_SEGMENT_KEY)||(f&&("c"===b[2]||"oc"===b[2])?c[b[0]]=b[1].split("/"):f&&(c[b[0]]=b[1]),c=a(d,c))}return c}}});let d=a.r(49063),e=a.r(23741),f=a.r(95784),g=a=>"string"==typeof a?"children"===a?"":a:a[1];function h(a){return a.reduce((a,b)=>{let c;return""===(b="/"===(c=b)[0]?c.slice(1):c)||(0,e.isGroupSegment)(b)?a:a+"/"+b},"")||"/"}function i(a){var b;let c=Array.isArray(a[0])?a[0][1]:a[0];if(c===e.DEFAULT_SEGMENT_KEY||d.INTERCEPTION_ROUTE_MARKERS.some(a=>c.startsWith(a)))return;if(c.startsWith(e.PAGE_SEGMENT_KEY))return"";let f=[g(c)],j=null!=(b=a[1])?b:{},k=j.children?i(j.children):void 0;if(void 0!==k)f.push(k);else for(let[a,b]of Object.entries(j)){if("children"===a)continue;let c=i(b);void 0!==c&&f.push(c)}return h(f)}function j(a,b){let c=function a(b,c){let[e,h]=b,[j,k]=c,l=g(e),m=g(j);if(d.INTERCEPTION_ROUTE_MARKERS.some(a=>l.startsWith(a)||m.startsWith(a)))return"";if(!(0,f.matchSegment)(e,j)){var n;return null!=(n=i(c))?n:""}for(let b in h)if(k[b]){let c=a(h[b],k[b]);if(null!==c)return g(j)+"/"+c}return null}(a,b);return null==c||"/"===c?c:h(c.split("/"))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},51222,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleMutable",{enumerable:!0,get:function(){return f}});let d=a.r(1856);function e(a){return void 0!==a}function f(a,b){var c,f;let g=null==(c=b.shouldScroll)||c,h=a.nextUrl;if(e(b.patchedTree)){let c=(0,d.computeChangedPath)(a.tree,b.patchedTree);c?h=c:h||(h=a.canonicalUrl)}return{canonicalUrl:e(b.canonicalUrl)?b.canonicalUrl===a.canonicalUrl?a.canonicalUrl:b.canonicalUrl:a.canonicalUrl,pushRef:{pendingPush:e(b.pendingPush)?b.pendingPush:a.pushRef.pendingPush,mpaNavigation:e(b.mpaNavigation)?b.mpaNavigation:a.pushRef.mpaNavigation,preserveCustomHistoryState:e(b.preserveCustomHistoryState)?b.preserveCustomHistoryState:a.pushRef.preserveCustomHistoryState},focusAndScrollRef:{apply:!!g&&(!!e(null==b?void 0:b.scrollableSegments)||a.focusAndScrollRef.apply),onlyHashChange:b.onlyHashChange||!1,hashFragment:g?b.hashFragment&&""!==b.hashFragment?decodeURIComponent(b.hashFragment.slice(1)):a.focusAndScrollRef.hashFragment:null,segmentPaths:g?null!=(f=null==b?void 0:b.scrollableSegments)?f:a.focusAndScrollRef.segmentPaths:[]},cache:b.cache?b.cache:a.cache,prefetchCache:b.prefetchCache?b.prefetchCache:a.prefetchCache,tree:e(b.patchedTree)?b.patchedTree:a.tree,nextUrl:h}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},97130,(a,b,c)=>{"use strict";c._=function(a,b){if(!Object.prototype.hasOwnProperty.call(a,b))throw TypeError("attempted to use private field on non-instance");return a}},77968,(a,b,c)=>{"use strict";var d=0;c._=function(a){return"__private_"+d+++"_"+a}},61252,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"PromiseQueue",{enumerable:!0,get:function(){return j}});let d=a.r(97130),e=a.r(77968);var f=e._("_maxConcurrency"),g=e._("_runningCount"),h=e._("_queue"),i=e._("_processNext");class j{enqueue(a){let b,c,e=new Promise((a,d)=>{b=a,c=d}),f=async()=>{try{d._(this,g)[g]++;let c=await a();b(c)}catch(a){c(a)}finally{d._(this,g)[g]--,d._(this,i)[i]()}};return d._(this,h)[h].push({promiseFn:e,task:f}),d._(this,i)[i](),e}bump(a){let b=d._(this,h)[h].findIndex(b=>b.promiseFn===a);if(b>-1){let a=d._(this,h)[h].splice(b,1)[0];d._(this,h)[h].unshift(a),d._(this,i)[i](!0)}}constructor(a=5){Object.defineProperty(this,i,{value:k}),Object.defineProperty(this,f,{writable:!0,value:void 0}),Object.defineProperty(this,g,{writable:!0,value:void 0}),Object.defineProperty(this,h,{writable:!0,value:void 0}),d._(this,f)[f]=a,d._(this,g)[g]=0,d._(this,h)[h]=[]}}function k(a){if(void 0===a&&(a=!1),(d._(this,g)[g]<d._(this,f)[f]||a)&&d._(this,h)[h].length>0){var b;null==(b=d._(this,h)[h].shift())||b.task()}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},44078,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{DYNAMIC_STALETIME_MS:function(){return m},STATIC_STALETIME_MS:function(){return n},createSeededPrefetchCacheEntry:function(){return j},getOrCreatePrefetchCacheEntry:function(){return i},prunePrefetchCache:function(){return l}});let d=a.r(84907),e=a.r(64048),f=a.r(64383);function g(a,b,c){let d=a.pathname;return(b&&(d+=a.search),c)?""+c+"%"+d:d}function h(a,b,c){return g(a,b===e.PrefetchKind.FULL,c)}function i(a){let{url:b,nextUrl:c,tree:d,prefetchCache:f,kind:h,allowAliasing:i=!0}=a,j=function(a,b,c,d,f){for(let h of(void 0===b&&(b=e.PrefetchKind.TEMPORARY),[c,null])){let c=g(a,!0,h),i=g(a,!1,h),j=a.search?c:i,k=d.get(j);if(k&&f){if(k.url.pathname===a.pathname&&k.url.search!==a.search)return{...k,aliased:!0};return k}let l=d.get(i);if(f&&a.search&&b!==e.PrefetchKind.FULL&&l&&!l.key.includes("%"))return{...l,aliased:!0}}if(b!==e.PrefetchKind.FULL&&f){for(let b of d.values())if(b.url.pathname===a.pathname&&!b.key.includes("%"))return{...b,aliased:!0}}}(b,h,c,f,i);return j?(j.status=o(j),j.kind!==e.PrefetchKind.FULL&&h===e.PrefetchKind.FULL&&j.data.then(a=>{if(!(Array.isArray(a.flightData)&&a.flightData.some(a=>a.isRootRender&&null!==a.seedData)))return k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:null!=h?h:e.PrefetchKind.TEMPORARY})}),h&&j.kind===e.PrefetchKind.TEMPORARY&&(j.kind=h),j):k({tree:d,url:b,nextUrl:c,prefetchCache:f,kind:h||e.PrefetchKind.TEMPORARY})}function j(a){let{nextUrl:b,tree:c,prefetchCache:d,url:f,data:g,kind:i}=a,j=g.couldBeIntercepted?h(f,i,b):h(f,i),k={treeAtTimeOfPrefetch:c,data:Promise.resolve(g),kind:i,prefetchTime:Date.now(),lastUsedTime:Date.now(),staleTime:g.staleTime,key:j,status:e.PrefetchCacheEntryStatus.fresh,url:f};return d.set(j,k),k}function k(a){let{url:b,kind:c,tree:g,nextUrl:i,prefetchCache:j}=a,k=h(b,c),l=f.prefetchQueue.enqueue(()=>(0,d.fetchServerResponse)(b,{flightRouterState:g,nextUrl:i,prefetchKind:c}).then(a=>{let c;if(a.couldBeIntercepted&&(c=function(a){let{url:b,nextUrl:c,prefetchCache:d,existingCacheKey:e}=a,f=d.get(e);if(!f)return;let g=h(b,f.kind,c);return d.set(g,{...f,key:g}),d.delete(e),g}({url:b,existingCacheKey:k,nextUrl:i,prefetchCache:j})),a.prerendered){let b=j.get(null!=c?c:k);b&&(b.kind=e.PrefetchKind.FULL,-1!==a.staleTime&&(b.staleTime=a.staleTime))}return a})),m={treeAtTimeOfPrefetch:g,data:l,kind:c,prefetchTime:Date.now(),lastUsedTime:null,staleTime:-1,key:k,status:e.PrefetchCacheEntryStatus.fresh,url:b};return j.set(k,m),m}function l(a){for(let[b,c]of a)o(c)===e.PrefetchCacheEntryStatus.expired&&a.delete(b)}let m=1e3*Number("0"),n=1e3*Number("300");function o(a){let{kind:b,prefetchTime:c,lastUsedTime:d}=a;return Date.now()<(null!=d?d:c)+m?d?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.fresh:b===e.PrefetchKind.AUTO&&Date.now()<c+n?e.PrefetchCacheEntryStatus.stale:b===e.PrefetchKind.FULL&&Date.now()<c+n?e.PrefetchCacheEntryStatus.reusable:e.PrefetchCacheEntryStatus.expired}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},64383,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{prefetchQueue:function(){return f},prefetchReducer:function(){return g}});let d=a.r(61252),e=a.r(44078),f=new d.PromiseQueue(5),g=function(a,b){(0,e.prunePrefetchCache)(a.prefetchCache);let{url:c}=b;return(0,e.getOrCreatePrefetchCacheEntry)({url:c,nextUrl:a.nextUrl,prefetchCache:a.prefetchCache,kind:b.kind,tree:a.tree,allowAliasing:!0}),a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},19979,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"HTML_LIMITED_BOT_UA_RE",{enumerable:!0,get:function(){return d}});let d=/[\w-]+-Google|Google-[\w-]+|Chrome-Lighthouse|Slurp|DuckDuckBot|baiduspider|yandex|sogou|bitlybot|tumblr|vkShare|quora link preview|redditbot|ia_archiver|Bingbot|BingPreview|applebot|facebookexternalhit|facebookcatalog|Twitterbot|LinkedInBot|Slackbot|Discordbot|WhatsApp|SkypeUriPreview|Yeti|googleweblight/i},71713,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{HTML_LIMITED_BOT_UA_RE:function(){return d.HTML_LIMITED_BOT_UA_RE},HTML_LIMITED_BOT_UA_RE_STRING:function(){return f},getBotType:function(){return i},isBot:function(){return h}});let d=a.r(19979),e=/Googlebot(?!-)|Googlebot$/i,f=d.HTML_LIMITED_BOT_UA_RE.source;function g(a){return d.HTML_LIMITED_BOT_UA_RE.test(a)}function h(a){return e.test(a)||g(a)}function i(a){return e.test(a)?"dom":g(a)?"html":void 0}},42147,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"AppRouterAnnouncer",{enumerable:!0,get:function(){return g}});let d=a.r(54436),e=a.r(27444),f="next-route-announcer";function g(a){let{tree:b}=a,[c,g]=(0,d.useState)(null);(0,d.useEffect)(()=>(g(function(){var a;let b=document.getElementsByName(f)[0];if(null==b||null==(a=b.shadowRoot)?void 0:a.childNodes[0])return b.shadowRoot.childNodes[0];{let a=document.createElement(f);a.style.cssText="position:absolute";let b=document.createElement("div");return b.ariaLive="assertive",b.id="__next-route-announcer__",b.role="alert",b.style.cssText="position:absolute;border:0;height:1px;margin:-1px;padding:0;width:1px;clip:rect(0 0 0 0);overflow:hidden;white-space:nowrap;word-wrap:normal",a.attachShadow({mode:"open"}).appendChild(b),document.body.appendChild(a),b}}()),()=>{let a=document.getElementsByTagName(f)[0];(null==a?void 0:a.isConnected)&&document.body.removeChild(a)}),[]);let[h,i]=(0,d.useState)(""),j=(0,d.useRef)(void 0);return(0,d.useEffect)(()=>{let a="";if(document.title)a=document.title;else{let b=document.querySelector("h1");b&&(a=b.innerText||b.textContent||"")}void 0!==j.current&&j.current!==a&&i(a),j.current=a},[b]),c?(0,e.createPortal)(h,c):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},48678,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{RedirectBoundary:function(){return l},RedirectErrorBoundary:function(){return k}});let d=a.r(84332),e=a.r(28386),f=d._(a.r(54436)),g=a.r(92018),h=a.r(28548),i=a.r(34816);function j(a){let{redirect:b,reset:c,redirectType:d}=a,e=(0,g.useRouter)();return(0,f.useEffect)(()=>{f.default.startTransition(()=>{d===i.RedirectType.push?e.push(b,{}):e.replace(b,{}),c()})},[b,d,c,e]),null}class k extends f.default.Component{static getDerivedStateFromError(a){if((0,i.isRedirectError)(a))return{redirect:(0,h.getURLFromRedirectError)(a),redirectType:(0,h.getRedirectTypeFromError)(a)};throw a}render(){let{redirect:a,redirectType:b}=this.state;return null!==a&&null!==b?(0,e.jsx)(j,{redirect:a,redirectType:b,reset:()=>this.setState({redirect:null})}):this.props.children}constructor(a){super(a),this.state={redirect:null,redirectType:null}}}function l(a){let{children:b}=a,c=(0,g.useRouter)();return(0,e.jsx)(k,{router:c,children:b})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},13184,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"findHeadInCache",{enumerable:!0,get:function(){return f}});let d=a.r(23741),e=a.r(92822);function f(a,b){return function a(b,c,f,g){if(0===Object.keys(c).length)return[b,f,g];let h=Object.keys(c).filter(a=>"children"!==a);for(let g of("children"in c&&h.unshift("children"),h)){let[h,i]=c[g];if(h===d.DEFAULT_SEGMENT_KEY)continue;let j=b.parallelRoutes.get(g);if(!j)continue;let k=(0,e.createRouterCacheKey)(h),l=(0,e.createRouterCacheKey)(h,!0),m=j.get(k);if(!m)continue;let n=a(m,i,f+"/"+k,f+"/"+l);if(n)return n}return null}(a,b,"","")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},27304,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"unresolvedThenable",{enumerable:!0,get:function(){return d}});let d={then:()=>{}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},85823,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"pathHasPrefix",{enumerable:!0,get:function(){return e}});let d=a.r(42192);function e(a,b){if("string"!=typeof a)return!1;let{pathname:c}=(0,d.parsePath)(a);return c===b||c.startsWith(b+"/")}},21981,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasBasePath",{enumerable:!0,get:function(){return e}});let d=a.r(85823);function e(a){return(0,d.pathHasPrefix)(a,"")}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},60155,(a,b,c)=>{"use strict";function d(a){return a}Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"removeBasePath",{enumerable:!0,get:function(){return d}}),a.r(21981),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},93786,(a,b,c)=>{"use strict";function d(a){return!1}function e(){}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{handleHardNavError:function(){return d},useNavFailureHandler:function(){return e}}),a.r(54436),a.r(831),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},69,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{GracefulDegradeBoundary:function(){return f},default:function(){return g}});let d=a.r(28386),e=a.r(54436);class f extends e.Component{static getDerivedStateFromError(a){return{hasError:!0}}componentDidMount(){let a=this.htmlRef.current;this.state.hasError&&a&&Object.entries(this.htmlAttributes).forEach(b=>{let[c,d]=b;a.setAttribute(c,d)})}render(){let{hasError:a}=this.state;return a?(0,d.jsx)("html",{ref:this.htmlRef,suppressHydrationWarning:!0,dangerouslySetInnerHTML:{__html:this.rootHtml}}):this.props.children}constructor(a){super(a),this.state={hasError:!1},this.rootHtml="",this.htmlAttributes={},this.htmlRef=(0,e.createRef)()}}let g=f;("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},22470,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"useUntrackedPathname",{enumerable:!0,get:function(){return f}});let d=a.r(54436),e=a.r(19383);function f(){return!function(){{let{workUnitAsyncStorage:b}=a.r(32319),c=b.getStore();if(!c)return!1;switch(c.type){case"prerender":case"prerender-client":case"prerender-ppr":let d=c.fallbackRouteParams;return!!d&&d.size>0}return!1}}()?(0,d.useContext)(e.PathnameContext):null}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},13317,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{ErrorBoundary:function(){return k},ErrorBoundaryHandler:function(){return j}});let d=a.r(95155),e=a.r(28386),f=d._(a.r(54436)),g=a.r(22470),h=a.r(34721);a.r(93786);let i=a.r(10840);a.r(71713);class j extends f.default.Component{static getDerivedStateFromError(a){if((0,h.isNextRouterError)(a))throw a;return{error:a}}static getDerivedStateFromProps(a,b){let{error:c}=b;return a.pathname!==b.previousPathname&&b.error?{error:null,previousPathname:a.pathname}:{error:b.error,previousPathname:a.pathname}}render(){return this.state.error&&1?(0,e.jsxs)(e.Fragment,{children:[(0,e.jsx)(i.HandleISRError,{error:this.state.error}),this.props.errorStyles,this.props.errorScripts,(0,e.jsx)(this.props.errorComponent,{error:this.state.error,reset:this.reset})]}):this.props.children}constructor(a){super(a),this.reset=()=>{this.setState({error:null})},this.state={error:null,previousPathname:this.props.pathname}}}function k(a){let{errorComponent:b,errorStyles:c,errorScripts:d,children:f}=a,h=(0,g.useUntrackedPathname)();return b?(0,e.jsx)(j,{pathname:h,errorComponent:b,errorStyles:c,errorScripts:d,children:f}):(0,e.jsx)(e.Fragment,{children:f})}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},98923,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"default",{enumerable:!0,get:function(){return f}}),a.r(95155);let d=a.r(28386);a.r(54436),a.r(69);let e=a.r(13317);function f(a){let{children:b,errorComponent:c,errorStyles:f,errorScripts:g}=a;return(0,d.jsx)(e.ErrorBoundary,{errorComponent:c,errorStyles:f,errorScripts:g,children:b})}a.r(71713),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},13172,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{MetadataBoundary:function(){return f},OutletBoundary:function(){return h},RootLayoutBoundary:function(){return i},ViewportBoundary:function(){return g}});let d=a.r(2343),e={[d.METADATA_BOUNDARY_NAME]:function({children:a}){return a},[d.VIEWPORT_BOUNDARY_NAME]:function({children:a}){return a},[d.OUTLET_BOUNDARY_NAME]:function({children:a}){return a},[d.ROOT_LAYOUT_BOUNDARY_NAME]:function({children:a}){return a}},f=e[d.METADATA_BOUNDARY_NAME.slice(0)],g=e[d.VIEWPORT_BOUNDARY_NAME.slice(0)],h=e[d.OUTLET_BOUNDARY_NAME.slice(0)],i=e[d.ROOT_LAYOUT_BOUNDARY_NAME.slice(0)]},70988,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createEmptyCacheNode:function(){return G},createPrefetchURL:function(){return E},default:function(){return K},isExternalURL:function(){return D}});let d=a.r(95155),e=a.r(84332),f=a.r(28386),g=e._(a.r(54436)),h=a.r(86847),i=a.r(64048),j=a.r(831),k=a.r(19383),l=a.r(39732),m=a.r(71713),n=a.r(41069),o=a.r(42147),p=a.r(48678),q=a.r(13184),r=a.r(27304),s=a.r(60155),t=a.r(21981),u=a.r(1856),v=a.r(93786),w=a.r(71729),x=a.r(28548),y=a.r(34816);a.r(59504);let z=d._(a.r(98923)),A=d._(a.r(98816)),B=a.r(13172),C={};function D(a){return a.origin!==window.location.origin}function E(a){let b;if((0,m.isBot)(window.navigator.userAgent))return null;try{b=new URL((0,n.addBasePath)(a),window.location.href)}catch(b){throw Object.defineProperty(Error("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),"__NEXT_ERROR_CODE",{value:"E234",enumerable:!1,configurable:!0})}return D(b)?null:b}function F(a){let{appRouterState:b}=a;return(0,g.useInsertionEffect)(()=>{let{tree:a,pushRef:c,canonicalUrl:d}=b,e={...c.preserveCustomHistoryState?window.history.state:{},__NA:!0,__PRIVATE_NEXTJS_INTERNALS_TREE:a};c.pendingPush&&(0,j.createHrefFromUrl)(new URL(window.location.href))!==d?(c.pendingPush=!1,window.history.pushState(e,"",d)):window.history.replaceState(e,"",d)},[b]),(0,g.useEffect)(()=>{},[b.nextUrl,b.tree]),null}function G(){return{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1}}function H(a){null==a&&(a={});let b=window.history.state,c=null==b?void 0:b.__NA;c&&(a.__NA=c);let d=null==b?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;return d&&(a.__PRIVATE_NEXTJS_INTERNALS_TREE=d),a}function I(a){let{headCacheNode:b}=a,c=null!==b?b.head:null,d=null!==b?b.prefetchHead:null,e=null!==d?d:c;return(0,g.useDeferredValue)(c,e)}function J(a){let b,{actionQueue:c,assetPrefix:d,globalError:e}=a,j=(0,l.useActionQueue)(c),{canonicalUrl:m}=j,{searchParams:n,pathname:v}=(0,g.useMemo)(()=>{let a=new URL(m,"http://n");return{searchParams:a.searchParams,pathname:(0,t.hasBasePath)(a.pathname)?(0,s.removeBasePath)(a.pathname):a.pathname}},[m]);(0,g.useEffect)(()=>{function a(a){var b;a.persisted&&(null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE)&&(C.pendingMpaPath=void 0,(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(window.location.href),tree:window.history.state.__PRIVATE_NEXTJS_INTERNALS_TREE}))}return window.addEventListener("pageshow",a),()=>{window.removeEventListener("pageshow",a)}},[]),(0,g.useEffect)(()=>{function a(a){let b="reason"in a?a.reason:a.error;if((0,y.isRedirectError)(b)){a.preventDefault();let c=(0,x.getURLFromRedirectError)(b);(0,x.getRedirectTypeFromError)(b)===y.RedirectType.push?w.publicAppRouterInstance.push(c,{}):w.publicAppRouterInstance.replace(c,{})}}return window.addEventListener("error",a),window.addEventListener("unhandledrejection",a),()=>{window.removeEventListener("error",a),window.removeEventListener("unhandledrejection",a)}},[]);let{pushRef:A}=j;if(A.mpaNavigation){if(C.pendingMpaPath!==m){let a=window.location;A.pendingPush?a.assign(m):a.replace(m),C.pendingMpaPath=m}throw r.unresolvedThenable}(0,g.useEffect)(()=>{let a=window.history.pushState.bind(window.history),b=window.history.replaceState.bind(window.history),c=a=>{var b;let c=window.location.href,d=null==(b=window.history.state)?void 0:b.__PRIVATE_NEXTJS_INTERNALS_TREE;(0,g.startTransition)(()=>{(0,l.dispatchAppRouterAction)({type:i.ACTION_RESTORE,url:new URL(null!=a?a:c,c),tree:d})})};window.history.pushState=function(b,d,e){return(null==b?void 0:b.__NA)||(null==b?void 0:b._N)||(b=H(b),e&&c(e)),a(b,d,e)},window.history.replaceState=function(a,d,e){return(null==a?void 0:a.__NA)||(null==a?void 0:a._N)||(a=H(a),e&&c(e)),b(a,d,e)};let d=a=>{if(a.state){if(!a.state.__NA)return void window.location.reload();(0,g.startTransition)(()=>{(0,w.dispatchTraverseAction)(window.location.href,a.state.__PRIVATE_NEXTJS_INTERNALS_TREE)})}};return window.addEventListener("popstate",d),()=>{window.history.pushState=a,window.history.replaceState=b,window.removeEventListener("popstate",d)}},[]);let{cache:D,tree:E,nextUrl:G,focusAndScrollRef:J}=j,K=(0,g.useMemo)(()=>(0,q.findHeadInCache)(D,E[1]),[D,E]),L=(0,g.useMemo)(()=>(0,u.getSelectedParams)(E),[E]),M=(0,g.useMemo)(()=>({parentTree:E,parentCacheNode:D,parentSegmentPath:null,url:m}),[E,D,m]),O=(0,g.useMemo)(()=>({tree:E,focusAndScrollRef:J,nextUrl:G}),[E,J,G]);if(null!==K){let[a,c,d]=K;b=(0,f.jsx)(I,{headCacheNode:a},d)}else b=null;let P=(0,f.jsxs)(p.RedirectBoundary,{children:[b,(0,f.jsx)(B.RootLayoutBoundary,{children:D.rsc}),(0,f.jsx)(o.AppRouterAnnouncer,{tree:E})]});return P=(0,f.jsx)(z.default,{errorComponent:e[0],errorStyles:e[1],children:P}),(0,f.jsxs)(f.Fragment,{children:[(0,f.jsx)(F,{appRouterState:j}),(0,f.jsx)(N,{}),(0,f.jsx)(k.PathParamsContext.Provider,{value:L,children:(0,f.jsx)(k.PathnameContext.Provider,{value:v,children:(0,f.jsx)(k.SearchParamsContext.Provider,{value:n,children:(0,f.jsx)(h.GlobalLayoutRouterContext.Provider,{value:O,children:(0,f.jsx)(h.AppRouterContext.Provider,{value:w.publicAppRouterInstance,children:(0,f.jsx)(h.LayoutRouterContext.Provider,{value:M,children:P})})})})})})]})}function K(a){let{actionQueue:b,globalErrorState:c,assetPrefix:d}=a;(0,v.useNavFailureHandler)();let e=(0,f.jsx)(J,{actionQueue:b,assetPrefix:d,globalError:c});return(0,f.jsx)(z.default,{errorComponent:A.default,children:e})}let L=new Set,M=new Set;function N(){let[,a]=g.default.useState(0),b=L.size;return(0,g.useEffect)(()=>{let c=()=>a(a=>a+1);return M.add(c),b!==L.size&&c(),()=>{M.delete(c)}},[b,a]),[...L].map((a,b)=>(0,f.jsx)("link",{rel:"stylesheet",href:""+a,precedence:"next"},b))}globalThis._N_E_STYLE_LOAD=function(a){let b=L.size;return L.add(a),L.size!==b&&M.forEach(a=>a()),Promise.resolve()},("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},93316,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{abortTask:function(){return o},listenForDynamicRequest:function(){return n},startPPRNavigation:function(){return j},updateCacheNodeOnPopstateRestoration:function(){return function a(b,c){let d=c[1],e=b.parallelRoutes,g=new Map(e);for(let b in d){let c=d[b],h=c[0],i=(0,f.createRouterCacheKey)(h),j=e.get(b);if(void 0!==j){let d=j.get(i);if(void 0!==d){let e=a(d,c),f=new Map(j);f.set(i,e),g.set(b,f)}}}let h=b.rsc,i=r(h)&&"pending"===h.status;return{lazyData:null,rsc:h,head:b.head,prefetchHead:i?b.prefetchHead:[null,null],prefetchRsc:i?b.prefetchRsc:null,loading:b.loading,parallelRoutes:g,navigatedAt:b.navigatedAt}}}});let d=a.r(23741),e=a.r(95784),f=a.r(92822),g=a.r(49215),h=a.r(44078),i={route:null,node:null,dynamicRequestTree:null,children:null};function j(a,b,c,g,h,j,m,n,o){return function a(b,c,g,h,j,m,n,o,p,q,r){let s=g[1],t=h[1],u=null!==m?m[2]:null;j||!0===h[4]&&(j=!0);let v=c.parallelRoutes,w=new Map(v),x={},y=null,z=!1,A={};for(let c in t){let g,h=t[c],l=s[c],m=v.get(c),B=null!==u?u[c]:null,C=h[0],D=q.concat([c,C]),E=(0,f.createRouterCacheKey)(C),F=void 0!==l?l[0]:void 0,G=void 0!==m?m.get(E):void 0;if(null!==(g=C===d.DEFAULT_SEGMENT_KEY?void 0!==l?{route:l,node:null,dynamicRequestTree:null,children:null}:k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):p&&0===Object.keys(h[1]).length?k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r):void 0!==l&&void 0!==F&&(0,e.matchSegment)(C,F)&&void 0!==G&&void 0!==l?a(b,G,l,h,j,B,n,o,p,D,r):k(b,l,h,G,j,void 0!==B?B:null,n,o,D,r))){if(null===g.route)return i;null===y&&(y=new Map),y.set(c,g);let a=g.node;if(null!==a){let b=new Map(m);b.set(E,a),w.set(c,b)}let b=g.route;x[c]=b;let d=g.dynamicRequestTree;null!==d?(z=!0,A[c]=d):A[c]=b}else x[c]=h,A[c]=h}if(null===y)return null;let B={lazyData:null,rsc:c.rsc,prefetchRsc:c.prefetchRsc,head:c.head,prefetchHead:c.prefetchHead,loading:c.loading,parallelRoutes:w,navigatedAt:b};return{route:l(h,x),node:B,dynamicRequestTree:z?l(h,A):null,children:y}}(a,b,c,g,!1,h,j,m,n,[],o)}function k(a,b,c,d,e,j,k,n,o,p){return!e&&(void 0===b||(0,g.isNavigatingToNewRootLayout)(b,c))?i:function a(b,c,d,e,g,i,j,k){let n,o,p,q,r=c[1],s=0===Object.keys(r).length;if(void 0!==d&&d.navigatedAt+h.DYNAMIC_STALETIME_MS>b)n=d.rsc,o=d.loading,p=d.head,q=d.navigatedAt;else if(null===e)return m(b,c,null,g,i,j,k);else if(n=e[1],o=e[3],p=s?g:null,q=b,e[4]||i&&s)return m(b,c,e,g,i,j,k);let t=null!==e?e[2]:null,u=new Map,v=void 0!==d?d.parallelRoutes:null,w=new Map(v),x={},y=!1;if(s)k.push(j);else for(let c in r){let d=r[c],e=null!==t?t[c]:null,h=null!==v?v.get(c):void 0,l=d[0],m=j.concat([c,l]),n=(0,f.createRouterCacheKey)(l),o=a(b,d,void 0!==h?h.get(n):void 0,e,g,i,m,k);u.set(c,o);let p=o.dynamicRequestTree;null!==p?(y=!0,x[c]=p):x[c]=d;let q=o.node;if(null!==q){let a=new Map;a.set(n,q),w.set(c,a)}}return{route:c,node:{lazyData:null,rsc:n,prefetchRsc:null,head:p,prefetchHead:null,loading:o,parallelRoutes:w,navigatedAt:q},dynamicRequestTree:y?l(c,x):null,children:u}}(a,c,d,j,k,n,o,p)}function l(a,b){let c=[a[0],b];return 2 in a&&(c[2]=a[2]),3 in a&&(c[3]=a[3]),4 in a&&(c[4]=a[4]),c}function m(a,b,c,d,e,g,h){let i=l(b,b[1]);return i[3]="refetch",{route:b,node:function a(b,c,d,e,g,h,i){let j=c[1],k=null!==d?d[2]:null,l=new Map;for(let c in j){let d=j[c],m=null!==k?k[c]:null,n=d[0],o=h.concat([c,n]),p=(0,f.createRouterCacheKey)(n),q=a(b,d,void 0===m?null:m,e,g,o,i),r=new Map;r.set(p,q),l.set(c,r)}let m=0===l.size;m&&i.push(h);let n=null!==d?d[1]:null,o=null!==d?d[3]:null;return{lazyData:null,parallelRoutes:l,prefetchRsc:void 0!==n?n:null,prefetchHead:m?e:[null,null],loading:void 0!==o?o:null,rsc:s(),head:m?s():null,navigatedAt:b}}(a,b,c,d,e,g,h),dynamicRequestTree:i,children:null}}function n(a,b){b.then(b=>{let{flightData:c}=b;if("string"!=typeof c){for(let b of c){let{segmentPath:c,tree:d,seedData:g,head:h}=b;g&&function(a,b,c,d,g){let h=a;for(let a=0;a<b.length;a+=2){let c=b[a],d=b[a+1],f=h.children;if(null!==f){let a=f.get(c);if(void 0!==a){let b=a.route[0];if((0,e.matchSegment)(d,b)){h=a;continue}}}return}!function a(b,c,d,g){if(null===b.dynamicRequestTree)return;let h=b.children,i=b.node;if(null===h){null!==i&&(function a(b,c,d,g,h){let i=c[1],j=d[1],k=g[2],l=b.parallelRoutes;for(let b in i){let c=i[b],d=j[b],g=k[b],m=l.get(b),n=c[0],o=(0,f.createRouterCacheKey)(n),q=void 0!==m?m.get(o):void 0;void 0!==q&&(void 0!==d&&(0,e.matchSegment)(n,d[0])&&null!=g?a(q,c,d,g,h):p(c,q,null))}let m=b.rsc,n=g[1];null===m?b.rsc=n:r(m)&&m.resolve(n);let o=b.head;r(o)&&o.resolve(h)}(i,b.route,c,d,g),b.dynamicRequestTree=null);return}let j=c[1],k=d[2];for(let b in c){let c=j[b],d=k[b],f=h.get(b);if(void 0!==f){let b=f.route[0];if((0,e.matchSegment)(c[0],b)&&null!=d)return a(f,c,d,g)}}}(h,c,d,g)}(a,c,d,g,h)}o(a,null)}},b=>{o(a,b)})}function o(a,b){let c=a.node;if(null===c)return;let d=a.children;if(null===d)p(a.route,c,b);else for(let a of d.values())o(a,b);a.dynamicRequestTree=null}function p(a,b,c){let d=a[1],e=b.parallelRoutes;for(let a in d){let b=d[a],g=e.get(a);if(void 0===g)continue;let h=b[0],i=(0,f.createRouterCacheKey)(h),j=g.get(i);void 0!==j&&p(b,j,c)}let g=b.rsc;r(g)&&(null===c?g.resolve(null):g.reject(c));let h=b.head;r(h)&&h.resolve(null)}let q=Symbol();function r(a){return a&&a.tag===q}function s(){let a,b,c=new Promise((c,d)=>{a=c,b=d});return c.status="pending",c.resolve=b=>{"pending"===c.status&&(c.status="fulfilled",c.value=b,a(b))},c.reject=a=>{"pending"===c.status&&(c.status="rejected",c.reason=a,b(a))},c.tag=q,c}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},55203,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"clearCacheNodeDataForSegmentPath",{enumerable:!0,get:function(){return function a(b,c,f){let g=f.length<=2,[h,i]=f,j=(0,e.createRouterCacheKey)(i),k=c.parallelRoutes.get(h),l=b.parallelRoutes.get(h);l&&l!==k||(l=new Map(k),b.parallelRoutes.set(h,l));let m=null==k?void 0:k.get(j),n=l.get(j);if(g){n&&n.lazyData&&n!==m||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}if(!n||!m){n||l.set(j,{lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1});return}return n===m&&(n={lazyData:n.lazyData,rsc:n.rsc,prefetchRsc:n.prefetchRsc,head:n.head,prefetchHead:n.prefetchHead,parallelRoutes:new Map(n.parallelRoutes),loading:n.loading},l.set(j,n)),a(n,m,(0,d.getNextFlightSegmentPath)(f))}}});let d=a.r(33865),e=a.r(92822);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},58307,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{addSearchParamsToPageSegments:function(){return m},handleAliasedPrefetchEntry:function(){return l}});let d=a.r(23741),e=a.r(70988),f=a.r(14057),g=a.r(831),h=a.r(92822),i=a.r(45137),j=a.r(51222),k=a.r(69138);function l(a,b,c,l,n){let o,p=b.tree,q=b.cache,r=(0,g.createHrefFromUrl)(l),s=[];if("string"==typeof c)return!1;for(let b of c){if(!function a(b){if(!b)return!1;let c=b[2];if(b[3])return!0;for(let b in c)if(a(c[b]))return!0;return!1}(b.seedData))continue;let c=b.tree;c=m(c,Object.fromEntries(l.searchParams));let{seedData:g,isRootRender:j,pathToSegment:n}=b,t=["",...n];c=m(c,Object.fromEntries(l.searchParams));let u=(0,f.applyRouterStatePatchToTree)(t,p,c,r),v=(0,e.createEmptyCacheNode)();if(j&&g){let b=g[1];v.loading=g[3],v.rsc=b,function a(b,c,e,f,g){if(0!==Object.keys(f[1]).length)for(let i in f[1]){let j,k=f[1][i],l=k[0],m=(0,h.createRouterCacheKey)(l),n=null!==g&&void 0!==g[2][i]?g[2][i]:null;if(null!==n){let a=n[1],c=n[3];j={lazyData:null,rsc:l.includes(d.PAGE_SEGMENT_KEY)?null:a,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:c,navigatedAt:b}}else j={lazyData:null,rsc:null,prefetchRsc:null,head:null,prefetchHead:null,parallelRoutes:new Map,loading:null,navigatedAt:-1};let o=c.parallelRoutes.get(i);o?o.set(m,j):c.parallelRoutes.set(i,new Map([[m,j]])),a(b,j,e,k,n)}}(a,v,q,c,g)}else v.rsc=q.rsc,v.prefetchRsc=q.prefetchRsc,v.loading=q.loading,v.parallelRoutes=new Map(q.parallelRoutes),(0,i.fillCacheWithNewSubTreeDataButOnlyLoading)(a,v,q,b);for(let a of(u&&(p=u,q=v,o=!0),(0,k.generateSegmentsFromPatch)(c))){let c=[...b.pathToSegment,...a];c[c.length-1]!==d.DEFAULT_SEGMENT_KEY&&s.push(c)}}return!!o&&(n.patchedTree=p,n.cache=q,n.canonicalUrl=r,n.hashFragment=l.hash,n.scrollableSegments=s,(0,j.handleMutable)(b,n))}function m(a,b){let[c,e,...f]=a;if(c.includes(d.PAGE_SEGMENT_KEY))return[(0,d.addSearchParamsIfPageSegment)(c,b),e,...f];let g={};for(let[a,c]of Object.entries(e))g[a]=m(c,b);return[c,g,...f]}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},45370,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{FetchStrategy:function(){return p},NavigationResultTag:function(){return n},PrefetchPriority:function(){return o},cancelPrefetchTask:function(){return j},createCacheKey:function(){return m},getCurrentCacheVersion:function(){return h},isPrefetchTaskDirty:function(){return l},navigate:function(){return f},prefetch:function(){return e},reschedulePrefetchTask:function(){return k},revalidateEntireCache:function(){return g},schedulePrefetchTask:function(){return i}});let d=()=>{throw Object.defineProperty(Error("Segment Cache experiment is not enabled. This is a bug in Next.js."),"__NEXT_ERROR_CODE",{value:"E654",enumerable:!1,configurable:!0})},e=d,f=d,g=d,h=d,i=d,j=d,k=d,l=d,m=d;var n=function(a){return a[a.MPA=0]="MPA",a[a.Success=1]="Success",a[a.NoOp=2]="NoOp",a[a.Async=3]="Async",a}({}),o=function(a){return a[a.Intent=2]="Intent",a[a.Default=1]="Default",a[a.Background=0]="Background",a}({}),p=function(a){return a[a.LoadingBoundary=0]="LoadingBoundary",a[a.PPR=1]="PPR",a[a.PPRRuntime=2]="PPRRuntime",a[a.Full=3]="Full",a}({});("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},69138,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{generateSegmentsFromPatch:function(){return u},handleExternalUrl:function(){return t},navigateReducer:function(){return function a(b,c){let{url:v,isExternalUrl:w,navigateType:x,shouldScroll:y,allowAliasing:z}=c,A={},{hash:B}=v,C=(0,e.createHrefFromUrl)(v),D="push"===x;if((0,q.prunePrefetchCache)(b.prefetchCache),A.preserveCustomHistoryState=!1,A.pendingPush=D,w)return t(b,A,v.toString(),D);if(document.getElementById("__next-page-redirect"))return t(b,A,C,D);let E=(0,q.getOrCreatePrefetchCacheEntry)({url:v,nextUrl:b.nextUrl,tree:b.tree,prefetchCache:b.prefetchCache,allowAliasing:z}),{treeAtTimeOfPrefetch:F,data:G}=E;return m.prefetchQueue.bump(G),G.then(m=>{let{flightData:q,canonicalUrl:w,postponed:x}=m,z=Date.now(),G=!1;if(E.lastUsedTime||(E.lastUsedTime=z,G=!0),E.aliased){let d=new URL(v.href);w&&(d.pathname=w.pathname);let e=(0,s.handleAliasedPrefetchEntry)(z,b,q,d,A);return!1===e?a(b,{...c,allowAliasing:!1}):e}if("string"==typeof q)return t(b,A,q,D);let H=w?(0,e.createHrefFromUrl)(w):C;if(B&&b.canonicalUrl.split("#",1)[0]===H.split("#",1)[0])return A.onlyHashChange=!0,A.canonicalUrl=H,A.shouldScroll=y,A.hashFragment=B,A.scrollableSegments=[],(0,k.handleMutable)(b,A);let I=b.tree,J=b.cache,K=[];for(let a of q){let{pathToSegment:c,seedData:e,head:k,isHeadPartial:m,isRootRender:q}=a,s=a.tree,w=["",...c],y=(0,g.applyRouterStatePatchToTree)(w,I,s,C);if(null===y&&(y=(0,g.applyRouterStatePatchToTree)(w,F,s,C)),null!==y){if(e&&q&&x){let a=(0,p.startPPRNavigation)(z,J,I,s,e,k,m,!1,K);if(null!==a){if(null===a.route)return t(b,A,C,D);y=a.route;let c=a.node;null!==c&&(A.cache=c);let e=a.dynamicRequestTree;if(null!==e){let c=(0,d.fetchServerResponse)(new URL(H,v.origin),{flightRouterState:e,nextUrl:b.nextUrl});(0,p.listenForDynamicRequest)(a,c)}}else y=s}else{if((0,i.isNavigatingToNewRootLayout)(I,y))return t(b,A,C,D);let d=(0,n.createEmptyCacheNode)(),e=!1;for(let b of(E.status!==j.PrefetchCacheEntryStatus.stale||G?e=(0,l.applyFlightData)(z,J,d,a,E):(e=function(a,b,c,d){let e=!1;for(let f of(a.rsc=b.rsc,a.prefetchRsc=b.prefetchRsc,a.loading=b.loading,a.parallelRoutes=new Map(b.parallelRoutes),u(d).map(a=>[...c,...a])))(0,r.clearCacheNodeDataForSegmentPath)(a,b,f),e=!0;return e}(d,J,c,s),E.lastUsedTime=z),(0,h.shouldHardNavigate)(w,I)?(d.rsc=J.rsc,d.prefetchRsc=J.prefetchRsc,(0,f.invalidateCacheBelowFlightSegmentPath)(d,J,c),A.cache=d):e&&(A.cache=d,J=d),u(s))){let a=[...c,...b];a[a.length-1]!==o.DEFAULT_SEGMENT_KEY&&K.push(a)}}I=y}}return A.patchedTree=I,A.canonicalUrl=H,A.scrollableSegments=K,A.hashFragment=B,A.shouldScroll=y,(0,k.handleMutable)(b,A)},()=>b)}}});let d=a.r(84907),e=a.r(831),f=a.r(2559),g=a.r(14057),h=a.r(25144),i=a.r(49215),j=a.r(64048),k=a.r(51222),l=a.r(18609),m=a.r(64383),n=a.r(70988),o=a.r(23741),p=a.r(93316),q=a.r(44078),r=a.r(55203),s=a.r(58307);function t(a,b,c,d){return b.mpaNavigation=!0,b.canonicalUrl=c,b.pendingPush=d,b.scrollableSegments=void 0,(0,k.handleMutable)(a,b)}function u(a){let b=[],[c,d]=a;if(0===Object.keys(d).length)return[[c]];for(let[a,e]of Object.entries(d))for(let d of u(e))""===c?b.push([a,...d]):b.push([c,a,...d]);return b}a.r(45370),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},40047,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverPatchReducer",{enumerable:!0,get:function(){return k}});let d=a.r(831),e=a.r(14057),f=a.r(49215),g=a.r(69138),h=a.r(18609),i=a.r(51222),j=a.r(70988);function k(a,b){let{serverResponse:{flightData:c,canonicalUrl:k},navigatedAt:l}=b,m={};if(m.preserveCustomHistoryState=!1,"string"==typeof c)return(0,g.handleExternalUrl)(a,m,c,a.pushRef.pendingPush);let n=a.tree,o=a.cache;for(let b of c){let{segmentPath:c,tree:i}=b,p=(0,e.applyRouterStatePatchToTree)(["",...c],n,i,a.canonicalUrl);if(null===p)return a;if((0,f.isNavigatingToNewRootLayout)(n,p))return(0,g.handleExternalUrl)(a,m,a.canonicalUrl,a.pushRef.pendingPush);let q=k?(0,d.createHrefFromUrl)(k):void 0;q&&(m.canonicalUrl=q);let r=(0,j.createEmptyCacheNode)();(0,h.applyFlightData)(l,o,r,b),m.patchedTree=p,m.cache=r,o=r,n=p}return(0,i.handleMutable)(a,m)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},13704,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"restoreReducer",{enumerable:!0,get:function(){return f}});let d=a.r(831),e=a.r(1856);function f(a,b){var c;let{url:f,tree:g}=b,h=(0,d.createHrefFromUrl)(f),i=g||a.tree,j=a.cache;return{canonicalUrl:h,pushRef:{pendingPush:!1,mpaNavigation:!1,preserveCustomHistoryState:!0},focusAndScrollRef:a.focusAndScrollRef,cache:j,prefetchCache:a.prefetchCache,tree:i,nextUrl:null!=(c=(0,e.extractPathFromFlightRouterState)(i))?c:f.pathname}}a.r(93316),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},5778,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"handleSegmentMismatch",{enumerable:!0,get:function(){return e}});let d=a.r(69138);function e(a,b,c){return(0,d.handleExternalUrl)(a,{},a.canonicalUrl,!0)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},15728,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hasInterceptionRouteInCurrentTree",{enumerable:!0,get:function(){return function a(b){let[c,e]=b;if(Array.isArray(c)&&("di"===c[2]||"ci"===c[2])||"string"==typeof c&&(0,d.isInterceptionRouteAppPath)(c))return!0;if(e){for(let b in e)if(a(e[b]))return!0}return!1}}});let d=a.r(49063);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},54712,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"refreshReducer",{enumerable:!0,get:function(){return o}});let d=a.r(84907),e=a.r(831),f=a.r(14057),g=a.r(49215),h=a.r(69138),i=a.r(51222),j=a.r(65492),k=a.r(70988),l=a.r(5778),m=a.r(15728),n=a.r(56085);function o(a,b){let{origin:c}=b,o={},p=a.canonicalUrl,q=a.tree;o.preserveCustomHistoryState=!1;let r=(0,k.createEmptyCacheNode)(),s=(0,m.hasInterceptionRouteInCurrentTree)(a.tree);r.lazyData=(0,d.fetchServerResponse)(new URL(p,c),{flightRouterState:[q[0],q[1],q[2],"refetch"],nextUrl:s?a.nextUrl:null});let t=Date.now();return r.lazyData.then(async c=>{let{flightData:d,canonicalUrl:k}=c;if("string"==typeof d)return(0,h.handleExternalUrl)(a,o,d,a.pushRef.pendingPush);for(let c of(r.lazyData=null,d)){let{tree:d,seedData:i,head:m,isRootRender:u}=c;if(!u)return console.log("REFRESH FAILED"),a;let v=(0,f.applyRouterStatePatchToTree)([""],q,d,a.canonicalUrl);if(null===v)return(0,l.handleSegmentMismatch)(a,b,d);if((0,g.isNavigatingToNewRootLayout)(q,v))return(0,h.handleExternalUrl)(a,o,p,a.pushRef.pendingPush);let w=k?(0,e.createHrefFromUrl)(k):void 0;if(k&&(o.canonicalUrl=w),null!==i){let a=i[1],b=i[3];r.rsc=a,r.prefetchRsc=null,r.loading=b,(0,j.fillLazyItemsTillLeafWithHead)(t,r,void 0,d,i,m,void 0),o.prefetchCache=new Map}await (0,n.refreshInactiveParallelSegments)({navigatedAt:t,state:a,updatedTree:v,updatedCache:r,includeNextUrl:s,canonicalUrl:o.canonicalUrl||a.canonicalUrl}),o.cache=r,o.patchedTree=v,q=v}return(0,i.handleMutable)(a,o)},()=>a)}a.r(45370),("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},15221,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"hmrRefreshReducer",{enumerable:!0,get:function(){return d}}),a.r(84907),a.r(831),a.r(14057),a.r(49215),a.r(69138),a.r(51222),a.r(18609),a.r(70988),a.r(5778),a.r(15728);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},78275,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"assignLocation",{enumerable:!0,get:function(){return e}});let d=a.r(41069);function e(a,b){if(a.startsWith(".")){let c=b.origin+b.pathname;return new URL((c.endsWith("/")?c:c+"/")+a)}return new URL((0,d.addBasePath)(a),b.href)}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},19189,(a,b,c)=>{"use strict";function d(a){let b=parseInt(a.slice(0,2),16),c=b>>1&63,d=Array(6);for(let a=0;a<6;a++){let b=c>>5-a&1;d[a]=1===b}return{type:1==(b>>7&1)?"use-cache":"server-action",usedArgs:d,hasRestArgs:1==(1&b)}}function e(a,b){let c=Array(a.length);for(let d=0;d<a.length;d++)(d<6&&b.usedArgs[d]||d>=6&&b.hasRestArgs)&&(c[d]=a[d]);return c}Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{extractInfoFromServerReferenceId:function(){return d},omitUnusedArgs:function(){return e}})},87623,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"serverActionReducer",{enumerable:!0,get:function(){return E}});let d=a.r(62778),e=a.r(70522),f=a.r(28070),g=a.r(79045),h=a.r(41376),i=a.r(64048),j=a.r(78275),k=a.r(831),l=a.r(69138),m=a.r(14057),n=a.r(49215),o=a.r(51222),p=a.r(65492),q=a.r(70988),r=a.r(15728),s=a.r(5778),t=a.r(56085),u=a.r(33865),v=a.r(28548),w=a.r(34816),x=a.r(44078),y=a.r(60155),z=a.r(21981),A=a.r(19189);a.r(45370);let B=h.createFromFetch;async function C(a,b,c){let i,k,l,m,{actionId:n,actionArgs:o}=c,p=(0,h.createTemporaryReferenceSet)(),q=(0,A.extractInfoFromServerReferenceId)(n),r="use-cache"===q.type?(0,A.omitUnusedArgs)(o,q):o,s=await (0,h.encodeReply)(r,{temporaryReferences:p}),t=await fetch(a.canonicalUrl,{method:"POST",headers:{Accept:f.RSC_CONTENT_TYPE_HEADER,[f.ACTION_HEADER]:n,[f.NEXT_ROUTER_STATE_TREE_HEADER]:(0,u.prepareFlightRouterStateForRequest)(a.tree),...{},...b?{[f.NEXT_URL]:b}:{}},body:s});if("1"===t.headers.get(f.NEXT_ACTION_NOT_FOUND_HEADER))throw Object.defineProperty(new g.UnrecognizedActionError('Server Action "'+n+'" was not found on the server. \nRead more: https://nextjs.org/docs/messages/failed-to-find-server-action'),"__NEXT_ERROR_CODE",{value:"E715",enumerable:!1,configurable:!0});let v=t.headers.get("x-action-redirect"),[x,y]=(null==v?void 0:v.split(";"))||[];switch(y){case"push":i=w.RedirectType.push;break;case"replace":i=w.RedirectType.replace;break;default:i=void 0}let z=!!t.headers.get(f.NEXT_IS_PRERENDER_HEADER);try{let a=JSON.parse(t.headers.get("x-action-revalidated")||"[[],0,0]");k={paths:a[0]||[],tag:!!a[1],cookie:a[2]}}catch(a){k=D}let C=x?(0,j.assignLocation)(x,new URL(a.canonicalUrl,window.location.href)):void 0,E=t.headers.get("content-type"),F=!!(E&&E.startsWith(f.RSC_CONTENT_TYPE_HEADER));if(!F&&!C)throw Object.defineProperty(Error(t.status>=400&&"text/plain"===E?await t.text():"An unexpected response was received from the server."),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});if(F){let a=await B(Promise.resolve(t),{callServer:d.callServer,findSourceMapURL:e.findSourceMapURL,temporaryReferences:p});l=C?void 0:a.a,m=(0,u.normalizeFlightData)(a.f)}else l=void 0,m=void 0;return{actionResult:l,actionFlightData:m,redirectLocation:C,redirectType:i,revalidatedParts:k,isPrerender:z}}let D={paths:[],tag:!1,cookie:!1};function E(a,b){let{resolve:c,reject:d}=b,e={},f=a.tree;e.preserveCustomHistoryState=!1;let g=a.nextUrl&&(0,r.hasInterceptionRouteInCurrentTree)(a.tree)?a.nextUrl:null,h=Date.now();return C(a,g,b).then(async j=>{let r,{actionResult:u,actionFlightData:A,redirectLocation:B,redirectType:C,isPrerender:D,revalidatedParts:E}=j;if(B&&(C===w.RedirectType.replace?(a.pushRef.pendingPush=!1,e.pendingPush=!1):(a.pushRef.pendingPush=!0,e.pendingPush=!0),e.canonicalUrl=r=(0,k.createHrefFromUrl)(B,!1)),!A)return(c(u),B)?(0,l.handleExternalUrl)(a,e,B.href,a.pushRef.pendingPush):a;if("string"==typeof A)return c(u),(0,l.handleExternalUrl)(a,e,A,a.pushRef.pendingPush);let F=E.paths.length>0||E.tag||E.cookie;for(let d of A){let{tree:i,seedData:j,head:k,isRootRender:o}=d;if(!o)return console.log("SERVER ACTION APPLY FAILED"),c(u),a;let v=(0,m.applyRouterStatePatchToTree)([""],f,i,r||a.canonicalUrl);if(null===v)return c(u),(0,s.handleSegmentMismatch)(a,b,i);if((0,n.isNavigatingToNewRootLayout)(f,v))return c(u),(0,l.handleExternalUrl)(a,e,r||a.canonicalUrl,a.pushRef.pendingPush);if(null!==j){let b=j[1],c=(0,q.createEmptyCacheNode)();c.rsc=b,c.prefetchRsc=null,c.loading=j[3],(0,p.fillLazyItemsTillLeafWithHead)(h,c,void 0,i,j,k,void 0),e.cache=c,e.prefetchCache=new Map,F&&await (0,t.refreshInactiveParallelSegments)({navigatedAt:h,state:a,updatedTree:v,updatedCache:c,includeNextUrl:!!g,canonicalUrl:e.canonicalUrl||a.canonicalUrl})}e.patchedTree=v,f=v}return B&&r?(F||((0,x.createSeededPrefetchCacheEntry)({url:B,data:{flightData:A,canonicalUrl:void 0,couldBeIntercepted:!1,prerendered:!1,postponed:!1,staleTime:-1},tree:a.tree,prefetchCache:a.prefetchCache,nextUrl:a.nextUrl,kind:D?i.PrefetchKind.FULL:i.PrefetchKind.AUTO}),e.prefetchCache=a.prefetchCache),d((0,v.getRedirectError)((0,z.hasBasePath)(r)?(0,y.removeBasePath)(r):r,C||w.RedirectType.push))):c(u),(0,o.handleMutable)(a,e)},b=>(d(b),a))}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},2558,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"reducer",{enumerable:!0,get:function(){return d}}),a.r(64048),a.r(69138),a.r(40047),a.r(13704),a.r(54712),a.r(64383),a.r(15221),a.r(87623);let d=function(a,b){return a};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},71729,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{createMutableActionQueue:function(){return o},dispatchNavigateAction:function(){return q},dispatchTraverseAction:function(){return r},getCurrentAppRouterState:function(){return p},publicAppRouterInstance:function(){return s}});let d=a.r(64048),e=a.r(2558),f=a.r(54436),g=a.r(30820);a.r(45370);let h=a.r(39732),i=a.r(41069),j=a.r(70988),k=a.r(64383),l=a.r(59504);function m(a,b){null!==a.pending&&(a.pending=a.pending.next,null!==a.pending?n({actionQueue:a,action:a.pending,setState:b}):a.needsRefresh&&(a.needsRefresh=!1,a.dispatch({type:d.ACTION_REFRESH,origin:window.location.origin},b)))}async function n(a){let{actionQueue:b,action:c,setState:d}=a,e=b.state;b.pending=c;let f=c.payload,h=b.action(e,f);function i(a){c.discarded||(b.state=a,m(b,d),c.resolve(a))}(0,g.isThenable)(h)?h.then(i,a=>{m(b,d),c.reject(a)}):i(h)}function o(a,b){let c={state:a,dispatch:(a,b)=>(function(a,b,c){let e={resolve:c,reject:()=>{}};if(b.type!==d.ACTION_RESTORE){let a=new Promise((a,b)=>{e={resolve:a,reject:b}});(0,f.startTransition)(()=>{c(a)})}let g={payload:b,next:null,resolve:e.resolve,reject:e.reject};null===a.pending?(a.last=g,n({actionQueue:a,action:g,setState:c})):b.type===d.ACTION_NAVIGATE||b.type===d.ACTION_RESTORE?(a.pending.discarded=!0,g.next=a.pending.next,a.pending.payload.type===d.ACTION_SERVER_ACTION&&(a.needsRefresh=!0),n({actionQueue:a,action:g,setState:c})):(null!==a.last&&(a.last.next=g),a.last=g)})(c,a,b),action:async(a,b)=>(0,e.reducer)(a,b),pending:null,last:null,onRouterTransitionStart:null!==b&&"function"==typeof b.onRouterTransitionStart?b.onRouterTransitionStart:null};return c}function p(){return null}function q(a,b,c,e){let f=new URL((0,i.addBasePath)(a),location.href);(0,l.setLinkForCurrentNavigation)(e);(0,h.dispatchAppRouterAction)({type:d.ACTION_NAVIGATE,url:f,isExternalUrl:(0,j.isExternalURL)(f),locationSearch:location.search,shouldScroll:c,navigateType:b,allowAliasing:!0})}function r(a,b){(0,h.dispatchAppRouterAction)({type:d.ACTION_RESTORE,url:new URL(a),tree:b})}let s={back:()=>window.history.back(),forward:()=>window.history.forward(),prefetch:(a,b)=>{let c=function(){throw Object.defineProperty(Error("Internal Next.js error: Router action dispatched before initialization."),"__NEXT_ERROR_CODE",{value:"E668",enumerable:!1,configurable:!0})}(),e=(0,j.createPrefetchURL)(a);if(null!==e){var f;(0,k.prefetchReducer)(c.state,{type:d.ACTION_PREFETCH,url:e,kind:null!=(f=null==b?void 0:b.kind)?f:d.PrefetchKind.FULL})}},replace:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"replace",null==(c=null==b?void 0:b.scroll)||c,null)})},push:(a,b)=>{(0,f.startTransition)(()=>{var c;q(a,"push",null==(c=null==b?void 0:b.scroll)||c,null)})},refresh:()=>{(0,f.startTransition)(()=>{(0,h.dispatchAppRouterAction)({type:d.ACTION_REFRESH,origin:window.location.origin})})},hmrRefresh:()=>{throw Object.defineProperty(Error("hmrRefresh can only be used in development mode. Please use refresh instead."),"__NEXT_ERROR_CODE",{value:"E485",enumerable:!1,configurable:!0})}};("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},59504,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{IDLE_LINK_STATUS:function(){return i},PENDING_LINK_STATUS:function(){return h},mountFormInstance:function(){return r},mountLinkInstance:function(){return q},onLinkVisibilityChanged:function(){return t},onNavigationIntent:function(){return u},pingVisibleLinks:function(){return w},setLinkForCurrentNavigation:function(){return j},unmountLinkForCurrentNavigation:function(){return k},unmountPrefetchableInstance:function(){return s}}),a.r(71729);let d=a.r(70988),e=a.r(45370),f=a.r(54436);a.r(64048),a.r(14156);let g=null,h={pending:!0},i={pending:!1};function j(a){(0,f.startTransition)(()=>{null==g||g.setOptimisticLinkStatus(i),null==a||a.setOptimisticLinkStatus(h),g=a})}function k(a){g===a&&(g=null)}let l="function"==typeof WeakMap?new WeakMap:new Map,m=new Set,n="function"==typeof IntersectionObserver?new IntersectionObserver(function(a){for(let b of a){let a=b.intersectionRatio>0;t(b.target,a)}},{rootMargin:"200px"}):null;function o(a,b){void 0!==l.get(a)&&s(a),l.set(a,b),null!==n&&n.observe(a)}function p(a){try{return(0,d.createPrefetchURL)(a)}catch(b){return("function"==typeof reportError?reportError:console.error)("Cannot prefetch '"+a+"' because it cannot be converted to a URL."),null}}function q(a,b,c,d,e,f){if(e){let e=p(b);if(null!==e){let b={router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:f};return o(a,b),b}}return{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:null,setOptimisticLinkStatus:f}}function r(a,b,c,d){let e=p(b);null!==e&&o(a,{router:c,fetchStrategy:d,isVisible:!1,prefetchTask:null,prefetchHref:e.href,setOptimisticLinkStatus:null})}function s(a){let b=l.get(a);if(void 0!==b){l.delete(a),m.delete(b);let c=b.prefetchTask;null!==c&&(0,e.cancelPrefetchTask)(c)}null!==n&&n.unobserve(a)}function t(a,b){let c=l.get(a);void 0!==c&&(c.isVisible=b,b?m.add(c):m.delete(c),v(c,e.PrefetchPriority.Default))}function u(a,b){let c=l.get(a);void 0!==c&&void 0!==c&&v(c,e.PrefetchPriority.Intent)}function v(a,b){let c=a.prefetchTask;if(!a.isVisible){null!==c&&(0,e.cancelPrefetchTask)(c);return}}function w(a,b){for(let c of m){let d=c.prefetchTask;if(null!==d&&!(0,e.isPrefetchTaskDirty)(d,a,b))continue;null!==d&&(0,e.cancelPrefetchTask)(d);let f=(0,e.createCacheKey)(c.prefetchHref,a);c.prefetchTask=(0,e.schedulePrefetchTask)(f,b,c.fetchStrategy,e.PrefetchPriority.Default,null)}}("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)},30978,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"isLocalURL",{enumerable:!0,get:function(){return f}});let d=a.r(54109),e=a.r(21981);function f(a){if(!(0,d.isAbsoluteUrl)(a))return!0;try{let b=(0,d.getLocationOrigin)(),c=new URL(a,b);return c.origin===b&&(0,e.hasBasePath)(c.pathname)}catch(a){return!1}}},40027,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),Object.defineProperty(c,"errorOnce",{enumerable:!0,get:function(){return d}});let d=a=>{}},46563,(a,b,c)=>{"use strict";Object.defineProperty(c,"__esModule",{value:!0}),!function(a,b){for(var c in b)Object.defineProperty(a,c,{enumerable:!0,get:b[c]})}(c,{default:function(){return q},useLinkStatus:function(){return s}});let d=a.r(84332),e=a.r(28386),f=d._(a.r(54436)),g=a.r(88542),h=a.r(86847),i=a.r(32663),j=a.r(54109),k=a.r(41069);a.r(80108);let l=a.r(59504),m=a.r(30978),n=a.r(71729);a.r(40027);let o=a.r(45370);function p(a){return"string"==typeof a?a:(0,g.formatUrl)(a)}function q(a){var b;let c,d,g,[q,s]=(0,f.useOptimistic)(l.IDLE_LINK_STATUS),t=(0,f.useRef)(null),{href:u,as:v,children:w,prefetch:x=null,passHref:y,replace:z,shallow:A,scroll:B,onClick:C,onMouseEnter:D,onTouchStart:E,legacyBehavior:F=!1,onNavigate:G,ref:H,unstable_dynamicOnHover:I,...J}=a;c=w,F&&("string"==typeof c||"number"==typeof c)&&(c=(0,e.jsx)("a",{children:c}));let K=f.default.useContext(h.AppRouterContext),L=!1!==x,M=!1!==x?null===(b=x)||"auto"===b?o.FetchStrategy.PPR:o.FetchStrategy.Full:o.FetchStrategy.PPR,{href:N,as:O}=f.default.useMemo(()=>{let a=p(u);return{href:a,as:v?p(v):a}},[u,v]);F&&(d=f.default.Children.only(c));let P=F?d&&"object"==typeof d&&d.ref:H,Q=f.default.useCallback(a=>(null!==K&&(t.current=(0,l.mountLinkInstance)(a,N,K,M,L,s)),()=>{t.current&&((0,l.unmountLinkForCurrentNavigation)(t.current),t.current=null),(0,l.unmountPrefetchableInstance)(a)}),[L,N,K,M,s]),R={ref:(0,i.useMergedRef)(Q,P),onClick(a){F||"function"!=typeof C||C(a),F&&d.props&&"function"==typeof d.props.onClick&&d.props.onClick(a),K&&(a.defaultPrevented||function(a,b,c,d,e,g,h){let{nodeName:i}=a.currentTarget;if(!("A"===i.toUpperCase()&&function(a){let b=a.currentTarget.getAttribute("target");return b&&"_self"!==b||a.metaKey||a.ctrlKey||a.shiftKey||a.altKey||a.nativeEvent&&2===a.nativeEvent.which}(a)||a.currentTarget.hasAttribute("download"))){if(!(0,m.isLocalURL)(b)){e&&(a.preventDefault(),location.replace(b));return}if(a.preventDefault(),h){let a=!1;if(h({preventDefault:()=>{a=!0}}),a)return}f.default.startTransition(()=>{(0,n.dispatchNavigateAction)(c||b,e?"replace":"push",null==g||g,d.current)})}}(a,N,O,t,z,B,G))},onMouseEnter(a){F||"function"!=typeof D||D(a),F&&d.props&&"function"==typeof d.props.onMouseEnter&&d.props.onMouseEnter(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)},onTouchStart:function(a){F||"function"!=typeof E||E(a),F&&d.props&&"function"==typeof d.props.onTouchStart&&d.props.onTouchStart(a),K&&L&&(0,l.onNavigationIntent)(a.currentTarget,!0===I)}};return(0,j.isAbsoluteUrl)(O)?R.href=O:F&&!y&&("a"!==d.type||"href"in d.props)||(R.href=(0,k.addBasePath)(O)),g=F?f.default.cloneElement(d,R):(0,e.jsx)("a",{...J,...R,children:c}),(0,e.jsx)(r.Provider,{value:q,children:g})}let r=(0,f.createContext)(l.IDLE_LINK_STATUS),s=()=>(0,f.useContext)(r);("function"==typeof c.default||"object"==typeof c.default&&null!==c.default)&&void 0===c.default.__esModule&&(Object.defineProperty(c.default,"__esModule",{value:!0}),Object.assign(c.default,c),b.exports=c.default)}];

//# sourceMappingURL=renderer_4de90480._.js.map