"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/rehype-parse";
exports.ids = ["vendor-chunks/rehype-parse"];
exports.modules = {

/***/ "(ssr)/./node_modules/rehype-parse/lib/index.js":
/*!************************************************!*\
  !*** ./node_modules/rehype-parse/lib/index.js ***!
  \************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ rehypeParse)\n/* harmony export */ });\n/* harmony import */ var hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! hast-util-from-html */ \"(ssr)/./node_modules/hast-util-from-html/lib/index.js\");\n/**\n * @import {Root} from 'hast'\n * @import {Options as FromHtmlOptions} from 'hast-util-from-html'\n * @import {Parser, Processor} from 'unified'\n */\n\n/**\n * @typedef {Omit<FromHtmlOptions, 'onerror'> & RehypeParseFields} Options\n *   Configuration.\n *\n * @typedef RehypeParseFields\n *   Extra fields.\n * @property {boolean | null | undefined} [emitParseErrors=false]\n *   Whether to emit parse errors while parsing (default: `false`).\n *\n *   > 👉 **Note**: parse errors are currently being added to HTML.\n *   > Not all errors emitted by parse5 (or us) are specced yet.\n *   > Some documentation may still be missing.\n */\n\n\n\n/**\n * Plugin to add support for parsing from HTML.\n *\n * > 👉 **Note**: this is not an XML parser.\n * > It supports SVG as embedded in HTML.\n * > It does not support the features available in XML.\n * > Passing SVG files might break but fragments of modern SVG should be fine.\n * > Use [`xast-util-from-xml`][xast-util-from-xml] to parse XML.\n *\n * @param {Options | null | undefined} [options]\n *   Configuration (optional).\n * @returns {undefined}\n *   Nothing.\n */\nfunction rehypeParse(options) {\n  /** @type {Processor<Root>} */\n  // @ts-expect-error: TS in JSDoc generates wrong types if `this` is typed regularly.\n  const self = this\n  const {emitParseErrors, ...settings} = {...self.data('settings'), ...options}\n\n  self.parser = parser\n\n  /**\n   * @type {Parser<Root>}\n   */\n  function parser(document, file) {\n    return (0,hast_util_from_html__WEBPACK_IMPORTED_MODULE_0__.fromHtml)(document, {\n      ...settings,\n      onerror: emitParseErrors\n        ? function (message) {\n            if (file.path) {\n              message.name = file.path + ':' + message.name\n              message.file = file.path\n            }\n\n            file.messages.push(message)\n          }\n        : undefined\n    })\n  }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/rehype-parse/lib/index.js\n");

/***/ })

};
;