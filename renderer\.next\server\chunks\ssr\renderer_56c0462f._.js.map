{"version": 3, "sources": ["turbopack:///[project]/renderer/src/components/ui/alert-dialog.tsx", "turbopack:///[project]/renderer/node_modules/@radix-ui/react-alert-dialog/dist/index.mjs", "turbopack:///[project]/renderer/node_modules/@radix-ui/number/src/number.ts", "turbopack:///[project]/renderer/node_modules/@radix-ui/react-direction/src/direction.tsx", "turbopack:///[project]/renderer/node_modules/lucide-react/src/icons/check.ts", "turbopack:///[project]/renderer/node_modules/@radix-ui/react-roving-focus/src/roving-focus-group.tsx", "turbopack:///[project]/renderer/src/lib/ai-service.ts", "turbopack:///[project]/renderer/node_modules/lucide-react/src/icons/loader-circle.ts"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AlertDialogPrimitive from \"@radix-ui/react-alert-dialog\"\n\nimport { cn } from \"@/lib/utils\"\nimport { buttonVariants } from \"@/components/ui/button\"\n\nconst AlertDialog = AlertDialogPrimitive.Root\n\nconst AlertDialogTrigger = AlertDialogPrimitive.Trigger\n\nconst AlertDialogPortal = AlertDialogPrimitive.Portal\n\nconst AlertDialogOverlay = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Overlay>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Overlay>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Overlay\n    className={cn(\n      \"fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0\",\n      className\n    )}\n    {...props}\n    ref={ref}\n  />\n))\nAlertDialogOverlay.displayName = AlertDialogPrimitive.Overlay.displayName\n\nconst AlertDialogContent = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Content>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Content>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPortal>\n    <AlertDialogOverlay />\n    <AlertDialogPrimitive.Content\n      ref={ref}\n      className={cn(\n        \"fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg\",\n        className\n      )}\n      {...props}\n    />\n  </AlertDialogPortal>\n))\nAlertDialogContent.displayName = AlertDialogPrimitive.Content.displayName\n\nconst AlertDialogHeader = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col space-y-2 text-center sm:text-left\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogHeader.displayName = \"AlertDialogHeader\"\n\nconst AlertDialogFooter = ({\n  className,\n  ...props\n}: React.HTMLAttributes<HTMLDivElement>) => (\n  <div\n    className={cn(\n      \"flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2\",\n      className\n    )}\n    {...props}\n  />\n)\nAlertDialogFooter.displayName = \"AlertDialogFooter\"\n\nconst AlertDialogTitle = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Title>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Title>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Title\n    ref={ref}\n    className={cn(\"text-lg font-semibold\", className)}\n    {...props}\n  />\n))\nAlertDialogTitle.displayName = AlertDialogPrimitive.Title.displayName\n\nconst AlertDialogDescription = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Description>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Description>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Description\n    ref={ref}\n    className={cn(\"text-sm text-muted-foreground\", className)}\n    {...props}\n  />\n))\nAlertDialogDescription.displayName =\n  AlertDialogPrimitive.Description.displayName\n\nconst AlertDialogAction = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Action>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Action>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Action\n    ref={ref}\n    className={cn(buttonVariants(), className)}\n    {...props}\n  />\n))\nAlertDialogAction.displayName = AlertDialogPrimitive.Action.displayName\n\nconst AlertDialogCancel = React.forwardRef<\n  React.ElementRef<typeof AlertDialogPrimitive.Cancel>,\n  React.ComponentPropsWithoutRef<typeof AlertDialogPrimitive.Cancel>\n>(({ className, ...props }, ref) => (\n  <AlertDialogPrimitive.Cancel\n    ref={ref}\n    className={cn(\n      buttonVariants({ variant: \"outline\" }),\n      \"mt-2 sm:mt-0\",\n      className\n    )}\n    {...props}\n  />\n))\nAlertDialogCancel.displayName = AlertDialogPrimitive.Cancel.displayName\n\nexport {\n  AlertDialog,\n  AlertDialogPortal,\n  AlertDialogOverlay,\n  AlertDialogTrigger,\n  AlertDialogContent,\n  AlertDialogHeader,\n  AlertDialogFooter,\n  AlertDialogTitle,\n  AlertDialogDescription,\n  AlertDialogAction,\n  AlertDialogCancel,\n}\n", "\"use client\";\n\n// src/alert-dialog.tsx\nimport * as React from \"react\";\nimport { createContextScope } from \"@radix-ui/react-context\";\nimport { useComposedRefs } from \"@radix-ui/react-compose-refs\";\nimport * as DialogPrimitive from \"@radix-ui/react-dialog\";\nimport { createDialogScope } from \"@radix-ui/react-dialog\";\nimport { composeEventHandlers } from \"@radix-ui/primitive\";\nimport { createSlottable } from \"@radix-ui/react-slot\";\nimport { jsx, jsxs } from \"react/jsx-runtime\";\nvar ROOT_NAME = \"AlertDialog\";\nvar [createAlertDialogContext, createAlertDialogScope] = createContextScope(ROOT_NAME, [\n  createDialogScope\n]);\nvar useDialogScope = createDialogScope();\nvar AlertDialog = (props) => {\n  const { __scopeAlertDialog, ...alertDialogProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Root, { ...dialogScope, ...alertDialogProps, modal: true });\n};\nAlertDialog.displayName = ROOT_NAME;\nvar TRIGGER_NAME = \"AlertDialogTrigger\";\nvar AlertDialogTrigger = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...triggerProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Trigger, { ...dialogScope, ...triggerProps, ref: forwardedRef });\n  }\n);\nAlertDialogTrigger.displayName = TRIGGER_NAME;\nvar PORTAL_NAME = \"AlertDialogPortal\";\nvar AlertDialogPortal = (props) => {\n  const { __scopeAlertDialog, ...portalProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Portal, { ...dialogScope, ...portalProps });\n};\nAlertDialogPortal.displayName = PORTAL_NAME;\nvar OVERLAY_NAME = \"AlertDialogOverlay\";\nvar AlertDialogOverlay = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...overlayProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Overlay, { ...dialogScope, ...overlayProps, ref: forwardedRef });\n  }\n);\nAlertDialogOverlay.displayName = OVERLAY_NAME;\nvar CONTENT_NAME = \"AlertDialogContent\";\nvar [AlertDialogContentProvider, useAlertDialogContentContext] = createAlertDialogContext(CONTENT_NAME);\nvar Slottable = createSlottable(\"AlertDialogContent\");\nvar AlertDialogContent = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, children, ...contentProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const contentRef = React.useRef(null);\n    const composedRefs = useComposedRefs(forwardedRef, contentRef);\n    const cancelRef = React.useRef(null);\n    return /* @__PURE__ */ jsx(\n      DialogPrimitive.WarningProvider,\n      {\n        contentName: CONTENT_NAME,\n        titleName: TITLE_NAME,\n        docsSlug: \"alert-dialog\",\n        children: /* @__PURE__ */ jsx(AlertDialogContentProvider, { scope: __scopeAlertDialog, cancelRef, children: /* @__PURE__ */ jsxs(\n          DialogPrimitive.Content,\n          {\n            role: \"alertdialog\",\n            ...dialogScope,\n            ...contentProps,\n            ref: composedRefs,\n            onOpenAutoFocus: composeEventHandlers(contentProps.onOpenAutoFocus, (event) => {\n              event.preventDefault();\n              cancelRef.current?.focus({ preventScroll: true });\n            }),\n            onPointerDownOutside: (event) => event.preventDefault(),\n            onInteractOutside: (event) => event.preventDefault(),\n            children: [\n              /* @__PURE__ */ jsx(Slottable, { children }),\n              /* @__PURE__ */ jsx(DescriptionWarning, { contentRef })\n            ]\n          }\n        ) })\n      }\n    );\n  }\n);\nAlertDialogContent.displayName = CONTENT_NAME;\nvar TITLE_NAME = \"AlertDialogTitle\";\nvar AlertDialogTitle = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...titleProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Title, { ...dialogScope, ...titleProps, ref: forwardedRef });\n  }\n);\nAlertDialogTitle.displayName = TITLE_NAME;\nvar DESCRIPTION_NAME = \"AlertDialogDescription\";\nvar AlertDialogDescription = React.forwardRef((props, forwardedRef) => {\n  const { __scopeAlertDialog, ...descriptionProps } = props;\n  const dialogScope = useDialogScope(__scopeAlertDialog);\n  return /* @__PURE__ */ jsx(DialogPrimitive.Description, { ...dialogScope, ...descriptionProps, ref: forwardedRef });\n});\nAlertDialogDescription.displayName = DESCRIPTION_NAME;\nvar ACTION_NAME = \"AlertDialogAction\";\nvar AlertDialogAction = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...actionProps } = props;\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...actionProps, ref: forwardedRef });\n  }\n);\nAlertDialogAction.displayName = ACTION_NAME;\nvar CANCEL_NAME = \"AlertDialogCancel\";\nvar AlertDialogCancel = React.forwardRef(\n  (props, forwardedRef) => {\n    const { __scopeAlertDialog, ...cancelProps } = props;\n    const { cancelRef } = useAlertDialogContentContext(CANCEL_NAME, __scopeAlertDialog);\n    const dialogScope = useDialogScope(__scopeAlertDialog);\n    const ref = useComposedRefs(forwardedRef, cancelRef);\n    return /* @__PURE__ */ jsx(DialogPrimitive.Close, { ...dialogScope, ...cancelProps, ref });\n  }\n);\nAlertDialogCancel.displayName = CANCEL_NAME;\nvar DescriptionWarning = ({ contentRef }) => {\n  const MESSAGE = `\\`${CONTENT_NAME}\\` requires a description for the component to be accessible for screen reader users.\n\nYou can add a description to the \\`${CONTENT_NAME}\\` by passing a \\`${DESCRIPTION_NAME}\\` component as a child, which also benefits sighted users by adding visible context to the dialog.\n\nAlternatively, you can use your own component as a description by assigning it an \\`id\\` and passing the same value to the \\`aria-describedby\\` prop in \\`${CONTENT_NAME}\\`. If the description is confusing or duplicative for sighted users, you can use the \\`@radix-ui/react-visually-hidden\\` primitive as a wrapper around your description component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;\n  React.useEffect(() => {\n    const hasDescription = document.getElementById(\n      contentRef.current?.getAttribute(\"aria-describedby\")\n    );\n    if (!hasDescription) console.warn(MESSAGE);\n  }, [MESSAGE, contentRef]);\n  return null;\n};\nvar Root2 = AlertDialog;\nvar Trigger2 = AlertDialogTrigger;\nvar Portal2 = AlertDialogPortal;\nvar Overlay2 = AlertDialogOverlay;\nvar Content2 = AlertDialogContent;\nvar Action = AlertDialogAction;\nvar Cancel = AlertDialogCancel;\nvar Title2 = AlertDialogTitle;\nvar Description2 = AlertDialogDescription;\nexport {\n  Action,\n  AlertDialog,\n  AlertDialogAction,\n  AlertDialogCancel,\n  AlertDialogContent,\n  AlertDialogDescription,\n  AlertDialogOverlay,\n  AlertDialogPortal,\n  AlertDialogTitle,\n  AlertDialogTrigger,\n  Cancel,\n  Content2 as Content,\n  Description2 as Description,\n  Overlay2 as Overlay,\n  Portal2 as Portal,\n  Root2 as Root,\n  Title2 as Title,\n  Trigger2 as Trigger,\n  createAlertDialogScope\n};\n//# sourceMappingURL=index.mjs.map\n", "function clamp(value: number, [min, max]: [number, number]): number {\n  return Math.min(max, Math.max(min, value));\n}\n\nexport { clamp };\n", "import * as React from 'react';\n\ntype Direction = 'ltr' | 'rtl';\nconst DirectionContext = React.createContext<Direction | undefined>(undefined);\n\n/* -------------------------------------------------------------------------------------------------\n * Direction\n * -----------------------------------------------------------------------------------------------*/\n\ninterface DirectionProviderProps {\n  children?: React.ReactNode;\n  dir: Direction;\n}\nconst DirectionProvider: React.FC<DirectionProviderProps> = (props) => {\n  const { dir, children } = props;\n  return <DirectionContext.Provider value={dir}>{children}</DirectionContext.Provider>;\n};\n\n/* -----------------------------------------------------------------------------------------------*/\n\nfunction useDirection(localDir?: Direction) {\n  const globalDir = React.useContext(DirectionContext);\n  return localDir || globalDir || 'ltr';\n}\n\nconst Provider = DirectionProvider;\n\nexport {\n  useDirection,\n  //\n  Provider,\n  //\n  DirectionProvider,\n};\n", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M20 6 9 17l-5-5', key: '1gmf2c' }]];\n\n/**\n * @component @name Check\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjAgNiA5IDE3bC01LTUiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/check\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst Check = createLucideIcon('check', __iconNode);\n\nexport default Check;\n", "import * as React from 'react';\nimport { composeEventHandlers } from '@radix-ui/primitive';\nimport { createCollection } from '@radix-ui/react-collection';\nimport { useComposedRefs } from '@radix-ui/react-compose-refs';\nimport { createContextScope } from '@radix-ui/react-context';\nimport { useId } from '@radix-ui/react-id';\nimport { Primitive } from '@radix-ui/react-primitive';\nimport { useCallbackRef } from '@radix-ui/react-use-callback-ref';\nimport { useControllableState } from '@radix-ui/react-use-controllable-state';\nimport { useDirection } from '@radix-ui/react-direction';\n\nimport type { Scope } from '@radix-ui/react-context';\n\nconst ENTRY_FOCUS = 'rovingFocusGroup.onEntryFocus';\nconst EVENT_OPTIONS = { bubbles: false, cancelable: true };\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroup\n * -----------------------------------------------------------------------------------------------*/\n\nconst GROUP_NAME = 'RovingFocusGroup';\n\ntype ItemData = { id: string; focusable: boolean; active: boolean };\nconst [Collection, useCollection, createCollectionScope] = createCollection<\n  HTMLSpanElement,\n  ItemData\n>(GROUP_NAME);\n\ntype ScopedProps<P> = P & { __scopeRovingFocusGroup?: Scope };\nconst [createRovingFocusGroupContext, createRovingFocusGroupScope] = createContextScope(\n  GROUP_NAME,\n  [createCollectionScope]\n);\n\ntype Orientation = React.AriaAttributes['aria-orientation'];\ntype Direction = 'ltr' | 'rtl';\n\ninterface RovingFocusGroupOptions {\n  /**\n   * The orientation of the group.\n   * Mainly so arrow navigation is done accordingly (left & right vs. up & down)\n   */\n  orientation?: Orientation;\n  /**\n   * The direction of navigation between items.\n   */\n  dir?: Direction;\n  /**\n   * Whether keyboard navigation should loop around\n   * @defaultValue false\n   */\n  loop?: boolean;\n}\n\ntype RovingContextValue = RovingFocusGroupOptions & {\n  currentTabStopId: string | null;\n  onItemFocus(tabStopId: string): void;\n  onItemShiftTab(): void;\n  onFocusableItemAdd(): void;\n  onFocusableItemRemove(): void;\n};\n\nconst [RovingFocusProvider, useRovingFocusContext] =\n  createRovingFocusGroupContext<RovingContextValue>(GROUP_NAME);\n\ntype RovingFocusGroupElement = RovingFocusGroupImplElement;\ninterface RovingFocusGroupProps extends RovingFocusGroupImplProps {}\n\nconst RovingFocusGroup = React.forwardRef<RovingFocusGroupElement, RovingFocusGroupProps>(\n  (props: ScopedProps<RovingFocusGroupProps>, forwardedRef) => {\n    return (\n      <Collection.Provider scope={props.__scopeRovingFocusGroup}>\n        <Collection.Slot scope={props.__scopeRovingFocusGroup}>\n          <RovingFocusGroupImpl {...props} ref={forwardedRef} />\n        </Collection.Slot>\n      </Collection.Provider>\n    );\n  }\n);\n\nRovingFocusGroup.displayName = GROUP_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\ntype RovingFocusGroupImplElement = React.ComponentRef<typeof Primitive.div>;\ntype PrimitiveDivProps = React.ComponentPropsWithoutRef<typeof Primitive.div>;\ninterface RovingFocusGroupImplProps\n  extends Omit<PrimitiveDivProps, 'dir'>,\n    RovingFocusGroupOptions {\n  currentTabStopId?: string | null;\n  defaultCurrentTabStopId?: string;\n  onCurrentTabStopIdChange?: (tabStopId: string | null) => void;\n  onEntryFocus?: (event: Event) => void;\n  preventScrollOnEntryFocus?: boolean;\n}\n\nconst RovingFocusGroupImpl = React.forwardRef<\n  RovingFocusGroupImplElement,\n  RovingFocusGroupImplProps\n>((props: ScopedProps<RovingFocusGroupImplProps>, forwardedRef) => {\n  const {\n    __scopeRovingFocusGroup,\n    orientation,\n    loop = false,\n    dir,\n    currentTabStopId: currentTabStopIdProp,\n    defaultCurrentTabStopId,\n    onCurrentTabStopIdChange,\n    onEntryFocus,\n    preventScrollOnEntryFocus = false,\n    ...groupProps\n  } = props;\n  const ref = React.useRef<RovingFocusGroupImplElement>(null);\n  const composedRefs = useComposedRefs(forwardedRef, ref);\n  const direction = useDirection(dir);\n  const [currentTabStopId, setCurrentTabStopId] = useControllableState({\n    prop: currentTabStopIdProp,\n    defaultProp: defaultCurrentTabStopId ?? null,\n    onChange: onCurrentTabStopIdChange,\n    caller: GROUP_NAME,\n  });\n  const [isTabbingBackOut, setIsTabbingBackOut] = React.useState(false);\n  const handleEntryFocus = useCallbackRef(onEntryFocus);\n  const getItems = useCollection(__scopeRovingFocusGroup);\n  const isClickFocusRef = React.useRef(false);\n  const [focusableItemsCount, setFocusableItemsCount] = React.useState(0);\n\n  React.useEffect(() => {\n    const node = ref.current;\n    if (node) {\n      node.addEventListener(ENTRY_FOCUS, handleEntryFocus);\n      return () => node.removeEventListener(ENTRY_FOCUS, handleEntryFocus);\n    }\n  }, [handleEntryFocus]);\n\n  return (\n    <RovingFocusProvider\n      scope={__scopeRovingFocusGroup}\n      orientation={orientation}\n      dir={direction}\n      loop={loop}\n      currentTabStopId={currentTabStopId}\n      onItemFocus={React.useCallback(\n        (tabStopId) => setCurrentTabStopId(tabStopId),\n        [setCurrentTabStopId]\n      )}\n      onItemShiftTab={React.useCallback(() => setIsTabbingBackOut(true), [])}\n      onFocusableItemAdd={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount + 1),\n        []\n      )}\n      onFocusableItemRemove={React.useCallback(\n        () => setFocusableItemsCount((prevCount) => prevCount - 1),\n        []\n      )}\n    >\n      <Primitive.div\n        tabIndex={isTabbingBackOut || focusableItemsCount === 0 ? -1 : 0}\n        data-orientation={orientation}\n        {...groupProps}\n        ref={composedRefs}\n        style={{ outline: 'none', ...props.style }}\n        onMouseDown={composeEventHandlers(props.onMouseDown, () => {\n          isClickFocusRef.current = true;\n        })}\n        onFocus={composeEventHandlers(props.onFocus, (event) => {\n          // We normally wouldn't need this check, because we already check\n          // that the focus is on the current target and not bubbling to it.\n          // We do this because Safari doesn't focus buttons when clicked, and\n          // instead, the wrapper will get focused and not through a bubbling event.\n          const isKeyboardFocus = !isClickFocusRef.current;\n\n          if (event.target === event.currentTarget && isKeyboardFocus && !isTabbingBackOut) {\n            const entryFocusEvent = new CustomEvent(ENTRY_FOCUS, EVENT_OPTIONS);\n            event.currentTarget.dispatchEvent(entryFocusEvent);\n\n            if (!entryFocusEvent.defaultPrevented) {\n              const items = getItems().filter((item) => item.focusable);\n              const activeItem = items.find((item) => item.active);\n              const currentItem = items.find((item) => item.id === currentTabStopId);\n              const candidateItems = [activeItem, currentItem, ...items].filter(\n                Boolean\n              ) as typeof items;\n              const candidateNodes = candidateItems.map((item) => item.ref.current!);\n              focusFirst(candidateNodes, preventScrollOnEntryFocus);\n            }\n          }\n\n          isClickFocusRef.current = false;\n        })}\n        onBlur={composeEventHandlers(props.onBlur, () => setIsTabbingBackOut(false))}\n      />\n    </RovingFocusProvider>\n  );\n});\n\n/* -------------------------------------------------------------------------------------------------\n * RovingFocusGroupItem\n * -----------------------------------------------------------------------------------------------*/\n\nconst ITEM_NAME = 'RovingFocusGroupItem';\n\ntype RovingFocusItemElement = React.ComponentRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface RovingFocusItemProps extends Omit<PrimitiveSpanProps, 'children'> {\n  tabStopId?: string;\n  focusable?: boolean;\n  active?: boolean;\n  children?:\n    | React.ReactNode\n    | ((props: { hasTabStop: boolean; isCurrentTabStop: boolean }) => React.ReactNode);\n}\n\nconst RovingFocusGroupItem = React.forwardRef<RovingFocusItemElement, RovingFocusItemProps>(\n  (props: ScopedProps<RovingFocusItemProps>, forwardedRef) => {\n    const {\n      __scopeRovingFocusGroup,\n      focusable = true,\n      active = false,\n      tabStopId,\n      children,\n      ...itemProps\n    } = props;\n    const autoId = useId();\n    const id = tabStopId || autoId;\n    const context = useRovingFocusContext(ITEM_NAME, __scopeRovingFocusGroup);\n    const isCurrentTabStop = context.currentTabStopId === id;\n    const getItems = useCollection(__scopeRovingFocusGroup);\n\n    const { onFocusableItemAdd, onFocusableItemRemove, currentTabStopId } = context;\n\n    React.useEffect(() => {\n      if (focusable) {\n        onFocusableItemAdd();\n        return () => onFocusableItemRemove();\n      }\n    }, [focusable, onFocusableItemAdd, onFocusableItemRemove]);\n\n    return (\n      <Collection.ItemSlot\n        scope={__scopeRovingFocusGroup}\n        id={id}\n        focusable={focusable}\n        active={active}\n      >\n        <Primitive.span\n          tabIndex={isCurrentTabStop ? 0 : -1}\n          data-orientation={context.orientation}\n          {...itemProps}\n          ref={forwardedRef}\n          onMouseDown={composeEventHandlers(props.onMouseDown, (event) => {\n            // We prevent focusing non-focusable items on `mousedown`.\n            // Even though the item has tabIndex={-1}, that only means take it out of the tab order.\n            if (!focusable) event.preventDefault();\n            // Safari doesn't focus a button when clicked so we run our logic on mousedown also\n            else context.onItemFocus(id);\n          })}\n          onFocus={composeEventHandlers(props.onFocus, () => context.onItemFocus(id))}\n          onKeyDown={composeEventHandlers(props.onKeyDown, (event) => {\n            if (event.key === 'Tab' && event.shiftKey) {\n              context.onItemShiftTab();\n              return;\n            }\n\n            if (event.target !== event.currentTarget) return;\n\n            const focusIntent = getFocusIntent(event, context.orientation, context.dir);\n\n            if (focusIntent !== undefined) {\n              if (event.metaKey || event.ctrlKey || event.altKey || event.shiftKey) return;\n              event.preventDefault();\n              const items = getItems().filter((item) => item.focusable);\n              let candidateNodes = items.map((item) => item.ref.current!);\n\n              if (focusIntent === 'last') candidateNodes.reverse();\n              else if (focusIntent === 'prev' || focusIntent === 'next') {\n                if (focusIntent === 'prev') candidateNodes.reverse();\n                const currentIndex = candidateNodes.indexOf(event.currentTarget);\n                candidateNodes = context.loop\n                  ? wrapArray(candidateNodes, currentIndex + 1)\n                  : candidateNodes.slice(currentIndex + 1);\n              }\n\n              /**\n               * Imperative focus during keydown is risky so we prevent React's batching updates\n               * to avoid potential bugs. See: https://github.com/facebook/react/issues/20332\n               */\n              setTimeout(() => focusFirst(candidateNodes));\n            }\n          })}\n        >\n          {typeof children === 'function'\n            ? children({ isCurrentTabStop, hasTabStop: currentTabStopId != null })\n            : children}\n        </Primitive.span>\n      </Collection.ItemSlot>\n    );\n  }\n);\n\nRovingFocusGroupItem.displayName = ITEM_NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\n// prettier-ignore\nconst MAP_KEY_TO_FOCUS_INTENT: Record<string, FocusIntent> = {\n  ArrowLeft: 'prev', ArrowUp: 'prev',\n  ArrowRight: 'next', ArrowDown: 'next',\n  PageUp: 'first', Home: 'first',\n  PageDown: 'last', End: 'last',\n};\n\nfunction getDirectionAwareKey(key: string, dir?: Direction) {\n  if (dir !== 'rtl') return key;\n  return key === 'ArrowLeft' ? 'ArrowRight' : key === 'ArrowRight' ? 'ArrowLeft' : key;\n}\n\ntype FocusIntent = 'first' | 'last' | 'prev' | 'next';\n\nfunction getFocusIntent(event: React.KeyboardEvent, orientation?: Orientation, dir?: Direction) {\n  const key = getDirectionAwareKey(event.key, dir);\n  if (orientation === 'vertical' && ['ArrowLeft', 'ArrowRight'].includes(key)) return undefined;\n  if (orientation === 'horizontal' && ['ArrowUp', 'ArrowDown'].includes(key)) return undefined;\n  return MAP_KEY_TO_FOCUS_INTENT[key];\n}\n\nfunction focusFirst(candidates: HTMLElement[], preventScroll = false) {\n  const PREVIOUSLY_FOCUSED_ELEMENT = document.activeElement;\n  for (const candidate of candidates) {\n    // if focus is already where we want to go, we don't want to keep going through the candidates\n    if (candidate === PREVIOUSLY_FOCUSED_ELEMENT) return;\n    candidate.focus({ preventScroll });\n    if (document.activeElement !== PREVIOUSLY_FOCUSED_ELEMENT) return;\n  }\n}\n\n/**\n * Wraps an array around itself at a given start index\n * Example: `wrapArray(['a', 'b', 'c', 'd'], 2) === ['c', 'd', 'a', 'b']`\n */\nfunction wrapArray<T>(array: T[], startIndex: number) {\n  return array.map<T>((_, index) => array[(startIndex + index) % array.length]!);\n}\n\nconst Root = RovingFocusGroup;\nconst Item = RovingFocusGroupItem;\n\nexport {\n  createRovingFocusGroupScope,\n  //\n  RovingFocusGroup,\n  RovingFocusGroupItem,\n  //\n  Root,\n  Item,\n};\nexport type { RovingFocusGroupProps, RovingFocusItemProps };\n", "// lib/ai-service.ts\r\nimport { toast } from \"@/hooks/use-toast\";\r\n\r\nconst DEFAULT_GEMINI_API_BASE_URL = \"https://rainbow-gumption-2fc85c.netlify.app/\";\r\nconst OPENROUTER_API_BASE_URL = \"https://openrouter.ai/api/v1/chat/completions\";\r\n\r\nfunction getGeminiApiBaseUrl(): string {\r\n  if (typeof window === \"undefined\") return DEFAULT_GEMINI_API_BASE_URL;\r\n  return localStorage.getItem(\"gemini_api_base_url\") || DEFAULT_GEMINI_API_BASE_URL;\r\n}\r\n\r\nfunction getApiKey(service: \"gemini\" | \"openrouter\"): string | null {\r\n  if (typeof window === \"undefined\") return null;\r\n  return localStorage.getItem(`${service}_api_key`);\r\n}\r\n\r\nfunction getAiProvider(): string {\r\n  if (typeof window === \"undefined\") return \"gemini\";\r\n  return localStorage.getItem(\"ai_provider\") || \"gemini\";\r\n}\r\n\r\nfunction getOpenRouterModel(): string {\r\n  if (typeof window === \"undefined\") return \"mistralai/mistral-7b-instruct\";\r\n  return (\r\n    localStorage.getItem(\"openrouter_model\") || \"mistralai/mistral-7b-instruct\"\r\n  );\r\n}\r\n\r\nfunction getGeminiModel(): string {\r\n  if (typeof window === \"undefined\") return \"gemini-2.5-pro\";\r\n  const raw = localStorage.getItem(\"gemini_model\") || \"gemini-2.5-pro\";\r\n  // 自動移除 models/ 前綴\r\n  return raw.replace(/^models\\//, \"\");\r\n}\r\n\r\n// --- Stream Processing Utilities ---\r\n\r\ninterface StreamRequestConfig {\r\n  url: string;\r\n  headers: Record<string, string>;\r\n  body: string;\r\n  onChunk: (chunk: string) => void;\r\n}\r\n\r\nasync function processStreamResponse(config: StreamRequestConfig): Promise<void> {\r\n  const { url, headers, body, onChunk } = config;\r\n\r\n  const response = await fetch(url, {\r\n    method: \"POST\",\r\n    headers,\r\n    body,\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    console.error(\"Stream API Error:\", errorText);\r\n    try {\r\n      const errorData = JSON.parse(errorText);\r\n      const errorMessage = errorData.error?.message || \"串流請求失敗。\";\r\n      toast({\r\n        title: \"串流請求失敗\",\r\n        description: errorMessage,\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(errorMessage);\r\n    } catch (e) {\r\n      const errorMessage = `串流請求失敗: ${errorText}`;\r\n      toast({\r\n        title: \"串流請求失敗\",\r\n        description: errorText,\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(errorMessage);\r\n    }\r\n  }\r\n\r\n  const reader = response.body?.getReader();\r\n  if (!reader) {\r\n    throw new Error(\"無法獲取 response reader\");\r\n  }\r\n\r\n  const decoder = new TextDecoder();\r\n  let buffer = \"\";\r\n\r\n  try {\r\n    while (true) {\r\n      const { done, value } = await reader.read();\r\n      if (done) break;\r\n\r\n      buffer += decoder.decode(value, { stream: true });\r\n      const lines = buffer.split(\"\\n\");\r\n      buffer = lines.pop() || \"\"; // 保留最後一個不完整的行\r\n\r\n      for (const line of lines) {\r\n        if (line.startsWith(\"data: \")) {\r\n          const data = line.slice(6);\r\n          if (data === \"[DONE]\") continue;\r\n\r\n          try {\r\n            const parsed = JSON.parse(data);\r\n            const content = parsed.choices?.[0]?.delta?.content ||\r\n                           parsed.candidates?.[0]?.content?.parts?.[0]?.text;\r\n            if (content) {\r\n              console.log(\"Stream chunk:\", content);\r\n              onChunk(content);\r\n            }\r\n          } catch (e) {\r\n            console.log(\"Stream parse error:\", e, \"data:\", data);\r\n          }\r\n        }\r\n      }\r\n    }\r\n  } finally {\r\n    reader.releaseLock();\r\n  }\r\n}\r\n\r\n// --- Unified AI Functions ---\r\n\r\nexport async function summarize(content: string): Promise<string> {\r\n  const provider = getAiProvider();\r\n  if (provider === \"gemini\") {\r\n    return summarizeWithGemini(content);\r\n  } else {\r\n    return callOpenRouter([\r\n      {\r\n        role: \"system\",\r\n        content: \"Summarize the following text.\",\r\n      },\r\n      { role: \"user\", content },\r\n    ]);\r\n  }\r\n}\r\n\r\nexport async function generateTitle(content: string): Promise<string> {\r\n  const provider = getAiProvider();\r\n  if (provider === \"gemini\") {\r\n    return generateTitleWithGemini(content);\r\n  } else {\r\n    return callOpenRouter([\r\n      {\r\n        role: \"system\",\r\n        content: \"Generate a concise title for the following text.\",\r\n      },\r\n      { role: \"user\", content: content.substring(0, 200) },\r\n    ]);\r\n  }\r\n}\r\n\r\n// --- Gemini Functions ---\r\n\r\nasync function summarizeWithGemini(content: string): Promise<string> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    toast({\r\n      title: \"API 金鑰未設定\",\r\n      description: \"請在設定中設定 Gemini API 金鑰。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n  const url = `${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`;\r\n  console.log(\"[Gemini] API URL:\", url);\r\n  console.log(\"[Gemini] model:\", model);\r\n  console.log(\"[Gemini] apiKey prefix:\", apiKey.slice(0, 6));\r\n\r\n  const response = await fetch(url, {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位專業的內容總結專家，分析並總結以下筆記內容。請遵循以下原則：\r\n\r\n## 總結要求：\r\n1. **重點提取**：識別並保留最重要的資訊、關鍵概念和核心觀點\r\n2. **結構清晰**：使用簡潔的段落結構，確保邏輯連貫\r\n3. **長度適中**：總結長度約為原文的 1/3 到 1/4，重點突出，避免冗餘\r\n4. **客觀準確**：保持原文的原意，不添加個人解釋或外部資訊\r\n5. **實用價值**：確保總結具有實用性，可以幫助讀者快速了解原文要點\r\n\r\n## 總結格式：\r\n- 使用條列式或段落式結構\r\n- 保留重要的數據、日期、名稱等關鍵資訊\r\n- 如果原文有明確的結論或建議，請特別強調\r\n\r\n## 原文內容：\r\n${content}\r\n\r\n請提供一個結構清晰、重點突出且實用的總結：`\r\n        }]\r\n      }]\r\n    }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    console.error(\"Gemini API Error:\", errorText);\r\n    toast({\r\n      title: \"API 請求失敗\",\r\n      description: \"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\");\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.candidates[0].content.parts[0].text;\r\n}\r\n\r\nasync function generateTitleWithGemini(content: string): Promise<string> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    toast({\r\n      title: \"API 金鑰未設定\",\r\n      description: \"請在設定中設定 Gemini API 金鑰。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n  const response = await fetch(`${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`, {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位專業的標題撰寫專家，根據以下內容生成一個吸引人且準確的標題。請遵循以下原則：\r\n\r\n## 標題要求：\r\n1. **簡潔有力**：標題長度控制在 5-15 個中文字，突出重點\r\n2. **吸引注意力**：使用能引起讀者興趣的關鍵詞或表述\r\n3. **準確反映內容**：標題必須準確代表原文的核心主題和主要觀點\r\n4. **SEO 友好**：考慮搜尋引擎優化，使用相關關鍵詞\r\n5. **獨特性**：避免使用常見的通用性標題\r\n\r\n## 標題風格建議：\r\n- 問題式標題：以疑問句形式吸引讀者\r\n- 結果式標題：強調內容帶來的價值或結果\r\n- 數字式標題：如果內容涉及步驟、方法或清單\r\n- 故事式標題：如果內容有敘事性質\r\n\r\n## 內容預覽：\r\n${content.substring(0, 300)}\r\n\r\n請生成 3 個不同的標題選項，並為每個標題簡要說明選擇的原因：`\r\n        }]\r\n      }]\r\n    }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    console.error(\"Gemini API Error:\", errorText);\r\n    toast({\r\n      title: \"API 請求失敗\",\r\n      description: \"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\");\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.candidates[0].content.parts[0].text;\r\n}\r\n\r\n// --- OpenRouter Functions ---\r\n\r\nasync function callOpenRouter(\r\n  messages: { role: string; content: string }[]\r\n): Promise<string> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    toast({\r\n      title: \"API 金鑰未設定\",\r\n      description: \"請在設定中設定 OpenRouter API 金鑰。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  const response = await fetch(OPENROUTER_API_BASE_URL, {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\", // Required by OpenRouter\r\n      \"X-Title\": \"MyNote AI Assistant\", // Recommended by OpenRouter\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages,\r\n    }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    console.error(\"OpenRouter API Error:\", errorText);\r\n    try {\r\n      const errorData = JSON.parse(errorText);\r\n      const errorMessage = errorData.error?.message || \"OpenRouter API 請求失敗。\";\r\n      toast({\r\n        title: \"API 請求失敗\",\r\n        description: errorMessage,\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(errorMessage);\r\n    } catch (e) {\r\n      const errorMessage = `OpenRouter API 請求失敗: ${errorText}`;\r\n      toast({\r\n        title: \"API 請求失敗\",\r\n        description: errorText,\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(errorMessage);\r\n    }\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.choices[0].message.content;\r\n}\r\n\r\nexport async function polishWithOpenRouter(text: string): Promise<string> {\r\n  return callOpenRouter([\r\n    {\r\n      role: \"system\",\r\n      content: `You are a professional text editor and writing consultant. Perform comprehensive polishing and improvement on the following text. Follow these editing principles:\r\n\r\nEDITING FOCUS:\r\n1. **Grammatical Correctness**: Correct all grammar errors, punctuation usage, and sentence structure issues\r\n2. **Expression Clarity**: Simplify complex sentences to make them clearer and easier to understand\r\n3. **Logical Coherence**: Ensure smooth logical flow between paragraphs and natural transitions between ideas\r\n4. **Word Precision**: Use more appropriate and professional vocabulary to replace vague or inaccurate expressions\r\n5. **Style Consistency**: Maintain consistency in overall style\r\n6. **Length Optimization**: Adjust length appropriately while preserving the original meaning, avoiding redundancy\r\n\r\nIMPROVEMENT STRATEGY:\r\n- Convert passive voice to active voice when appropriate\r\n- Merge duplicate or redundant sentences\r\n- Improve paragraph structure and sentence rhythm\r\n- Add necessary connecting words to improve fluency\r\n- Ensure correct use of professional terminology\r\n\r\nOUTPUT REQUIREMENTS:\r\nPlease provide the polished complete text and briefly explain the main improvement points at the end.\r\n\r\nORIGINAL CONTENT:`,\r\n    },\r\n    { role: \"user\", content: text },\r\n  ]);\r\n}\r\n\r\nexport async function expandWithOpenRouter(prompt: string): Promise<string> {\r\n  return callOpenRouter([\r\n    {\r\n      role: \"system\",\r\n      content: `You are a creative writing expert. Based on the following prompt, create a detailed, engaging, and structurally complete text. Follow these creative principles:\r\n\r\nCREATIVE REQUIREMENTS:\r\n1. **Content Richness**: Add relevant details, examples, and explanations while maintaining the core theme\r\n2. **Structural Integrity**: Include introduction, main content, and conclusion sections\r\n3. **Logical Coherence**: Ensure natural transitions between sections with clear thinking\r\n4. **Reader Engagement**: Use vivid language and concrete descriptions\r\n5. **Practical Value**: Provide meaningful information or insights\r\n\r\nCREATIVE STRATEGY:\r\n- **Introduction**: Set the background, spark reader interest, and present the core theme\r\n- **Main Content**: Develop the theme in detail with specific examples, data, or cases\r\n- **Conclusion**: Summarize key points and provide suggestions or inspirational thoughts\r\n- **Appropriate Length**: Expand to 3-5 times the length of the original prompt\r\n\r\nSTYLE SUGGESTIONS:\r\n- Use first-person or second-person narrative to increase intimacy\r\n- Use rhetorical devices such as metaphors and contrasts when appropriate\r\n- Balance professionalism with readability\r\n\r\nBased on the original prompt, create a complete and attractive text:`,\r\n    },\r\n    { role: \"user\", content: prompt },\r\n  ]);\r\n}\r\n\r\nasync function expandWithOpenRouterStream(\r\n  prompt: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  await processStreamResponse({\r\n    url: OPENROUTER_API_BASE_URL,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\",\r\n      \"X-Title\": \"MyNote AI Assistant\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages: [\r\n        {\r\n          role: \"system\",\r\n          content: `You are a creative writing expert. Based on the following prompt, create a detailed, engaging, and structurally complete text. Follow these creative principles:\r\n\r\nCREATIVE REQUIREMENTS:\r\n1. **Content Richness**: Add relevant details, examples, and explanations while maintaining the core theme\r\n2. **Structural Integrity**: Include introduction, main content, and conclusion sections\r\n3. **Logical Coherence**: Ensure natural transitions between sections with clear thinking\r\n4. **Reader Engagement**: Use vivid language and concrete descriptions\r\n5. **Practical Value**: Provide meaningful information or insights\r\n\r\nCREATIVE STRATEGY:\r\n- **Introduction**: Set the background, spark reader interest, and present the core theme\r\n- **Main Content**: Develop the theme in detail with specific examples, data, or cases\r\n- **Conclusion**: Summarize key points and provide suggestions or inspirational thoughts\r\n- **Appropriate Length**: Expand to 3-5 times the length of the original prompt\r\n\r\nSTYLE SUGGESTIONS:\r\n- Use first-person or second-person narrative to increase intimacy\r\n- Use rhetorical devices such as metaphors and contrasts when appropriate\r\n- Balance professionalism with readability\r\n\r\nBased on the original prompt, create a complete and attractive text:`,\r\n        },\r\n        { role: \"user\", content: prompt },\r\n      ],\r\n      stream: true,\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nasync function expandWithGemini(prompt: string): Promise<string> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n  const response = await fetch(`${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`, {\r\n    method: \"POST\",\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請根據以下提示詞創建更詳細的文本：\\n\\n${prompt}`\r\n        }]\r\n      }]\r\n    }),\r\n  });\r\n\r\n  if (!response.ok) {\r\n    const errorText = await response.text();\r\n    console.error(\"Gemini API Error:\", errorText);\r\n    toast({\r\n      title: \"API 請求失敗\",\r\n      description: \"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\",\r\n      variant: \"destructive\",\r\n    });\r\n    throw new Error(\"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\");\r\n  }\r\n\r\n  const data = await response.json();\r\n  return data.candidates[0].content.parts[0].text;\r\n}\r\n\r\nasync function expandWithGeminiStream(\r\n  prompt: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n\r\n  await processStreamResponse({\r\n    url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位創意寫作專家，根據以下提示詞創建一個詳細、引人入勝且結構完整的文本。請遵循以下創作原則：\r\n\r\n## 創作要求：\r\n1. **內容豐富性**：在保持核心主題的前提下，添加相關的細節、例子和說明\r\n2. **結構完整性**：包含引言、主要內容和結論三個部分\r\n3. **邏輯連貫性**：確保各部分之間的過渡自然，思路清晰\r\n4. **吸引讀者**：使用生動的語言和具體的描述\r\n5. **實用價值**：提供有實際意義的資訊或見解\r\n\r\n## 創作策略：\r\n- **引言部分**：設定背景，引起讀者興趣，提出核心主題\r\n- **主要內容**：詳細展開主題，提供具體例子、數據或案例\r\n- **結論部分**：總結要點，提供建議或啟發性思考\r\n- **適當長度**：擴展到原提示詞的 3-5 倍長度\r\n\r\n## 風格建議：\r\n- 使用第一人稱或第二人稱敘述，增加親切感\r\n- 適時使用修辭手法，如比喻、對比等\r\n- 保持語言的專業性和可讀性平衡\r\n\r\n## 原始提示詞：\r\n${prompt}\r\n\r\n請以此為基礎，創建一個完整而吸引人的文本：`\r\n        }]\r\n      }]\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nexport async function expandContent(prompt: string): Promise<string> {\r\n  const provider = getAiProvider();\r\n  if (provider === \"gemini\") {\r\n    return expandWithGemini(prompt);\r\n  } else {\r\n    return expandWithOpenRouter(prompt);\r\n  }\r\n}\r\n\r\nexport async function expandContentStream(\r\n  prompt: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const provider = getAiProvider();\r\n  console.log(\"Starting streaming with provider:\", provider);\r\n  if (provider === \"gemini\") {\r\n    return expandWithGeminiStream(prompt, onChunk);\r\n  } else {\r\n    return expandWithOpenRouterStream(prompt, onChunk);\r\n  }\r\n}\r\n\r\n// --- Streaming Functions for Other AI Tools ---\r\n\r\nasync function summarizeWithOpenRouterStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  await processStreamResponse({\r\n    url: OPENROUTER_API_BASE_URL,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\",\r\n      \"X-Title\": \"MyNote AI Assistant\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages: [\r\n        {\r\n          role: \"system\",\r\n          content: \"Summarize the following text.\",\r\n        },\r\n        { role: \"user\", content },\r\n      ],\r\n      stream: true,\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nasync function summarizeWithGeminiStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n\r\n  await processStreamResponse({\r\n    url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位專業的內容總結專家，分析並總結以下筆記內容。請遵循以下原則：\r\n\r\n## 總結要求：\r\n1. **重點提取**：識別並保留最重要的資訊、關鍵概念和核心觀點\r\n2. **結構清晰**：使用簡潔的段落結構，確保邏輯連貫\r\n3. **長度適中**：總結長度約為原文的 1/3 到 1/4，重點突出，避免冗餘\r\n4. **客觀準確**：保持原文的原意，不添加個人解釋或外部資訊\r\n5. **實用價值**：確保總結具有實用性，可以幫助讀者快速了解原文要點\r\n\r\n## 總結格式：\r\n- 使用條列式或段落式結構\r\n- 保留重要的數據、日期、名稱等關鍵資訊\r\n- 如果原文有明確的結論或建議，請特別強調\r\n\r\n## 原文內容：\r\n${content}\r\n\r\n請提供一個結構清晰、重點突出且實用的總結：`\r\n        }]\r\n      }]\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nexport async function summarizeStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const provider = getAiProvider();\r\n  console.log(\"Starting summarize streaming with provider:\", provider);\r\n  if (provider === \"gemini\") {\r\n    return summarizeWithGeminiStream(content, onChunk);\r\n  } else {\r\n    return summarizeWithOpenRouterStream(content, onChunk);\r\n  }\r\n}\r\n\r\nasync function generateTitleWithOpenRouterStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  await processStreamResponse({\r\n    url: OPENROUTER_API_BASE_URL,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\",\r\n      \"X-Title\": \"MyNote AI Assistant\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages: [\r\n        {\r\n          role: \"system\",\r\n          content: \"Generate a concise title for the following text.\",\r\n        },\r\n        { role: \"user\", content: content.substring(0, 200) },\r\n      ],\r\n      stream: true,\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nasync function generateTitleWithGeminiStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n\r\n  await processStreamResponse({\r\n    url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位專業的標題撰寫專家，根據以下內容生成一個吸引人且準確的標題。請遵循以下原則：\r\n\r\n## 標題要求：\r\n1. **簡潔有力**：標題長度控制在 5-15 個中文字，突出重點\r\n2. **吸引注意力**：使用能引起讀者興趣的關鍵詞或表述\r\n3. **準確反映內容**：標題必須準確代表原文的核心主題和主要觀點\r\n4. **SEO 友好**：考慮搜尋引擎優化，使用相關關鍵詞\r\n5. **獨特性**：避免使用常見的通用性標題\r\n\r\n## 標題風格建議：\r\n- 問題式標題：以疑問句形式吸引讀者\r\n- 結果式標題：強調內容帶來的價值或結果\r\n- 數字式標題：如果內容涉及步驟、方法或清單\r\n- 故事式標題：如果內容有敘事性質\r\n\r\n## 內容預覽：\r\n${content.substring(0, 300)}\r\n\r\n請生成 3 個不同的標題選項，並為每個標題簡要說明選擇的原因：`\r\n        }]\r\n      }]\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nexport async function generateTitleStream(\r\n  content: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const provider = getAiProvider();\r\n  console.log(\"Starting title generation streaming with provider:\", provider);\r\n  if (provider === \"gemini\") {\r\n    return generateTitleWithGeminiStream(content, onChunk);\r\n  } else {\r\n    return generateTitleWithOpenRouterStream(content, onChunk);\r\n  }\r\n}\r\n\r\nexport async function changeToneWithOpenRouter(\r\n  text: string,\r\n  tone: \"正式\" | \"休閒\" | \"專業\" | \"幽默\"\r\n): Promise<string> {\r\n  let toneInstruction = \"\";\r\n  switch (tone) {\r\n    case \"正式\":\r\n      toneInstruction = \"You are an expert in formal writing. Rewrite the following text in a formal, structured, and objective tone suitable for official documents or academic papers. Avoid colloquialisms, contractions, and personal pronouns. Focus on clarity, precision, and a professional voice.\";\r\n      break;\r\n    case \"休閒\":\r\n      toneInstruction = \"You are a friendly and engaging conversationalist. Rewrite the following text in a casual, relaxed, and friendly tone. Use contractions, personal anecdotes, and simple language to make it more approachable and easy to read.\";\r\n      break;\r\n    case \"專業\":\r\n      toneInstruction = \"You are a leading expert in your field. Rewrite the following text in a professional, authoritative, and knowledgeable tone. Use industry-specific terminology where appropriate, maintain a confident voice, and ensure the information is presented clearly and concisely.\";\r\n      break;\r\n    case \"幽默\":\r\n      toneInstruction = \"You are a witty and humorous writer. Rewrite the following text in a clever and humorous tone. Use wordplay, irony, or lighthearted examples to make the content more entertaining, while still conveying the core message.\";\r\n      break;\r\n  }\r\n\r\n  return callOpenRouter([\r\n    { role: \"system\", content: toneInstruction },\r\n    { role: \"user\", content: text },\r\n  ]);\r\n}\r\n\r\nasync function polishWithOpenRouterStream(\r\n  text: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  await processStreamResponse({\r\n    url: OPENROUTER_API_BASE_URL,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\",\r\n      \"X-Title\": \"MyNote AI Assistant\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages: [\r\n        {\r\n          role: \"system\",\r\n          content: `You are a professional text editor and writing consultant. Perform comprehensive polishing and improvement on the following text. Follow these editing principles:\r\n\r\nEDITING FOCUS:\r\n1. **Grammatical Correctness**: Correct all grammar errors, punctuation usage, and sentence structure issues\r\n2. **Expression Clarity**: Simplify complex sentences to make them clearer and easier to understand\r\n3. **Logical Coherence**: Ensure smooth logical flow between paragraphs and natural transitions between ideas\r\n4. **Word Precision**: Use more appropriate and professional vocabulary to replace vague or inaccurate expressions\r\n5. **Style Consistency**: Maintain consistency in overall style\r\n6. **Length Optimization**: Adjust length appropriately while preserving the original meaning, avoiding redundancy\r\n\r\nIMPROVEMENT STRATEGY:\r\n- Convert passive voice to active voice when appropriate\r\n- Merge duplicate or redundant sentences\r\n- Improve paragraph structure and sentence rhythm\r\n- Add necessary connecting words to improve fluency\r\n- Ensure correct use of professional terminology\r\n\r\nOUTPUT REQUIREMENTS:\r\nPlease provide the polished complete text and briefly explain the main improvement points at the end.\r\n\r\nORIGINAL CONTENT:`,\r\n        },\r\n        { role: \"user\", content: text },\r\n      ],\r\n      stream: true,\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nasync function polishWithGeminiStream(\r\n  text: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n\r\n  await processStreamResponse({\r\n    url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `請作為一位專業的文字編輯和寫作顧問，對以下文字進行全面的潤飾和改進。請遵循以下編輯原則：\r\n\r\n## 編輯重點：\r\n1. **語法正確性**：修正所有語法錯誤、標點符號使用和句子結構問題\r\n2. **表達清晰度**：簡化複雜的句子，使表達更清晰易懂\r\n3. **邏輯連貫性**：確保段落之間的邏輯流暢，想法之間的過渡自然\r\n4. **用詞精準性**：使用更恰當、專業的詞彙替代模糊或不準確的表達\r\n5. **風格一致性**：保持整體風格的一致性\r\n6. **長度優化**：在保持原意的前提下，適度調整長度，避免冗餘\r\n\r\n## 改進策略：\r\n- 修正被動語態為主動語態（適當時機）\r\n- 合併重複或多餘的句子\r\n- 改善段落結構和句子節奏\r\n- 增加必要的連接詞以改善流暢度\r\n- 確保專業術語使用正確\r\n\r\n## 輸出要求：\r\n請提供潤飾後的完整文字，並在文末簡要說明主要的改進點。\r\n\r\n## 原文內容：\r\n${text}\r\n\r\n請開始潤飾：`\r\n        }]\r\n      }]\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nexport async function polishStream(\r\n  text: string,\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const provider = getAiProvider();\r\n  console.log(\"Starting polish streaming with provider:\", provider);\r\n  if (provider === \"gemini\") {\r\n    return polishWithGeminiStream(text, onChunk);\r\n  } else {\r\n    return polishWithOpenRouterStream(text, onChunk);\r\n  }\r\n}\r\n\r\nasync function changeToneWithOpenRouterStream(\r\n  text: string,\r\n  tone: \"正式\" | \"休閒\" | \"專業\" | \"幽默\",\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"openrouter\");\r\n  if (!apiKey) {\r\n    throw new Error(\"OpenRouter API 金鑰未設定。\");\r\n  }\r\n\r\n  let toneInstruction = \"\";\r\n  switch (tone) {\r\n    case \"正式\":\r\n      toneInstruction = \"Rewrite the text in a formal tone.\";\r\n      break;\r\n    case \"休閒\":\r\n      toneInstruction = \"Rewrite the text in a casual and friendly tone.\";\r\n      break;\r\n    case \"專業\":\r\n      toneInstruction = \"Rewrite the text in a professional and authoritative tone.\";\r\n      break;\r\n    case \"幽默\":\r\n      toneInstruction = \"Rewrite the text in a witty and humorous tone.\";\r\n      break;\r\n  }\r\n\r\n  await processStreamResponse({\r\n    url: OPENROUTER_API_BASE_URL,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n      Authorization: `Bearer ${apiKey}`,\r\n      \"HTTP-Referer\": \"http://localhost:3000\",\r\n      \"X-Title\": \"MyNote AI Assistant\",\r\n    },\r\n    body: JSON.stringify({\r\n      model: getOpenRouterModel(),\r\n      messages: [\r\n        { role: \"system\", content: toneInstruction },\r\n        { role: \"user\", content: text },\r\n      ],\r\n      stream: true,\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nasync function changeToneWithGeminiStream(\r\n  text: string,\r\n  tone: \"正式\" | \"休閒\" | \"專業\" | \"幽默\",\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const apiKey = getApiKey(\"gemini\");\r\n  if (!apiKey) {\r\n    throw new Error(\"Gemini API 金鑰未設定。\");\r\n  }\r\n\r\n  let toneInstruction = \"\";\r\n  switch (tone) {\r\n    case \"正式\":\r\n      toneInstruction = \"請您扮演一位專業的公文與學術寫作專家。將以下文字改寫為正式、結構化且客觀的語氣，使其適用於官方文件或學術論文。請避免使用口語、縮寫和個人代名詞，並專注於清晰、精確與專業的表達。\";\r\n      break;\r\n    case \"休閒\":\r\n      toneInstruction = \"請您扮演一位友善且健談的朋友。將以下文字改寫為輕鬆、休閒且友好的語氣。請適度使用生活化用語、個人化的例子或比喻，使其更平易近人、更容易閱讀。\";\r\n      break;\r\n    case \"專業\":\r\n      toneInstruction = \"請您扮演一位行業內的頂尖專家。將以下文字改寫為專業、權威且知識豐富的語氣。請在適當之處使用行業術語，保持自信的口吻，並確保資訊呈現清晰、簡潔。\";\r\n      break;\r\n    case \"幽默\":\r\n      toneInstruction = \"請您扮演一位機智且風趣的作家。將以下文字改寫為巧妙且幽默的語氣。請運用雙關語、反諷或輕鬆的例子，使內容更具娛樂性，同時仍能傳達核心訊息。\";\r\n      break;\r\n  }\r\n\r\n  const model = getGeminiModel();\r\n  const baseUrl = getGeminiApiBaseUrl();\r\n\r\n  await processStreamResponse({\r\n    url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n    headers: {\r\n      \"Content-Type\": \"application/json\",\r\n    },\r\n    body: JSON.stringify({\r\n      contents: [{\r\n        parts: [{\r\n          text: `${toneInstruction}\\n\\n${text}`\r\n        }]\r\n      }]\r\n    }),\r\n    onChunk,\r\n  });\r\n}\r\n\r\nexport async function changeToneStream(\r\n  text: string,\r\n  tone: \"正式\" | \"休閒\" | \"專業\" | \"幽默\",\r\n  onChunk: (chunk: string) => void\r\n): Promise<void> {\r\n  const provider = getAiProvider();\r\n  console.log(\"Starting changeTone streaming with provider:\", provider);\r\n  if (provider === \"gemini\") {\r\n    return changeToneWithGeminiStream(text, tone, onChunk);\r\n  } else {\r\n    return changeToneWithOpenRouterStream(text, tone, onChunk);\r\n  }\r\n}\r\n\r\n// --- Model Fetching Functions ---\r\n\r\nexport async function getOpenRouterModels(): Promise<{ id: string; name: string }[]> {\r\n  try {\r\n    const response = await fetch(\"https://openrouter.ai/api/v1/models\");\r\n    if (!response.ok) {\r\n      throw new Error(\"無法獲取 OpenRouter 模型列表。\");\r\n    }\r\n    const data = await response.json();\r\n    return data.data.map((model: any) => ({\r\n      id: model.id,\r\n      name: model.name || model.id,\r\n    }));\r\n  } catch (error) {\r\n    console.error(\"獲取 OpenRouter 模型失敗:\", error);\r\n    return []; // Return empty array on failure\r\n  }\r\n}\r\n\r\n// Mock function for Gemini models as there's no public REST endpoint for listing them\r\nexport async function getGeminiModels(): Promise<{ id: string; name: string }[]> {\r\n  return Promise.resolve([\r\n    { id: \"gemini-2.5-pro\", name: \"Gemini 2.5 Pro\" },\r\n    { id: \"gemini-2.5-flash\", name: \"Gemini 2.5 Flash\" },\r\n    { id: \"gemini-2.0-flash-exp\", name: \"Gemini 2.0 Flash Experimental\" },\r\n  ]);\r\n}\r\n\r\n\r\n// --- Translation Functions ---\r\n\r\nexport async function translateText(text: string, targetLanguage: string, sourceLanguage?: string): Promise<string> {\r\n  const provider = getAiProvider();\r\n\r\n  const translationPrompt = `請作為一位專業的翻譯專家，將以下文字翻譯成${targetLanguage}。\r\n\r\n翻譯要求：\r\n1. **準確性**：確保翻譯的準確性和忠實度，保持原文的意思和語境\r\n2. **自然流暢**：使用目標語言的自然表達方式，避免生硬的直譯\r\n3. **文化適應**：考慮文化差異，使用合適的表達方式\r\n4. **專業術語**：正確處理專業術語和專有名詞\r\n5. **格式保持**：維持原文的格式、標點符號和結構\r\n\r\n${sourceLanguage ? `原文語言：${sourceLanguage}` : '原文語言：自動檢測'}\r\n\r\n原文內容：\r\n${text}\r\n\r\n請提供高品質的翻譯：`;\r\n\r\n  if (provider === \"gemini\") {\r\n    const apiKey = getApiKey(\"gemini\");\r\n    if (!apiKey) {\r\n      throw new Error(\"Gemini API 金鑰未設定。\");\r\n    }\r\n\r\n    const model = getGeminiModel();\r\n    const baseUrl = getGeminiApiBaseUrl();\r\n    const response = await fetch(`${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        contents: [{\r\n          parts: [{\r\n            text: translationPrompt\r\n          }]\r\n        }]\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(\"Gemini API Error:\", errorText);\r\n      toast({\r\n        title: \"API 請求失敗\",\r\n        description: \"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\",\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(\"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    return data.candidates[0].content.parts[0].text;\r\n  } else {\r\n    return callOpenRouter([\r\n      {\r\n        role: \"system\",\r\n        content: `You are a professional translator. Translate the following text to ${targetLanguage}. Ensure accuracy, natural flow, and cultural adaptation.`,\r\n      },\r\n      { role: \"user\", content: text },\r\n    ]);\r\n  }\r\n}\r\n\r\nexport async function translateTextStream(text: string, targetLanguage: string, sourceLanguage: string | undefined, onChunk: (chunk: string) => void): Promise<void> {\r\n  const provider = getAiProvider();\r\n\r\n  const translationPrompt = `請作為一位專業的翻譯專家，將以下文字翻譯成${targetLanguage}。\r\n\r\n翻譯要求：\r\n1. **準確性**：確保翻譯的準確性和忠實度，保持原文的意思和語境\r\n2. **自然流暢**：使用目標語言的自然表達方式，避免生硬的直譯\r\n3. **文化適應**：考慮文化差異，使用合適的表達方式\r\n4. **專業術語**：正確處理專業術語和專有名詞\r\n5. **格式保持**：維持原文的格式、標點符號和結構\r\n\r\n${sourceLanguage ? `原文語言：${sourceLanguage}` : '原文語言：自動檢測'}\r\n\r\n原文內容：\r\n${text}\r\n\r\n請提供高品質的翻譯：`;\r\n\r\n  if (provider === \"gemini\") {\r\n    const apiKey = getApiKey(\"gemini\");\r\n    if (!apiKey) {\r\n      throw new Error(\"Gemini API 金鑰未設定。\");\r\n    }\r\n\r\n    const model = getGeminiModel();\r\n    const baseUrl = getGeminiApiBaseUrl();\r\n\r\n    await processStreamResponse({\r\n      url: `${baseUrl}v1/models/${model}:streamGenerateContent?key=${apiKey}&alt=sse`,\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        contents: [{\r\n          parts: [{\r\n            text: translationPrompt\r\n          }]\r\n        }]\r\n      }),\r\n      onChunk,\r\n    });\r\n  } else {\r\n    await processStreamResponse({\r\n      url: OPENROUTER_API_BASE_URL,\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n        Authorization: `Bearer ${getApiKey(\"openrouter\")}`,\r\n        \"HTTP-Referer\": \"http://localhost:3000\",\r\n        \"X-Title\": \"MyNote AI Assistant\",\r\n      },\r\n      body: JSON.stringify({\r\n        model: getOpenRouterModel(),\r\n        messages: [\r\n          {\r\n            role: \"system\",\r\n            content: `You are a professional translator. Translate the following text to ${targetLanguage}. Ensure accuracy, natural flow, and cultural adaptation.`,\r\n          },\r\n          { role: \"user\", content: text },\r\n        ],\r\n        stream: true,\r\n      }),\r\n      onChunk,\r\n    });\r\n  }\r\n}\r\n\r\n// --- Todo Extraction Functions ---\r\n\r\nexport interface TodoItem {\r\n  id: string;\r\n  text: string;\r\n  priority: \"high\" | \"medium\" | \"low\";\r\n  category?: string;\r\n  dueDate?: string;\r\n  completed: boolean;\r\n}\r\n\r\nexport async function extractTodos(text: string): Promise<TodoItem[]> {\r\n  const provider = getAiProvider();\r\n\r\n  const extractionPrompt = `請作為一位專業的任務分析專家，從以下文字中識別並提取所有可執行的待辦事項。\r\n\r\n分析要求：\r\n1. **識別任務**：找出所有明確或隱含的行動項目、任務和待辦事項\r\n2. **任務分類**：將任務分類為高、中、低優先級\r\n3. **任務細化**：將模糊的任務細化為具體的可執行步驟\r\n4. **時間敏感性**：識別具有時間限制或緊急性的任務\r\n5. **邏輯排序**：按照邏輯順序或優先級排列任務\r\n\r\n任務屬性：\r\n- **優先級**：high（緊急重要）、medium（重要但不緊急）、low（一般性任務）\r\n- **類別**：工作、個人、學習、健康等\r\n- **截止日期**：如果文字中提到具體時間\r\n- **任務描述**：清晰簡潔的任務說明\r\n\r\n請以結構化的 JSON 格式輸出，包含以下欄位：\r\n- id: 唯一識別碼\r\n- text: 任務描述\r\n- priority: 優先級 (high/medium/low)\r\n- category: 類別（可選）\r\n- dueDate: 截止日期（可選）\r\n- completed: 是否已完成（預設為 false）\r\n\r\n原文內容：\r\n${text}\r\n\r\n請提取所有待辦事項並以 JSON 格式輸出：`;\r\n\r\n  if (provider === \"gemini\") {\r\n    const apiKey = getApiKey(\"gemini\");\r\n    if (!apiKey) {\r\n      throw new Error(\"Gemini API 金鑰未設定。\");\r\n    }\r\n\r\n    const model = getGeminiModel();\r\n    const baseUrl = getGeminiApiBaseUrl();\r\n    const response = await fetch(`${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`, {\r\n      method: \"POST\",\r\n      headers: {\r\n        \"Content-Type\": \"application/json\",\r\n      },\r\n      body: JSON.stringify({\r\n        contents: [{\r\n          parts: [{\r\n            text: extractionPrompt\r\n          }]\r\n        }]\r\n      }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(\"Gemini API Error:\", errorText);\r\n      toast({\r\n        title: \"API 請求失敗\",\r\n        description: \"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\",\r\n        variant: \"destructive\",\r\n      });\r\n      throw new Error(\"Gemini API 請求失敗。請檢查 API 金鑰是否正確。\");\r\n    }\r\n\r\n    const data = await response.json();\r\n    const result = data.candidates[0].content.parts[0].text;\r\n\r\n    try {\r\n      // Try to parse the JSON response\r\n      const jsonMatch = result.match(/\\[[\\s\\S]*\\]/) || result.match(/\\{[\\s\\S]*\\}/);\r\n      if (jsonMatch) {\r\n        const todos = JSON.parse(jsonMatch[0]);\r\n        return Array.isArray(todos) ? todos : [todos];\r\n      }\r\n      throw new Error(\"無法解析待辦事項\");\r\n    } catch (e) {\r\n      console.error(\"Failed to parse todos:\", e);\r\n      throw new Error(\"無法解析待辦事項，請檢查回應格式\");\r\n    }\r\n  } else {\r\n    const result = await callOpenRouter([\r\n      {\r\n        role: \"system\",\r\n        content: `You are a professional task analysis expert. Extract all actionable todo items from the following text. Return them as a JSON array with fields: id, text, priority (high/medium/low), category (optional), dueDate (optional), completed (boolean).`,\r\n      },\r\n      { role: \"user\", content: text },\r\n    ]);\r\n\r\n    try {\r\n      const todos = JSON.parse(result);\r\n      return Array.isArray(todos) ? todos : [todos];\r\n    } catch (e) {\r\n      console.error(\"Failed to parse todos:\", e);\r\n      throw new Error(\"無法解析待辦事項，請檢查回應格式\");\r\n    }\r\n  }\r\n}\r\n\r\n// --- Tag Generation Functions ---\r\n\r\nexport async function generateTags(content: string): Promise<string[]> {\r\n  const provider = getAiProvider();\r\n\r\n  const tagPrompt = `請作為一位專業的內容分析師和分類專家，仔細閱讀以下筆記內容，並提取出最核心、最相關的關鍵字作為標籤。\r\n\r\n分析要求：\r\n1. **核心主題**：識別文章的主要主題和核心概念。\r\n2. **關鍵實體**：提取出重要的人物、地點、組織、技術或專有名詞。\r\n3. **內容分類**：判斷文章的性質，例如是「教學」、「評論」、「筆記」還是「想法」。\r\n4. **精簡化**：確保每個標籤都是簡潔且具代表性的詞彙。\r\n5. **數量控制**：提供 3 到 5 個最相關的標籤。\r\n\r\n請以 JSON 格式的陣列輸出，例如：[\"標籤一\", \"標籤二\", \"標籤三\"]\r\n\r\n原文內容：\r\n${content}\r\n\r\n請僅提供 JSON 格式的標籤陣列：`;\r\n\r\n  let rawResult: string;\r\n\r\n  if (provider === \"gemini\") {\r\n    const apiKey = getApiKey(\"gemini\");\r\n    if (!apiKey) throw new Error(\"Gemini API 金鑰未設定。\");\r\n\r\n    const model = getGeminiModel();\r\n    const baseUrl = getGeminiApiBaseUrl();\r\n    const response = await fetch(`${baseUrl}v1/models/${model}:generateContent?key=${apiKey}`, {\r\n      method: \"POST\",\r\n      headers: { \"Content-Type\": \"application/json\" },\r\n      body: JSON.stringify({ contents: [{ parts: [{ text: tagPrompt }] }] }),\r\n    });\r\n\r\n    if (!response.ok) {\r\n      const errorText = await response.text();\r\n      console.error(\"Gemini API Error:\", errorText);\r\n      throw new Error(\"Gemini API 請求失敗。\");\r\n    }\r\n    const data = await response.json();\r\n    rawResult = data.candidates[0].content.parts[0].text;\r\n  } else {\r\n    rawResult = await callOpenRouter([\r\n      {\r\n        role: \"system\",\r\n        content: `You are a professional content analyst. Extract 3 to 5 core keywords from the following text to be used as tags. Return them as a JSON array of strings. For example: [\"Tag1\", \"Tag2\", \"Tag3\"]`,\r\n      },\r\n      { role: \"user\", content },\r\n    ]);\r\n  }\r\n\r\n  try {\r\n    const jsonMatch = rawResult.match(/(\\[[\\s\\S]*\\])/);\r\n    if (jsonMatch) {\r\n      const tags = JSON.parse(jsonMatch[0]);\r\n      return Array.isArray(tags) ? tags.map(tag => String(tag)) : [];\r\n    }\r\n    // Fallback for non-JSON array strings\r\n    return rawResult.split(',').map(tag => tag.trim()).filter(Boolean);\r\n  } catch (e) {\r\n    console.error(\"Failed to parse tags:\", e);\r\n    // Fallback for parsing errors\r\n    return rawResult.split(',').map(tag => tag.trim()).filter(Boolean);\r\n  }\r\n}", "import createLucideIcon from '../createLucideIcon';\nimport { IconNode } from '../types';\n\nexport const __iconNode: IconNode = [['path', { d: 'M21 12a9 9 0 1 1-6.219-8.56', key: '13zald' }]];\n\n/**\n * @component @name LoaderCircle\n * @description Lucide SVG icon component, renders SVG Element with children.\n *\n * @preview ![img](data:image/svg+xml;base64,PHN2ZyAgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIgogIHdpZHRoPSIyNCIKICBoZWlnaHQ9IjI0IgogIHZpZXdCb3g9IjAgMCAyNCAyNCIKICBmaWxsPSJub25lIgogIHN0cm9rZT0iIzAwMCIgc3R5bGU9ImJhY2tncm91bmQtY29sb3I6ICNmZmY7IGJvcmRlci1yYWRpdXM6IDJweCIKICBzdHJva2Utd2lkdGg9IjIiCiAgc3Ryb2tlLWxpbmVjYXA9InJvdW5kIgogIHN0cm9rZS1saW5lam9pbj0icm91bmQiCj4KICA8cGF0aCBkPSJNMjEgMTJhOSA5IDAgMSAxLTYuMjE5LTguNTYiIC8+Cjwvc3ZnPgo=) - https://lucide.dev/icons/loader-circle\n * @see https://lucide.dev/guide/packages/lucide-react - Documentation\n *\n * @param {Object} props - Lucide icons props and any valid SVG attribute\n * @returns {JSX.Element} JSX Element\n *\n */\nconst LoaderCircle = createLucideIcon('loader-circle', __iconNode);\n\nexport default LoaderCircle;\n"], "names": [], "mappings": "oRAEA,EAAA,EAAA,CAAA,CAAA,OCEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEI,EAAY,cACZ,CAAC,EAA0B,EAAuB,CAAG,CAAA,EAAA,EAAA,kBAAA,AAAkB,EAAC,EAAW,CACrF,EAAA,iBAAiB,CAClB,EACG,EAAiB,CAAA,EAAA,EAAA,iBAAA,AAAiB,IAClC,EAAc,AAAC,IACjB,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAkB,CAAG,EAC9C,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,EAA2B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAgB,CAAE,MAAO,EAAK,EACtG,EACA,EAAY,WAAW,CAAG,EAED,AAOzB,EAPyB,UAAgB,CACvC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAc,CAAG,EAC1C,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,KAA8B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAY,CAAE,IAAK,CAAa,EAC3G,GAEiB,WAAW,CARX,EAQc,mBAEjC,IAAI,EAAoB,AAAC,IACvB,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAa,CAAG,EACzC,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,IAA6B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAW,AAAC,EACtF,EACA,EAAkB,WAAW,CANX,EAMc,kBAEhC,IAAI,EAAqB,EAAA,UAAgB,CACvC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAc,CAAG,EAC1C,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,KAA8B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAY,CAAE,IAAK,CAAa,EAC3G,GAEF,EAAmB,WAAW,CARX,EAQc,mBACjC,IAAI,EAAe,qBACf,CAAC,EAA4B,EAA6B,CAAG,EAAyB,GACtF,EAAY,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,sBAC5B,EAAqB,EAAA,UAAgB,CACvC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,UAAQ,CAAE,GAAG,EAAc,CAAG,EACpD,EAAc,EAAe,GAC7B,EAAa,EAAA,MAAY,CAAC,MAC1B,EAAe,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC7C,EAAY,EAAA,MAAY,CAAC,MAC/B,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EACxB,EAAA,EADkB,aACa,CAC/B,CACE,YAAa,EACb,UAAW,EACX,SAAU,eACV,SAA0B,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAA4B,CAAE,CAArC,KAA4C,YAAoB,EAAW,SAA0B,CAAhB,AAAgB,EAAA,EAAA,IAAA,AAAI,EAC9H,EAAA,CADuH,MAChG,CACvB,CACE,KAAM,cACN,GAAG,CAAW,CACd,GAAG,CAAY,CACf,IAAK,EACL,gBAAiB,CAAA,EAAA,EAAA,oBAAoB,AAApB,EAAqB,EAAa,eAAe,CAAG,AAAD,IAClE,EAAM,cAAc,GACpB,EAAU,OAAO,EAAE,MAAM,CAAE,cAAe,EAAK,EACjD,GACA,qBAAsB,AAAC,GAAU,EAAM,cAAc,GACrD,kBAAmB,AAAC,GAAU,EAAM,cAAc,GAClD,SAAU,CACQ,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAW,UAAE,CAAS,GAC1B,CAAA,EAAA,EAAA,GAAA,AAAG,EAAC,EAAoB,YAAE,CAAW,GACtD,AACH,EACA,EACJ,EAEJ,GAEF,EAAmB,WAAW,CAAG,EACjC,IAAI,EAAa,mBACb,EAAmB,EAAA,UAAgB,CACrC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAY,CAAG,EACxC,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,GAA4B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAU,CAAE,IAAK,CAAa,EACvG,EAEF,GAAiB,WAAW,CAAG,EAC/B,IAAI,EAAmB,yBACnB,EAAyB,EAAA,UAAgB,CAAC,CAAC,EAAO,KACpD,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAkB,CAAG,EAC9C,EAAc,EAAe,GACnC,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,SAAkC,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAgB,CAAE,IAAK,CAAa,EACnH,GACA,EAAuB,WAAW,CAAG,EAErC,IAAI,EAAoB,EAAA,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAa,CAAG,EACzC,EAAc,EAAe,GACnC,MAAuB,CAAA,AAAhB,EAAgB,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,GAA4B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAW,CAAE,IAAK,CAAa,EACxG,GAEF,EAAkB,WAAW,CARX,EAQc,kBAChC,IAAI,EAAc,oBACd,EAAoB,EAAA,UAAgB,CACtC,CAAC,EAAO,KACN,GAAM,oBAAE,CAAkB,CAAE,GAAG,EAAa,CAAG,EACzC,WAAE,CAAS,CAAE,CAAG,EAA6B,EAAa,GAC1D,EAAc,EAAe,GAC7B,EAAM,CAAA,EAAA,EAAA,eAAA,AAAe,EAAC,EAAc,GAC1C,MAAuB,CAAhB,AAAgB,EAAA,EAAA,GAAA,AAAG,EAAC,EAAA,EAAP,GAA4B,CAAE,CAAE,GAAG,CAAW,CAAE,GAAG,CAAW,KAAE,CAAI,EAC1F,GAEF,EAAkB,WAAW,CAAG,EAChC,IAAI,EAAqB,CAAC,YAAE,CAAU,CAAE,IACtC,IAAM,EAAU,CAAC,EAAE,EAAE,EAAa;;mCAED,EAAE,EAAa,kBAAkB,EAAE,EAAiB;;0JAEmE,EAAE,EAAa;;sFAEnF,CAAC,CAOrF,OANA,EAAA,SAAe,CAAC,KACS,AAGnB,CAAC,QAH2B,cAAc,CAC5C,EAAW,OAAO,EAAE,aAAa,sBAEd,QAAQ,IAAI,CAAC,EACpC,EAAG,CAAC,EAAS,EAAW,EACjB,IACT,EDrIA,EAAA,EAAA,CAAA,CAAA,OACA,EAAA,EAAA,CAAA,CAAA,OAEA,IAAM,ECmIM,ED7HN,EAAqB,EAAA,OANP,GAMuB,CAGzC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,0JACA,GAED,GAAG,CAAK,CACT,IAAK,IAGT,GAAmB,WAAW,CAAG,ACmHlB,EDnH+C,WAAW,CAEzE,IAAM,EAAqB,EAAA,UAAgB,CAGzC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,IAAA,EAAC,AC4GW,ED5GX,WACC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAA,GACD,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,4gBACA,GAED,GAAG,CAAK,OAIf,EAAmB,WAAW,CAAG,ACkGlB,EDlG+C,WAAW,CAEzE,IAAM,EAAoB,CAAC,WACzB,CAAS,CACT,GAAG,EACkC,GACrC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,mDACA,GAED,GAAG,CAAK,GAGb,EAAkB,WAAW,CAAG,oBAEhC,IAAM,EAAoB,CAAC,CACzB,WAAS,CACT,GAAG,EACkC,GACrC,CAAA,EAAA,EAAA,GAAA,EAAC,MAAA,CACC,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,gEACA,GAED,GAAG,CAAK,GAGb,EAAkB,WAAW,CAAG,oBAEhC,IAAM,EAAmB,EAAA,UAAgB,CAGvC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,wBAAyB,GACtC,GAAG,CAAK,IAGb,EAAiB,WAAW,CAAG,AC6DlB,ED7D6C,WAAW,CAErE,IAAM,EAAyB,EAAA,UAAgB,CAG7C,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,gCAAiC,GAC9C,GAAG,CAAK,GAGb,GAAuB,WAAW,CAChC,ACiDiB,EDjDgB,WAAW,CAE9C,IAAM,EAAoB,EAAA,UAAgB,CAGxC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EAAC,CAAA,EAAA,EAAA,cAAA,AAAc,IAAI,GAC/B,GAAG,CAAK,IAGb,EAAkB,WAAW,CAAG,ACkCnB,EDlC+C,WAAW,CAEvE,IAAM,EAAoB,EAAA,UAAgB,CAGxC,CAAC,WAAE,CAAS,CAAE,GAAG,EAAO,CAAE,IAC1B,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CACC,IAAK,EACL,UAAW,CAAA,EAAA,EAAA,EAAA,AAAE,EACX,CAAA,EAAA,EAAA,cAAA,AAAc,EAAC,CAAE,QAAS,SAAU,GACpC,eACA,GAED,GAAG,CAAK,IAGb,EAAkB,WAAW,CAAG,ACmBnB,EDnB+C,WAAW,yBE9HvE,SAAS,EAAM,CAAA,CAAe,CAAC,EAAK,EAAG,CAAA,CAA6B,AAClE,OAAO,KAAK,GAAA,CAAI,EAAK,KAAK,GAAA,CAAI,EAAK,GACrC,EAD0C,CAAC,uECD3C,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,KAeH,EAAA,CAAA,CAAA,EAfc,KAGvB,IAAM,EAAyB,EAAA,aAAA,CAAqC,KAAA,CAAS,EAiB7E,SAAS,EAAa,CAAA,EAAsB,AAC1C,IAAM,EAAkB,EAAA,UAAA,CAAW,GACnC,OAAO,GAAY,GADgC,AACnB,KAClC,qDCPA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAQ,AAAR,CAAQ,AAAR,CAAQ,AAAR,CAAQ,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAbK,CAAC,AAaG,CAbF,AAaE,CAAA,CAAA,CAAA,CAAA,CAAA,CAbF,AAaE,CAbF,AAAQ,AAaN,CAbM,AAaN,AAbQ,CAaE,CAAA,AAbC,iBAAA,CAAmB,AAAnB,CAAmB,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA,+FCHtF,IAAA,EAAuB,EAAA,CAAA,CAAA,EAAX,KACZ,EAAqC,EAAA,CAA5B,AAA4B,CAAA,AADd,OAEvB,EAAiC,EAAA,CAAxB,AAAwB,CAAA,OACjC,EAAgC,EAAA,CAAvB,AAAuB,CAAA,CAFK,MAGrC,EAAmC,CAFF,CAEE,CAAA,AAA1B,CAA0B,OACnC,EAAsB,AAFU,EAEV,CAAb,AAAa,CAAA,OACtB,EAA0B,EAAA,CADJ,AACb,AAAiB,AAFS,CAET,OAC1B,EAA+B,EAAA,CAAA,AAAtB,CAAsB,GADL,IAE1B,EAAqC,EAAA,CAA5B,AAA4B,CAAA,MACrC,EAA6B,AAFE,EAEF,CAApB,AAAoB,CAAA,OAgEnB,EAAA,EAAA,CAAA,CAAA,EAjE2B,IACR,CAIvB,EAAc,gCACd,EAAgB,CAAE,SAAS,EAAO,WAAY,EAAK,EAMnD,EAAa,mBAGb,CAAC,EAAY,EAAe,EAAqB,CAAI,CAAA,EAAA,EAAA,aAAJ,GAAI,EAGzD,GAGI,CAAC,EAA+B,EAA2B,CAAA,CAAA,AAHrD,EAGyD,EAAA,kBAAA,CAAJ,CAC/D,EACA,CAAC,EAAqB,EA+BlB,CAAC,EAAqB,EAAqB,CAC/C,EAAkD,GAK9C,EAAyB,EAAA,EArCP,CAgCsC,MADb,CAMlB,CAC7B,CAAC,EAA2C,IAExC,CAAA,EAAA,EAAA,GAAA,EAAC,EAAW,CAF6C,OAE7C,CAAX,CAAoB,MAAO,EAAM,uBAAA,CAChC,SAAA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAW,EAAZ,EAAY,CAAX,CAAgB,MAAO,EAAM,uBAAA,CAC5B,SAAA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,CAAsB,CAAvB,EAA0B,CAAA,CAAO,IAAK,CAAA,CAAc,CAAA,CACtD,CAAA,CACF,GAKN,EAAiB,WAAA,CAAc,EAgB/B,IAAM,EAA6B,EAAA,UAAA,CAGjC,CAAC,EAA+C,KAChD,GAAM,CACJ,QAF+D,iBAE/D,aACA,CAAA,MACA,GAAO,CAAA,KACP,CAAA,CACA,iBAAkB,CAAA,yBAClB,CAAA,0BACA,CAAA,CACA,cAAA,2BACA,EAA4B,EAAA,CAC5B,GAAG,EACL,CAAI,EACE,EAAY,EAAA,MAAA,CAAoC,IAAI,EACpD,EAAA,CAAA,EAAe,EAAA,eAAA,EAAgB,EAAc,GAAG,AAChD,EAAA,CAAA,EAAY,EAAA,YAAA,EAAa,GACzB,AAD4B,CAC3B,EAAkB,EAAmB,CAAI,CAAA,EAAA,EAAA,WAAJ,SAAI,EAAqB,CACnE,KAAM,EACN,YAAa,GAA2B,KACxC,SAAU,EACV,OAAQ,CACV,CAAC,EACK,CAAC,EAAkB,EAAmB,CAAU,EAAA,QAAA,EAAS,GACzD,CADsC,CAAwB,AAC9D,CAAA,EAAmB,EAAA,cAAA,EAAe,GAClC,EAAW,EAAc,GACzB,EAAwB,AAFsB,EAEtB,MAAA,EAAO,GAC/B,CAAC,CADmC,CACd,EAF0B,AAEJ,CAAU,EAAA,QAAA,CAAS,CAAC,EAUtE,KAVkD,EAWhD,AATI,EAAA,SAAA,CAAU,CASd,IARA,CADoB,GACd,EAAO,EAAI,OAAA,CACjB,GAAI,EAEF,IAFQ,GACR,EAAK,gBAAA,CAAiB,EAAa,GAC5B,IAAM,EAAK,OADiC,YACjC,CAAoB,EAAa,EAEvD,EAAG,CAAC,EAAiB,EAGnB,CAAA,EAAA,EAAA,EALqE,CAKrE,EAAC,EAAA,AAHiB,CAIhB,MAAO,cACP,EACA,IAAK,OACL,mBACA,EACA,YAAmB,EAAA,WAAA,CACjB,AAAC,GAAc,EAAoB,GACnC,CAAC,EAAmB,EAEtB,CAH8C,cAGxB,AAFA,EAEA,WAAA,CAAY,IAAM,GAAoB,GAAO,CAAH,AAAI,CAAC,EACrE,mBAA0B,EAAA,WAAA,CACxB,IAAM,EAAuB,AAAC,GAAc,EAAY,CAAC,EACzD,CAAC,CAAA,EAEH,sBAA6B,EAAA,WAAA,CAC3B,IAAM,EAAwB,AAAD,GAAe,EAAY,CAAC,EACzD,CAAC,CAAA,EAGH,SAAA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,EAAD,OAAC,CAAU,GAAA,CAAV,CACC,SAAU,GAA4C,IAAxB,EAA4B,CAAA,EAAK,EAC/D,mBAAkB,EACjB,GAAG,CAAA,CACJ,IAAK,EACL,MAAO,CAAE,QAAS,OAAQ,GAAG,EAAM,KAAA,AAAM,EACzC,YAAA,CAAA,EAAa,EAAA,oBAAA,EAAqB,EAAM,WAAA,CAAa,KACnD,CADyD,CACzC,OAAA,EAAU,CAC5B,CAAC,EACD,QAAA,CAAA,EAAS,EAAA,oBAAA,EAAqB,EAAM,OAAA,CAAS,AAAC,IAK5C,IAAM,EAAkB,AAL8B,CAK7B,EAAgB,OAAA,CAEzC,GAAI,EAAM,MAAA,GAAW,EAAM,aAAA,EAAiB,GAAmB,CAAC,EAAkB,CAChF,IAAM,EAAkB,IAAI,YAAY,EAAa,GAGrD,GAFA,EAAM,KAD4D,QAC5D,CAAc,aAAA,CAAc,GAE9B,CAAC,EAAgB,SAF4B,OAE5B,CAAkB,CACrC,IAAM,EAAQ,IAAW,KAAF,CAAE,CAAO,AAAC,GAAS,EAAK,SAAS,EAOxD,EAJuB,AAGA,CALJ,EAAM,IAAA,CAAM,AAAD,CAMnB,EAN6B,EAAK,MAAM,EAC/B,EAAM,IAAA,CAAM,AAAD,GAAU,EAAK,EAAA,GAAO,MACD,EAAK,CAAE,EAAF,IAAE,CADU,AAEnE,SAEoC,GAAA,CAAI,AAAC,GAAS,EAAK,GAAA,CAAI,OAAQ,EAC1C,EAC7B,CACF,CAEA,EAAgB,OAAA,EAAU,CAC5B,CAAC,EACD,MAN0D,CAM1D,CAAA,EAAQ,EAAA,oBAAA,EAAqB,EAAM,MAAA,CAAQ,IAAM,GAAoB,GAAM,EAAD,AAC5E,CAD6E,CAInF,CAAC,EAMK,EAAY,uBAaZ,EAA6B,EAAA,UAAA,CACjC,CAAC,EAA0C,KACzC,GAAM,SADoD,gBAExD,CAAA,WACA,EAAY,EAAA,QACZ,GAAS,CAAA,WACT,CAAA,UACA,CAAA,CACA,GAAG,EACL,CAAI,EACE,EAAA,CAAA,EAAS,EAAA,KAAA,CAAM,GACf,EAAK,GAAa,EAClB,EAAU,EAAsB,EAAW,GAC3C,EAAmB,EAAQ,gBAAA,AADuC,GAClB,EAChD,EAAW,EAAc,GAEzB,oBAFgD,AAE9C,CAAA,uBAAoB,CAAA,kBAAuB,CAAA,CAAiB,CAAI,EASxE,OAPM,AAQJ,EARI,SAAA,CAAU,CAQd,IAPA,CADoB,EAChB,EAEF,OADA,EADa,EAEN,IAAM,GAEjB,EAAG,CAAC,EAAW,EAAoB,CAHZ,CAGkC,EAGvD,CAAA,EAAA,EAAA,GAAA,AALqC,EAKpC,EAAW,KAH0C,GAG1C,CAAX,CACC,MAAO,EACP,eACA,SACA,EAEA,SAAA,CAAA,EAAA,EAAA,GAAA,EAAC,EAAA,EAAD,OAAC,CAAU,IAAA,CAAV,CACC,SAAU,EAAmB,EAAI,CAAA,EACjC,mBAAkB,EAAQ,WAAA,CACzB,GAAG,CAAA,CACJ,IAAK,EACL,YAAA,CAAA,EAAa,EAAA,oBAAA,EAAqB,EAAM,WAAA,CAAa,AAAC,IAG/C,EAEA,EAAQ,EALiD,IAG9C,KAEH,CAAY,EAAE,CAFX,EAAM,cAAA,CAAe,CAGvC,CAAC,EACD,QAAA,CAAA,EAAS,EAAA,oBAAA,EAAqB,EAAM,OAAA,CAAS,IAAM,EAAQ,WAAA,CAAY,EAAE,CAAC,CAC1E,UAAA,CAAA,EAAW,EAAA,oBAAA,EAAqB,EAAM,SAAA,CAAW,AAAC,IAChD,GAAkB,GADwC,KACtD,EAAM,GAAA,EAAiB,EAAM,QAAA,CAAU,YACzC,EAAQ,cAAA,CAAe,EAIzB,GAAI,EAAM,MAAA,GAAW,EAAM,aAAA,CAAe,OAE1C,IAAM,EAAc,AAqDhC,SAAS,AAAe,CAAA,CAA4B,CAAA,CAA2B,CAAA,EAAiB,MAC9F,IAAM,GARsB,EAQK,CARL,AAQhB,CAA2B,CARE,EAQF,CAP3B,AAD6B,AACzC,AAAI,EADsD,IACvC,CAAA,CAOyB,EAPlB,CAOqB,CANhC,cAAR,EAAsB,aAAuB,eAAR,EAAuB,YAAc,GAOjF,KAAI,AAAgB,gBAAc,CAAC,YAAa,YAAY,CAAA,CAAE,QAAA,CAAS,EAAG,EAAG,CAAA,EACzD,KADgE,KAAA,KAChF,GAAgC,CAAC,UAAW,WAAW,CAAA,CAAE,QAAA,CAAS,EAAG,EAAG,AAC5E,CAD4E,MACrE,CAD4E,AAC5E,CAAwB,EACjC,AADoC,CAAA,CAzDW,AAwDsC,EAxD/B,EAAQ,WAAA,CAAa,EAAQ,GAAG,EAE1E,GAAoB,KAAA,IAAhB,EAA2B,CAC7B,GAAI,EAAM,OAAA,EAAW,EAAM,OAAA,EAAW,EAAM,MAAA,EAAU,EAAM,QAAA,CAAU,OACtE,EAAM,cAAA,CAAe,EAErB,IAAI,EADU,AACO,IADI,KAAF,CAAE,CAAO,AAAC,GAAS,EAAK,SAAS,EAC7B,GAAA,CAAI,AAAC,GAAS,EAAK,GAAA,CAAI,OAAQ,EAE1D,GAAoB,OAAQ,EAAxB,EAAwB,EAAe,OAAA,CAAQ,OAAA,GAC1B,SAAhB,GAA0C,SAAhB,EAAwB,CACrD,AAAgB,OAAQ,KAAA,EAAe,OAAA,CAAQ,EACnD,IAAM,EAAe,EAAe,OAAA,CAAQ,EAAM,aAAa,EAC/D,EAAiB,EAAQ,IAAA,CACrB,AA6DpB,SAAS,AAAa,CAAA,CAAY,CAAA,EAAoB,AACpD,OAAO,EAAM,GAAA,CAAO,CAAC,EAAG,IAAU,CAAA,CAAA,CAAO,EAAa,CAAA,CAAA,CAAS,EAAM,MAAM,CAAE,CAC/E,EA/D8B,EAAgB,EAAe,CAAC,EAC1C,EAAe,KAAA,CAAM,EAAe,CAAC,CAC3C,CAMA,WAAW,IAAM,EAAW,GAC9B,CACF,CAAC,EAEA,OAJ6C,CAAC,CAI1B,YAApB,OAAO,EACJ,EAAS,kBAAE,EAAkB,WAAgC,MAApB,CAAyB,CAAC,EACnE,CAAA,EACN,EAGN,EAGF,GAAqB,WAAA,CAAc,EAKnC,IAAM,EAAuD,CAC3D,UAAW,OAAQ,QAAS,OAC5B,WAAY,OAAQ,UAAW,OAC/B,OAAQ,QAAS,KAAM,QACvB,SAAU,OAAQ,IAAK,MACzB,EAgBA,SAAS,EAAW,CAAA,CAA2B,GAAgB,CAAA,EAAO,AACpE,IAAM,EAA6B,SAAS,aAAA,CAC5C,IAAA,IAAW,KAAa,EAEtB,GAAI,IAAc,EAFgB,EAGlC,EAAU,KAAA,CAAM,eAD8B,AAC5B,CAAc,CAAC,EAC7B,SAAS,aAAA,GAAkB,GAFe,MAIlD,CAUA,IAAM,EAAO,EACP,EAAO,OAbkD,CAAA,4OC3U/D,IAAA,EAAA,EAAA,CAAA,CAAA,MAGA,IAAM,EAA0B,gDAEhC,SAAS,IAC4B,MAJD,CAIQ,6CAE5C,CAOA,SAAS,IAC4B,MAAO,QAE5C,CAEA,SAAS,IAC4B,MAAO,+BAI5C,CAEA,SAAS,IAC4B,MAAO,gBAI5C,CAWA,eAAe,EAAsB,CAA2B,EAC9D,GAAM,KAAE,CAAG,CAAE,SAAO,MAAE,CAAI,SAAE,CAAO,CAAE,CAAG,EAElC,EAAW,MAAM,MAAM,EAAK,CAChC,OAAQ,eACR,OACA,CACF,GAEA,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAY,MAAM,EAAS,IAAI,GACrC,QAAQ,KAAK,CAAC,oBAAqB,GACnC,GAAI,CACF,IAAM,EAAY,KAAK,KAAK,CAAC,GACvB,EAAe,EAAU,KAAK,EAAE,SAAW,SAMjD,MALA,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,CACJ,MAAO,SACP,YAAa,EACb,QAAS,aACX,GACM,AAAI,MAAM,EAClB,CAAE,MAAO,EAAG,CACV,IAAM,EAAe,CAAC,QAAQ,EAAE,EAAA,CAAW,AAM3C,MALA,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,CACJ,MAAO,SACP,YAAa,EACb,QAAS,aACX,GACM,AAAI,MAAM,EAClB,CACF,CAEA,IAAM,EAAS,EAAS,IAAI,EAAE,YAC9B,GAAI,CAAC,EACH,MAAU,AAAJ,AADK,MACK,wBAGlB,IAAM,EAAU,IAAI,YAChB,EAAS,GAEb,GAAI,CACF,MAAO,CAAM,CACX,GAAM,MAAE,CAAI,OAAE,CAAK,CAAE,CAAG,MAAM,EAAO,IAAI,GACzC,GAAI,EAAM,MAGV,IAAM,EAAQ,CADd,GAAU,EAAQ,MAAM,CAAC,EAAO,CAAE,QAAQ,CAAK,EAAA,EAC1B,KAAK,CAAC,MAG3B,IAAK,IAAM,KAFX,EAAS,EAAM,GAAG,IAAM,GAEL,CAFS,EAG1B,GADwB,AACpB,EAAK,OAH+B,GAGrB,CAAC,UAAW,CAC7B,IAAM,EAAO,EAAK,KAAK,CAAC,GACxB,GAAa,WAAT,EAAmB,SAEvB,GAAI,CACF,IAAM,EAAS,KAAK,KAAK,CAAC,GACpB,EAAU,EAAO,OAAO,EAAE,CAAC,EAAE,EAAE,OAAO,SAC7B,EAAO,UAAU,EAAE,CAAC,EAAE,EAAE,SAAS,OAAO,CAAC,EAAE,EAAE,KACxD,IACF,KADW,GACH,GAAG,CAAC,gBAAiB,GAC7B,EAAQ,GAEZ,CAAE,MAAO,EAAG,CACV,QAAQ,GAAG,CAAC,sBAAuB,EAAG,QAAS,EACjD,CACF,CAEJ,CACF,QAAU,CACR,EAAO,WAAW,EACpB,CACF,CAkKA,eAAe,EACb,CAA6C,EAE7C,IAAM,OACN,EADe,CACX,CAAC,EAMH,KALA,CAAA,AADW,AADY,EAEvB,EAAA,KAAA,AAAK,EAAC,CACJ,MAAO,YACP,YAAa,6BACb,QAAS,aACX,GACM,AAAI,MAAM,yBAGlB,IAAM,EAAW,MAAM,MAAM,EAAyB,CACpD,OAAQ,OACR,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,aACP,CACF,EACF,GAEA,GAAI,CAAC,EAAS,EAAE,CAAE,CAChB,IAAM,EAAY,MAAM,EAAS,IAAI,GACrC,QAAQ,KAAK,CAAC,wBAAyB,GACvC,GAAI,CACF,IAAM,EAAY,KAAK,KAAK,CAAC,GACvB,EAAe,EAAU,KAAK,EAAE,SAAW,sBAMjD,MALA,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,CACJ,MAAO,WACP,YAAa,EACb,QAAS,aACX,GACM,AAAI,MAAM,EAClB,CAAE,MAAO,EAAG,CACV,IAAM,EAAe,CAAC,qBAAqB,EAAE,EAAA,CAAW,AAMxD,MALA,CAAA,EAAA,EAAA,KAAA,AAAK,EAAC,CACJ,MAAO,WACP,YAAa,EACb,QAAS,aACX,GACM,AAAI,MAAM,EAClB,CACF,CAGA,MAAO,CADM,MAAM,EAAS,IAAI,EAAA,EACpB,OAAO,CAAC,EAAE,CAAC,OAAO,CAAC,OAAO,AACxC,CA8DA,eAAe,EACb,CAAc,CACd,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAAM,AAAI,AAFa,AACZ,MACK,wBAGlB,OAAM,EAAsB,CAC1B,IAAK,EACL,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,IACP,SAAU,CACR,CACE,KAAM,SACN,QAAS,CAAC;;;;;;;;;;;;;;;;;;;;oEAoBgD,CAAC,AAC7D,EACA,CAAE,KAAM,OAAQ,QAAS,CAAO,EACjC,CACD,QAAQ,CACV,WACA,CACF,EACF,CAuCA,eAAe,EACb,CAAc,CACd,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,qBAGlB,IAAM,EAAQ,IACR,EAAU,GAEhB,OAAM,EAAsB,CAC1B,IAAK,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,2BAA2B,EAAE,EAAO,QAAQ,CAAC,CAC/E,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBjB,EAAE,OAAO;;qBAEY,CACb,AADc,EACZ,AACJ,EACF,AADI,WAEJ,CACF,EACF,CAWO,eAAe,EACpB,CAAc,CACd,CAAgC,EAEhC,IAAM,EAAW,UAEjB,CADA,QAAQ,GAAG,CAAC,oCAAqC,GAChC,UAAU,CAAvB,GACK,EAAuB,EAAQ,GAE/B,EAA2B,EAAQ,EAE9C,CAIA,eAAe,EACb,CAAe,CACf,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,wBAGlB,OAAM,EAAsB,CAC1B,IAAK,EACL,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,IACP,SAAU,CACR,CACE,KAAM,SACN,QAAS,+BACX,EACA,CAAE,KAAM,eAAQ,CAAQ,EACzB,CACD,OAAQ,EACV,WACA,CACF,EACF,CAEA,eAAe,EACb,CAAe,CACf,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,qBAGlB,IAAM,EAAQ,IACR,EAAU,GAEhB,OAAM,EAAsB,CAC1B,IAAK,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,2BAA2B,EAAE,EAAO,QAAQ,CAAC,CAC/E,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CAAC;;;;;;;;;;;;;;;AAejB,EAAE,QAAQ;;qBAEW,CAAC,AACd,EAAE,AACJ,EAAE,AACJ,WACA,CACF,EACF,CAEO,eAAe,EACpB,CAAe,CACf,CAAgC,EAEhC,IAAM,EAAW,UAEjB,CADA,QAAQ,GAAG,CAAC,8CAA+C,GAC1C,UAAU,CAAvB,GACK,EAA0B,EAAS,GAEnC,EAA8B,EAAS,EAElD,CAEA,eAAe,EACb,CAAe,CACf,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,wBAGlB,OAAM,EAAsB,CAC1B,IAAK,EACL,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,IACP,SAAU,CACR,CACE,KAAM,SACN,QAAS,kDACX,EACA,CAAE,KAAM,OAAQ,QAAS,EAAQ,SAAS,CAAC,EAAG,IAAK,EACpD,CACD,QAAQ,CACV,GACA,SACF,EACF,CAEA,eAAe,EACb,CAAe,CACf,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AAEb,AADC,AACL,MAAU,qBAGlB,IAAM,EAAQ,IACR,EAAU,GAEhB,OAAM,EAAsB,CAC1B,IAAK,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,2BAA2B,EAAE,EAAO,QAAQ,CAAC,CAC/E,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CAAC;;;;;;;;;;;;;;;;AAgBjB,EAAE,EAAQ,SAAS,CAAC,EAAG,KAAK;;+BAEG,CAAC,AACxB,EAAE,AACJ,EAAE,AACJ,WACA,CACF,EACF,CAEO,eAAe,EACpB,CAAe,CACf,CAAgC,EAEhC,IAAM,EAAW,UAEjB,CADA,QAAQ,GAAG,CAAC,qDAAsD,GACjD,UAAU,CAAvB,GACK,EAA8B,EAAS,GAEvC,EAAkC,EAAS,EAEtD,CA4BA,eAAe,EACb,CAAY,CACZ,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,wBAGlB,OAAM,EAAsB,CAC1B,IAAK,EACL,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,IACP,SAAU,CACR,CACE,KAAM,SACN,QAAS,CAAC;;;;;;;;;;;;;;;;;;;;iBAoBH,CAAC,AACV,EACA,CAAE,KAAM,OAAQ,QAAS,CAAK,EAC/B,CACD,QAAQ,CACV,WACA,CACF,EACF,CAEA,eAAe,EACb,CAAY,CACZ,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,qBAGlB,IAAM,EAAQ,IACR,EAAU,GAEhB,OAAM,EAAsB,CAC1B,IAAK,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,2BAA2B,EAAE,EAAO,QAAQ,CAAC,CAC/E,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CAAC;;;;;;;;;;;;;;;;;;;;;AAqBjB,EAAE,KAAK;;MAED,CAAC,AACC,EAAE,AACJ,EAAE,AACJ,GACA,SACF,EACF,CAEO,eAAe,EACpB,CAAY,CACZ,CAAgC,EAEhC,IAAM,EAAW,UAEjB,CADA,QAAQ,GAAG,CAAC,2CAA4C,GACvC,AAAb,UAAuB,IAClB,EAAuB,EAAM,GAE7B,EAA2B,EAAM,EAE5C,CAEA,eAAe,EACb,CAAY,CACZ,CAA+B,CAC/B,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MADW,AACD,AAFa,AAEjB,MAAU,yBAGlB,IAAI,EAAkB,GACtB,OAAQ,GACN,IAAK,KACH,EAAkB,qCAClB,KACF,KAAK,KACH,EAAkB,kDAClB,KACF,KAAK,KACH,EAAkB,6DAClB,KACF,KAAK,KACH,EAAkB,gDAEtB,CAEA,MAAM,EAAsB,CAC1B,IAAK,EACL,QAAS,CACP,eAAgB,mBAChB,cAAe,CAAC,OAAO,EAAE,EAAA,CAAQ,CACjC,eAAgB,wBAChB,UAAW,qBACb,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,MAAO,IACP,SAAU,CACR,CAAE,KAAM,SAAU,QAAS,CAAgB,EAC3C,CAAE,KAAM,OAAQ,QAAS,CAAK,EAC/B,CACD,QAAQ,CACV,WACA,CACF,EACF,CAEA,eAAe,EACb,CAAY,CACZ,CAA+B,CAC/B,CAAgC,EAEhC,IAAM,OACN,EADe,CACX,CAAC,EACH,MAFuB,AACZ,AACL,AAAI,MAAM,qBAGlB,IAAI,EAAkB,GACtB,OAAQ,GACN,IAAK,KACH,EAAkB,2FAClB,KACF,KAAK,KACH,EAAkB,yEAClB,KACF,KAAK,KACH,EAAkB,0EAClB,KACF,KAAK,KACH,EAAkB,sEAEtB,CAEA,IAAM,EAAQ,IACR,EAAU,GAEhB,OAAM,EAAsB,CAC1B,IAAK,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,2BAA2B,EAAE,EAAO,QAAQ,CAAC,CAC/E,QAAS,CACP,eAAgB,kBAClB,EACA,KAAM,KAAK,SAAS,CAAC,CACnB,SAAU,CAAC,CACT,MAAO,CAAC,CACN,KAAM,CAAA,EAAG,gBAAgB;AAAA;AAAI,EAAE,EAAA,CAAM,AACvC,EAAE,AACJ,EAAE,AACJ,WACA,CACF,EACF,CAEO,eAAe,EACpB,CAAY,CACZ,CAA+B,CAC/B,CAAgC,EAEhC,IAAM,EAAW,UAEjB,CADA,QAAQ,GAAG,CAAC,+CAAgD,GAC3C,UAAU,CAAvB,GACK,EAA2B,EAAM,EAAM,GAEvC,EAA+B,EAAM,EAAM,EAEtD,CAIO,eAAe,IACpB,GAAI,CACF,IAAM,EAAW,MAAM,MAAM,uCAC7B,GAAI,CAAC,EAAS,EAAE,CACd,CADgB,KACN,AAAJ,MAAU,yBAGlB,MAAO,CADM,MAAM,EAAS,IAAI,EAAA,EACpB,IAAI,CAAC,GAAG,CAAC,AAAC,IAAgB,CACpC,GADmC,AAC/B,EAAM,EAAE,CACZ,KAAM,EAAM,IAAI,EAAI,EAAM,EAAE,CAC9B,CAAC,CACH,CAAE,MAAO,EAAO,CAEd,OADA,QAAQ,KAAK,CAAC,sBAAuB,GAC9B,EACT,AADW,CAEb,CAGO,AALQ,eAKO,IACpB,OAAO,MANsC,EAM9B,OAAO,CAAC,CACrB,CAAE,GAAI,iBAAkB,KAAM,gBAAiB,EAC/C,CAAE,GAAI,mBAAoB,KAAM,kBAAmB,EACnD,CAAE,GAAI,uBAAwB,KAAM,+BAAgC,EACrE,CACH,CAwPO,eAAe,EAAa,CAAe,EAChD,IAkBI,EAlBE,EAAW,IAEX,EAAY,CAAC;;;;;;;;;;;;AAYrB,EAAE,QAAQ;;kBAEQ,CAAC,CAIjB,GAAiB,WAAb,EAAuB,CACzB,IAAM,EAnvCkC,KAovCxC,EADe,CACX,CAAC,EAAQ,MAAM,AAAI,AADE,MACI,qBAE7B,IAAM,EAAQ,IACR,EAAU,IACV,EAAW,MAAM,MAAM,CAAA,EAAG,EAAQ,UAAU,EAAE,EAAM,qBAAqB,EAAE,EAAA,CAAQ,CAAE,CACzF,OAAQ,OACR,QAAS,CAAE,eAAgB,kBAAmB,EAC9C,KAAM,KAAK,SAAS,CAAC,CAAE,SAAU,CAAC,CAAE,MAAO,CAAC,CAAE,KAAM,CAAU,EAAG,AAAD,EAAG,AAAC,EACtE,GAEA,GAAI,CAAC,EAAS,EAAE,CAGd,CAHgB,KAEhB,QAAQ,KAAK,CAAC,oBADI,CACiB,KADX,EAAS,IAAI,IAE/B,AAAI,MAAM,oBAGlB,EADa,AACD,OADO,EAAS,IAAI,EAAA,EACf,UAAU,CAAC,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC,IAClD,AADsD,MAEpD,CADK,CACO,MAAM,EAAe,CAC/B,CACE,KAAM,SACN,QAAS,CAAC,8LAA8L,CAAC,AAC3M,EACA,CAAE,KAAM,eAAQ,CAAQ,EACzB,EAGH,GAAI,CACF,IAAM,EAAY,EAAU,KAAK,CAAC,iBAClC,GAAI,EAAW,CACb,IAAM,EAAO,KAAK,KAAK,CAAC,CAAS,CAAC,EAAE,EACpC,OAAO,MAAM,OAAO,CAAC,GAAQ,EAAK,GAAG,CAAC,GAAO,OAAO,IAAQ,EAAE,AAChE,CAEA,OAAO,EAAU,KAAK,CAAC,KAAK,GAAG,CAAC,GAAO,EAAI,IAAI,IAAI,MAAM,CAAC,QAC5D,CAAE,MAAO,EAAG,CAGV,OAFA,QAAQ,KAAK,CAAC,wBAAyB,GAEhC,EAAU,KAAK,CAAC,KAAK,GAAG,CAAC,GAAO,EAAI,IAAI,IAAI,MAAM,CAAC,QAC5D,CACF,8BCxxCA,CAAA,CAAA,CAAA,CAAA,AAAM,CAAN,CAAM,CAAA,EAAe,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,AAAf,CAAA,AAAe,CAAA,AAAf,CAAe,CAAA,CAAA,CAAA,CAAA,CAAA,GAAA,EAAiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAbF,CAAC,AAakB,CAbjB,AAaiB,CAAA,CAAA,CAAA,CAAA,CAAA,CAbjB,AAaiB,CAbjB,AAAQ,AAaS,CAbT,AAAE,AAaO,CAAU,CAAA,AAbd,6BAAA,CAAA,AAA+B,CAAA,CAAA,CAAA,CAAA,AAAK,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAA,CAAU,CAAC,CAAA", "ignoreList": [1, 4, 7]}