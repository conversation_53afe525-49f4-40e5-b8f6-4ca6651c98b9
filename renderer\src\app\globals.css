@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 0 0% 0%;
    --card: 0 0% 98%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 98%;
    --popover-foreground: 0 0% 0%;
    --primary: 0 0% 0%;
    --primary-foreground: 0 0% 100%;
    --secondary: 0 0% 90%;
    --secondary-foreground: 0 0% 0%;
    --muted: 0 0% 85%;
    --muted-foreground: 0 0% 40%;
    --accent: 0 0% 90%;
    --accent-foreground: 0 0% 0%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 85%;
    --input: 0 0% 85%;
    --ring: 0 0% 0%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 0 0% 5%;
    --foreground: 0 0% 100%;
    --card: 0 0% 10%;
    --card-foreground: 0 0% 100%;
    --popover: 0 0% 10%;
    --popover-foreground: 0 0% 100%;
    --primary: 0 0% 100%;
    --primary-foreground: 0 0% 0%;
    --secondary: 0 0% 15%;
    --secondary-foreground: 0 0% 100%;
    --muted: 0 0% 20%;
    --muted-foreground: 0 0% 60%;
    --accent: 0 0% 15%;
    --accent-foreground: 0 0% 100%;
    --destructive: 0 84% 50%;
    --destructive-foreground: 0 0% 100%;
    --border: 0 0% 20%;
    --input: 0 0% 20%;
    --ring: 0 0% 100%;
  }
}

@layer base {
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }
}

@layer utilities {
  /* Custom scrollbar for sidebar */
  .sidebar-scrollbar::-webkit-scrollbar {
    width: 6px;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-track {
    background: transparent;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-thumb {
    background: hsl(var(--border));
    border-radius: 3px;
  }
  
  .sidebar-scrollbar::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--accent));
  }

  /* Line clamp utilities */
  .line-clamp-2 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
  }
  
  .line-clamp-3 {
    overflow: hidden;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
  }

  /* Improved backdrop blur */
  .backdrop-blur-custom {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
  }

  /* Sidebar note card hover effects */
  .note-card-hover {
    transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .note-card-hover:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px -2px hsl(var(--foreground) / 0.1);
  }

  /* Resizable handle custom styles */
  .resizable-handle-custom {
    position: relative;
    background: hsl(var(--border));
    transition: background-color 0.2s ease;
  }
  
  .resizable-handle-custom::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 1px;
    height: 20px;
    background: hsl(var(--muted-foreground) / 0.3);
  }
  
  .resizable-handle-custom:hover {
    background: hsl(var(--accent));
  }
  
  .resizable-handle-custom:hover::before {
    background: hsl(var(--muted-foreground) / 0.6);
  }

  /* Gradient text effect */
  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.7));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }
}
