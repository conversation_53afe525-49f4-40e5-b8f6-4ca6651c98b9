"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/unist-util-filter";
exports.ids = ["vendor-chunks/unist-util-filter"];
exports.modules = {

/***/ "(ssr)/./node_modules/unist-util-filter/lib/index.js":
/*!*****************************************************!*\
  !*** ./node_modules/unist-util-filter/lib/index.js ***!
  \*****************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   filter: () => (/* binding */ filter)\n/* harmony export */ });\n/* harmony import */ var unist_util_is__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! unist-util-is */ \"(ssr)/./node_modules/unist-util-is/lib/index.js\");\n/**\n * @typedef {import('unist').Node} Node\n * @typedef {import('unist').Parent} Parent\n *\n * @typedef {Exclude<import('unist-util-is').Test, undefined> | undefined} Test\n *   Test from `unist-util-is`.\n *\n *   Note: we have remove and add `undefined`, because otherwise when generating\n *   automatic `.d.ts` files, TS tries to flatten paths from a local perspective,\n *   which doesn’t work when publishing on npm.\n */\n\n/**\n * @typedef Options\n *   Configuration (optional).\n * @property {boolean | null | undefined} [cascade=true]\n *   Whether to drop parent nodes if they had children, but all their children\n *   were filtered out (default: `true`).\n */\n\n\n\nconst own = {}.hasOwnProperty\n\n/**\n * Create a new `tree` of copies of all nodes that pass `test`.\n *\n * The tree is walked in *preorder* (NLR), visiting the node itself, then its\n * head, etc.\n *\n * @template {Node} Tree\n * @template {Test} Check\n *\n * @overload\n * @param {Tree} tree\n * @param {Options | null | undefined} options\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {Check} test\n * @returns {import('./complex-types.js').Matches<Tree, Check>}\n *\n * @overload\n * @param {Tree} tree\n * @param {null | undefined} [options]\n * @returns {Tree}\n *\n * @param {Node} tree\n *   Tree to filter.\n * @param {Options | Test} [options]\n *   Configuration (optional).\n * @param {Test} [test]\n *   `unist-util-is` compatible test.\n * @returns {Node | undefined}\n *   New filtered tree.\n *\n *   `undefined` is returned if `tree` itself didn’t pass the test, or is\n *   cascaded away.\n */\nfunction filter(tree, options, test) {\n  const is = (0,unist_util_is__WEBPACK_IMPORTED_MODULE_0__.convert)(test || options)\n  const cascadeRaw =\n    options && typeof options === 'object' && 'cascade' in options\n      ? /** @type {boolean | null | undefined} */ (options.cascade)\n      : undefined\n  const cascade =\n    cascadeRaw === undefined || cascadeRaw === null ? true : cascadeRaw\n\n  return preorder(tree)\n\n  /**\n   * @param {Node} node\n   *   Current node.\n   * @param {number | undefined} [index]\n   *   Index of `node` in `parent`.\n   * @param {Parent | undefined} [parentNode]\n   *   Parent node.\n   * @returns {Node | undefined}\n   *   Shallow copy of `node`.\n   */\n  function preorder(node, index, parentNode) {\n    /** @type {Array<Node>} */\n    const children = []\n\n    if (!is(node, index, parentNode)) return undefined\n\n    if (parent(node)) {\n      let childIndex = -1\n\n      while (++childIndex < node.children.length) {\n        const result = preorder(node.children[childIndex], childIndex, node)\n\n        if (result) {\n          children.push(result)\n        }\n      }\n\n      if (cascade && node.children.length > 0 && children.length === 0) {\n        return undefined\n      }\n    }\n\n    // Create a shallow clone, using the new children.\n    /** @type {typeof node} */\n    // @ts-expect-error all the fields will be copied over.\n    const next = {}\n    /** @type {string} */\n    let key\n\n    for (key in node) {\n      if (own.call(node, key)) {\n        // @ts-expect-error: Looks like a record.\n        next[key] = key === 'children' ? children : node[key]\n      }\n    }\n\n    return next\n  }\n}\n\n/**\n * @param {Node} node\n * @returns {node is Parent}\n */\nfunction parent(node) {\n  return 'children' in node && node.children !== undefined\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdW5pc3QtdXRpbC1maWx0ZXIvbGliL2luZGV4LmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7QUFDQSxhQUFhLHNCQUFzQjtBQUNuQyxhQUFhLHdCQUF3QjtBQUNyQztBQUNBLGFBQWEsOERBQThEO0FBQzNFO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxjQUFjLDRCQUE0QjtBQUMxQztBQUNBO0FBQ0E7O0FBRXFDOztBQUVyQyxjQUFjOztBQUVkO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLGNBQWMsTUFBTTtBQUNwQixjQUFjLE1BQU07QUFDcEI7QUFDQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixXQUFXLDRCQUE0QjtBQUN2QyxXQUFXLE9BQU87QUFDbEIsYUFBYTtBQUNiO0FBQ0E7QUFDQSxXQUFXLE1BQU07QUFDakIsV0FBVyxPQUFPO0FBQ2xCLGFBQWE7QUFDYjtBQUNBO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCLFdBQVcsa0JBQWtCO0FBQzdCLGFBQWE7QUFDYjtBQUNBLFdBQVcsTUFBTTtBQUNqQjtBQUNBLFdBQVcsZ0JBQWdCO0FBQzNCO0FBQ0EsV0FBVyxNQUFNO0FBQ2pCO0FBQ0EsYUFBYTtBQUNiO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDTztBQUNQLGFBQWEsc0RBQU87QUFDcEI7QUFDQTtBQUNBLG1CQUFtQiw0QkFBNEI7QUFDL0M7QUFDQTtBQUNBOztBQUVBOztBQUVBO0FBQ0EsYUFBYSxNQUFNO0FBQ25CO0FBQ0EsYUFBYSxvQkFBb0I7QUFDakM7QUFDQSxhQUFhLG9CQUFvQjtBQUNqQztBQUNBLGVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQSxlQUFlLGFBQWE7QUFDNUI7O0FBRUE7O0FBRUE7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0EsZUFBZSxhQUFhO0FBQzVCO0FBQ0E7QUFDQSxlQUFlLFFBQVE7QUFDdkI7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBLFdBQVcsTUFBTTtBQUNqQixhQUFhO0FBQ2I7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcYW50aG9cXERvY3VtZW50c1xcYXVnbWVudC1wcm9qZWN0c1xcbXlub3RlXFxyZW5kZXJlclxcbm9kZV9tb2R1bGVzXFx1bmlzdC11dGlsLWZpbHRlclxcbGliXFxpbmRleC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEB0eXBlZGVmIHtpbXBvcnQoJ3VuaXN0JykuTm9kZX0gTm9kZVxuICogQHR5cGVkZWYge2ltcG9ydCgndW5pc3QnKS5QYXJlbnR9IFBhcmVudFxuICpcbiAqIEB0eXBlZGVmIHtFeGNsdWRlPGltcG9ydCgndW5pc3QtdXRpbC1pcycpLlRlc3QsIHVuZGVmaW5lZD4gfCB1bmRlZmluZWR9IFRlc3RcbiAqICAgVGVzdCBmcm9tIGB1bmlzdC11dGlsLWlzYC5cbiAqXG4gKiAgIE5vdGU6IHdlIGhhdmUgcmVtb3ZlIGFuZCBhZGQgYHVuZGVmaW5lZGAsIGJlY2F1c2Ugb3RoZXJ3aXNlIHdoZW4gZ2VuZXJhdGluZ1xuICogICBhdXRvbWF0aWMgYC5kLnRzYCBmaWxlcywgVFMgdHJpZXMgdG8gZmxhdHRlbiBwYXRocyBmcm9tIGEgbG9jYWwgcGVyc3BlY3RpdmUsXG4gKiAgIHdoaWNoIGRvZXNu4oCZdCB3b3JrIHdoZW4gcHVibGlzaGluZyBvbiBucG0uXG4gKi9cblxuLyoqXG4gKiBAdHlwZWRlZiBPcHRpb25zXG4gKiAgIENvbmZpZ3VyYXRpb24gKG9wdGlvbmFsKS5cbiAqIEBwcm9wZXJ0eSB7Ym9vbGVhbiB8IG51bGwgfCB1bmRlZmluZWR9IFtjYXNjYWRlPXRydWVdXG4gKiAgIFdoZXRoZXIgdG8gZHJvcCBwYXJlbnQgbm9kZXMgaWYgdGhleSBoYWQgY2hpbGRyZW4sIGJ1dCBhbGwgdGhlaXIgY2hpbGRyZW5cbiAqICAgd2VyZSBmaWx0ZXJlZCBvdXQgKGRlZmF1bHQ6IGB0cnVlYCkuXG4gKi9cblxuaW1wb3J0IHtjb252ZXJ0fSBmcm9tICd1bmlzdC11dGlsLWlzJ1xuXG5jb25zdCBvd24gPSB7fS5oYXNPd25Qcm9wZXJ0eVxuXG4vKipcbiAqIENyZWF0ZSBhIG5ldyBgdHJlZWAgb2YgY29waWVzIG9mIGFsbCBub2RlcyB0aGF0IHBhc3MgYHRlc3RgLlxuICpcbiAqIFRoZSB0cmVlIGlzIHdhbGtlZCBpbiAqcHJlb3JkZXIqIChOTFIpLCB2aXNpdGluZyB0aGUgbm9kZSBpdHNlbGYsIHRoZW4gaXRzXG4gKiBoZWFkLCBldGMuXG4gKlxuICogQHRlbXBsYXRlIHtOb2RlfSBUcmVlXG4gKiBAdGVtcGxhdGUge1Rlc3R9IENoZWNrXG4gKlxuICogQG92ZXJsb2FkXG4gKiBAcGFyYW0ge1RyZWV9IHRyZWVcbiAqIEBwYXJhbSB7T3B0aW9ucyB8IG51bGwgfCB1bmRlZmluZWR9IG9wdGlvbnNcbiAqIEBwYXJhbSB7Q2hlY2t9IHRlc3RcbiAqIEByZXR1cm5zIHtpbXBvcnQoJy4vY29tcGxleC10eXBlcy5qcycpLk1hdGNoZXM8VHJlZSwgQ2hlY2s+fVxuICpcbiAqIEBvdmVybG9hZFxuICogQHBhcmFtIHtUcmVlfSB0cmVlXG4gKiBAcGFyYW0ge0NoZWNrfSB0ZXN0XG4gKiBAcmV0dXJucyB7aW1wb3J0KCcuL2NvbXBsZXgtdHlwZXMuanMnKS5NYXRjaGVzPFRyZWUsIENoZWNrPn1cbiAqXG4gKiBAb3ZlcmxvYWRcbiAqIEBwYXJhbSB7VHJlZX0gdHJlZVxuICogQHBhcmFtIHtudWxsIHwgdW5kZWZpbmVkfSBbb3B0aW9uc11cbiAqIEByZXR1cm5zIHtUcmVlfVxuICpcbiAqIEBwYXJhbSB7Tm9kZX0gdHJlZVxuICogICBUcmVlIHRvIGZpbHRlci5cbiAqIEBwYXJhbSB7T3B0aW9ucyB8IFRlc3R9IFtvcHRpb25zXVxuICogICBDb25maWd1cmF0aW9uIChvcHRpb25hbCkuXG4gKiBAcGFyYW0ge1Rlc3R9IFt0ZXN0XVxuICogICBgdW5pc3QtdXRpbC1pc2AgY29tcGF0aWJsZSB0ZXN0LlxuICogQHJldHVybnMge05vZGUgfCB1bmRlZmluZWR9XG4gKiAgIE5ldyBmaWx0ZXJlZCB0cmVlLlxuICpcbiAqICAgYHVuZGVmaW5lZGAgaXMgcmV0dXJuZWQgaWYgYHRyZWVgIGl0c2VsZiBkaWRu4oCZdCBwYXNzIHRoZSB0ZXN0LCBvciBpc1xuICogICBjYXNjYWRlZCBhd2F5LlxuICovXG5leHBvcnQgZnVuY3Rpb24gZmlsdGVyKHRyZWUsIG9wdGlvbnMsIHRlc3QpIHtcbiAgY29uc3QgaXMgPSBjb252ZXJ0KHRlc3QgfHwgb3B0aW9ucylcbiAgY29uc3QgY2FzY2FkZVJhdyA9XG4gICAgb3B0aW9ucyAmJiB0eXBlb2Ygb3B0aW9ucyA9PT0gJ29iamVjdCcgJiYgJ2Nhc2NhZGUnIGluIG9wdGlvbnNcbiAgICAgID8gLyoqIEB0eXBlIHtib29sZWFuIHwgbnVsbCB8IHVuZGVmaW5lZH0gKi8gKG9wdGlvbnMuY2FzY2FkZSlcbiAgICAgIDogdW5kZWZpbmVkXG4gIGNvbnN0IGNhc2NhZGUgPVxuICAgIGNhc2NhZGVSYXcgPT09IHVuZGVmaW5lZCB8fCBjYXNjYWRlUmF3ID09PSBudWxsID8gdHJ1ZSA6IGNhc2NhZGVSYXdcblxuICByZXR1cm4gcHJlb3JkZXIodHJlZSlcblxuICAvKipcbiAgICogQHBhcmFtIHtOb2RlfSBub2RlXG4gICAqICAgQ3VycmVudCBub2RlLlxuICAgKiBAcGFyYW0ge251bWJlciB8IHVuZGVmaW5lZH0gW2luZGV4XVxuICAgKiAgIEluZGV4IG9mIGBub2RlYCBpbiBgcGFyZW50YC5cbiAgICogQHBhcmFtIHtQYXJlbnQgfCB1bmRlZmluZWR9IFtwYXJlbnROb2RlXVxuICAgKiAgIFBhcmVudCBub2RlLlxuICAgKiBAcmV0dXJucyB7Tm9kZSB8IHVuZGVmaW5lZH1cbiAgICogICBTaGFsbG93IGNvcHkgb2YgYG5vZGVgLlxuICAgKi9cbiAgZnVuY3Rpb24gcHJlb3JkZXIobm9kZSwgaW5kZXgsIHBhcmVudE5vZGUpIHtcbiAgICAvKiogQHR5cGUge0FycmF5PE5vZGU+fSAqL1xuICAgIGNvbnN0IGNoaWxkcmVuID0gW11cblxuICAgIGlmICghaXMobm9kZSwgaW5kZXgsIHBhcmVudE5vZGUpKSByZXR1cm4gdW5kZWZpbmVkXG5cbiAgICBpZiAocGFyZW50KG5vZGUpKSB7XG4gICAgICBsZXQgY2hpbGRJbmRleCA9IC0xXG5cbiAgICAgIHdoaWxlICgrK2NoaWxkSW5kZXggPCBub2RlLmNoaWxkcmVuLmxlbmd0aCkge1xuICAgICAgICBjb25zdCByZXN1bHQgPSBwcmVvcmRlcihub2RlLmNoaWxkcmVuW2NoaWxkSW5kZXhdLCBjaGlsZEluZGV4LCBub2RlKVxuXG4gICAgICAgIGlmIChyZXN1bHQpIHtcbiAgICAgICAgICBjaGlsZHJlbi5wdXNoKHJlc3VsdClcbiAgICAgICAgfVxuICAgICAgfVxuXG4gICAgICBpZiAoY2FzY2FkZSAmJiBub2RlLmNoaWxkcmVuLmxlbmd0aCA+IDAgJiYgY2hpbGRyZW4ubGVuZ3RoID09PSAwKSB7XG4gICAgICAgIHJldHVybiB1bmRlZmluZWRcbiAgICAgIH1cbiAgICB9XG5cbiAgICAvLyBDcmVhdGUgYSBzaGFsbG93IGNsb25lLCB1c2luZyB0aGUgbmV3IGNoaWxkcmVuLlxuICAgIC8qKiBAdHlwZSB7dHlwZW9mIG5vZGV9ICovXG4gICAgLy8gQHRzLWV4cGVjdC1lcnJvciBhbGwgdGhlIGZpZWxkcyB3aWxsIGJlIGNvcGllZCBvdmVyLlxuICAgIGNvbnN0IG5leHQgPSB7fVxuICAgIC8qKiBAdHlwZSB7c3RyaW5nfSAqL1xuICAgIGxldCBrZXlcblxuICAgIGZvciAoa2V5IGluIG5vZGUpIHtcbiAgICAgIGlmIChvd24uY2FsbChub2RlLCBrZXkpKSB7XG4gICAgICAgIC8vIEB0cy1leHBlY3QtZXJyb3I6IExvb2tzIGxpa2UgYSByZWNvcmQuXG4gICAgICAgIG5leHRba2V5XSA9IGtleSA9PT0gJ2NoaWxkcmVuJyA/IGNoaWxkcmVuIDogbm9kZVtrZXldXG4gICAgICB9XG4gICAgfVxuXG4gICAgcmV0dXJuIG5leHRcbiAgfVxufVxuXG4vKipcbiAqIEBwYXJhbSB7Tm9kZX0gbm9kZVxuICogQHJldHVybnMge25vZGUgaXMgUGFyZW50fVxuICovXG5mdW5jdGlvbiBwYXJlbnQobm9kZSkge1xuICByZXR1cm4gJ2NoaWxkcmVuJyBpbiBub2RlICYmIG5vZGUuY2hpbGRyZW4gIT09IHVuZGVmaW5lZFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/unist-util-filter/lib/index.js\n");

/***/ })

};
;