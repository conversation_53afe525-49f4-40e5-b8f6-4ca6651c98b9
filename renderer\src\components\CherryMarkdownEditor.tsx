"use client";

import { useEffect, useRef, useImperativeHandle, forwardRef, useState } from "react";
import { useTheme } from "next-themes";
import dynamic from "next/dynamic";

// Fallback to @uiw/react-md-editor if <PERSON> fails
const MDEditor = dynamic(
  () => import("@uiw/react-md-editor").then((mod) => mod.default),
  { ssr: false }
);

interface CherryMarkdownEditorProps {
  value: string;
  onChange: (value: string | undefined) => void;
  preview?: "edit" | "preview" | "live";
  hideToolbar?: boolean;
  visibleDragbar?: boolean;
  className?: string;
  "data-color-mode"?: string;
  previewOptions?: any;
  onSelectionChange?: (selectedText: string) => void;
}

export interface CherryMarkdownEditorRef {
  getMarkdown: () => string;
  setMarkdown: (value: string) => void;
  getSelection: () => string;
  focus: () => void;
}

const CherryMarkdownEditor = forwardRef<CherryMarkdownEditorRef, CherryMarkdownEditorProps>(
  ({
    value,
    onChange,
    preview = "live",
    hideToolbar = false,
    visibleDragbar = false,
    className = "",
    "data-color-mode": dataColorMode,
    previewOptions,
    onSelectionChange
  }, ref) => {
    const containerRef = useRef<HTMLDivElement>(null);
    const cherryRef = useRef<any>(null);
    const [isClient, setIsClient] = useState(false);
    const [CherryClass, setCherryClass] = useState<any>(null);
    const [cherryLoadFailed, setCherryLoadFailed] = useState(false);
    const { theme } = useTheme();

    // Determine the effective theme
    const effectiveTheme = dataColorMode || (theme === "dark" ? "dark" : "light");

    // Ensure only runs on client side
    useEffect(() => {
      setIsClient(true);

      // Set timeout for Cherry loading, fallback to MDEditor if it takes too long
      const timeout = setTimeout(() => {
        console.log("Cherry loading timeout, using fallback editor");
        setCherryLoadFailed(true);
      }, 5000); // 5 second timeout

      // Dynamic import of Cherry Markdown
      import("cherry-markdown")
        .then((Cherry) => {
          console.log("Cherry Markdown loaded successfully");
          setCherryClass(Cherry.default || Cherry);
          clearTimeout(timeout);
        })
        .catch((error) => {
          console.error("Failed to load Cherry Markdown:", error);
          setCherryLoadFailed(true);
          clearTimeout(timeout);
        });

      return () => {
        clearTimeout(timeout);
      };
    }, []);

    // Expose methods via ref
    useImperativeHandle(ref, () => ({
      getMarkdown: () => {
        if (cherryRef.current) {
          return cherryRef.current.getMarkdown();
        }
        return value;
      },
      setMarkdown: (newValue: string) => {
        if (cherryRef.current) {
          cherryRef.current.setMarkdown(newValue);
        }
      },
      getSelection: () => {
        if (cherryRef.current) {
          return cherryRef.current.getSelection() || "";
        }
        return window.getSelection()?.toString() || "";
      },
      focus: () => {
        if (cherryRef.current) {
          cherryRef.current.focus();
        }
      }
    }), [value]);

    // Handle text selection for AI features
    useEffect(() => {
      const handleSelection = () => {
        const selectedText = window.getSelection()?.toString() || "";
        if (onSelectionChange) {
          onSelectionChange(selectedText);
        }
      };

      document.addEventListener("mouseup", handleSelection);
      document.addEventListener("keyup", handleSelection);

      return () => {
        document.removeEventListener("mouseup", handleSelection);
        document.removeEventListener("keyup", handleSelection);
      };
    }, [onSelectionChange]);

    // Initialize Cherry Markdown
    useEffect(() => {
      if (!isClient || !CherryClass || !containerRef.current) {
        return;
      }

      console.log("Initializing Cherry Markdown...");

      // Destroy existing instance
      if (cherryRef.current) {
        try {
          cherryRef.current.destroy?.();
        } catch (error) {
          console.warn("Error destroying Cherry instance:", error);
        }
        cherryRef.current = null;
      }

      // Clear container
      containerRef.current.innerHTML = '';

      try {
        // Cherry Markdown configuration
        const cherryConfig = {
          id: containerRef.current,
          value: value || "",
          editor: {
            defaultModel: preview === 'preview' ? 'previewOnly' :
                         preview === 'edit' ? 'editOnly' : 'edit&preview',
            height: '100%',
            width: '100%',
            autoHeight: false,
            autoWidth: false,
            codemirror: {
              lineNumbers: true,
              lineWrapping: true,
              theme: effectiveTheme === 'dark' ? 'material-darker' : 'default',
              viewportMargin: 0,
              scrollbarStyle: 'native',
            }
          },
          previewer: {
            dom: false,
            className: 'cherry-previewer',
            enablePreviewerBubble: false,
            lazyLoadImg: {
              loadingImgPath: '',
              maxNumPerTime: 2,
              noLoadImgNum: 5,
              autoLoadImgNum: 5,
              maxTryTimesPerSrc: 2,
              loadingImgMaxWaitTime: 5000,
            },
          },
          toolbars: hideToolbar ? {
            toolbar: false,
            bubble: false,
            float: false,
            sidebar: false,
          } : {
            toolbar: [
              'bold', 'italic', 'strikethrough', '|',
              'header', 'list', 'quote', 'hr', '|',
              'link', 'image', 'code', 'table', '|',
              'undo', 'redo'
            ],
          },
          callback: {
            afterChange: (markdown: string) => {
              if (onChange) {
                onChange(markdown);
              }
            },
            afterInit: () => {
              console.log("Cherry initialized successfully");
              // Apply theme styling
              const container = containerRef.current;
              if (container) {
                container.setAttribute('data-color-mode', effectiveTheme);
                container.classList.add(effectiveTheme === 'dark' ? 'theme__dark' : 'theme__default');
              }
            },
          },
        };

        // Create Cherry instance
        cherryRef.current = new CherryClass(cherryConfig);
        console.log("Cherry instance created successfully");

      } catch (error) {
        console.error("Error initializing Cherry Markdown:", error);
        setCherryLoadFailed(true);
      }
    }, [isClient, CherryClass, preview, hideToolbar, effectiveTheme]);

    // Update value when prop changes
    useEffect(() => {
      if (cherryRef.current && value !== undefined) {
        const currentValue = cherryRef.current.getMarkdown();
        if (currentValue !== value) {
          cherryRef.current.setMarkdown(value);
        }
      }
    }, [value]);

    // Cleanup on unmount
    useEffect(() => {
      return () => {
        if (cherryRef.current) {
          try {
            cherryRef.current.destroy?.();
          } catch (error) {
            console.warn("Error destroying Cherry instance on unmount:", error);
          }
        }
      };
    }, []);

    // If Cherry failed to load or is not available, use MDEditor as fallback
    if (!CherryClass || cherryLoadFailed) {
      return (
        <div
          className={`cherry-markdown-editor-fallback ${className}`}
          style={{
            height: "100%",
            width: "100%",
            maxHeight: "100%",
            display: "flex",
            flexDirection: "column",
            position: "relative",
            overflow: "hidden",
          }}
        >
          <MDEditor
            value={value}
            onChange={onChange}
            preview={preview === "preview" ? "preview" : preview === "edit" ? "edit" : "live"}
            hideToolbar={hideToolbar}
            visibleDragbar={visibleDragbar}
            data-color-mode={effectiveTheme as "dark" | "light"}
            previewOptions={previewOptions}
            className={className}
          />
        </div>
      );
    }

    // Loading state
    if (!isClient) {
      return (
        <div className={`cherry-markdown-editor-loading ${className}`}>
          <div>Loading editor...</div>
        </div>
      );
    }

    return (
      <div
        ref={containerRef}
        className={`cherry-markdown-editor ${className}`}
        style={{
          height: "100%",
          width: "100%",
          maxHeight: "100%",
          display: "flex",
          flexDirection: "column",
          position: "relative",
          overflow: "hidden",
        }}
        data-color-mode={effectiveTheme}
      />
    );
  }
);

CherryMarkdownEditor.displayName = "CherryMarkdownEditor";

export default CherryMarkdownEditor;