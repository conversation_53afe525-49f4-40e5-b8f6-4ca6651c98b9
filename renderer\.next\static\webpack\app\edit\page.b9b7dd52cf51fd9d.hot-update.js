"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/edit/page",{

/***/ "(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx":
/*!*************************************************!*\
  !*** ./src/components/CherryMarkdownEditor.tsx ***!
  \*************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\nconst CherryMarkdownEditor = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.forwardRef)(_c = _s((param, ref)=>{\n    let { value, onChange, preview = \"live\", hideToolbar = false, className = \"\", onSelectionChange } = param;\n    _s();\n    const containerRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const cherryRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [isClient, setIsClient] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [CherryClass, setCherryClass] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const { theme } = (0,next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme)();\n    // 確保只在客戶端運行\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            setIsClient(true);\n            // 動態導入 Cherry Markdown\n            const loadCherry = {\n                \"CherryMarkdownEditor.useEffect.loadCherry\": async ()=>{\n                    try {\n                        const CherryModule = await __webpack_require__.e(/*! import() */ \"_app-pages-browser_node_modules_cherry-markdown_dist_cherry-markdown_esm_js\").then(__webpack_require__.bind(__webpack_require__, /*! cherry-markdown */ \"(app-pages-browser)/./node_modules/cherry-markdown/dist/cherry-markdown.esm.js\"));\n                        const CherryMarkdown = CherryModule.default || CherryModule;\n                        // CSS 需要在全域載入，不用動態導入\n                        if (typeof CherryMarkdown === 'function') {\n                            // 使用函數式更新，避免 React 嘗試執行 Class\n                            setCherryClass({\n                                \"CherryMarkdownEditor.useEffect.loadCherry\": ()=>CherryMarkdown\n                            }[\"CherryMarkdownEditor.useEffect.loadCherry\"]);\n                        } else {\n                            console.error(\"Failed to load Cherry Markdown: not a constructor\", CherryMarkdown);\n                        }\n                    } catch (error) {\n                        console.error(\"Failed to load Cherry Markdown. Raw error object:\", error);\n                        if (error instanceof Error) {\n                            console.error(\"Error name:\", error.name);\n                            console.error(\"Error message:\", error.message);\n                            console.error(\"Error stack:\", error.stack);\n                        } else {\n                            console.error(\"The thrown object was not an Error instance. It is:\", JSON.stringify(error, null, 2));\n                        }\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.loadCherry\"];\n            loadCherry();\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useImperativeHandle)(ref, {\n        \"CherryMarkdownEditor.useImperativeHandle\": ()=>({\n                getMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        var _cherryRef_current;\n                        return ((_cherryRef_current = cherryRef.current) === null || _cherryRef_current === void 0 ? void 0 : _cherryRef_current.getMarkdown()) || \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                setMarkdown: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": (value)=>{\n                        if (cherryRef.current) {\n                            cherryRef.current.setMarkdown(value);\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                getSelection: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (false) {}\n                        const selection = window.getSelection();\n                        return selection ? selection.toString() : \"\";\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"],\n                focus: ({\n                    \"CherryMarkdownEditor.useImperativeHandle\": ()=>{\n                        if (containerRef.current) {\n                            const editor = containerRef.current.querySelector('.CodeMirror');\n                            if (editor) {\n                                var _editor_CodeMirror;\n                                (_editor_CodeMirror = editor.CodeMirror) === null || _editor_CodeMirror === void 0 ? void 0 : _editor_CodeMirror.focus();\n                            }\n                        }\n                    }\n                })[\"CherryMarkdownEditor.useImperativeHandle\"]\n            })\n    }[\"CherryMarkdownEditor.useImperativeHandle\"]);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient || !CherryClass || !containerRef.current) return;\n            // 銷毀現有實例\n            if (cherryRef.current) {\n                var _cherryRef_current_destroy, _cherryRef_current;\n                (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                cherryRef.current = null;\n            }\n            // 清空容器\n            containerRef.current.innerHTML = '';\n            // 基本配置\n            const cherryConfig = {\n                id: containerRef.current,\n                value: value,\n                editor: {\n                    defaultModel: preview === 'preview' ? 'previewOnly' : preview === 'edit' ? 'editOnly' : 'edit&preview',\n                    height: '100%',\n                    autoHeight: false,\n                    codemirror: {\n                        lineNumbers: true,\n                        lineWrapping: true,\n                        theme: theme === 'dark' ? 'material-darker' : 'default'\n                    }\n                },\n                previewer: {\n                    dom: false,\n                    className: 'cherry-previewer',\n                    enablePreviewerBubble: false\n                },\n                toolbars: hideToolbar ? {\n                    toolbar: false,\n                    bubble: false,\n                    float: false,\n                    sidebar: false\n                } : {\n                    toolbar: [\n                        'bold',\n                        'italic',\n                        'strikethrough',\n                        '|',\n                        'header',\n                        'list',\n                        'quote',\n                        'hr',\n                        '|',\n                        'link',\n                        'image',\n                        'code',\n                        'table',\n                        '|',\n                        'undo',\n                        'redo'\n                    ]\n                },\n                callback: {\n                    afterChange: {\n                        \"CherryMarkdownEditor.useEffect\": (markdown)=>{\n                            if (onChange) {\n                                onChange(markdown);\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"],\n                    afterInit: {\n                        \"CherryMarkdownEditor.useEffect\": ()=>{\n                            // 設置樣式\n                            const container = containerRef.current;\n                            if (container) {\n                                container.setAttribute('data-color-mode', theme === \"dark\" ? 'dark' : 'light');\n                                // 確保編輯器高度正確並且不會溢出\n                                const cherryInstance = cherryRef.current;\n                                if (cherryInstance) {\n                                    // 強制設置容器樣式\n                                    const cherryElement = container.querySelector('.cherry');\n                                    if (cherryElement) {\n                                        cherryElement.style.position = 'relative';\n                                        cherryElement.style.height = '100%';\n                                        cherryElement.style.maxHeight = '100%';\n                                        cherryElement.style.overflow = 'hidden';\n                                    }\n                                    // 刷新編輯器\n                                    if (cherryInstance.editor) {\n                                        setTimeout({\n                                            \"CherryMarkdownEditor.useEffect\": ()=>{\n                                                cherryInstance.editor.refresh();\n                                            }\n                                        }[\"CherryMarkdownEditor.useEffect\"], 100);\n                                    }\n                                }\n                            }\n                        }\n                    }[\"CherryMarkdownEditor.useEffect\"]\n                }\n            };\n            try {\n                cherryRef.current = new CherryClass(cherryConfig);\n            } catch (error) {\n                console.error('Failed to initialize Cherry Markdown:', error);\n            }\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    if (cherryRef.current) {\n                        var _cherryRef_current_destroy, _cherryRef_current;\n                        (_cherryRef_current_destroy = (_cherryRef_current = cherryRef.current).destroy) === null || _cherryRef_current_destroy === void 0 ? void 0 : _cherryRef_current_destroy.call(_cherryRef_current);\n                        cherryRef.current = null;\n                    }\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        CherryClass,\n        hideToolbar,\n        preview,\n        theme\n    ]);\n    // 當 value 從外部更新時，同步到編輯器\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (cherryRef.current && cherryRef.current.getMarkdown() !== value) {\n                cherryRef.current.setMarkdown(value);\n            }\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        value\n    ]);\n    // 處理選擇變更\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"CherryMarkdownEditor.useEffect\": ()=>{\n            if (!isClient) return;\n            const handleSelection = {\n                \"CherryMarkdownEditor.useEffect.handleSelection\": ()=>{\n                    var _containerRef_current;\n                    const selection = window.getSelection();\n                    const selectedText = selection ? selection.toString() : \"\";\n                    // 檢查選取的文字是否在編輯器內部\n                    if ((selection === null || selection === void 0 ? void 0 : selection.anchorNode) && ((_containerRef_current = containerRef.current) === null || _containerRef_current === void 0 ? void 0 : _containerRef_current.contains(selection.anchorNode))) {\n                        if (onSelectionChange) {\n                            onSelectionChange(selectedText);\n                        }\n                    } else if (onSelectionChange) {\n                        onSelectionChange(\"\");\n                    }\n                }\n            }[\"CherryMarkdownEditor.useEffect.handleSelection\"];\n            document.addEventListener(\"mouseup\", handleSelection);\n            document.addEventListener(\"keyup\", handleSelection);\n            document.addEventListener(\"selectionchange\", handleSelection);\n            return ({\n                \"CherryMarkdownEditor.useEffect\": ()=>{\n                    document.removeEventListener(\"mouseup\", handleSelection);\n                    document.removeEventListener(\"keyup\", handleSelection);\n                    document.removeEventListener(\"selectionchange\", handleSelection);\n                }\n            })[\"CherryMarkdownEditor.useEffect\"];\n        }\n    }[\"CherryMarkdownEditor.useEffect\"], [\n        isClient,\n        onSelectionChange\n    ]);\n    // 如果在服務端或還未載入，顯示載入訊息\n    if (!isClient || !CherryClass) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"cherry-markdown-editor \".concat(className),\n            style: {\n                height: \"100%\",\n                border: \"1px solid hsl(var(--border))\",\n                borderRadius: \"6px\",\n                display: \"flex\",\n                alignItems: \"center\",\n                justifyContent: \"center\",\n                backgroundColor: \"hsl(var(--background))\"\n            },\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                children: \"載入編輯器中...\"\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n                lineNumber: 230,\n                columnNumber: 11\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n            lineNumber: 218,\n            columnNumber: 9\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: containerRef,\n        className: \"cherry-markdown-editor \".concat(className),\n        style: {\n            height: \"100%\",\n            width: \"100%\",\n            minHeight: \"400px\",\n            maxHeight: \"100%\",\n            display: \"flex\",\n            flexDirection: \"column\",\n            position: \"relative\",\n            overflow: \"hidden\",\n            contain: \"layout style\"\n        },\n        \"data-color-mode\": theme === \"dark\" ? \"dark\" : \"light\"\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Documents\\\\augment-projects\\\\mynote\\\\renderer\\\\src\\\\components\\\\CherryMarkdownEditor.tsx\",\n        lineNumber: 236,\n        columnNumber: 7\n    }, undefined);\n}, \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n})), \"GV42Qi6L+ZgtDj6CqO68R2gy41Q=\", false, function() {\n    return [\n        next_themes__WEBPACK_IMPORTED_MODULE_2__.useTheme\n    ];\n});\n_c1 = CherryMarkdownEditor;\nCherryMarkdownEditor.displayName = \"CherryMarkdownEditor\";\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (CherryMarkdownEditor);\nvar _c, _c1;\n$RefreshReg$(_c, \"CherryMarkdownEditor$forwardRef\");\n$RefreshReg$(_c1, \"CherryMarkdownEditor\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/CherryMarkdownEditor.tsx\n"));

/***/ })

});