module.exports=[68389,a=>{"use strict";a.s(["AlertDialog",()=>E,"AlertDialogAction",()=>L,"AlertDialogCancel",()=>M,"AlertDialogContent",()=>G,"AlertDialogDescription",()=>K,"AlertDialogFooter",()=>I,"AlertDialogHeader",()=>H,"AlertDialogTitle",()=>J],68389);var b=a.i(28386),c=a.i(54436),d=a.i(33616),e=a.i(42527),f=a.i(19988),g=a.i(64161),h=a.i(57495),i="AlertDialog",[j,k]=(0,d.createContextScope)(i,[f.createDialogScope]),l=(0,f.createDialogScope)(),m=a=>{let{__scopeAlertDialog:c,...d}=a,e=l(c);return(0,b.jsx)(f.Root,{...e,...d,modal:!0})};m.displayName=i,c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...e}=a,g=l(d);return(0,b.jsx)(f.Trigger,{...g,...e,ref:c})}).displayName="AlertDialogTrigger";var n=a=>{let{__scopeAlertDialog:c,...d}=a,e=l(c);return(0,b.jsx)(f.Portal,{...e,...d})};n.displayName="AlertDialogPortal";var o=c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...e}=a,g=l(d);return(0,b.jsx)(f.Overlay,{...g,...e,ref:c})});o.displayName="AlertDialogOverlay";var p="AlertDialogContent",[q,r]=j(p),s=(0,h.createSlottable)("AlertDialogContent"),t=c.forwardRef((a,d)=>{let{__scopeAlertDialog:h,children:i,...j}=a,k=l(h),m=c.useRef(null),n=(0,e.useComposedRefs)(d,m),o=c.useRef(null);return(0,b.jsx)(f.WarningProvider,{contentName:p,titleName:u,docsSlug:"alert-dialog",children:(0,b.jsx)(q,{scope:h,cancelRef:o,children:(0,b.jsxs)(f.Content,{role:"alertdialog",...k,...j,ref:n,onOpenAutoFocus:(0,g.composeEventHandlers)(j.onOpenAutoFocus,a=>{a.preventDefault(),o.current?.focus({preventScroll:!0})}),onPointerDownOutside:a=>a.preventDefault(),onInteractOutside:a=>a.preventDefault(),children:[(0,b.jsx)(s,{children:i}),(0,b.jsx)(B,{contentRef:m})]})})})});t.displayName=p;var u="AlertDialogTitle",v=c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...e}=a,g=l(d);return(0,b.jsx)(f.Title,{...g,...e,ref:c})});v.displayName=u;var w="AlertDialogDescription",x=c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...e}=a,g=l(d);return(0,b.jsx)(f.Description,{...g,...e,ref:c})});x.displayName=w;var y=c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...e}=a,g=l(d);return(0,b.jsx)(f.Close,{...g,...e,ref:c})});y.displayName="AlertDialogAction";var z="AlertDialogCancel",A=c.forwardRef((a,c)=>{let{__scopeAlertDialog:d,...g}=a,{cancelRef:h}=r(z,d),i=l(d),j=(0,e.useComposedRefs)(c,h);return(0,b.jsx)(f.Close,{...i,...g,ref:j})});A.displayName=z;var B=({contentRef:a})=>{let b=`\`${p}\` requires a description for the component to be accessible for screen reader users.

You can add a description to the \`${p}\` by passing a \`${w}\` component as a child, which also benefits sighted users by adding visible context to the dialog.

Alternatively, you can use your own component as a description by assigning it an \`id\` and passing the same value to the \`aria-describedby\` prop in \`${p}\`. If the description is confusing or duplicative for sighted users, you can use the \`@radix-ui/react-visually-hidden\` primitive as a wrapper around your description component.

For more information, see https://radix-ui.com/primitives/docs/components/alert-dialog`;return c.useEffect(()=>{document.getElementById(a.current?.getAttribute("aria-describedby"))||console.warn(b)},[b,a]),null},C=a.i(90334),D=a.i(11105);let E=m,F=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(o,{className:(0,C.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",a),...c,ref:d}));F.displayName=o.displayName;let G=c.forwardRef(({className:a,...c},d)=>(0,b.jsxs)(n,{children:[(0,b.jsx)(F,{}),(0,b.jsx)(t,{ref:d,className:(0,C.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border border-border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",a),...c})]}));G.displayName=t.displayName;let H=({className:a,...c})=>(0,b.jsx)("div",{className:(0,C.cn)("flex flex-col space-y-2 text-center sm:text-left",a),...c});H.displayName="AlertDialogHeader";let I=({className:a,...c})=>(0,b.jsx)("div",{className:(0,C.cn)("flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2",a),...c});I.displayName="AlertDialogFooter";let J=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(v,{ref:d,className:(0,C.cn)("text-lg font-semibold",a),...c}));J.displayName=v.displayName;let K=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(x,{ref:d,className:(0,C.cn)("text-sm text-muted-foreground",a),...c}));K.displayName=x.displayName;let L=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(y,{ref:d,className:(0,C.cn)((0,D.buttonVariants)(),a),...c}));L.displayName=y.displayName;let M=c.forwardRef(({className:a,...c},d)=>(0,b.jsx)(A,{ref:d,className:(0,C.cn)((0,D.buttonVariants)({variant:"outline"}),"mt-2 sm:mt-0",a),...c}));M.displayName=A.displayName},43503,a=>{"use strict";function b(a,[b,c]){return Math.min(c,Math.max(b,a))}a.s(["clamp",()=>b])},56217,a=>{"use strict";a.s(["useDirection",()=>d]);var b=a.i(54436);a.i(28386);var c=b.createContext(void 0);function d(a){let d=b.useContext(c);return a||d||"ltr"}},74797,a=>{"use strict";a.s(["Check",()=>b],74797);let b=(0,a.i(93007).default)("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},85718,a=>{"use strict";a.s(["Item",()=>D,"Root",()=>C,"createRovingFocusGroupScope",()=>t]);var b=a.i(54436),c=a.i(64161),d=a.i(94120),e=a.i(42527),f=a.i(33616),g=a.i(73490),h=a.i(40686),i=a.i(98445),j=a.i(9851),k=a.i(56217),l=a.i(28386),m="rovingFocusGroup.onEntryFocus",n={bubbles:!1,cancelable:!0},o="RovingFocusGroup",[p,q,r]=(0,d.createCollection)(o),[s,t]=(0,f.createContextScope)(o,[r]),[u,v]=s(o),w=b.forwardRef((a,b)=>(0,l.jsx)(p.Provider,{scope:a.__scopeRovingFocusGroup,children:(0,l.jsx)(p.Slot,{scope:a.__scopeRovingFocusGroup,children:(0,l.jsx)(x,{...a,ref:b})})}));w.displayName=o;var x=b.forwardRef((a,d)=>{let{__scopeRovingFocusGroup:f,orientation:g,loop:p=!1,dir:r,currentTabStopId:s,defaultCurrentTabStopId:t,onCurrentTabStopIdChange:v,onEntryFocus:w,preventScrollOnEntryFocus:x=!1,...y}=a,z=b.useRef(null),A=(0,e.useComposedRefs)(d,z),C=(0,k.useDirection)(r),[D,E]=(0,j.useControllableState)({prop:s,defaultProp:t??null,onChange:v,caller:o}),[F,G]=b.useState(!1),H=(0,i.useCallbackRef)(w),I=q(f),J=b.useRef(!1),[K,L]=b.useState(0);return b.useEffect(()=>{let a=z.current;if(a)return a.addEventListener(m,H),()=>a.removeEventListener(m,H)},[H]),(0,l.jsx)(u,{scope:f,orientation:g,dir:C,loop:p,currentTabStopId:D,onItemFocus:b.useCallback(a=>E(a),[E]),onItemShiftTab:b.useCallback(()=>G(!0),[]),onFocusableItemAdd:b.useCallback(()=>L(a=>a+1),[]),onFocusableItemRemove:b.useCallback(()=>L(a=>a-1),[]),children:(0,l.jsx)(h.Primitive.div,{tabIndex:F||0===K?-1:0,"data-orientation":g,...y,ref:A,style:{outline:"none",...a.style},onMouseDown:(0,c.composeEventHandlers)(a.onMouseDown,()=>{J.current=!0}),onFocus:(0,c.composeEventHandlers)(a.onFocus,a=>{let b=!J.current;if(a.target===a.currentTarget&&b&&!F){let b=new CustomEvent(m,n);if(a.currentTarget.dispatchEvent(b),!b.defaultPrevented){let a=I().filter(a=>a.focusable);B([a.find(a=>a.active),a.find(a=>a.id===D),...a].filter(Boolean).map(a=>a.ref.current),x)}}J.current=!1}),onBlur:(0,c.composeEventHandlers)(a.onBlur,()=>G(!1))})})}),y="RovingFocusGroupItem",z=b.forwardRef((a,d)=>{let{__scopeRovingFocusGroup:e,focusable:f=!0,active:i=!1,tabStopId:j,children:k,...m}=a,n=(0,g.useId)(),o=j||n,r=v(y,e),s=r.currentTabStopId===o,t=q(e),{onFocusableItemAdd:u,onFocusableItemRemove:w,currentTabStopId:x}=r;return b.useEffect(()=>{if(f)return u(),()=>w()},[f,u,w]),(0,l.jsx)(p.ItemSlot,{scope:e,id:o,focusable:f,active:i,children:(0,l.jsx)(h.Primitive.span,{tabIndex:s?0:-1,"data-orientation":r.orientation,...m,ref:d,onMouseDown:(0,c.composeEventHandlers)(a.onMouseDown,a=>{f?r.onItemFocus(o):a.preventDefault()}),onFocus:(0,c.composeEventHandlers)(a.onFocus,()=>r.onItemFocus(o)),onKeyDown:(0,c.composeEventHandlers)(a.onKeyDown,a=>{if("Tab"===a.key&&a.shiftKey)return void r.onItemShiftTab();if(a.target!==a.currentTarget)return;let b=function(a,b,c){var d;let e=(d=a.key,"rtl"!==c?d:"ArrowLeft"===d?"ArrowRight":"ArrowRight"===d?"ArrowLeft":d);if(!("vertical"===b&&["ArrowLeft","ArrowRight"].includes(e))&&!("horizontal"===b&&["ArrowUp","ArrowDown"].includes(e)))return A[e]}(a,r.orientation,r.dir);if(void 0!==b){if(a.metaKey||a.ctrlKey||a.altKey||a.shiftKey)return;a.preventDefault();let c=t().filter(a=>a.focusable).map(a=>a.ref.current);if("last"===b)c.reverse();else if("prev"===b||"next"===b){"prev"===b&&c.reverse();let d=c.indexOf(a.currentTarget);c=r.loop?function(a,b){return a.map((c,d)=>a[(b+d)%a.length])}(c,d+1):c.slice(d+1)}setTimeout(()=>B(c))}}),children:"function"==typeof k?k({isCurrentTabStop:s,hasTabStop:null!=x}):k})})});z.displayName=y;var A={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function B(a,b=!1){let c=document.activeElement;for(let d of a)if(d===c||(d.focus({preventScroll:b}),document.activeElement!==c))return}var C=w,D=z},33518,74939,a=>{"use strict";a.s(["changeToneStream",()=>x,"expandContentStream",()=>l,"generateTags",()=>A,"generateTitleStream",()=>r,"getGeminiModels",()=>z,"getOpenRouterModels",()=>y,"polishStream",()=>u,"summarizeStream",()=>o],33518);var b=a.i(3332);let c="https://openrouter.ai/api/v1/chat/completions";function d(){return"https://rainbow-gumption-2fc85c.netlify.app/"}function e(){return"gemini"}function f(){return"mistralai/mistral-7b-instruct"}function g(){return"gemini-2.5-pro"}async function h(a){let{url:c,headers:d,body:e,onChunk:f}=a,g=await fetch(c,{method:"POST",headers:d,body:e});if(!g.ok){let a=await g.text();console.error("Stream API Error:",a);try{let c=JSON.parse(a),d=c.error?.message||"串流請求失敗。";throw(0,b.toast)({title:"串流請求失敗",description:d,variant:"destructive"}),Error(d)}catch(d){let c=`串流請求失敗: ${a}`;throw(0,b.toast)({title:"串流請求失敗",description:a,variant:"destructive"}),Error(c)}}let h=g.body?.getReader();if(!h)throw Error("無法獲取 response reader");let i=new TextDecoder,j="";try{for(;;){let{done:a,value:b}=await h.read();if(a)break;let c=(j+=i.decode(b,{stream:!0})).split("\n");for(let a of(j=c.pop()||"",c))if(a.startsWith("data: ")){let b=a.slice(6);if("[DONE]"===b)continue;try{let a=JSON.parse(b),c=a.choices?.[0]?.delta?.content||a.candidates?.[0]?.content?.parts?.[0]?.text;c&&(console.log("Stream chunk:",c),f(c))}catch(a){console.log("Stream parse error:",a,"data:",b)}}}}finally{h.releaseLock()}}async function i(a){let d=null;if(!d)throw(0,b.toast)({title:"API 金鑰未設定",description:"請在設定中設定 OpenRouter API 金鑰。",variant:"destructive"}),Error("OpenRouter API 金鑰未設定。");let e=await fetch(c,{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:a})});if(!e.ok){let a=await e.text();console.error("OpenRouter API Error:",a);try{let c=JSON.parse(a),d=c.error?.message||"OpenRouter API 請求失敗。";throw(0,b.toast)({title:"API 請求失敗",description:d,variant:"destructive"}),Error(d)}catch(d){let c=`OpenRouter API 請求失敗: ${a}`;throw(0,b.toast)({title:"API 請求失敗",description:a,variant:"destructive"}),Error(c)}}return(await e.json()).choices[0].message.content}async function j(a,b){let d=null;if(!d)throw Error("OpenRouter API 金鑰未設定。");await h({url:c,headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:[{role:"system",content:`You are a creative writing expert. Based on the following prompt, create a detailed, engaging, and structurally complete text. Follow these creative principles:

CREATIVE REQUIREMENTS:
1. **Content Richness**: Add relevant details, examples, and explanations while maintaining the core theme
2. **Structural Integrity**: Include introduction, main content, and conclusion sections
3. **Logical Coherence**: Ensure natural transitions between sections with clear thinking
4. **Reader Engagement**: Use vivid language and concrete descriptions
5. **Practical Value**: Provide meaningful information or insights

CREATIVE STRATEGY:
- **Introduction**: Set the background, spark reader interest, and present the core theme
- **Main Content**: Develop the theme in detail with specific examples, data, or cases
- **Conclusion**: Summarize key points and provide suggestions or inspirational thoughts
- **Appropriate Length**: Expand to 3-5 times the length of the original prompt

STYLE SUGGESTIONS:
- Use first-person or second-person narrative to increase intimacy
- Use rhetorical devices such as metaphors and contrasts when appropriate
- Balance professionalism with readability

Based on the original prompt, create a complete and attractive text:`},{role:"user",content:a}],stream:!0}),onChunk:b})}async function k(a,b){let c=null;if(!c)throw Error("Gemini API 金鑰未設定。");let e=g(),f=d();await h({url:`${f}v1/models/${e}:streamGenerateContent?key=${c}&alt=sse`,headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`請作為一位創意寫作專家，根據以下提示詞創建一個詳細、引人入勝且結構完整的文本。請遵循以下創作原則：

## 創作要求：
1. **內容豐富性**：在保持核心主題的前提下，添加相關的細節、例子和說明
2. **結構完整性**：包含引言、主要內容和結論三個部分
3. **邏輯連貫性**：確保各部分之間的過渡自然，思路清晰
4. **吸引讀者**：使用生動的語言和具體的描述
5. **實用價值**：提供有實際意義的資訊或見解

## 創作策略：
- **引言部分**：設定背景，引起讀者興趣，提出核心主題
- **主要內容**：詳細展開主題，提供具體例子、數據或案例
- **結論部分**：總結要點，提供建議或啟發性思考
- **適當長度**：擴展到原提示詞的 3-5 倍長度

## 風格建議：
- 使用第一人稱或第二人稱敘述，增加親切感
- 適時使用修辭手法，如比喻、對比等
- 保持語言的專業性和可讀性平衡

## 原始提示詞：
${a}

請以此為基礎，創建一個完整而吸引人的文本：`}]}]}),onChunk:b})}async function l(a,b){let c=e();return(console.log("Starting streaming with provider:",c),"gemini"===c)?k(a,b):j(a,b)}async function m(a,b){let d=null;if(!d)throw Error("OpenRouter API 金鑰未設定。");await h({url:c,headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:[{role:"system",content:"Summarize the following text."},{role:"user",content:a}],stream:!0}),onChunk:b})}async function n(a,b){let c=null;if(!c)throw Error("Gemini API 金鑰未設定。");let e=g(),f=d();await h({url:`${f}v1/models/${e}:streamGenerateContent?key=${c}&alt=sse`,headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`請作為一位專業的內容總結專家，分析並總結以下筆記內容。請遵循以下原則：

## 總結要求：
1. **重點提取**：識別並保留最重要的資訊、關鍵概念和核心觀點
2. **結構清晰**：使用簡潔的段落結構，確保邏輯連貫
3. **長度適中**：總結長度約為原文的 1/3 到 1/4，重點突出，避免冗餘
4. **客觀準確**：保持原文的原意，不添加個人解釋或外部資訊
5. **實用價值**：確保總結具有實用性，可以幫助讀者快速了解原文要點

## 總結格式：
- 使用條列式或段落式結構
- 保留重要的數據、日期、名稱等關鍵資訊
- 如果原文有明確的結論或建議，請特別強調

## 原文內容：
${a}

請提供一個結構清晰、重點突出且實用的總結：`}]}]}),onChunk:b})}async function o(a,b){let c=e();return(console.log("Starting summarize streaming with provider:",c),"gemini"===c)?n(a,b):m(a,b)}async function p(a,b){let d=null;if(!d)throw Error("OpenRouter API 金鑰未設定。");await h({url:c,headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:[{role:"system",content:"Generate a concise title for the following text."},{role:"user",content:a.substring(0,200)}],stream:!0}),onChunk:b})}async function q(a,b){let c=null;if(!c)throw Error("Gemini API 金鑰未設定。");let e=g(),f=d();await h({url:`${f}v1/models/${e}:streamGenerateContent?key=${c}&alt=sse`,headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`請作為一位專業的標題撰寫專家，根據以下內容生成一個吸引人且準確的標題。請遵循以下原則：

## 標題要求：
1. **簡潔有力**：標題長度控制在 5-15 個中文字，突出重點
2. **吸引注意力**：使用能引起讀者興趣的關鍵詞或表述
3. **準確反映內容**：標題必須準確代表原文的核心主題和主要觀點
4. **SEO 友好**：考慮搜尋引擎優化，使用相關關鍵詞
5. **獨特性**：避免使用常見的通用性標題

## 標題風格建議：
- 問題式標題：以疑問句形式吸引讀者
- 結果式標題：強調內容帶來的價值或結果
- 數字式標題：如果內容涉及步驟、方法或清單
- 故事式標題：如果內容有敘事性質

## 內容預覽：
${a.substring(0,300)}

請生成 3 個不同的標題選項，並為每個標題簡要說明選擇的原因：`}]}]}),onChunk:b})}async function r(a,b){let c=e();return(console.log("Starting title generation streaming with provider:",c),"gemini"===c)?q(a,b):p(a,b)}async function s(a,b){let d=null;if(!d)throw Error("OpenRouter API 金鑰未設定。");await h({url:c,headers:{"Content-Type":"application/json",Authorization:`Bearer ${d}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:[{role:"system",content:`You are a professional text editor and writing consultant. Perform comprehensive polishing and improvement on the following text. Follow these editing principles:

EDITING FOCUS:
1. **Grammatical Correctness**: Correct all grammar errors, punctuation usage, and sentence structure issues
2. **Expression Clarity**: Simplify complex sentences to make them clearer and easier to understand
3. **Logical Coherence**: Ensure smooth logical flow between paragraphs and natural transitions between ideas
4. **Word Precision**: Use more appropriate and professional vocabulary to replace vague or inaccurate expressions
5. **Style Consistency**: Maintain consistency in overall style
6. **Length Optimization**: Adjust length appropriately while preserving the original meaning, avoiding redundancy

IMPROVEMENT STRATEGY:
- Convert passive voice to active voice when appropriate
- Merge duplicate or redundant sentences
- Improve paragraph structure and sentence rhythm
- Add necessary connecting words to improve fluency
- Ensure correct use of professional terminology

OUTPUT REQUIREMENTS:
Please provide the polished complete text and briefly explain the main improvement points at the end.

ORIGINAL CONTENT:`},{role:"user",content:a}],stream:!0}),onChunk:b})}async function t(a,b){let c=null;if(!c)throw Error("Gemini API 金鑰未設定。");let e=g(),f=d();await h({url:`${f}v1/models/${e}:streamGenerateContent?key=${c}&alt=sse`,headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`請作為一位專業的文字編輯和寫作顧問，對以下文字進行全面的潤飾和改進。請遵循以下編輯原則：

## 編輯重點：
1. **語法正確性**：修正所有語法錯誤、標點符號使用和句子結構問題
2. **表達清晰度**：簡化複雜的句子，使表達更清晰易懂
3. **邏輯連貫性**：確保段落之間的邏輯流暢，想法之間的過渡自然
4. **用詞精準性**：使用更恰當、專業的詞彙替代模糊或不準確的表達
5. **風格一致性**：保持整體風格的一致性
6. **長度優化**：在保持原意的前提下，適度調整長度，避免冗餘

## 改進策略：
- 修正被動語態為主動語態（適當時機）
- 合併重複或多餘的句子
- 改善段落結構和句子節奏
- 增加必要的連接詞以改善流暢度
- 確保專業術語使用正確

## 輸出要求：
請提供潤飾後的完整文字，並在文末簡要說明主要的改進點。

## 原文內容：
${a}

請開始潤飾：`}]}]}),onChunk:b})}async function u(a,b){let c=e();return(console.log("Starting polish streaming with provider:",c),"gemini"===c)?t(a,b):s(a,b)}async function v(a,b,d){let e=null;if(!e)throw Error("OpenRouter API 金鑰未設定。");let g="";switch(b){case"正式":g="Rewrite the text in a formal tone.";break;case"休閒":g="Rewrite the text in a casual and friendly tone.";break;case"專業":g="Rewrite the text in a professional and authoritative tone.";break;case"幽默":g="Rewrite the text in a witty and humorous tone."}await h({url:c,headers:{"Content-Type":"application/json",Authorization:`Bearer ${e}`,"HTTP-Referer":"http://localhost:3000","X-Title":"MyNote AI Assistant"},body:JSON.stringify({model:f(),messages:[{role:"system",content:g},{role:"user",content:a}],stream:!0}),onChunk:d})}async function w(a,b,c){let e=null;if(!e)throw Error("Gemini API 金鑰未設定。");let f="";switch(b){case"正式":f="請您扮演一位專業的公文與學術寫作專家。將以下文字改寫為正式、結構化且客觀的語氣，使其適用於官方文件或學術論文。請避免使用口語、縮寫和個人代名詞，並專注於清晰、精確與專業的表達。";break;case"休閒":f="請您扮演一位友善且健談的朋友。將以下文字改寫為輕鬆、休閒且友好的語氣。請適度使用生活化用語、個人化的例子或比喻，使其更平易近人、更容易閱讀。";break;case"專業":f="請您扮演一位行業內的頂尖專家。將以下文字改寫為專業、權威且知識豐富的語氣。請在適當之處使用行業術語，保持自信的口吻，並確保資訊呈現清晰、簡潔。";break;case"幽默":f="請您扮演一位機智且風趣的作家。將以下文字改寫為巧妙且幽默的語氣。請運用雙關語、反諷或輕鬆的例子，使內容更具娛樂性，同時仍能傳達核心訊息。"}let i=g(),j=d();await h({url:`${j}v1/models/${i}:streamGenerateContent?key=${e}&alt=sse`,headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:`${f}

${a}`}]}]}),onChunk:c})}async function x(a,b,c){let d=e();return(console.log("Starting changeTone streaming with provider:",d),"gemini"===d)?w(a,b,c):v(a,b,c)}async function y(){try{let a=await fetch("https://openrouter.ai/api/v1/models");if(!a.ok)throw Error("無法獲取 OpenRouter 模型列表。");return(await a.json()).data.map(a=>({id:a.id,name:a.name||a.id}))}catch(a){return console.error("獲取 OpenRouter 模型失敗:",a),[]}}async function z(){return Promise.resolve([{id:"gemini-2.5-pro",name:"Gemini 2.5 Pro"},{id:"gemini-2.5-flash",name:"Gemini 2.5 Flash"},{id:"gemini-2.0-flash-exp",name:"Gemini 2.0 Flash Experimental"}])}async function A(a){let b,c=e(),f=`請作為一位專業的內容分析師和分類專家，仔細閱讀以下筆記內容，並提取出最核心、最相關的關鍵字作為標籤。

分析要求：
1. **核心主題**：識別文章的主要主題和核心概念。
2. **關鍵實體**：提取出重要的人物、地點、組織、技術或專有名詞。
3. **內容分類**：判斷文章的性質，例如是「教學」、「評論」、「筆記」還是「想法」。
4. **精簡化**：確保每個標籤都是簡潔且具代表性的詞彙。
5. **數量控制**：提供 3 到 5 個最相關的標籤。

請以 JSON 格式的陣列輸出，例如：["標籤一", "標籤二", "標籤三"]

原文內容：
${a}

請僅提供 JSON 格式的標籤陣列：`;if("gemini"===c){let a=null;if(!a)throw Error("Gemini API 金鑰未設定。");let c=g(),e=d(),h=await fetch(`${e}v1/models/${c}:generateContent?key=${a}`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({contents:[{parts:[{text:f}]}]})});if(!h.ok)throw console.error("Gemini API Error:",await h.text()),Error("Gemini API 請求失敗。");b=(await h.json()).candidates[0].content.parts[0].text}else b=await i([{role:"system",content:'You are a professional content analyst. Extract 3 to 5 core keywords from the following text to be used as tags. Return them as a JSON array of strings. For example: ["Tag1", "Tag2", "Tag3"]'},{role:"user",content:a}]);try{let a=b.match(/(\[[\s\S]*\])/);if(a){let b=JSON.parse(a[0]);return Array.isArray(b)?b.map(a=>String(a)):[]}return b.split(",").map(a=>a.trim()).filter(Boolean)}catch(a){return console.error("Failed to parse tags:",a),b.split(",").map(a=>a.trim()).filter(Boolean)}}a.s(["Loader2",()=>B],74939);let B=(0,a.i(93007).default)("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])}];

//# sourceMappingURL=renderer_56c0462f._.js.map